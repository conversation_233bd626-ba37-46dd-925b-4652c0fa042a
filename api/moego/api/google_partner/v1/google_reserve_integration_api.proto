syntax = "proto3";

package moego.api.google_partner.v1;

import "moego/models/google_partner/v1/google_reserve_integration_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/google_partner/v1;googlepartnerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.google_partner.v1";

// update Google reserve integration request param
message UpdateGoogleReserveIntegrationParam {
  // enable
  optional bool enabled = 1;
}

// get Google reserve integration request param
message GetOrInsertGoogleReserveIntegrationParam {}

// Google reserve integration service
service GoogleReserveIntegrationService {
  // get Google reserve integration
  rpc GetOrInsertGoogleReserveIntegration(GetOrInsertGoogleReserveIntegrationParam) returns (moego.models.google_partner.v1.GoogleReserveIntegrationModel) {}
  // update Google reserve integration
  rpc UpdateGoogleReserveIntegration(UpdateGoogleReserveIntegrationParam) returns (moego.models.google_partner.v1.GoogleReserveIntegrationModel) {}
}
