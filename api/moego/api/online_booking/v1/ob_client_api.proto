syntax = "proto3";

package moego.api.online_booking.v1;

import "moego/models/customer/v1/customer_defs.proto";
import "moego/models/customer/v1/customer_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/online_booking/v1/ob_config_enums.proto";
import "moego/models/payment/v1/credit_card_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.online_booking.v1";

// update client request params
message UpdateOBClientRequest {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // customer info
  moego.models.customer.v1.CustomerDef customer = 3;
  // custom questions
  map<string, string> custom_questions = 4;
  // address
  optional UpdateOBClientRequestAddress address = 5;

  // Update OB client request address
  message UpdateOBClientRequestAddress {
    // Unique identifier for the address, must be greater than 0 if provided.
    // If not provided, a new address will be created.
    optional int64 id = 3 [(validate.rules).int64.gt = 0];
    // Primary address line, required if provided.
    optional string address1 = 4 [(validate.rules).string = {max_len: 255}];
    // Secondary address line, optional and can be empty.
    optional string address2 = 5 [(validate.rules).string = {max_len: 255}];
    // City name, required if provided.
    optional string city = 6 [(validate.rules).string = {max_len: 255}];
    // Country name, required if provided.
    optional string country = 7 [(validate.rules).string = {max_len: 255}];
    // Latitude, must be within valid global coordinates if provided.
    optional string lat = 8 [(validate.rules).string = {max_len: 100}];
    // Longitude, must be within valid global coordinates if provided.
    optional string lng = 9 [(validate.rules).string = {max_len: 100}];
    // State or province.
    optional string state = 10 [(validate.rules).string = {max_len: 255}];
    // Postal or ZIP code, required if provided.
    optional string zipcode = 11 [(validate.rules).string = {max_len: 50}];
    // Whether this address is the primary address for the customer.
    optional int32 is_primary = 12 [(validate.rules).int32 = {
      in: [
        0,
        1
      ]
    }];
    // Whether this address is the profile request address.
    optional bool is_profile_request_address = 13;
  }
}

// get client request params
message GetOBClientRequest {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// online booking client info response
message OBClientInfoResponse {
  // customer info
  moego.models.customer.v1.CustomerModelOnlineBookingView customer = 1;
  // credit card list
  repeated moego.models.payment.v1.CreditCardModel credit_card_list = 2;
  // ob custom question answers
  map<string, string> custom_questions = 3;
  // necessary problem information needs to be updated
  bool required_update = 4;
  // blocked service item types
  repeated moego.models.offering.v1.ServiceItemType blocked_service_item_types = 5;
}

// get client request params
message UpdateOBClientResponse {
  // customer id
  int32 id = 1;
  // address response
  UpdateOBClientAddressResponse address = 2;

  // Update OB client address response
  message UpdateOBClientAddressResponse {
    // Id of the address.
    int64 id = 1;
    // Unique identifier for the customer associated with this address.
    int64 customer_id = 2;
    // Primary address line.
    string address1 = 3;
    // Secondary address line.
    string address2 = 4;
    // City name.
    string city = 5;
    // Country name.
    string country = 6;
    // Latitude.
    string lat = 7;
    // Longitude.
    string lng = 8;
    // State or province.
    string state = 9;
    // Postal or ZIP code.
    string zipcode = 10;
    // Whether this address is the primary address for the customer.
    int32 is_primary = 11;
    // Whether this address is the profile request address,
    // if true, id uses profile_request_address id,
    // if false, id uses business_customer_address id.
    bool is_profile_request_address = 12;
  }
}

//payment type
message OBClientPaymentTypeResponse {
  // pt
  .moego.models.online_booking.v1.PaymentType payment_type = 1;
  // accept client
  int32 accept_client = 2;
  // accept rule
  string accept_rule = 3;
  // prepay type
  int32 prepay_type = 4;
  // prepay tip enable
  int32 prepay_tip_enable = 5;
  // deposit type
  int32 deposit_type = 6;
  // deposit percentage
  int32 deposit_percentage = 7;
  // deposit amount
  double deposit_amount = 8;
  // pre auth tip enable
  int32 pre_auth_tip_enable = 9;
  // prepay policy
  string prepay_policy = 10;
  // pre auth policy
  string pre_auth_policy = 11;
  // cancellation policy
  string cancellation_policy = 12;
}

// online booking client service
service OBClientService {
  // get client info
  rpc GetOBClientInfo(GetOBClientRequest) returns (OBClientInfoResponse);
  // update client info
  rpc UpdateOBClient(UpdateOBClientRequest) returns (UpdateOBClientResponse);
  //query ob config payment type and save
  rpc GetOnlineBookingClientPaymentType(GetOBClientRequest) returns (OBClientPaymentTypeResponse);
}
