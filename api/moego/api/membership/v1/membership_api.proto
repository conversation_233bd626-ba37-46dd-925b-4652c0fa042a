// @since 2024-06-13 11:16:20
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.membership.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/membership/v1/membership_defs.proto";
import "moego/models/membership/v1/membership_models.proto";
import "moego/models/membership/v1/redeem_models.proto";
import "moego/models/organization/v1/tax_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/membership/v1;membershipapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.membership.v1";

// the membership service
service MembershipService {
  // create membership
  rpc CreateMembership(CreateMembershipParams) returns (CreateMembershipResult);
  // get membership
  rpc GetMembership(GetMembershipParams) returns (GetMembershipResult);
  // list membership
  rpc ListMemberships(ListMembershipsParams) returns (ListMembershipsResult);
  // update membership
  rpc UpdateMembership(UpdateMembershipParams) returns (UpdateMembershipResult);
  // delete membership (internal only)
  rpc DeleteMembership(DeleteMembershipParams) returns (DeleteMembershipResult);
  // 查询推荐会员内容
  rpc ListRecommendedMemberships(ListRecommendMembershipsParams) returns (ListRecommendMembershipsResult);
  // 查询用户的会员
  rpc ListMembershipsForCustomer(ListMembershipsForCustomerParams) returns (ListMembershipsForCustomerResult);
  // 查询核销会员权益历史
  rpc GetRedeemHistory(GetRedeemHistoryParams) returns (GetRedeemHistoryResult);
  // apply membership
  rpc ApplyMembership(ApplyMembershipParams) returns (ApplyMembershipResult);
  // remove membership
  rpc RemoveMembership(RemoveMembershipParams) returns (RemoveMembershipResult);
  // 查询perk的周期值
  rpc ListAllPerkCycle(ListAllPerkCycleParams) returns (ListAllPerkCycleResult);
  // get perk usage detail
  rpc GetPerkUsageDetail(GetPerkUsageDetailParams) returns (GetPerkUsageDetailResult);
  // 权益转换
  rpc TransferCredits(TransferCreditsParams) returns (TransferCreditsResult);
  // list membership for sale
  rpc ListMembershipsForSale(ListMembershipsForSaleParams) returns (ListMembershipsForSaleResult);
}

// list membership for sale params
message ListMembershipsForSaleParams {
  //filter
  message Filter {
    // selected pet
    repeated int64 pet_ids = 1;
    // filter by status
    optional moego.models.membership.v1.MembershipModel.Status status = 2 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
    // name like, case insensitive
    optional string name_like = 3 [(validate.rules).string = {max_len: 50}];
  }
  // filter
  optional Filter filter = 1;

  // pagination, default size is 20
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// list memberships for sale result
message ListMembershipsForSaleResult {
  // the membership
  repeated moego.models.membership.v1.MembershipModel memberships = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// create membership params
message CreateMembershipParams {
  // the membership def
  moego.models.membership.v1.MembershipCreateDef membership_def = 1 [(validate.rules).message = {required: true}];
  // discount
  optional moego.models.membership.v1.CreateMembershipDiscountBenefitsDef membership_discount_benefits = 2;
  // quantity
  optional moego.models.membership.v1.CreateMembershipQuantityBenefitsDef membership_quantity_benefits = 3;
}

// create membership result
message CreateMembershipResult {
  // the created membership
  moego.models.membership.v1.MembershipModel membership = 1;
  // discount
  optional moego.models.membership.v1.MembershipDiscountBenefitsDef membership_discount_benefits = 2;
  // quantity
  optional moego.models.membership.v1.MembershipQuantityBenefitsDef membership_quantity_benefits = 3;
}

// get membership params
message GetMembershipParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get membership result
message GetMembershipResult {
  // the membership
  moego.models.membership.v1.MembershipModel membership = 1;
  // the tax
  moego.models.organization.v1.TaxRuleModel tax = 2;
  // discount
  optional moego.models.membership.v1.MembershipDiscountBenefitsDef membership_discount_benefits = 3;
  // quantity
  optional moego.models.membership.v1.MembershipQuantityBenefitsDef membership_quantity_benefits = 4;
}

// list membership params
message ListMembershipsParams {
  // filter by status
  optional moego.models.membership.v1.MembershipModel.Status status = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // name like, case insensitive
  optional string name_like = 2 [(validate.rules).string = {max_len: 50}];
  // pagination, default size is 20
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// list membership result
message ListMembershipsResult {
  // memberships
  repeated moego.models.membership.v1.MembershipModel memberships = 1;
  // membership summaries
  repeated moego.models.membership.v1.MembershipSummaryModel membership_summaries = 2;
  // membership discount benefits
  repeated moego.models.membership.v1.MembershipDiscountBenefitsDef membership_discount_benefits = 3;
  // membership quantity benefit
  repeated moego.models.membership.v1.MembershipQuantityBenefitsDef membership_quantity_benefits = 4;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// create membership params
message UpdateMembershipParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the current revision, if set, will check the current revision
  // is it or not, if not, will throw an error.
  optional int32 revision = 2 [(validate.rules).int32 = {gte: 0}];

  // the membership def
  moego.models.membership.v1.MembershipUpdateDef membership_def = 3 [(validate.rules).message = {required: true}];

  // discount
  optional moego.models.membership.v1.UpdateMembershipDiscountBenefitsDef membership_discount_benefits = 4;
  // quantity
  optional moego.models.membership.v1.UpdateMembershipQuantityBenefitsDef membership_quantity_benefits = 5;
}

// create membership result
message UpdateMembershipResult {
  // the updated membership
  moego.models.membership.v1.MembershipModel membership = 1;
  // discount
  optional moego.models.membership.v1.MembershipDiscountBenefitsDef membership_discount_benefits = 2;
  // quantity
  optional moego.models.membership.v1.MembershipQuantityBenefitsDef membership_quantity_benefits = 3;
}

// delete membership (internal only) params
message DeleteMembershipParams {
  // the membership id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete membership (internal only) result
message DeleteMembershipResult {}

// list recommend memberships params
message ListRecommendMembershipsParams {
  // Filter
  message Filter {
    // membership ids
    repeated int64 target_membership_ids = 1 [(validate.rules).repeated = {
      max_items: 1000
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];
  }
  // customer id
  int64 customer_id = 1 [(validate.rules).int64 = {gt: 0}];
  // context
  optional moego.models.membership.v1.RedeemContext context = 2;
  // filter
  optional Filter filter = 3;
}

// list recommended memberships result
message ListRecommendMembershipsResult {
  // the recommended memberships
  repeated moego.models.membership.v1.MembershipModel recommended = 1;
  // all memberships
  repeated moego.models.membership.v1.MembershipModel all = 2;
  // the recommend benefit combination
  repeated moego.models.membership.v1.BenefitRecommendView benefit_combination = 3;
}

// list user memberships params
message ListMembershipsForCustomerParams {
  // the customer id
  int64 customer_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// list user memberships result
message ListMembershipsForCustomerResult {
  // the user memberships
  repeated moego.models.membership.v1.MembershipModel memberships = 1;
}

// query benefit summary params
message QueryBenefitSummaryParams {
  // the customer id
  int64 customer_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the membership id
  int64 membership_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// query benefit summary result
message QueryBenefitSummaryResult {
  // discounts
  repeated moego.models.membership.v1.BenefitSummaryView discounts = 1;
  // quantities
  repeated moego.models.membership.v1.BenefitSummaryView quantities = 2;
}

// get redeem history params
message GetRedeemHistoryParams {
  // the customer id
  int64 customer_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the membership id
  int64 membership_id = 2 [(validate.rules).int64 = {gt: 0}];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// get redeem history result
message GetRedeemHistoryResult {
  // the included benefits
  repeated moego.models.membership.v1.IncludeBenefitView included_benefits = 1;

  // the redeem history
  repeated moego.models.membership.v1.RedeemHistory redeem_history = 2;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// apply membership params
message ApplyMembershipParams {
  // order id
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // customer id
  int64 customer_id = 2 [(validate.rules).int64 = {gt: 0}];
  // membership ids, if empty, will auto apply memberships
  repeated int64 membership_ids = 3 [(validate.rules).repeated = {
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// apply membership result
message ApplyMembershipResult {
  // message
  optional string message = 1;
}

// remove membership params
message RemoveMembershipParams {
  // order id
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // customer id
  int64 customer_id = 2 [(validate.rules).int64 = {gt: 0}];
  // membership ids
  repeated int64 membership_ids = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// remove membership result
message RemoveMembershipResult {}

// list all perk cycle
message ListAllPerkCycleParams {
  // membership id
  int64 membership_id = 1;
  // customer id
  int64 customer_id = 2;
}

// list all perk cycle result
message ListAllPerkCycleResult {
  // the perk cycle items
  repeated models.membership.v1.PerkCycleItemDef perk_cycle_item = 1;
}

// get perk usage detail
message GetPerkUsageDetailParams {
  // filter
  message Filter {
    // validity start time
    optional google.protobuf.Timestamp validity_start_time = 2;
  }
  // filter
  optional Filter filter = 1;

  // membership id
  int64 membership_id = 2;

  // customer id
  int64 customer_id = 3;
}

// get perk usage detail result
message GetPerkUsageDetailResult {
  // the included benefits
  repeated moego.models.membership.v1.IncludeBenefitView included_benefits = 1;
}

// transfer credits params
message TransferCreditsParams {
  // customer id
  int64 customer_id = 1;
  // quantity id
  int64 quantity_id = 2;
  // transfer quantity nums
  google.protobuf.Timestamp validity_start_time = 3;
  // transfer credit nums
  int64 transfer_quantity_num = 4;
  // transfer credit nums
  int64 transfer_credit_num = 5;
}

// transfer credits result
message TransferCreditsResult {}
