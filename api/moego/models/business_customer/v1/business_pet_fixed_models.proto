syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// pet fixed model
message BusinessPetFixedModel {
  // fixed id
  int64 id = 1;

  // fixed name
  string name = 2;

  // fixed sort. The larger the sort number, the higher the priority.
  int32 sort = 3;

  // if the pet fixed is deleted
  bool deleted = 4;
}

// pet fixed name view
message BusinessPetFixedNameView {
  // fixed name
  string name = 2;
}
