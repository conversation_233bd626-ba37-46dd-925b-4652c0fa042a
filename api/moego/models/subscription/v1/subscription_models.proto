syntax = "proto3";

package moego.models.subscription.v1;

import "google/protobuf/timestamp.proto";
import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/utils/v1/time_period.proto";
import "moego/utils/v2/condition_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/subscription/v1;subscriptionmodpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.subscription.v1";

// 订阅
message Subscription {
  // 状态
  enum Status {
    // 未定义
    STATUS_UNSPECIFIED = 0;
    // 试用期，试用期内可以免费使用服务
    TRIAL = 1;
    // 待激活，无法使用服务
    PENDING = 2;
    // 激活，一定可以使用服务
    ACTIVE = 3;
    // 宽限期，宽限期内可以继续使用服务，但会累计欠费
    GRACE = 4;
    // 已过期，无法使用服务，但订阅仍然保留
    EXPIRED = 5;
    // 已取消，无法使用服务，且会取消订阅
    CANCELLED = 6;
    // 暂停，暂停期内无法使用服务，但会保留订阅
    PAUSED = 7;
    // 未完成，订阅创建未完成
    INCOMPLETE = 8;
  }
  // ID
  int64 id = 1;
  // 名称，继承自 plan product
  string name = 2;
  // 描述，继承自 plan product
  string description = 3;
  // 状态
  Status status = 4;
  // 订阅的有效期，起点为当前扣款时间，终点为下一次扣款时间
  google.type.Interval validity_period = 5;
  // 总商品价格，冗余字段，将所有的 PurchaseDetail 的价格加和得到
  google.type.Money total_price = 6;
  // 总税，冗余字段，将所有的 PurchaseDetail 的税加和得到
  google.type.Money total_tax = 7;
  // 计费周期
  moego.utils.v1.TimePeriod billing_cycle = 8;
  // 宽限期
  moego.utils.v1.TimePeriod grace_period = 9;
  // 是否在周期结束时取消订阅
  bool cancel_at_period_end = 10;
  // 买家
  User buyer = 11;
  // 卖家
  User seller = 12;
  // 每个订阅仅有唯一一个套餐产品，冗余字段，可在 PurchaseDetail 查到
  int64 plan_product_id = 13;
  // 最新的 invoice ID
  int64 latest_invoice_id = 14;
  // payment 的映射 ID
  int64 payment_subscription_id = 15;
  // 最新的 card on file ID，即当前订阅的扣款卡
  string latest_card_on_file_id = 16;
  // 执行者（staff）的 ID
  int64 operator_id = 17;
  // 暂停时间
  google.protobuf.Timestamp paused_at = 18;
  // 自动恢复时间
  google.protobuf.Timestamp auto_resume_at = 19;
  // 取消原因，只有处于未激活状态的订阅此字段才有效
  string cancel_reason = 20;
}

// 订阅统计
message SubscriptionStat {
  // 产品 ID
  int64 product_id = 1;
  // 订阅状态
  Subscription.Status status = 2;
  // 是否在周期结束时取消订阅
  bool cancel_at_period_end = 3;
  // 数量
  int32 count = 4;
}

// 购买者订阅信息
message BuyerSubscription {
  // 购买者
  models.subscription.v1.User buyer = 1;
  // 订阅信息
  repeated models.subscription.v1.Subscription subscriptions = 2;
  // 订阅总数
  int32 total = 3;
}

// 购买行为
message Purchase {
  // 产品 ID
  int64 product_id = 1;
  // 价格 ID
  int64 price_id = 2;
  // 购买数量
  int32 quantity = 3;
  // 需要应用的税 IDs
  repeated int64 tax_ids = 4;
}

// 购买详情，作为创建订阅时购买产品的快照
// 记录了购买时的价格和税，也用于计算订阅当前包含多少产品
message PurchaseDetail {
  // 状态
  enum Status {
    // 未定义
    STATUS_UNSPECIFIED = 0;
    // 有效
    VALID = 1;
    // 无效
    INVALID = 2;
  }

  // 税，作为购买时记录的税快照
  message Tax {
    // ID
    int64 id = 1;
    // 购买详情 ID
    int64 purchase_detail_id = 2;
    // 名称
    string name = 3;
    // 金额
    google.type.Money amount = 4;
    // 税率
    double rate = 5;
    // 税种代码
    string code = 6;
  }

  // ID
  int64 id = 1;
  // 订阅 ID
  int64 subscription_id = 2;
  // 产品
  Product product = 3;
  // 价格
  Price price = 4;
  // 购买数量
  int32 quantity = 5;
  // 应用的税
  repeated Tax taxes = 6;
  // 状态
  Status status = 7;
  // 买家
  User buyer = 8;
  // 冗余总价和总税，用来给前端展示
  // 总价
  google.type.Money total_price = 9;
  // 总税
  google.type.Money total_tax = 10;
  // 所应用的discount ID
  repeated int64 discount_ids = 11;
}

// 产品
message Product {
  // 产品类型
  enum Type {
    // 未定义
    TYPE_UNSPECIFIED = 0;
    // 套餐，所有订阅必须从套餐中创建
    PLAN = 1;
    // 增值服务，可单独购买，也可作为套餐的附加服务合并计费
    ADDON = 2;
  }

  // 业务类型
  enum BusinessType {
    // 未定义
    BUSINESS_TYPE_UNSPECIFIED = 0;
    // membership
    MEMBERSHIP = 1;
    // accounting
    ACCOUNTING = 2;
    // payroll
    PAYROLL = 3;
  }

  // ID
  int64 id = 1;
  // 名称
  string name = 2;
  // 描述
  string description = 3;
  // 类型
  Type type = 4;
  // seller 需要设置到 product metadata 中
  User seller = 5;
  // 购买限制
  PurchaseLimit purchase_limit = 6;
  // 产品包含的功能 ID
  repeated int64 feature_ids = 7;
  // 下游 payment 的映射 ID
  int64 payment_product_id = 8;
  // 业务类型
  BusinessType business_type = 9;
  // extra
  ProductExtra extra = 10;
}

// 产品额外信息
message ProductExtra {
  // 额外信息
  oneof extra {
    // accounting
    AccountingExtra accounting_extra = 1;
  }
}

// accounting 产品的额外信息
message AccountingExtra {
  // 浮动定价
  message FloatingPrice {
    // 价格表行
    message PriceTableRow {
      // 营业额
      string smb_revenue = 1;
      // 价格
      string smb_price = 2;
      // 价格的Money
      google.type.Money smb_price_money = 3;
    }

    // 描述
    string description = 1;
    // 价格表
    repeated PriceTableRow price_table = 2;
  }

  // 仅用于显示在前端的文案，不可用作业务计算逻辑
  message Feature {
    // 用于显示在前端的文案，如："Basic including:", "Everything in basic, plus:"
    string title = 1;
    // 用于显示在前端的feature文案，注意仅用于营销文案，无任何业务逻辑，
    repeated string features = 2;
  }

  // 用于显示在前端的文案之一，如："Basic including:", "Everything in basic, plus:"
  string features_title = 1;
  // 用于显示在前端的feature文案，注意仅用于营销文案，无任何业务逻辑，
  // 如："Effortless integration with MoeGo for invoices, payroll, and capital tracking"
  repeated string features = 2;
  // 用于显示在前端的文案，展示浮动价格
  optional FloatingPrice floating_price = 3;
  // 用于显示在前端的文案，展示功能
  repeated Feature feature_texts = 4;
}

// 价格
message Price {
  // 计价类型
  enum Type {
    // 未定义
    TYPE_UNSPECIFIED = 0;
    // 一次性付费
    ONE_TIME = 1;
    // 循环付费
    CYCLE = 2;
  }

  // 预审
  message Prequalification {
    // 是否需要预审
    bool required = 1;
    // 预审规则
    oneof rule {
      // 营业额预审规则
      RevenueRule revenue_rule = 2;
    }
    // 是否通过预审，注意只有 "请求携带 buyer" 时才有值
    optional bool passed = 3;
  }

  // 营业额预审规则: 在过去的一段时间 period 内，营业额满足 revenue_condition
  message RevenueRule {
    // peroid
    moego.utils.v1.TimePeriod period = 1;
    // revenue condition
    moego.utils.v2.Predicate revenue_condition = 2;
  }

  // ID
  int64 id = 1;
  // 产品 ID
  int64 product_id = 2;
  // 名称
  string name = 3;
  // 类型
  Type type = 4;
  // 单价
  google.type.Money unit_amount = 5;
  // 计费周期
  moego.utils.v1.TimePeriod billing_cycle = 6;
  // JSON格式的价格模型规则 TODO: 定义具体的规则，如固定计价、按量计价、阶梯计价等
  // 需要设置 application_fee_percent
  string rules = 7;
  // 下游 payment 的映射 ID，由于我们的 tax 设计比较蠢，这里暂时无法映射
  int64 payment_price_id = 8;
  // 预审
  Prequalification prequalification = 9;
}

// 用户
message User {
  // 用户类型
  enum Type {
    // 未定义
    TYPE_UNSPECIFIED = 0;
    // MoeGo
    MOEGO = 1;
    // 账号
    ACCOUNT = 2;
    // 企业
    ENTERPRISE = 3;
    // 公司
    COMPANY = 4;
    // 商家
    BUSINESS = 5;
    // 顾客
    CUSTOMER = 6;
    // 员工
    STAFF = 7;
  }
  // ID
  int64 id = 1;
  // 类型
  Type type = 2;
}

// 购买限制
message PurchaseLimit {
  // 购买限制类型
  enum Type {
    // 未定义
    TYPE_UNSPECIFIED = 0;
    // 无限制
    UNLIMITED = 1;
    // 限制购买次数
    COUNT = 2;
  }
  // 类型
  Type type = 1;
  // 限制次数
  int32 count = 2;
}

// 功能
message Feature {
  // 功能键，用于业务系统识别功能 = feature code
  enum Key {
    // 未定义
    KEY_UNSPECIFIED = 0;
    // Accounting basic
    ACCOUNTING_BASIC = 1;
    // Accounting full-service
    ACCOUNTING_FULL_SERVICE = 2;
    // ACCOUNTING_ONBOARDING, 只包含 onboarding 操作，不含其他权益
    ACCOUNTING_ONBOARDING = 3;
    // Membership service discount
    MEMBERSHIP_SERVICE_DISCOUNT = 4;
    // Membership add on discount
    MEMBERSHIP_ADDON_DISCOUNT = 5;
    // Membership product discount
    MEMBERSHIP_PRODUCT_DISCOUNT = 6;
    // Membership service & add on quantity
    MEMBERSHIP_SERVICE_ADDON_QUANTITY = 7;
    // Credit
    CREDIT_CREDIT_POINT = 8;
  }

  // 功能设置
  message Setting {
    // 设置类型
    oneof setting {
      // 开关
      OnOff on_off = 1;
      // 计数
      Count count = 2;
      // 访问列表
      AccessList access_list = 3;
    }
  }

  // ID
  int64 id = 1;
  // 功能键，作为业务方识别功能的唯一标识
  Key key = 2;
  // 名称
  string name = 3;
  // 描述
  string description = 4;
  // 功能配置，包含使用时需要的属性和余量控制
  Setting setting = 5;
}

// 开关
message OnOff {
  // 是否开启
  bool on = 1;
}

// 计数
message Count {
  // 已使用数量
  int64 used_amount = 1;
  // 已分配数量
  int64 allocated_amount = 2;
  // 总数量
  int64 total_amount = 3;
  // 单位
  string unit = 4;
  // 是否无限制
  bool unlimited = 5;
  // 是否可刷新
  bool renewable = 6;
}

// 访问列表
message AccessList {
  // 允许列表
  repeated string allow_list = 1;
  // 拒绝列表
  repeated string deny_list = 2;
}

// 许可证
message License {
  // 状态
  enum Status {
    // 未定义
    STATUS_UNSPECIFIED = 0;
    // 有效
    VALID = 1;
    // 无效
    INVALID = 2;
  }

  // ID
  int64 id = 1;
  // 订阅 ID
  int64 subscription_id = 2;
  // 继承关系，记录当前节点到根节点的路径，形如 license_grand_parent_id/license_parent_id/license_id
  // for change plan and upgrade/downgrade
  string inherit_path = 3;
  // 所有者
  User owner = 4;
  // 状态
  Status status = 5;
}

// 权益
message Entitlement {
  // ID
  int64 id = 1;
  // 许可证 ID
  int64 license_id = 2;
  // 产品 ID
  int64 product_id = 3;
  // 功能
  Feature feature = 4;
}

// 修订记录
message Revision {
  // 类型
  enum Type {
    // 未定义
    TYPE_UNSPECIFIED = 0;
    // 创建订阅
    CREATE_SUBSCRIPTION = 1;
    // 修改订阅
    CHANGE_SUBSCRIPTION = 2;
    // 升级订阅
    UPGRADE_SUBSCRIPTION = 3;
    // 降级订阅
    DOWNGRADE_SUBSCRIPTION = 4;
    // 购买产品
    PURCHASE_PRODUCT = 5;
    // 分配 License
    ASSIGN_LICENSE = 6;
    // 修改 Entitlement
    CHANGE_ENTITLEMENT = 7;
  }
  // ID
  int64 id = 1;
  // 类型
  Type type = 2;
  // 详情
  RevisionDetail detail = 3;
  // 发生时间
  google.protobuf.Timestamp created_at = 4;
}

// 修订记录详情
message RevisionDetail {
  // 详情
  oneof detail {
    // TODO(arkxiong): 定义所有的事件结构
    // 创建订阅
    CreateSubscriptionDetail create_subscription_detail = 6;
    // 修改权益
    UpdateEntitlementDetail update_entitlement_detail = 7;
  }
}

// 创建订阅详情
message CreateSubscriptionDetail {
  // 已创建的订阅
  Subscription created_subscription = 1;
}

// 修改entitlement 详情
message UpdateEntitlementDetail {
  // updated entitlement
  Entitlement update_entitlement = 1;
  // feature
  Feature feature = 2;
  // operator staff id
  int64 operator_id = 3;
  // owner
  User owner = 4;
  // the information attached when modifying entitlement may be related to the business
  // or it may be a backend modification
  // anyway, it means changing the information attached to this entitlement,
  // which will be parsed into json and stored in the database.
  string details = 5;
}

// credit
message UpdateCredit {
  // update credit type
  enum Type {
    // meaningless
    TYPE_UNSPECIFIED = 0;
    // payment
    TYPE_PAYMENT = 1;
    // refund
    TYPE_REFUND = 2;
    // keep the change as credit
    TYPE_KEEP_CHANGE = 3;
    // manual adjustment
    TYPE_MANUAL = 4;
  }
  // update credit reason
  // the front-end copy may change
  // and strings cannot be stored directly
  enum Reason {
    // meaningless
    REASON_UNSPECIFIED = 0;
    // refund
    REFUND = 1;
    // bonus
    BONUS = 2;
    // transfer from perks
    TRANSFER_FROM_PERKS = 3;
    // other
    OTHER = 99;
  }
}

// discount
message Discount {
  // ID
  int64 id = 1;
  // Price id
  int64 price_id = 2;
  // 优惠金额
  google.type.Money amount_off = 3;
  // 有效期
  google.type.Interval validity_period = 4;
}

// Report
message Report {
  // in subscription count
  uint32 in_subscription_count = 1;
  // in subscription percentage
  double in_subscription_percentage = 2;
  // cancelled count
  uint32 cancelled_count = 3;
  // total sales
  google.type.Money total_sales_amount = 4;
  // bold/billing cycle
  google.type.Money cycle_amount = 5;
  // membership total sales
  google.type.Money membership_total_sales_amount = 6;
  // pause count
  uint32 paused_count = 7;
}
