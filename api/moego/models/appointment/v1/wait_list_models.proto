// @since 2024-01-24 15:19:50
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// wait list appointment view
message WaitListCalendarView {
  // appointment id
  int64 appointment_id = 1;
  // wait list id
  int64 id = 2;
}
