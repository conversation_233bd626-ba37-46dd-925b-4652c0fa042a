syntax = "proto3";

package moego.models.customer.v1;

import "moego/models/customer/v1/customer_pet_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.customer.v1";

// pet breed model
message PetBreedModel {
  // id
  int32 id = 1;
  // pet type
  PetType pet_type = 2;
  // name
  string name = 3;
  // sort
  int32 sort = 4;
  // is enable
  bool is_enable = 5;
}
