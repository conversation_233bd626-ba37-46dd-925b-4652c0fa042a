syntax = "proto3";

package moego.models.payment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// us bank account model
message UsBankAccountModel {
  // id of the payment method
  string id = 1;
  // account holder type
  string account_holder_type = 2;
  // account type
  string account_type = 3;
  // bank name
  string bank_name = 4;
  // financial connections account
  string financial_connections_account = 5;
  // fingerprint
  string fingerprint = 6;
  // last4
  string last4 = 7;
  // routing number
  string routing_number = 8;
  // status details
  string status_details = 9;
}
