// @since 2024-10-11 12:09:44
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/online_booking/v1/automation_defs.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// The Automation setting model
message AutomationSettingModel {
  // the unique id
  int64 id = 1;
  // the business id
  int64 business_id = 2;
  // the company id
  int64 company_id = 3;
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 4;
  // enable auto accept
  bool enable_auto_accept = 5;
  // auto accept condition
  AutomationConditionDef auto_accept_condition = 6;
  // the create time
  google.protobuf.Timestamp created_at = 7;
  // the update time
  google.protobuf.Timestamp updated_at = 8;
}
