// @since 2-23-10-07
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.pay_ops.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/pay_ops/v1;payopspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.pay_ops.v1";

/*
 * company feature relation model
 */
message CompanyFeatureRelationModel {
  // id
  uint64 id = 1;
  // company id
  int64 company_id = 2;
  // code
  string code = 3;
  // allow type
  int32 allow_type = 4;
  // enable
  int32 enable = 5;
  // quota
  int64 quota = 6;
  // expiration time
  int64 expiration_time = 7;
  // create time
  int64 create_time = 8;
  // update time
  int64 update_time = 9;
  // note
  string note = 10;
  // is deleted
  int32 is_deleted = 11;
}
