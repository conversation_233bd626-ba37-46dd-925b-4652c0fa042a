syntax = "proto3";

package moego.models.smart_scheduler.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/smart_scheduler/v1;smartschedulerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.smart_scheduler.v1";

// update smart schedule setting def definition
message UpdateSmartScheduleSettingDef {
  // certain area for certain days
  optional bool service_area_enable = 1;
  // appointment buffer time(company level, if param set business_id, use business level)
  optional int32 buffer_time = 2 [(validate.rules).int32 = {
    gte: -100
    lte: 100
  }];
  // business setting override list
  repeated BusinessSettingOverrideDef business_override_list = 3;
}

// business setting override model
message BusinessSettingOverrideDef {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // buffer time
  optional int32 buffer_time = 2 [(validate.rules).int32 = {
    gte: -100
    lte: 100
  }];
}
