// @since 2023-05-27 21:28:41
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.admin_permission.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/admin_permission/v1;adminpermissionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.admin_permission.v1";

// the eq condition
message EqConditionDef {
  // the target value
  string value = 1 [(validate.rules).string = {max_len: 50}];
}

// the in condition
message InConditionDef {
  // the target values
  repeated string values = 1 [(validate.rules).repeated = {
    max_items: 100
    items: {
      string: {max_len: 50}
    }
  }];
}

// match regexp
message MatchConditionDef {
  // the pattern
  string pattern = 1;
}

// the filter model
message FilterDef {
  // the attribute
  string attribute = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // reverse condition result
  bool reverse = 2;
  // condition
  oneof condition {
    option (validate.required) = true;
    // eq
    EqConditionDef eq = 3;
    // in
    InConditionDef in = 4;
    // match
    MatchConditionDef match = 5;
  }
}
