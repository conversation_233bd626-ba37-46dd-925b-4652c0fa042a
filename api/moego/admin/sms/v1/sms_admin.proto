// @since 2023-06-20 17:13:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.sms.v1;

import "moego/models/sms/v1/sms_defs.proto";
import "moego/models/sms/v1/sms_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/sms/v1;smsapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.sms.v1";

// create sms request
message CreateSmsRequest {
  // the business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the sms def
  moego.models.sms.v1.SmsDef sms_def = 2 [(validate.rules).message = {required: true}];
}

// get sms request
message GetSmsRequest {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// the sms service
service SmsService {
  // create sms
  rpc CreateSms(CreateSmsRequest) returns (moego.models.sms.v1.SmsModel);
  // get sms
  rpc GetSms(GetSmsRequest) returns (moego.models.sms.v1.SmsModel);
}
