// @since 2024-08-21 11:33:15
// <AUTHOR> Lei <<EMAIL>>

syntax = "proto3";

package moego.service.engagement.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/engagement/v1;engagementsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.engagement.v1";

// the twilio_hook service
service TwilioHookService {
  // call
  rpc Call(TwilioHookRequest) returns (TwilioHookResponse);
  // forward call
  rpc ForwardCall(TwilioHookRequest) returns (TwilioHookResponse);
  // fallback
  rpc Fallback(TwilioHookRequest) returns (TwilioHookResponse);
  // status
  rpc Status(TwilioHookRequest) returns (TwilioHookResponse);
  // dial action
  rpc DialAction(TwilioHookRequest) returns (TwilioHookResponse);
  // record status
  rpc RecordStatus(TwilioHookRequest) returns (TwilioHookResponse);
  // transcription status
  rpc TranscriptionStatus(TwilioHookRequest) returns (TwilioHookResponse);
  // B-APP outgoing call, 处理掩码过的呼叫
  rpc MaskedOutgoingCall(TwilioHookRequest) returns (TwilioHookResponse);
  // B-APP outgoing call gather, 处理掩码过的呼叫 digit 收集
  rpc MaskedOutgoingCallGather(TwilioHookRequest) returns (TwilioHookResponse);
  // ForwardMessage
  rpc ForwardMessage(ForwardMessageRequest) returns (ForwardMessageResponse);
}

// create twilio_hook request
message TwilioHookRequest {
  // the content def
  string content = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
  // query map
  map<string, string> query_map = 4;
}

// create twilio_hook response
message TwilioHookResponse {
  // the created def
  string content = 1;
}

// ForwardMessageRequest
message ForwardMessageRequest {
  // receiver number
  string receiver_number = 1 [(validate.rules).string.min_len = 1];
  // sender number
  string sender_number = 2 [(validate.rules).string.min_len = 1];
  // string content
  string content = 3;
  // disable auto reply
  bool disable_auto_reply = 4;
}

// ForwardMessageResponse
message ForwardMessageResponse {
  // redirect url
  string redirect_url = 1;
}
