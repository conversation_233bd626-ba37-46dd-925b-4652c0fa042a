syntax = "proto3";

package moego.service.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "moego/utils/v1/struct.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Create DaycareAddOnDetail request
message CreateDaycareAddOnDetailRequest {
  // The id of pet, associated with the current add-on
  optional int64 pet_id = 3;
  // The id of current add-on service
  optional int64 add_on_id = 4;
  // The specific dates of add-on service
  repeated string specific_dates = 5 [(validate.rules).repeated = {
    items: {
      string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
    }
  }];
  // The flag to indicate if the add-on service is everyday
  optional bool is_everyday = 6;
  // The price of current add-on service
  optional double service_price = 7;
  // The id of tax, associated with the current add-on
  optional int64 tax_id = 8;
  // The duration of current add-on service
  optional int32 duration = 9;
  // createdAt
  optional google.protobuf.Timestamp created_at = 10;
  // updatedAt
  optional google.protobuf.Timestamp updated_at = 11;
  // quantity per day
  optional int32 quantity_per_day = 12;
}

// DaycareAddOnDetail service
service DaycareAddOnDetailService {}

// Update DaycareAddOnDetail response
message UpdateDaycareAddOnDetailRequest {
  // The id of daycare add-on detail
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // The specific dates of add-on service
  optional moego.utils.v1.StringListValue specific_dates = 2;
  // The flag to indicate if the add-on service is everyday
  optional bool is_everyday = 3;
  // quantity per day
  optional int32 quantity_per_day = 4 [(validate.rules).int32 = {gte: 0}];
}
