syntax = "proto3";

package moego.service.sms.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/sms/v1;smssvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.sms.v1";

// the phone number service
service PhoneNumberService {
  // phone number
  rpc Format(FormatPhoneNumberRequest) returns (FormatPhoneNumberResponse);
}

// format phone number request
message FormatPhoneNumberRequest {
  // business id
  uint64 business_id = 1;
  // phone number
  string phone_number = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 30
  }];
}

// format phone number response
message FormatPhoneNumberResponse {
  // e164 phone number
  string e164_phone_number = 1;
}
