syntax = "proto3";

package moego.service.order.v1;

import "moego/models/order/v1/service_charge_model.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1;ordersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.order.v1";

// get service charge input params
message GetServiceChargeListInput {
  // business id
  int64 business_id = 1;
  // is active
  optional bool is_active = 2;
  // is mandatory
  optional bool is_mandatory = 3;
  // include deleted, default false
  optional bool included_deleted = 4;
  // company id
  int64 company_id = 5;
  // appointment id
  optional int64 appointment_id = 6 [(validate.rules).int64 = {gt: 0}];
}

// query service charge input params
message QueryServiceChargeListInput {
  // business id
  int64 business_id = 1;
  // id list
  repeated int64 service_charge_id = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
  // company id
  int64 company_id = 3;
}

// add service charge input params
message AddServiceChargeInput {
  // business id
  int64 business_id = 1;
  // name
  string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 150
  }];
  // split method
  optional string description = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 1000
  }];
  // price, must be positive
  double price = 4 [(validate.rules).double = {gt: 0}];
  // tax id, 0 or null means no tax
  optional int32 tax_id = 5 [(validate.rules).int32 = {gte: 0}];
  // is mandatory, preserved 8-19 for future use
  // deprecated by Freeman since 2024/9/25, use auto_apply_status instead
  optional bool is_mandatory = 6 [deprecated = true];
  // is active
  optional bool is_active = 7;
  // apply to upcoming
  optional bool apply_upcoming_appt = 8;
  // operator id
  int64 operator_id = 9;
  // company id
  int64 company_id = 10;
  // auto apply status
  optional moego.models.order.v1.ServiceCharge.AutoApplyStatus auto_apply_status = 11 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply condition
  optional moego.models.order.v1.ServiceCharge.AutoApplyCondition auto_apply_condition = 12 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply time
  optional int32 auto_apply_time = 13 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// update service charge input params
message UpdateServiceChargeInput {
  // business id
  int64 business_id = 1;
  // id, exist for update
  int64 id = 2 [(validate.rules).int64 = {gt: 0}];
  // name
  optional string name = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 150
  }];
  // split method
  optional string description = 4 [(validate.rules).string = {
    min_len: 0
    max_len: 1000
  }];
  // price, must be positive
  optional double price = 5 [(validate.rules).double = {gt: 0}];
  // tax id, 0 or null means no tax
  optional int32 tax_id = 6 [(validate.rules).int32 = {gte: 0}];
  // is mandatory
  // deprecated by Freeman since 2024/9/25, use auto_apply_status instead
  optional bool is_mandatory = 7 [deprecated = true];
  // is active
  optional bool is_active = 8;
  // apply to upcoming
  optional bool apply_upcoming_appt = 9;
  // operator id
  int64 operator_id = 10;
  // company id
  int64 company_id = 11;
  // auto apply status
  optional moego.models.order.v1.ServiceCharge.AutoApplyStatus auto_apply_status = 12 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply condition
  optional moego.models.order.v1.ServiceCharge.AutoApplyCondition auto_apply_condition = 13 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply time
  optional int32 auto_apply_time = 14 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// sort service charge input params
message SortServiceChargeInput {
  // business id
  int64 business_id = 1;
  // sorted id list
  repeated int64 sorted_id = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
  // operator id
  int64 operator_id = 3;
  // company id
  int64 company_id = 4;
}

// delete service charge input params
message DeleteServiceChargeInput {
  // business id
  int64 business_id = 1;
  // to be deleted id
  int64 id = 2;
  // operator id
  int64 operator_id = 3;
  // company id
  int64 company_id = 4;
  // apply to upcoming, if ture, will delete service charge from upcoming unconfirmed and no-fully-paid appointments
  optional bool apply_upcoming_appt = 5;
}

// get service charge list output
message GetServiceChargeListOutput {
  // service charge list
  repeated moego.models.order.v1.ServiceCharge service_charge = 1;
}

// operate service charge output
message OperateServiceChargeOutput {
  // result
  bool result = 1;
}

// service charge internal api
service ServiceChargeService {
  // get service charge list
  rpc GetServiceChargeList(GetServiceChargeListInput) returns (GetServiceChargeListOutput);
  // query service charge list
  rpc QueryServiceChargeList(QueryServiceChargeListInput) returns (GetServiceChargeListOutput);
  // add service charge
  rpc AddServiceCharge(AddServiceChargeInput) returns (moego.models.order.v1.ServiceCharge);
  // update service charge
  rpc UpdateServiceCharge(UpdateServiceChargeInput) returns (moego.models.order.v1.ServiceCharge);
  // sort service charge
  rpc SortServiceCharge(SortServiceChargeInput) returns (OperateServiceChargeOutput);
  // delete service charge
  rpc DeleteServiceCharge(DeleteServiceChargeInput) returns (OperateServiceChargeOutput);
}
