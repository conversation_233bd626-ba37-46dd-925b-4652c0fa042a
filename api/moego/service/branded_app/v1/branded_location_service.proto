// @since 2024-06-03 14:43:06
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.branded_app.v1;

import "moego/models/branded_app/v1/branded_location_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/branded_app/v1;brandedappsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.branded_app.v1";

// The request message for ListBrandedLocations
message ListBrandedLocationsRequest {
  // the branded app id
  string branded_app_id = 1 [(validate.rules).string = {max_len: 255}];
}

// The response message for ListBrandedLocations
message ListBrandedLocationsResponse {
  // the list of branded locations
  repeated moego.models.branded_app.v1.BrandedLocationModel locations = 1;
}

// the branded_location service
service BrandedLocationService {
  // list branded locations
  rpc ListBrandedLocations(ListBrandedLocationsRequest) returns (ListBrandedLocationsResponse);
}
