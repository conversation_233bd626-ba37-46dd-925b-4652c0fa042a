// @since 2024-07-07 15:21:44
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.ws.v1;

import "moego/models/ws/v1/ws_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/ws/v1;wssvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.ws.v1";

// push message input
message PushRequest {
  // the target group type
  moego.models.ws.v1.GroupType group_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // the target group id
  int64 group_id = 2 [(validate.rules).int64 = {gt: 0}];

  // the action
  string action = 3 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 100
  }];
  // the meta data
  map<string, string> meta = 4 [(validate.rules).map = {max_pairs: 10}];
  // the payload
  string payload = 5 [(validate.rules).string = {max_bytes: 10000}];
}

// push response
message PushResponse {
  // success count
  int64 success_count = 1;
  // fail count
  int64 fail_count = 2;
}

// ws service for push
service WsService {
  // push new message
  rpc Push(PushRequest) returns (PushResponse);
}
