package com.moego.common.enums;

import static com.moego.common.enums.PropertyTableEnum.ABANDONED_RECORD;
import static com.moego.common.enums.PropertyTableEnum.ADDRESS;
import static com.moego.common.enums.PropertyTableEnum.ADDRESS_COUNT;
import static com.moego.common.enums.PropertyTableEnum.APPT_COUNT;
import static com.moego.common.enums.PropertyTableEnum.APPT_DATE;
import static com.moego.common.enums.PropertyTableEnum.APPT_GROOMER;
import static com.moego.common.enums.PropertyTableEnum.CONTACT;
import static com.moego.common.enums.PropertyTableEnum.CONTACT_COUNT;
import static com.moego.common.enums.PropertyTableEnum.CUSTOMER;
import static com.moego.common.enums.PropertyTableEnum.CUSTOMER_FOR_ONLINE_BOOKING;
import static com.moego.common.enums.PropertyTableEnum.MEMBERSHIP;
import static com.moego.common.enums.PropertyTableEnum.MESSAGE_CARD_LINK;
import static com.moego.common.enums.PropertyTableEnum.PAYMENT;
import static com.moego.common.enums.PropertyTableEnum.PAYMENT_CREDIT_CARD;
import static com.moego.common.enums.PropertyTableEnum.PET;
import static com.moego.common.enums.PropertyTableEnum.PET_CODE;
import static com.moego.common.enums.PropertyTableEnum.PET_COUNT;
import static com.moego.common.enums.PropertyTableEnum.PET_VACCINE;
import static com.moego.common.enums.PropertyTableEnum.REVIEW_BOOSTER;
import static com.moego.common.enums.PropertyTableEnum.SERVICE_AREA;
import static com.moego.common.enums.PropertyTableEnum.TAG;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * List property name
 *
 * <AUTHOR>
 * @since 2023/3/22
 */
@Getter
@AllArgsConstructor
public enum PropertyEnum {
    /**
     * Client
     */
    client_id(CUSTOMER, "id", PropertyTypeEnum.NUMBER),
    client_status(CUSTOMER, "", PropertyTypeEnum.STRING),
    client_type(CUSTOMER, "", PropertyTypeEnum.STRING),
    first_name(CUSTOMER, "first_name", PropertyTypeEnum.STRING),
    last_name(CUSTOMER, "last_name", PropertyTypeEnum.STRING),
    client_created_from(CUSTOMER, "source", PropertyTypeEnum.STRING),
    referral_source(CUSTOMER, "referral_source_id", PropertyTypeEnum.NUMBER),
    preferred_frequency_day(CUSTOMER, "preferred_frequency_day", PropertyTypeEnum.NUMBER),
    preferred_groomer(CUSTOMER, "preferred_groomer_id", PropertyTypeEnum.NUMBER),
    preferred_weekday(CUSTOMER, "preferred_day", PropertyTypeEnum.STRING),
    unsubscribed_marketing_emails(CUSTOMER, "is_unsubscribed", PropertyTypeEnum.NUMBER),
    primary_email(CUSTOMER, "email", PropertyTypeEnum.STRING),
    preferred_business(CUSTOMER, "business_id", PropertyTypeEnum.NUMBER),
    has_account(CUSTOMER, "", PropertyTypeEnum.BOOLEAN),
    account_id(CUSTOMER, "account_id", PropertyTypeEnum.NUMBER),
    creation_date(CUSTOMER, "create_time", PropertyTypeEnum.NUMBER),
    customer_type(CUSTOMER, "type", PropertyTypeEnum.STRING),
    allocate_staff_id(CUSTOMER, "allocate_staff_id", PropertyTypeEnum.NUMBER),
    life_cycle_id(CUSTOMER, "customize_life_cycle_id", PropertyTypeEnum.NUMBER),
    action_state_id(CUSTOMER, "customize_action_state_id", PropertyTypeEnum.NUMBER),
    /**
     * Status of client
     */
    blocked_from_message(CUSTOMER, "is_block_message", PropertyTypeEnum.BOOLEAN),
    blocked_from_ob(CUSTOMER, "is_block_online_booking", PropertyTypeEnum.BOOLEAN),
    inactive_client(CUSTOMER, "inactive", PropertyTypeEnum.BOOLEAN),
    lapsed_client(
            CUSTOMER, "DATE_ADD(last_service_time, INTERVAL preferred_frequency_day DAY)", PropertyTypeEnum.STRING),
    blocked_from_ob_selected_service(CUSTOMER_FOR_ONLINE_BOOKING, "", PropertyTypeEnum.NUMBER),
    /**
     * address
     */
    address_cnt(ADDRESS_COUNT, "COUNT(mca.id)", PropertyTypeEnum.NUMBER),
    zipcode(ADDRESS, "zipcode", PropertyTypeEnum.STRING),
    service_areas(SERVICE_AREA, "", PropertyTypeEnum.NUMBER),
    /**
     * contact
     */
    phone_number(CONTACT, "phone_number", PropertyTypeEnum.STRING),
    email(CONTACT, "email", PropertyTypeEnum.STRING),
    email_cnt(CONTACT_COUNT, "COUNT(mcc.id)", PropertyTypeEnum.NUMBER),
    /**
     * tag
     */
    client_tag(TAG, "customer_tag_id", PropertyTypeEnum.NUMBER),
    /**
     * Type of client
     */
    new_client(APPT_COUNT, "", PropertyTypeEnum.BOOLEAN),
    recurring_client(APPT_COUNT, "", PropertyTypeEnum.BOOLEAN),
    waitlist_client(APPT_COUNT, "", PropertyTypeEnum.BOOLEAN),
    /**
     * review booster
     */
    review_rating(REVIEW_BOOSTER, "AVG(positive_score)", PropertyTypeEnum.NUMBER),
    review_cnt(REVIEW_BOOSTER, "COUNT(*)", PropertyTypeEnum.NUMBER),
    /**
     * Pet
     */
    pet_code(PET_CODE, "pet_code_id", PropertyTypeEnum.NUMBER),
    pet_name(PET, "pet_name", PropertyTypeEnum.STRING),
    pet_type(PET, "pet_type_id", PropertyTypeEnum.NUMBER),
    pet_breed(PET, "breed", PropertyTypeEnum.STRING),
    pet_weight(PET, "CAST(weight AS SIGNED)", PropertyTypeEnum.NUMBER),
    pet_size(PET, "CAST(weight AS SIGNED)", PropertyTypeEnum.NUMBER),
    hair_length(PET, "hair_length", PropertyTypeEnum.STRING),
    pet_cnt(PET_COUNT, "COUNT(mcp.id)", PropertyTypeEnum.NUMBER),
    expired_vaccine_cnt(PET_VACCINE, "COUNT(mppvb.id)", PropertyTypeEnum.NUMBER),
    vaccine_cnt(PET_VACCINE, "COUNT(mppvb.id)", PropertyTypeEnum.NUMBER),
    pet_vaccine(PET_VACCINE, "COUNT(*)", PropertyTypeEnum.STRING),
    /**
     * Appt
     */
    first_appt_date(APPT_DATE, "MIN(appointment_date)", PropertyTypeEnum.DATE),
    upcoming_appt_date(APPT_DATE, "MIN(appointment_date)", PropertyTypeEnum.DATE),
    last_appt_date(APPT_DATE, "MAX(appointment_date)", PropertyTypeEnum.DATE),
    next_appt_date(APPT_DATE, "MIN(appointment_date)", PropertyTypeEnum.DATE),
    expected_service_date(APPT_DATE, "", PropertyTypeEnum.DATE),
    total_appt_cnt(APPT_COUNT, "total_appt_cnt", PropertyTypeEnum.NUMBER),
    total_appt_ob_requests_cnt(APPT_COUNT, "total_appt_ob_requests_cnt", PropertyTypeEnum.NUMBER),
    client_total_appt_cnt(APPT_COUNT, "client_total_appt_cnt", PropertyTypeEnum.NUMBER),
    finished_appt_cnt(APPT_COUNT, "finished_appt_cnt", PropertyTypeEnum.NUMBER),
    upcoming_appt_cnt(APPT_COUNT, "upcoming_appt_cnt", PropertyTypeEnum.NUMBER),
    no_show_appt_cnt(APPT_COUNT, "no_show_appt_cnt", PropertyTypeEnum.NUMBER),
    cancelled_appt_cnt(APPT_COUNT, "cancelled_appt_cnt", PropertyTypeEnum.NUMBER),
    waitlist_appt_cnt(APPT_COUNT, "waitlist_appt_cnt", PropertyTypeEnum.NUMBER),
    unpaid_invoice_cnt(APPT_COUNT, "unpaid_appt_cnt", PropertyTypeEnum.NUMBER),
    total_paid(PAYMENT, "SUM(amount)", PropertyTypeEnum.NUMBER),
    overdue(APPT_DATE, "", PropertyTypeEnum.NUMBER),
    last_appt_groomer(APPT_GROOMER, "staff_id", PropertyTypeEnum.NUMBER),

    /**
     * cof
     */
    has_cof(PAYMENT_CREDIT_CARD, "", PropertyTypeEnum.STRING),
    cof_cnt(PAYMENT_CREDIT_CARD, "COUNT(credit_card.id)", PropertyTypeEnum.NUMBER),
    cof_status(
            PAYMENT_CREDIT_CARD, "concat(credit_card.exp_year, '-', credit_card.exp_month)", PropertyTypeEnum.STRING),
    cof_request(MESSAGE_CARD_LINK, "status", PropertyTypeEnum.NUMBER),
    cof_request_date(MESSAGE_CARD_LINK, "cof_timestamp", PropertyTypeEnum.NUMBER),
    /**
     * Abandoned record
     */
    lead_type(ABANDONED_RECORD, "lead_type", PropertyTypeEnum.STRING),
    abandoned_step(ABANDONED_RECORD, "abandon_step", PropertyTypeEnum.STRING),
    abandoned_status(ABANDONED_RECORD, "abandon_status", PropertyTypeEnum.STRING),
    abandoned_date(ABANDONED_RECORD, "abandon_time", PropertyTypeEnum.DATE),
    last_contact_time(ABANDONED_RECORD, "GREATEST(last_texted_time, last_emailed_time)", PropertyTypeEnum.NUMBER),
    performance_history(APPT_COUNT, "", PropertyTypeEnum.STRING),
    /**
     * membership
     */
    membership(MEMBERSHIP, "membership_id", PropertyTypeEnum.NUMBER),
    membership_status(MEMBERSHIP, "membership_status", PropertyTypeEnum.STRING);

    private final PropertyTableEnum table;
    private final String column;
    private final PropertyTypeEnum type;
}
