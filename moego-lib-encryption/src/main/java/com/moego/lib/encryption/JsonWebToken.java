package com.moego.lib.encryption;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moego.lib.utils.CoreUtils;
import com.moego.lib.utils.StringUtils;
import com.moego.lib.utils.model.Pair;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.InvalidClaimException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.MissingClaimException;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.io.Encoders;
import io.jsonwebtoken.lang.DateFormats;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SignatureException;
import java.security.Key;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.text.ParseException;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

/**
 * JWT class(readonly)
 */
public final class JsonWebToken {

    public static final String JSON_WEB_KEY = io.jsonwebtoken.JwsHeader.JSON_WEB_KEY;

    // JWT header default keys
    public static final String JWK_ALGORITHM = io.jsonwebtoken.JwsHeader.ALGORITHM;
    public static final String JWK_KEY_ID = io.jsonwebtoken.JwsHeader.KEY_ID;
    public static final String JWK_SET_URL = io.jsonwebtoken.JwsHeader.JWK_SET_URL;
    public static final String JWK_TYPE = io.jsonwebtoken.Header.TYPE;

    // JWT claim default keys
    public static final String JWK_ISSUER = io.jsonwebtoken.Claims.ISSUER;
    public static final String JWK_SUBJECT = io.jsonwebtoken.Claims.SUBJECT;
    public static final String JWK_AUDIENCE = io.jsonwebtoken.Claims.AUDIENCE;
    public static final String JWK_EXPIRATION = io.jsonwebtoken.Claims.EXPIRATION;
    public static final String JWK_NOT_BEFORE = io.jsonwebtoken.Claims.NOT_BEFORE;
    public static final String JWK_ISSUED_AT = io.jsonwebtoken.Claims.ISSUED_AT;
    public static final String JWK_JWT_ID = io.jsonwebtoken.Claims.ID;

    private final String signature;
    private final Map<String, Object> headerMap;
    private final Map<String, Object> claimMap;
    private final Headers headers = new Headers();
    private final Claims claims = new Claims();

    private JsonWebToken(String s, Map<String, Object> h, Map<String, Object> c) {
        this.signature = s;
        this.headerMap = h;
        this.claimMap = c;
    }

    public String signature() {
        return this.signature;
    }

    public Headers headers() {
        return this.headers;
    }

    public Claims claims() {
        return this.claims;
    }

    @Override
    public String toString() {
        var value = Map.of("signature", this.signature, "headers", this.headerMap, "claims", this.claimMap);
        try {
            return new ObjectMapper().writeValueAsString(value);
        } catch (JsonProcessingException e) {
            return "signature: " + this.signature + ", headers: " + this.headerMap + ", claims: " + this.claimMap;
        }
    }

    public class Headers {

        public Set<String> keys() {
            return JsonWebToken.this.headerMap.keySet();
        }

        public String algorithm() {
            return this.getString(JWK_ALGORITHM);
        }

        public String type() {
            return this.getString(JWK_TYPE);
        }

        public String kid() {
            return this.getString(JWK_KEY_ID);
        }

        public Object get(String key) {
            return JsonWebToken.this.headerMap.get(key);
        }

        private String getString(String key) {
            var value = this.get(key);
            if (value != null) {
                return value.toString();
            }

            return null;
        }
    }

    public class Claims {

        public Set<String> keys() {
            return JsonWebToken.this.claimMap.keySet();
        }

        public String issuer() {
            return this.getString(JWK_ISSUER);
        }

        public String subject() {
            return this.getString(JWK_SUBJECT);
        }

        public String audience() {
            return this.getString(JWK_AUDIENCE);
        }

        public Long expiration() {
            return this.getTimestamp(JWK_EXPIRATION);
        }

        public Long notBefore() {
            return this.getTimestamp(JWK_NOT_BEFORE);
        }

        public Long issuedAt() {
            return this.getTimestamp(JWK_ISSUED_AT);
        }

        public String jwtId() {
            return this.getString(JWK_JWT_ID);
        }

        public Object get(String key) {
            return JsonWebToken.this.claimMap.get(key);
        }

        private String getString(String key) {
            var value = this.get(key);
            if (value != null) {
                return value.toString();
            }

            return null;
        }

        private Long getTimestamp(String key) {
            var t = this.get(key);
            if (t != null) {
                return convertToMilliseconds(t);
            }

            return null;
        }
    }

    // list all algorithms supported for JWT
    public static List<String> listAlgorithms() {
        List<String> algs = new ArrayList<>();
        for (var alg : SignatureAlgorithm.values()) {
            if (alg.getJcaName() != null) {
                algs.add(alg.getValue());
            }
        }
        return algs;
    }

    // JWT encode with key and claims
    public static String encode(
            Key key, String iss, String sub, String aud, Long iat, Long nbf, Long exp, String jid, String... claims) {
        return encode(key, null, makeClaims(iss, sub, aud, iat, nbf, exp, jid, claims));
    }

    // JWT encode with key, algorithm, headers, claims
    public static String encode(Key key, String alg, Map<String, Object> headers, Map<String, Object> claims) {
        if (StringUtils.isBlank(alg)) {
            return encode(key, headers, claims);
        } else {
            return encode(key, SignatureAlgorithm.forName(alg), headers, claims);
        }
    }

    // JWT encode with key, headers, claims
    public static String encode(Key key, Map<String, Object> headers, Map<String, Object> claims) {
        return encode(key, (SignatureAlgorithm) null, headers, claims);
    }

    // JWT encode with key, algorithm, headers, claims
    private static String encode(
            Key key, SignatureAlgorithm alg, Map<String, Object> headers, Map<String, Object> claims) {
        var builder = Jwts.builder().setHeaderParam(JWK_TYPE, "JWT");
        if (headers != null) {
            builder.setHeaderParams(headers);
        }
        if (claims != null) {
            applyClaims(builder::claim, claims);
        }

        if (alg == null) {
            return builder.signWith(key).compact();
        } else {
            return builder.signWith(key, alg).compact();
        }
    }

    // JWT decode with key
    public static JsonWebToken decode(Key key, String token) {
        var jws = Jwts.parserBuilder().setSigningKey(key).build().parseClaimsJws(token);
        return new JsonWebToken(jws.getSignature(), CoreUtils.castMap(jws.getHeader()), jws.getBody());
    }

    /**
     * generate a Key for JWT specified algorithm
     * the alg must be one of: HS256, HS384, HS512
     */
    public static SecretKey generateKey(String alg) {
        return Keys.secretKeyFor(SignatureAlgorithm.forName(alg));
    }

    /**
     * generate a KeyPair for JWT specified algorithm
     * the alg must be one of: RS256, RS384, RS512, ES256, ES384, ES512, PS256, PS384, PS512
     */
    public static KeyPair generateKeyPair(String alg) {
        return Keys.keyPairFor(SignatureAlgorithm.forName(alg));
    }

    /**
     * generate a key string for JWT specified algorithm
     * the alg must be one of: HS256, HS384, HS512
     */
    public static String generateKeyString(String alg) {
        return toString(generateKey(alg));
    }

    /**
     * generate a public key string and a private key string for JWT specified algorithm
     * the alg must be one of: RS256, RS384, RS512, ES256, ES384, ES512, PS256, PS384, PS512
     */
    public static Pair<String, String> generateKeyPairString(String alg) {
        return toString(generateKeyPair(alg));
    }

    /**
     * build the Key of the specified algorithm with the string
     * the alg must be one of: HS256, HS384, HS512
     */
    public static Key buildKey(String alg, String key) {
        return new SecretKeySpec(
                Decoders.BASE64.decode(key), SignatureAlgorithm.forName(alg).getJcaName());
    }

    /**
     * build the KeyPair of the specified algorithm with the public key string and private key string
     * the alg must be one of: RS256, RS384, RS512, ES256, ES384, ES512, PS256, PS384, PS512
     */
    public static KeyPair buildKeyPair(String alg, String pubKey, String priKey)
            throws NoSuchAlgorithmException, InvalidKeySpecException {
        var sa = SignatureAlgorithm.forName(alg);
        if (sa != null) {
            KeyFactory keyFactory = null;
            if ("RSA".equals(sa.getFamilyName())) {
                keyFactory = KeyFactory.getInstance("RSA");
            } else if ("ECDSA".equals(sa.getFamilyName())) {
                keyFactory = KeyFactory.getInstance("EC");
            }
            if (keyFactory != null) {
                var k1 = keyFactory.generatePublic(new X509EncodedKeySpec(Decoders.BASE64.decode(pubKey)));
                var k2 = keyFactory.generatePrivate(new PKCS8EncodedKeySpec(Decoders.BASE64.decode(priKey)));
                return new KeyPair(k1, k2);
            }
        }

        throw new NoSuchAlgorithmException("not supported algorithm: " + alg);
    }

    // Key to string
    public static String toString(Key key) {
        return Encoders.BASE64.encode(key.getEncoded());
    }

    // KeyPair to a pair of string
    public static Pair<String, String> toString(KeyPair pair) {
        return toString(pair.getPublic(), pair.getPrivate());
    }

    // public key and private key to a pair of string
    public static Pair<String, String> toString(Key k1, Key k2) {
        return Pair.of(toString(k1), toString(k2));
    }

    enum VerifyResult {
        OK,
        INVALID_TOKEN,
        INVALID_SIGN,
        MISSING_CLAIM,
        INVALID_CLAIM,
        EXPIRED_TOKEN,
    }

    // verify whether the JWT token is correct
    public static Pair<VerifyResult, String> verify(
            Key key,
            String token,
            String iss,
            String sub,
            String aud,
            Long iat,
            Long nbf,
            Long exp,
            String jid,
            String... claims) {
        return verify(key, token, makeClaims(iss, sub, aud, iat, nbf, exp, jid, claims));
    }

    // verify whether the JWT token is correct
    public static Pair<VerifyResult, String> verify(Key key, String token, Map<String, Object> claims) {
        try {
            var builder = Jwts.parserBuilder();
            if (claims != null) {
                applyClaims(builder::require, claims);
            }
            var jwt = builder.setSigningKey(key).build().parseClaimsJws(token);
            if (jwt.getHeader().isEmpty() || jwt.getBody().isEmpty() || StringUtils.isBlank(jwt.getSignature())) {
                return Pair.of(VerifyResult.INVALID_TOKEN, "missing information");
            }

            return Pair.of(VerifyResult.OK, "success");
        } catch (SignatureException e) {
            return Pair.of(VerifyResult.INVALID_SIGN, e.getMessage());
        } catch (ExpiredJwtException e) {
            return Pair.of(VerifyResult.EXPIRED_TOKEN, e.getMessage());
        } catch (MissingClaimException e) {
            return Pair.of(VerifyResult.MISSING_CLAIM, e.getClaimName());
        } catch (InvalidClaimException e) {
            return Pair.of(VerifyResult.INVALID_CLAIM, e.getClaimName());
        } catch (Exception e) {
            return Pair.of(VerifyResult.INVALID_TOKEN, e.getMessage());
        }
    }

    private static Long convertToSeconds(Object v) {
        return convertToTimestamp(v, true);
    }

    private static Long convertToMilliseconds(Object v) {
        return convertToTimestamp(v, false);
    }

    private static Long convertToTimestamp(Object v, boolean inSecond) {
        if (v instanceof Number t) {
            return inSecond ? (t.longValue() / 1000) : (t.longValue() * 1000);
        } else if (v instanceof Date t) {
            return inSecond ? (t.getTime() / 1000) : t.getTime();
        } else if (v instanceof Instant t) {
            return inSecond ? (t.toEpochMilli() / 1000) : t.toEpochMilli();
        } else if (v instanceof ZonedDateTime t) {
            return inSecond
                    ? (t.toInstant().toEpochMilli() / 1000)
                    : t.toInstant().toEpochMilli();
        } else if (v instanceof OffsetDateTime t) {
            return inSecond
                    ? (t.toInstant().toEpochMilli() / 1000)
                    : t.toInstant().toEpochMilli();
        } else if (v instanceof String s) {
            try {
                return Long.parseLong(s);
            } catch (NumberFormatException ignored) {
            }

            try {
                var t = DateFormats.parseIso8601Date(s).getTime();
                return inSecond ? (t / 1000) : t;
            } catch (ParseException ignored) {
            }
        }

        return null;
    }

    private static Map<String, Object> makeClaims(
            String iss, String sub, String aud, Long iat, Long nbf, Long exp, String jid, String... claims) {
        Map<String, Object> claimMap = new HashMap<>();
        if (iss != null) {
            claimMap.put(JWK_ISSUER, iss);
        }
        if (sub != null) {
            claimMap.put(JWK_SUBJECT, sub);
        }
        if (aud != null) {
            claimMap.put(JWK_AUDIENCE, aud);
        }
        if (iat != null) {
            claimMap.put(JWK_ISSUED_AT, iat);
        }
        if (nbf != null) {
            claimMap.put(JWK_NOT_BEFORE, nbf);
        }
        if (exp != null) {
            claimMap.put(JWK_EXPIRATION, exp);
        }
        if (jid != null) {
            claimMap.put(JWK_JWT_ID, jid);
        }
        if (claims != null && 0 < claims.length) {
            if (1 == claims.length % 2) {
                throw new IllegalArgumentException("the length of claims must be an even");
            }
            for (int i = 0; i < claims.length; i += 2) {
                claimMap.put(claims[i], claims[i + 1]);
            }
        }

        return claimMap;
    }

    private static void applyClaims(BiConsumer<String, Object> consumer, Map<String, Object> claims) {
        for (var entry : claims.entrySet()) {
            var key = entry.getKey();
            if (JWK_ISSUED_AT.equals(key) || JWK_NOT_BEFORE.equals(key) || JWK_EXPIRATION.equals(key)) {
                var date = convertToSeconds(entry.getValue());
                if (date == null) {
                    consumer.accept(key, entry.getValue());
                } else {
                    consumer.accept(key, date);
                }
            } else {
                consumer.accept(key, entry.getValue());
            }
        }
    }
}
