package com.moego.server.retail.api;

import com.moego.common.dto.CustomerPaymentSummary;
import com.moego.common.params.CustomerIdsParams;
import com.moego.server.retail.dto.InvoiceDto;
import com.moego.server.retail.param.SetPaymentParams;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IRetailInvoiceService {
    @Operation(summary = "payment 回调设置支付状态")
    @PostMapping("/service/retail/invoice/setPaymentResult")
    Boolean setPaymentResult(@RequestBody SetPaymentParams params);

    @Operation(summary = "获取 invoice")
    @GetMapping("/service/retail/invoice/getWithPaymentById")
    InvoiceDto getWithPaymentById(@RequestParam("invoiceId") Integer invoiceId);

    /**
     * DONE(account structure): company 迁移后，查询 company 维度的数据
     * 忽略传入的business_id
     */
    @Operation(summary = "批量获取 customer 支付摘要")
    @PostMapping("/service/retail/invoice/batchGetCustomerPaymentSummary")
    List<CustomerPaymentSummary> batchGetCustomerPaymentSummary(@RequestBody CustomerIdsParams params);

    @Operation(summary = "取消预约的时候，释放库存")
    @PostMapping("/service/retail/invoice/batchReleaseStock")
    Boolean batchReleaseStock(@RequestParam("businessId") Integer businessId, @RequestBody List<Integer> invoiceIds);
}
