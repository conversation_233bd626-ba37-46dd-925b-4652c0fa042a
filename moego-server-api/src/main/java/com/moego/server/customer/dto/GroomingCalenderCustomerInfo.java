package com.moego.server.customer.dto;

import lombok.Data;

@Data
public class GroomingCalenderCustomerInfo {

    private Integer customerId;
    private Integer groomingId;
    private Integer customerAddressId;

    // client full name
    private String customerLastName;
    private String customerFirstName;
    private String clientColor;
    // Client phone number
    private String clientPhoneNumber;
    private String primaryContactFirstName;
    private String primaryContactLastName;
    private Integer primaryContactId;
    private String email;

    // client full address(包含 city zipcode)
    private String address1;
    private String address2;
    private String country;
    private String state;
    // City
    private String city;
    // Zipcode
    private String zipcode;
    private String lat;
    private String lng;

    private String avatarPath;
    // 删除状态
    private Byte status;
    private Boolean hasPetParentAppAccount;
}
