package com.moego.server.customer.api;

import com.moego.common.response.ResponseResult;
import com.moego.server.customer.dto.AddResultDTO;
import com.moego.server.customer.dto.PetNoteDTO;
import com.moego.server.customer.params.PetNoteSaveVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IPetNoteService {
    /**
     * DONE(account structure): 不需要兼容;
     * save custom pet question and answer
     *
     * @param tokenStaffId
     * @param saveVo
     * @return
     */
    @PostMapping("/service/customer/petNote/insertPetNote")
    ResponseResult insertPetNote(
            @RequestParam(value = "tokenStaffId", required = false) Integer tokenStaffId,
            @RequestBody PetNoteSaveVo saveVo);

    /**
     * DONE(account structure): 不需要兼容;
     * @param tokenStaffId
     * @param saveVo
     * @return
     */
    @PostMapping("/service/customer/petNote/insertIdempotentPetNote")
    AddResultDTO insertIdempotentPetNote(
            @RequestParam(value = "tokenStaffId", required = false) Integer tokenStaffId,
            @RequestBody PetNoteSaveVo saveVo);

    /**
     * DONE(account structure): 不需要兼容;
     * @param petNoteId
     * @return
     */
    @GetMapping("/service/customer/petNote/getPetNoteById")
    PetNoteDTO getPetNoteById(@RequestParam("petNoteId") int petNoteId);
}
