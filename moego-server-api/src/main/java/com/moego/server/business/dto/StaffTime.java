package com.moego.server.business.dto;

import com.moego.server.grooming.dto.LimitDto;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class StaffTime {

    private Boolean isSelected;
    private List<TimeRangeDto> timeRange;
    private LimitDto limitDto;
    private List<Long> limitIds;

    public StaffTime() {
        this.isSelected = true;
        this.timeRange = new ArrayList<>();
        this.limitDto = new LimitDto();
        this.limitIds = new ArrayList<>();
    }
}
