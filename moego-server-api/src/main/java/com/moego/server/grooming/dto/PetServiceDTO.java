package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class PetServiceDTO implements Cloneable {

    private Integer id;
    private Integer businessId;
    private Integer categoryId;
    private String name;
    private String description;
    private Byte type;
    private BigDecimal price;
    private Integer duration;
    private Byte inactive; // inactive功能：增加查询inactive字段值
    private Integer sort;
    private String colorCode;
    private Byte showBasePrice;
    private Byte bookOnlineAvailable;

    private Boolean isSavePrice;
    private Boolean isSaveTime;

    // filter by breed/weight开关
    private Byte breedFilter;
    private Byte weightFilter;
    // filter by weight范围
    private BigDecimal weightDownLimit;
    private BigDecimal weightUpLimit;

    private Byte coatFilter;

    private Byte petSizeFilter;
    private String allowedPetSizeList;

    // 对应数据库中的 moe_grooming_service.service_filter，因为字段有重名，需要使用一个新的字段名
    private Byte addOnServiceFilter;

    /**
     * true: is applicable
     * false: not applicable
     */
    private Boolean serviceFilter;

    @Override
    public PetServiceDTO clone() {
        try {
            return (PetServiceDTO) super.clone();
        } catch (CloneNotSupportedException e) {
            log.error("clone object failed", e);
        }
        return null;
    }
}
