package com.moego.server.grooming.params;

import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.params.PageQuery;
import com.moego.common.utils.Pagination;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/9/25
 */
@Data
@Builder
public class AppointmentListParams {

    /**
     * Appointment type
     *
     * @see com.moego.common.enums.ClientApptConst
     */
    @NotNull
    private Byte apptType;

    /**
     * Link customers
     */
    @NotEmpty
    private List<@NotNull BaseBusinessCustomerIdDTO> linkCustomers;

    /**
     * Business date time map
     */
    private Map<Integer, BusinessDateTimeDTO> businessDateTimeDTOMap;

    /**
     * Pagination
     */
    @NotNull
    private Pagination pagination;

    /**
     * Sorts
     */
    @NotEmpty
    private List<PageQuery.SortQuery> sorts;
}
