package com.moego.server.grooming.params.appointment;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Builder;

@Builder(toBuilder = true)
public record EditAppointmentColorCodeParams(
        @Schema(description = "business id", hidden = true) Integer businessId,
        @Schema(description = "token staff id", hidden = true) Integer tokenStaffId,
        @Schema(description = "appointment id") Long appointmentId,
        @Schema(description = "color code") String colorCode,
        @Schema(description = "repeat type: 1-only this, 2-apply to upcoming, 3-apply to all") @Max(3) @Min(1)
                Integer repeatType) {}
