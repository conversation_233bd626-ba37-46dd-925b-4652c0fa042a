package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class PackageServiceDTO {

    private Integer id;
    private Integer invoiceId;
    private Integer invoiceItemId;
    private Integer packageId;
    private Integer serviceId;
    private BigDecimal servicePrice;
    private Integer packageServiceId;
    private String packageName;
    private String serviceName;
    private Integer quantity;
    private Long createTime;
    private Long updateTime;
}
