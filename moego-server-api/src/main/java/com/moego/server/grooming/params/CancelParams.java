package com.moego.server.grooming.params;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelParams {

    private Integer id;
    private Integer businessId;
    private Integer accountId;

    @Schema(description = "1-means true (did not show), 2-means false")
    private Byte noShow; // "1" means true (did not show), "2" means false

    @Schema(description = "0-by business, 1-by customer reply msg, 2-by delete pet")
    private Byte cancelByType; // 0-by business, 1-by customer reply msg, 2-by delete pet

    private String cancelReason;
    /**
     * repeat 预约取消种类，1，只取消当前    2，this one and following    3，all
     */
    @Schema(description = "repeat 预约取消种类，1-只取消当前 2-this one and following 3-all")
    private Integer repeatType;

    @Schema(description = "预先支付金额是否退款，只针对OB订单有效")
    private Boolean refundPrepaid;

    @Schema(description = "是否检查预约状态，如果是 finish 则不 cancelled")
    private Boolean checkFinishStatus;

    private Boolean releasePreAuth = true;

    @Schema(description = "控制是否自动触发关联的订单的 refund。true 需要， false 不需要")
    private Boolean autoRefundOrder = false;
}
