package com.moego.server.payment.api;

import com.moego.server.payment.dto.CompanyCardDTO;
import com.moego.server.payment.dto.CompanyPermissionStateView;
import com.moego.server.payment.dto.StripeCustomerInfo;
import com.moego.server.payment.dto.StripeSubscriptionInfo;
import com.moego.server.payment.dto.billing.CouponDetailDTO;
import com.moego.server.payment.dto.billing.CouponDetailView;
import com.moego.server.payment.dto.billing.StripeCompanyCustomerDTO;
import com.moego.server.payment.dto.billing.StripeCompanyCustomerView;
import com.moego.server.payment.params.CouponParams;
import com.moego.server.payment.params.PaymentRecordParam;
import com.moego.server.payment.params.billing.ChangeCustomerBalanceParams;
import com.moego.server.payment.params.billing.QueryCouponParams;
import com.moego.server.payment.params.billing.QueryStripeCompanyCustomerParams;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2023/11/22
 */
public interface IPaymentBillingService {
    @PostMapping("/service/payment/billing/get/company-custom/list")
    StripeCompanyCustomerDTO searchCompanyCustomBalance(
            @RequestBody @Validated QueryStripeCompanyCustomerParams params);

    @PostMapping("/service/payment/billing/get/company-custom/id")
    StripeCompanyCustomerView getCompanyCustomBalance(@RequestParam("id") Integer id);

    @PostMapping("/service/payment/billing/get/stripe-customer/customer-id")
    StripeCustomerInfo getStripeCustomerInfoByCustomerId(@RequestParam("customerId") String customerId);

    @PostMapping("/service/payment/billing/change/customer-balance")
    String changeCustomerBalance(@RequestBody @Validated ChangeCustomerBalanceParams params);

    @PostMapping("/service/payment/billing/get/company-permission-state/company-id")
    CompanyPermissionStateView getCompanyPermissionStateByCompanyId(@RequestParam("companyId") Integer companyId);

    @PostMapping("/service/payment/billing/get/subscription/sub-id")
    StripeSubscriptionInfo getSubscriptionInfo(@RequestParam("subId") String subId);

    @PostMapping("/service/payment/billing/update/subscription/coupon")
    String updateSubscriptionCoupon(
            @RequestParam("couponId") String couponId, @RequestParam("subscriptionId") String subscriptionId);

    @PostMapping("/service/payment/billing/coupon/create")
    Integer createNewCoupon(@RequestBody @Validated CouponParams couponParams);

    @PostMapping("/service/payment/billing/coupon/list")
    CouponDetailDTO queryCouponList(@RequestBody @Validated QueryCouponParams params);

    @PostMapping("/service/payment/billing/coupon/get")
    CouponDetailView getCouponById(@RequestParam("id") Long id);

    @PostMapping("/service/payment/billing/coupon/delete")
    Integer deleteCouponById(@RequestParam("id") Long id);

    @PostMapping("/service/payment/billing/getCompanyCardList")
    List<CompanyCardDTO> getCompanyCardList(@RequestParam("companyId") Integer companyId);

    @PostMapping("/service/payment/billing/reCharge")
    String reCharge(@RequestParam("companyId") Integer companyId, @RequestParam("cardId") String cardId);

    @PostMapping("/service/payment/billing/payAdminCreateInvoiceItem")
    String payAdminCreateInvoiceItem(
            @RequestParam("customer") String customer,
            @RequestParam("description") String description,
            @RequestParam("amount") Long amount,
            @RequestParam("invoiceId") String invoiceId);

    @PostMapping("/service/payment/billing/payAdminCreateInvoice")
    String payAdminCreateInvoice(@RequestParam("customerId") String customerId);

    @PostMapping("/service/payment/billing/payAdminPayInvoice")
    String payAdminPayInvoice(@RequestParam("invoiceId") String invoiceId, @RequestParam("cardId") String cardId);

    @PostMapping("/service/payment/billing/record/create")
    Integer createPaymentRecord(@RequestBody @Validated PaymentRecordParam paymentRecordParam);
}
