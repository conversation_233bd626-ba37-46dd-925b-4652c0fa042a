package com.moego.server.message.client;

import com.moego.server.message.api.IMoeGoVerificationCodeService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-message-server",
        url = "${moego.server.url.message}",
        contextId = "IMoeGoVerificationCodeClient")
public interface IMoeGoVerificationCodeClient extends IMoeGoVerificationCodeService {}
