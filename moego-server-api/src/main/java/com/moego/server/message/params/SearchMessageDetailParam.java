package com.moego.server.message.params;

import jakarta.annotation.Nullable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SearchMessageDetailParam {

    @Nullable
    private Integer businessId;

    @Nullable
    private Integer customerId;

    @Nullable
    private Sort sort;

    @Nullable
    private Page page;

    @Data
    @Accessors(chain = true)
    public static class Page {

        private int pageNumber = 1;
        private int pageSize = 10;
    }

    @Data
    @Accessors(chain = true)
    public static class Sort {

        private String field = "id";
        private Boolean isAsc = false;
    }
}
