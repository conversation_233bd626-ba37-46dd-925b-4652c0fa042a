package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationPendingPetVaccineRequestParams extends NotificationParams {
    private String title = "Vaccine info awaiting review";
    private String type = NotificationEnum.TYPE_PENDING_REVIEW_PET_VACCINE_REQUEST;
    private ExtraDTO webPushDto;

    @Data
    public static class ExtraDTO {
        private String customerFirstName;
        private String customerLastName;
        // second
        private Long createTime;
        private Long petVaccineRequestId;
    }
}
