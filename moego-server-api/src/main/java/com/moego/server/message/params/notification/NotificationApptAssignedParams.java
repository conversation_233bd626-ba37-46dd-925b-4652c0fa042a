package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraApptCommonDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationApptAssignedParams extends NotificationParams {
    // 通知是发给其他 staff 时的通知标题
    public static final String OTHER_NOTIFIER_TITLE = "Appointment assigned to {staffFirstName}";

    // 通知是发给预约相关的 staff 时的默认标题
    private String title = "Appointment assigned to you";
    private String type = NotificationEnum.TYPE_ACTIVITY_APPT_ASSIGNED;
    private NotificationExtraApptCommonDto webPushDto;
    private String mobilePushTitle = "New appointment booked";
    private String mobilePushBody = "{customerFullName} {Date}{Time} with {staffFirstName}";
    private Boolean isAppointmentRelated = true;
}
