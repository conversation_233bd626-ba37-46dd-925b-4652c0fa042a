package com.moego.server.message.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MessageThreadDTO {

    private Integer id;
    private Integer businessId;
    private Long companyId;
    private Integer staffId;
    private Integer customerId;
    private String firstName;
    private String lastName;
    private String customerAvatar;
    private String lastMessageText;
    private Integer lastMessageTime;
    private Integer unreadCount;
    private Integer status;
    private Integer createTime;
    private Integer updateTime;
    private Integer deleteTime;
    private Integer customerUnreadCount;
    private Integer openStatus;
    private Integer starsTime;
    private Integer blockTime;
    private Integer oldGroupId;
    private Integer lastErrorCode;
    private String lastErrorMsg;
    private Integer lastMessageId;
    private String clientColor;
    private Boolean isNewCustomer;
    private Boolean isProspectCustomer;
}
