{"customName": "mp4", "name": "File Group", "outputGroupSettings": {"fileGroupSettings": {"destination": "s3://moego-media/output/"}, "type": "FILE_GROUP_SETTINGS"}, "outputs": [{"audioDescriptions": [{"audioTypeControl": "FOLLOW_INPUT", "codecSettings": {"aacSettings": {"audioDescriptionBroadcasterMix": "NORMAL", "bitrate": 160000, "codecProfile": "LC", "codingMode": "CODING_MODE_2_0", "rateControlMode": "CBR", "rawFormat": "NONE", "sampleRate": 44100, "specification": "MPEG4"}, "codec": "AAC"}, "languageCodeControl": "FOLLOW_INPUT"}], "containerSettings": {"container": "MP4"}, "extension": "mp4", "videoDescription": {"afdSignaling": "NONE", "antiAlias": "ENABLED", "codecSettings": {"codec": "H_264", "h264Settings": {"adaptiveQuantization": "HIGH", "codecLevel": "AUTO", "codecProfile": "MAIN", "dynamicSubGop": "STATIC", "entropyEncoding": "CABAC", "fieldEncoding": "PAFF", "flickerAdaptiveQuantization": "DISABLED", "framerateControl": "INITIALIZE_FROM_SOURCE", "framerateConversionAlgorithm": "DUPLICATE_DROP", "gopBReference": "DISABLED", "gopClosedCadence": 1, "gopSize": 2.0, "gopSizeUnits": "SECONDS", "interlaceMode": "PROGRESSIVE", "maxBitrate": 2400000, "minIInterval": 0, "numberBFramesBetweenReferenceFrames": 2, "numberReferenceFrames": 3, "parControl": "INITIALIZE_FROM_SOURCE", "qualityTuningLevel": "SINGLE_PASS", "qvbrSettings": {"qvbrQualityLevel": 8}, "rateControlMode": "QVBR", "repeatPps": "DISABLED", "sceneChangeDetect": "ENABLED", "slices": 1, "slowPal": "DISABLED", "softness": 0, "spatialAdaptiveQuantization": "ENABLED", "syntax": "DEFAULT", "telecine": "NONE", "temporalAdaptiveQuantization": "ENABLED", "unregisteredSeiTimecode": "DISABLED"}}, "colorMetadata": "INSERT", "dropFrameTimecode": "ENABLED", "height": 720, "respondToAfd": "NONE", "scalingBehavior": "DEFAULT", "sharpness": 50, "timecodeInsertion": "DISABLED", "width": 1280}}]}