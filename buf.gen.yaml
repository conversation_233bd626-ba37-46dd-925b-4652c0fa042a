version: v1
plugins:
  - name: go
    out: temp/go
  - name: go-grpc
    out: temp/go
    opt:
      - require_unimplemented_servers=true

  - plugin: buf.build/protocolbuffers/python
    out: temp/python
  - plugin: buf.build/protocolbuffers/pyi
    out: temp/python
  - plugin: buf.build/grpc/python
    out: temp/python
  - name: es
    out: temp/node
    opt: target=ts

  - name: ts
    out: temp/web
    path: ./node_modules/ts-proto/protoc-gen-ts_proto
    strategy: all
    opt:
      - globalThisPolyfill=false
      - context=false
      - forceLong=string
      - esModuleInterop=true
      - env=browser
      - useOptionals=none
      - exportCommonSymbols=false
      #      - oneof=unions
      - unrecognizedEnum=false
      - removeEnumPrefix=true
      - lowerCaseServiceMethods=true
      - snakeToCamel=true
      - outputEncodeMethods=false
      - outputJsonMethods=false
      - outputPartialMethods=false
      - stringEnums=false
      - outputClientImpl=false
      - returnObservable=false
      - addGrpcMetadata=false
      - addNestjsRestParameter=false
      - nestJs=false
      - useDate=string
      - useMongoObjectId=string
      - outputSchema=false
      - outputTypeAnnotations=false
      - outputTypeRegistry=false
      #      - outputServices=generic-definitions
      #      - metadataType=Foo@./some-file
      - useAbortSignal=false
      - useAsyncIterable=false
      - emitImportedFiles=true
      #      - fileSuffix=
      #      - importSuffix=
      - enumsAsLiterals=false
      - useExactTypes=true
      - unknownFields=false
      - onlyTypes=true
      - usePrototypeForDefaults=false
      - useJsonWireFormat=true
      - useNumericEnumForJson=true
      - initializeFieldsAsUndefined=false
      #      - Mgoogle/protobuf/empty.proto=./google3/protobuf/empty
      - useMapType=false
      - useReadonlyTypes=false
      - useSnakeTypeName=false
      - outputExtensions=false
      - outputIndex=false
      #      - emitDefaultValues=json-methods
