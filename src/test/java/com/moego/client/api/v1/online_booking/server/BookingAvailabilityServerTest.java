package com.moego.client.api.v1.online_booking.server;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.client.api.v1.shared.util.ProtobufUtil;
import com.moego.idl.client.online_booking.v1.GetAvailableTimeRangesParams;
import com.moego.idl.models.offering.v1.AdditionalServiceRule;
import com.moego.idl.models.offering.v1.CustomizedServiceCategoryView;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BookingAvailabilityServerTest {

    @InjectMocks
    private BookingAvailabilityServer bookingAvailabilityServer;

    @Test
    void extractAdditionalServiceIds_shouldReturnEmptyList_whenCategoriesIsEmpty() {
        // given
        List<CustomizedServiceCategoryView> categories = List.of();

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).isEmpty();
    }

    @Test
    void extractAdditionalServiceIds_shouldReturnEmptyList_whenNoRulesAndNoBundleIds() {
        // given
        CustomizedServiceView service =
                CustomizedServiceView.newBuilder().setId(1L).build();
        CustomizedServiceCategoryView category =
                CustomizedServiceCategoryView.newBuilder().addServices(service).build();
        List<CustomizedServiceCategoryView> categories = List.of(category);

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).isEmpty();
    }

    @Test
    void extractAdditionalServiceIds_shouldReturnBundleServiceIds_whenOnlyBundleIdsExist() {
        // given
        CustomizedServiceView service = CustomizedServiceView.newBuilder()
                .setId(1L)
                .addAllBundleServiceIds(List.of(2L, 3L))
                .build();
        CustomizedServiceCategoryView category =
                CustomizedServiceCategoryView.newBuilder().addServices(service).build();
        List<CustomizedServiceCategoryView> categories = List.of(category);

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).hasSize(2).containsExactlyInAnyOrder(2L, 3L);
    }

    @Test
    void extractAdditionalServiceIds_shouldReturnEmptyList_whenAdditionalServiceRuleDisabled() {
        // given
        AdditionalServiceRule rule = AdditionalServiceRule.newBuilder()
                .setEnable(false)
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(2L)
                        .build())
                .build();
        CustomizedServiceView service = CustomizedServiceView.newBuilder()
                .setId(1L)
                .setAdditionalServiceRule(rule)
                .build();
        CustomizedServiceCategoryView category =
                CustomizedServiceCategoryView.newBuilder().addServices(service).build();
        List<CustomizedServiceCategoryView> categories = List.of(category);

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).isEmpty();
    }

    @Test
    void extractAdditionalServiceIds_shouldReturnServiceIds_whenAdditionalServiceRuleEnabled() {
        // given
        AdditionalServiceRule rule = AdditionalServiceRule.newBuilder()
                .setEnable(true)
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(2L)
                        .build())
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(3L)
                        .build())
                .build();
        CustomizedServiceView service = CustomizedServiceView.newBuilder()
                .setId(1L)
                .setAdditionalServiceRule(rule)
                .build();
        CustomizedServiceCategoryView category =
                CustomizedServiceCategoryView.newBuilder().addServices(service).build();
        List<CustomizedServiceCategoryView> categories = List.of(category);

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).hasSize(2).containsExactlyInAnyOrder(2L, 3L);
    }

    @Test
    void extractAdditionalServiceIds_shouldReturnCombinedIds_whenBothBundleAndRuleExist() {
        // given
        AdditionalServiceRule rule = AdditionalServiceRule.newBuilder()
                .setEnable(true)
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(3L)
                        .build())
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(4L)
                        .build())
                .build();
        CustomizedServiceView service = CustomizedServiceView.newBuilder()
                .setId(1L)
                .addAllBundleServiceIds(List.of(2L, 3L))
                .setAdditionalServiceRule(rule)
                .build();
        CustomizedServiceCategoryView category =
                CustomizedServiceCategoryView.newBuilder().addServices(service).build();
        List<CustomizedServiceCategoryView> categories = List.of(category);

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).hasSize(3).containsExactlyInAnyOrder(2L, 3L, 4L);
    }

    @Test
    void extractAdditionalServiceIds_shouldReturnDistinctServiceIds_whenDuplicateServiceIds() {
        // given
        AdditionalServiceRule rule = AdditionalServiceRule.newBuilder()
                .setEnable(true)
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(2L)
                        .build())
                .build();
        CustomizedServiceView service = CustomizedServiceView.newBuilder()
                .setId(1L)
                .addAllBundleServiceIds(List.of(2L, 3L))
                .setAdditionalServiceRule(rule)
                .build();
        CustomizedServiceCategoryView category =
                CustomizedServiceCategoryView.newBuilder().addServices(service).build();
        List<CustomizedServiceCategoryView> categories = List.of(category);

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).hasSize(2).containsExactlyInAnyOrder(2L, 3L);
    }

    @Test
    void extractAdditionalServiceIds_shouldHandleMultipleCategories() {
        // given
        AdditionalServiceRule rule1 = AdditionalServiceRule.newBuilder()
                .setEnable(true)
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(3L)
                        .build())
                .build();
        AdditionalServiceRule rule2 = AdditionalServiceRule.newBuilder()
                .setEnable(true)
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(4L)
                        .build())
                .build();
        CustomizedServiceView service1 = CustomizedServiceView.newBuilder()
                .setId(1L)
                .addAllBundleServiceIds(List.of(2L))
                .setAdditionalServiceRule(rule1)
                .build();
        CustomizedServiceView service2 = CustomizedServiceView.newBuilder()
                .setId(5L)
                .addAllBundleServiceIds(List.of(6L))
                .setAdditionalServiceRule(rule2)
                .build();
        CustomizedServiceCategoryView category1 =
                CustomizedServiceCategoryView.newBuilder().addServices(service1).build();
        CustomizedServiceCategoryView category2 =
                CustomizedServiceCategoryView.newBuilder().addServices(service2).build();
        List<CustomizedServiceCategoryView> categories = List.of(category1, category2);

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).hasSize(4).containsExactlyInAnyOrder(2L, 3L, 4L, 6L);
    }

    @Nested
    class FilterAvailableTimeRangeForEvaluationTests {

        @Test
        void shouldReturnOriginalMapWhenPetServicesListIsEmpty() {
            // Arrange
            var availableTimeRange = createSampleTimeRangeMap();
            var petServicesList = List.<GetAvailableTimeRangesParams.PetServices>of();

            // Act
            var actual = BookingAvailabilityServer.filterAvailableTimeRangeForEvaluation(
                    availableTimeRange, petServicesList);

            // Assert
            var expected = availableTimeRange;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnOriginalMapWhenNoEvaluationsHaveTime() {
            // Arrange
            var availableTimeRange = createSampleTimeRangeMap();

            var petService = GetAvailableTimeRangesParams.PetServices.newBuilder()
                    .addServices(GetAvailableTimeRangesParams.PetServices.Service.newBuilder()
                            .setEvaluation(GetAvailableTimeRangesParams.PetServices.Service.Evaluation.newBuilder()
                                    .setEvaluationId(1L)
                                    .build())
                            .build())
                    .build();

            var petServicesList = List.of(petService);

            // Act
            var actual = BookingAvailabilityServer.filterAvailableTimeRangeForEvaluation(
                    availableTimeRange, petServicesList);

            // Assert
            var expected = availableTimeRange;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReducePetCapacityWhenEvaluationTimeMatchesTimeRange() {
            // Arrange
            var today = LocalDate.now();
            var availableTimeRange = Map.of(
                    today,
                    List.of(DayTimeRangeDef.newBuilder()
                            .setStartTime(540) // 9:00 AM
                            .setEndTime(600) // 10:00 AM
                            .setPetCapacity(5)
                            .build()));

            var evaluationTime = 550; // 9:10 AM
            var evaluationDate = ProtobufUtil.toProtobufDate(today.toString());

            var petService = GetAvailableTimeRangesParams.PetServices.newBuilder()
                    .addServices(GetAvailableTimeRangesParams.PetServices.Service.newBuilder()
                            .setEvaluation(GetAvailableTimeRangesParams.PetServices.Service.Evaluation.newBuilder()
                                    .setEvaluationId(1L)
                                    .setDate(evaluationDate)
                                    .setTime(evaluationTime)
                                    .build())
                            .build())
                    .build();

            var petServicesList = List.of(petService);

            // Act
            var actual = BookingAvailabilityServer.filterAvailableTimeRangeForEvaluation(
                    availableTimeRange, petServicesList);

            // Assert
            var expected = Map.of(
                    today,
                    List.of(DayTimeRangeDef.newBuilder()
                            .setStartTime(540)
                            .setEndTime(600)
                            .setPetCapacity(4) // Reduced from 5 to 4
                            .build()));
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldRemoveTimeRangeWhenPetCapacityReachesZero() {
            // Arrange
            var today = LocalDate.now();
            var availableTimeRange = Map.of(
                    today,
                    List.of(DayTimeRangeDef.newBuilder()
                            .setStartTime(540) // 9:00 AM
                            .setEndTime(600) // 10:00 AM
                            .setPetCapacity(1) // Only 1 capacity
                            .build()));

            var evaluationTime = 550; // 9:10 AM
            var evaluationDate = ProtobufUtil.toProtobufDate(today.toString());

            var petService = GetAvailableTimeRangesParams.PetServices.newBuilder()
                    .addServices(GetAvailableTimeRangesParams.PetServices.Service.newBuilder()
                            .setEvaluation(GetAvailableTimeRangesParams.PetServices.Service.Evaluation.newBuilder()
                                    .setEvaluationId(1L)
                                    .setDate(evaluationDate)
                                    .setTime(evaluationTime)
                                    .build())
                            .build())
                    .build();

            var petServicesList = List.of(petService);

            // Act
            var actual = BookingAvailabilityServer.filterAvailableTimeRangeForEvaluation(
                    availableTimeRange, petServicesList);

            // Assert
            var expected = Map.of(today, List.<DayTimeRangeDef>of()); // Empty list as time range was removed
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldNotReducePetCapacityWhenEvaluationTimeIsOutsideTimeRange() {
            // Arrange
            var today = LocalDate.now();
            var availableTimeRange = Map.of(
                    today,
                    List.of(DayTimeRangeDef.newBuilder()
                            .setStartTime(540) // 9:00 AM
                            .setEndTime(600) // 10:00 AM
                            .setPetCapacity(5)
                            .build()));

            var evaluationTime = 660; // 11:00 AM - outside the time range
            var evaluationDate = ProtobufUtil.toProtobufDate(today.toString());

            var petService = GetAvailableTimeRangesParams.PetServices.newBuilder()
                    .addServices(GetAvailableTimeRangesParams.PetServices.Service.newBuilder()
                            .setEvaluation(GetAvailableTimeRangesParams.PetServices.Service.Evaluation.newBuilder()
                                    .setEvaluationId(1L)
                                    .setDate(evaluationDate)
                                    .setTime(evaluationTime)
                                    .build())
                            .build())
                    .build();

            var petServicesList = List.of(petService);

            // Act
            var actual = BookingAvailabilityServer.filterAvailableTimeRangeForEvaluation(
                    availableTimeRange, petServicesList);

            // Assert
            var expected = availableTimeRange; // No change
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldHandleMultipleEvaluationsInSameTimeRange() {
            // Arrange
            var today = LocalDate.now();
            var availableTimeRange = Map.of(
                    today,
                    List.of(DayTimeRangeDef.newBuilder()
                            .setStartTime(540) // 9:00 AM
                            .setEndTime(600) // 10:00 AM
                            .setPetCapacity(5)
                            .build()));

            var evaluationDate = ProtobufUtil.toProtobufDate(today.toString());

            var petService1 = GetAvailableTimeRangesParams.PetServices.newBuilder()
                    .addServices(GetAvailableTimeRangesParams.PetServices.Service.newBuilder()
                            .setEvaluation(GetAvailableTimeRangesParams.PetServices.Service.Evaluation.newBuilder()
                                    .setEvaluationId(1L)
                                    .setDate(evaluationDate)
                                    .setTime(550) // 9:10 AM
                                    .build())
                            .build())
                    .build();

            var petService2 = GetAvailableTimeRangesParams.PetServices.newBuilder()
                    .addServices(GetAvailableTimeRangesParams.PetServices.Service.newBuilder()
                            .setEvaluation(GetAvailableTimeRangesParams.PetServices.Service.Evaluation.newBuilder()
                                    .setEvaluationId(2L)
                                    .setDate(evaluationDate)
                                    .setTime(570) // 9:30 AM
                                    .build())
                            .build())
                    .build();

            var petServicesList = List.of(petService1, petService2);

            // Act
            var actual = BookingAvailabilityServer.filterAvailableTimeRangeForEvaluation(
                    availableTimeRange, petServicesList);

            // Assert
            var expected = Map.of(
                    today,
                    List.of(DayTimeRangeDef.newBuilder()
                            .setStartTime(540)
                            .setEndTime(600)
                            .setPetCapacity(3) // Reduced from 5 to 3 (2 pets)
                            .build()));
            assertThat(actual).isEqualTo(expected);
        }

        private static Map<LocalDate, List<DayTimeRangeDef>> createSampleTimeRangeMap() {
            var today = LocalDate.now();
            var tomorrow = today.plusDays(1);

            var timeRange1 = DayTimeRangeDef.newBuilder()
                    .setStartTime(540) // 9:00 AM
                    .setEndTime(600) // 10:00 AM
                    .setPetCapacity(5)
                    .build();

            var timeRange2 = DayTimeRangeDef.newBuilder()
                    .setStartTime(660) // 11:00 AM
                    .setEndTime(720) // 12:00 PM
                    .setPetCapacity(3)
                    .build();

            return Map.of(
                    today, List.of(timeRange1, timeRange2),
                    tomorrow, List.of(timeRange1));
        }
    }
}
