spring:
  profiles:
    active: local
  application:
    name: moego-client-api-v1
  main:
    web-application-type: none
  data:
    redis:
      host: ${secret.redis.host}
      port: ${secret.redis.port}
      ssl:
        enabled: ${secret.redis.tls}
      timeout: 60000
      password: ${secret.redis.password}
      key:
        delimiter: ':'
        prefix: apiv2

moego:
  server:
    url:
      business: http://moego-service-business:9203
      customer: http://moego-service-customer:9201
      payment: http://moego-service-payment:9204
      grooming: http://moego-service-grooming:9206
      retail: http://moego-service-retail:9207
      message: http://moego-service-message:9205
  grpc:
    server:
      empty-server-enabled: true
      debug-enabled: true
      port: 9090
    client:
      stubs:
        - service: moego.service.account.v1.**
          authority: moego-svc-account:9090
        - service: moego.service.agreement.v1.**
          authority: moego-svc-agreement:9090
        - service: moego.service.customer.v1.**
          authority: moego-svc-customer:9090
        - service: moego.service.risk_control.v1.**
          authority: moego-svc-risk-control:9090
        - service: moego.service.file.v2.**
          authority: moego-svc-file:9090
        - service: moego.service.notification.v1.**
          authority: moego-svc-notification:9090
        - service: moego.service.marketing.v1.**
          authority: moego-svc-marketing:9090
        - service: moego.service.organization.v1.**
          authority: moego-svc-organization:9090
        - service: moego.service.business_customer.v1.**
          authority: moego-svc-business-customer:9090
        - service: moego.service.offering.**
          authority: moego-svc-offering:9090
        - service: moego.service.map.v1.**
          authority: moego-svc-map:9090
        - service: moego.service.online_booking.v1.**
          authority: moego-svc-online-booking:9090
        - service: moego.service.sms.v1.**
          authority: moego-svc-sms:9090
        - service: moego.service.branded_app.v1.**
          authority: moego-svc-branded-app:9090
        - service: moego.service.appointment.**
          authority: moego-svc-appointment:9090
        - service: moego.service.order.v1.**
          authority: moego-svc-order:9090
        - service: moego.service.membership.v1.**
          authority: moego-svc-membership:9090
        - service: moego.service.message.v2.**
          authority: moego-svc-message-v2:9090
        - service: moego.service.activity_log.**
          authority: moego-svc-activity-log:9090
        - service: moego.service.ratelimit.v1.**
          authority: moego-svc-ratelimit:9090
        - service: moego.service.subscription.v1.*
          authority: moego-svc-subscription:9090
        - service: moego.service.enterprise.v1.**
          authority: moego-svc-enterprise:9090
        - service: moego.service.fulfillment.**
          authority: moego-svc-fulfillment:9090
  feature-flag:
    growth-book:
      api-host: ${secret.growthbook.host}
      client-key: ${secret.growthbook.client_key}
  map:
    autocomplete:
      radius: 5000
