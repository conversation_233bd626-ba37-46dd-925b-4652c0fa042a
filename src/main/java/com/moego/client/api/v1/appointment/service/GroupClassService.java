package com.moego.client.api.v1.appointment.service;

import com.google.protobuf.Timestamp;
import com.moego.client.api.v1.shared.util.PageUtil;
import com.moego.idl.client.appointment.v1.GetInstanceDetailResult;
import com.moego.idl.client.appointment.v1.GroupInstanceView;
import com.moego.idl.client.appointment.v1.ListInstancesResult;
import com.moego.idl.client.appointment.v1.ListSessionsParams;
import com.moego.idl.client.appointment.v1.ListSessionsResult;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.fulfillment.v1.FulfillmentModel;
import com.moego.idl.models.fulfillment.v1.GroupClassDetailModel;
import com.moego.idl.models.offering.v1.GroupClassInstance;
import com.moego.idl.models.offering.v1.GroupClassModel;
import com.moego.idl.models.offering.v1.GroupClassSession;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.fulfillment.v1.FulfillmentServiceGrpc;
import com.moego.idl.service.fulfillment.v1.GroupClassDetailServiceGrpc;
import com.moego.idl.service.fulfillment.v1.ListFulfillmentsRequest;
import com.moego.idl.service.fulfillment.v1.ListGroupClassDetailsRequest;
import com.moego.idl.service.offering.v1.GetInstanceRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.GroupClassServiceGrpc;
import com.moego.idl.service.offering.v1.ListInstancesRequest;
import com.moego.idl.service.offering.v1.ListSessionsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsRequest;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.exception.ExceptionUtil;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class GroupClassService {

    private final GroupClassServiceGrpc.GroupClassServiceBlockingStub groupClassBlockingStub;
    private final FulfillmentServiceGrpc.FulfillmentServiceBlockingStub fulfillmentService;
    private final BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestService;
    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub businessCustomerPetService;
    private final GroupClassDetailServiceGrpc.GroupClassDetailServiceBlockingStub groupClassDetailService;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementService;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffService;

    public ListInstancesResult listInstances(long companyId, long businessId, long customerId) {
        // 1. list booking requests
        final var bookingRequests = bookingRequestService
                .listBookingRequests(ListBookingRequestsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .addCustomerId(customerId)
                        .addAllStatuses(List.of(BookingRequestStatus.SUBMITTED, BookingRequestStatus.WAIT_LIST))
                        .addServiceItems(ServiceItemType.GROUP_CLASS)
                        .addAssociatedModels(BookingRequestAssociatedModel.SERVICE)
                        .build())
                .getBookingRequestsList();
        if (bookingRequests.isEmpty()) {
            return ListInstancesResult.newBuilder().build();
        }

        // 2. list instances
        final var instanceIds = bookingRequests.stream()
                .flatMap(br -> br.getServicesList().stream())
                .map(si -> si.getGroupClass().getService().getClassInstanceId())
                .distinct()
                .toList();
        if (instanceIds.isEmpty()) {
            return ListInstancesResult.newBuilder().build();
        }
        final var instances = groupClassBlockingStub
                .listInstances(ListInstancesRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllIds(instanceIds)
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(instanceIds.size())
                                .build())
                        .build())
                .getGroupClassInstancesList();
        if (instances.isEmpty()) {
            return ListInstancesResult.newBuilder().build();
        }

        // 3. list sessions
        final var sessions = groupClassBlockingStub
                .listSessions(ListSessionsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllGroupClassInstanceId(instanceIds)
                        .build())
                .getSessionsList();

        // 4. list pet info
        final var petIds = bookingRequests.stream()
                .flatMap(br -> br.getServicesList().stream())
                .map(si -> si.getGroupClass().getService().getPetId())
                .distinct()
                .toList();
        final var pets = businessCustomerPetService
                .batchGetPetInfo(BatchGetPetInfoRequest.newBuilder()
                        .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                        .addAllIds(petIds)
                        .build())
                .getPetsList();

        // 5. list group classes
        final var groupClassIds =
                instances.stream().map(GroupClassInstance::getGroupClassId).toList();
        final var serviceRequest = GetServiceListByIdsRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllServiceIds(groupClassIds)
                .build();

        final var groupClasses =
                serviceManagementService.getServiceListByIds(serviceRequest).getServicesList();
        return buildResult(bookingRequests, groupClasses, instances, sessions, pets);
    }

    private static ListInstancesResult buildResult(
            List<BookingRequestModel> bookingRequests,
            List<ServiceBriefView> groupClasses,
            List<GroupClassInstance> instances,
            List<GroupClassSession> sessions,
            List<BusinessCustomerPetInfoModel> pets) {
        // instance list -> instance map by id
        final var instanceMap =
                instances.stream().collect(Collectors.toMap(GroupClassInstance::getId, Function.identity()));
        // session list -> session map by instance id
        final var sessionMap =
                sessions.stream().collect(Collectors.groupingBy(GroupClassSession::getGroupClassInstanceId));
        // pet list -> pet map by id
        final var petMap =
                pets.stream().collect(Collectors.toMap(BusinessCustomerPetInfoModel::getId, Function.identity()));

        final var result = ListInstancesResult.newBuilder();
        for (final var bookingRequest : bookingRequests) {
            final var instanceId =
                    bookingRequest.getServices(0).getGroupClass().getService().getClassInstanceId();
            final var instance = instanceMap.get(instanceId);
            final var sessionList = sessionMap.get(instanceId);
            final var petList = bookingRequest.getServicesList().stream()
                    .map(si -> petMap.get(si.getGroupClass().getService().getPetId()))
                    .toList();
            result.addGroupInstances(ListInstancesResult.GroupInstanceView.newBuilder()
                    .setGroupClassInstance(instance)
                    .addAllSessions(sessionList)
                    .addAllPets(petList)
                    .build());
        }
        result.addAllGroupClasses(toGroupClassBriefView(groupClasses));
        return result.build();
    }

    public ListSessionsResult listSessions(
            long companyId,
            long businessId,
            long customerId,
            ListSessionsParams.Status status,
            PaginationRequest pagination) {
        // 1. list fulfillments
        final var fulfillments = fulfillmentService
                .listFulfillments(ListFulfillmentsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addBusinessIds(businessId)
                        .setFilter(ListFulfillmentsRequest.Filter.newBuilder().addCustomerIds(customerId))
                        .build())
                .getFulfillmentsList();
        if (fulfillments.isEmpty()) {
            return ListSessionsResult.newBuilder().build();
        }

        // 2. list group class details
        final var fulfillmentIds =
                fulfillments.stream().map(FulfillmentModel::getId).toList();
        final var groupClassDetails = groupClassDetailService
                .listGroupClassDetails(ListGroupClassDetailsRequest.newBuilder()
                        .setFilter(
                                ListGroupClassDetailsRequest.Filter.newBuilder().addAllFulfillmentIds(fulfillmentIds))
                        .build())
                .getGroupClassDetailsList();

        // 3. list instances
        final var instanceIds = groupClassDetails.stream()
                .map(GroupClassDetailModel::getGroupClassInstanceId)
                .toList();
        final var instances = listGroupInstances(companyId, instanceIds);

        // 4. list sessions
        final var groupClassInstanceIds = groupClassDetails.stream()
                .map(GroupClassDetailModel::getGroupClassInstanceId)
                .toList();
        final var sessionRequest = ListSessionsRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllGroupClassInstanceId(groupClassInstanceIds);
        switch (status) {
            case PAST:
                sessionRequest.setStartTimeMax(Timestamp.newBuilder()
                        .setSeconds(System.currentTimeMillis() / 1000)
                        .build());
                break;
            case UPCOMING:
                sessionRequest.setStartTimeMin(Timestamp.newBuilder()
                        .setSeconds(System.currentTimeMillis() / 1000)
                        .build());
                break;
            default:
        }
        final var sessions =
                groupClassBlockingStub.listSessions(sessionRequest.build()).getSessionsList();
        final var sortedSessions = sessions.stream()
                .sorted(Comparator.comparingLong(
                        s -> s.getInterval().getStartTime().getSeconds()))
                .toList();

        // 分页
        final var pageResult = PageUtil.paginate(sortedSessions, pagination);
        final var paginatedSessions = pageResult.getKey();
        final var paginationResponse = pageResult.getValue();
        // 4. list pet info
        final var instanceToPetsMap = getPetMap(companyId, groupClassDetails);

        // 5. list group class
        final var groupClassIds = instances.stream()
                .map(i -> i.getGroupInstance().getGroupClassId())
                .toList();
        final var serviceRequest = GetServiceListByIdsRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllServiceIds(groupClassIds)
                .build();
        final var groupClasses =
                serviceManagementService.getServiceListByIds(serviceRequest).getServicesList();

        return buildResult(paginatedSessions, instances, groupClasses, instanceToPetsMap, paginationResponse);
    }

    private List<GroupInstanceView> listGroupInstances(long companyId, List<Long> instanceIds) {
        // 1. list instances
        final var instances = groupClassBlockingStub
                .listInstances(ListInstancesRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllIds(instanceIds)
                        .build())
                .getGroupClassInstancesList();

        // 2. list sessions
        final var sessionRequest = ListSessionsRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllGroupClassInstanceId(instanceIds)
                .build();
        final var sessions = groupClassBlockingStub.listSessions(sessionRequest).getSessionsList();

        return instances.stream()
                .map(i -> GroupInstanceView.newBuilder()
                        .setGroupInstance(i)
                        .addAllSessions(sessions)
                        .build())
                .toList();
    }

    private static List<GroupClassModel> toGroupClassBriefView(List<ServiceBriefView> groupClasses) {
        return groupClasses.stream()
                .map(GroupClassService::toGroupClassBriefView)
                .toList();
    }

    private static GroupClassModel toGroupClassBriefView(ServiceBriefView groupClass) {
        return GroupClassModel.newBuilder()
                .setId(groupClass.getId())
                .setName(groupClass.getName())
                .setDurationSessionMin(groupClass.getDurationSessionMin())
                .setPrice(groupClass.getPrice())
                .setNumSessions(groupClass.getNumSessions())
                .setDescription(groupClass.getDescription())
                .build();
    }

    private Map<Long /* group class instance id */, List<BusinessCustomerPetInfoModel>> getPetMap(
            long companyId, List<GroupClassDetailModel> groupClassDetails) {
        final var petIds = groupClassDetails.stream()
                .map(GroupClassDetailModel::getPetId)
                .distinct()
                .toList();
        final var pets = businessCustomerPetService
                .batchGetPetInfo(BatchGetPetInfoRequest.newBuilder()
                        .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                        .addAllIds(petIds)
                        .build())
                .getPetsList();
        final var petMap =
                pets.stream().collect(Collectors.toMap(BusinessCustomerPetInfoModel::getId, Function.identity()));

        final var instanceToPetsMap = new HashMap<Long, List<BusinessCustomerPetInfoModel>>();
        for (final var groupClassDetail : groupClassDetails) {
            if (!petMap.containsKey(groupClassDetail.getPetId())) {
                continue;
            }
            instanceToPetsMap
                    .computeIfAbsent(groupClassDetail.getGroupClassInstanceId(), k -> new ArrayList<>())
                    .add(petMap.get(groupClassDetail.getPetId()));
        }
        return instanceToPetsMap;
    }

    private static ListSessionsResult buildResult(
            List<GroupClassSession> sessions,
            List<GroupInstanceView> groupInstances,
            List<ServiceBriefView> groupClasses,
            Map<Long /* group class instance id */, List<BusinessCustomerPetInfoModel>> instanceToPetsMap,
            PaginationResponse pagination) {
        final var result = ListSessionsResult.newBuilder()
                .addAllGroupClasses(toGroupClassBriefView(groupClasses))
                .addAllGroupInstances(groupInstances)
                .setPagination(pagination);
        for (final var session : sessions) {
            result.addSessions(ListSessionsResult.GroupSessionView.newBuilder()
                    .setSession(session)
                    .addAllPets(instanceToPetsMap.getOrDefault(session.getGroupClassInstanceId(), List.of()))
                    .build());
        }
        return result.build();
    }

    public GetInstanceDetailResult getInstanceDetail(long companyId, long instanceId, List<Long> petIds) {
        // 1. get instance detail
        final var instance = groupClassBlockingStub
                .getInstance(GetInstanceRequest.newBuilder().setId(instanceId).build())
                .getGroupClassInstance();
        if (instance.getCompanyId() != companyId) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format("Invalid instance id[%d], can't found in company[%d]", instanceId, companyId));
        }

        // 2. get sessions
        final var sessions = groupClassBlockingStub
                .listSessions(ListSessionsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllGroupClassInstanceId(List.of(instanceId))
                        .build())
                .getSessionsList();

        // 3. get group class
        final var groupClass = serviceManagementService
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addServiceIds(instance.getGroupClassId())
                        .build())
                .getServicesList();
        final var groupClassBriefView = toGroupClassBriefView(groupClass.get(0));

        // 4. get staff
        final var staffResponse = staffService.queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                .addStaffIds(instance.getStaffId())
                .build());
        if (staffResponse.getStaffsCount() == 0) {
            throw ExceptionUtil.bizException(
                    Code.CODE_STAFF_NOT_FOUND,
                    String.format(
                            "Invalid staff id[%d], can't found in company[%d]", instance.getStaffId(), companyId));
        }
        final var staff = staffResponse.getStaffs(0);

        // 5. get pet info
        final var petResponse = businessCustomerPetService.batchGetPetInfo(BatchGetPetInfoRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                .addAllIds(petIds)
                .build());
        final var pets = petResponse.getPetsList();

        return buildResult(instance, sessions, groupClassBriefView, staff, pets);
    }

    private static GetInstanceDetailResult buildResult(
            GroupClassInstance instance,
            List<GroupClassSession> sessions,
            GroupClassModel groupClassBriefView,
            StaffModel staff,
            List<BusinessCustomerPetInfoModel> pets) {
        final var result = GetInstanceDetailResult.newBuilder()
                .setGroupInstance(instance)
                .addAllSessions(sessions)
                .setGroupClass(groupClassBriefView)
                .setTrainer(GetInstanceDetailResult.TrainerView.newBuilder()
                        .setId(staff.getId())
                        .setFirstName(staff.getFirstName())
                        .setLastName(staff.getLastName())
                        .setAvatarPath(staff.getAvatarPath())
                        .build())
                .addAllPets(pets);
        return result.build();
    }
}
