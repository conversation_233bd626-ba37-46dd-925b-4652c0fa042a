package com.moego.client.api.v1.online_booking.utils;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.AuthContext;
import com.moego.server.customer.dto.OBMainSessionDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import org.springframework.util.StringUtils;

public class OBSessionUtil {

    public static final String OB_NEW_CLIENT_SESSION_PHONE_NUMBER_KEY = "customer.phone_number";

    public static OBMainSessionDTO mustGetOBSession(OBAnonymousParams anonymousParams) {
        var context = AuthContext.get();
        // main session id is required
        var sessionId = context.sessionId();
        if (sessionId == null) {
            throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
        }
        // main account id should be < -1
        var accountId = context.accountId();
        if (accountId == null || accountId >= -1) {
            throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
        }

        // obName is required, 优先解析 domain（自定义域名的 OB）, 然后解析 name （统一域名的 ob）
        var obName = getOBName(anonymousParams);
        if (!StringUtils.hasText(obName)) {
            throw bizException(Code.CODE_BOOK_ONLINE_NAME_INVALID);
        }

        return new OBMainSessionDTO(sessionId, accountId, obName);
    }

    private static String getOBName(OBAnonymousParams anonymousParams) {
        var obName = anonymousParams.getDomain();
        return StringUtils.hasText(obName) ? obName : anonymousParams.getName();
    }
}
