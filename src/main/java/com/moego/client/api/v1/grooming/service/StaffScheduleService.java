package com.moego.client.api.v1.grooming.service;

import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityStatusRequest;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.StaffTime;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.grooming.client.IBookOnlineAvailableStaffClient;
import com.moego.server.grooming.client.IBookOnlineStaffTimeClient;
import com.moego.server.grooming.dto.BookOnlineStaffTimeDTO;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedCaseInsensitiveMap;

/**
 * <AUTHOR>
 * @since 2023/10/18
 */
@Service
@RequiredArgsConstructor
public class StaffScheduleService {

    private final IBookOnlineStaffTimeClient staffTimeClient;
    private final IBookOnlineAvailableStaffClient availableStaffClient;
    private final OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub
            obStaffAvailabilityServiceBlockingStub;

    /**
     * Get the list of staff available in the current availability type
     *
     * @param businessId business id
     * @return available staff list
     */
    public List<MoeStaffDto> getAvailableStaffListInAvailabilityType(Integer businessId) {
        return availableStaffClient.getAvailableStaffListInAvailabilityType(businessId);
    }

    /**
     * Get staff time in by working hours mode
     *
     * @param businessId business id
     * @param staffIds   staff ids
     * @return staff id to working hour
     */
    public Map<Integer, BookOnlineStaffTimeDTO> getOnlineBookingStaffWorkingHour(
            Integer businessId, List<Integer> staffIds) {
        Map<Integer, BookOnlineStaffTimeDTO> staffIdToTime =
                staffTimeClient.listBookOnlineStaffTimeByBusinessId(businessId).stream()
                        .filter(staffTime -> staffIds.contains(staffTime.getStaffId()))
                        .collect(
                                Collectors.toMap(BookOnlineStaffTimeDTO::getStaffId, Function.identity(), (o, n) -> o));
        var response = obStaffAvailabilityServiceBlockingStub.getStaffAvailabilityStatus(
                GetStaffAvailabilityStatusRequest.newBuilder()
                        .setBusinessId(businessId)
                        .build());
        Map<Long, Boolean> staffAvailabilityMap = response.getStaffAvailabilityMap();
        staffAvailabilityMap.entrySet().stream()
                .filter(staffAvailability -> !staffAvailability.getValue())
                .map(staffAvailability -> staffAvailability.getKey().intValue())
                .forEach(staffIdToTime::remove);
        return staffIdToTime;
    }

    /**
     * Merge staff working hours
     *
     * @param staffWorkingHour staff shift management working hours
     * @param staffIdToTime    staff online booking working hours
     * @return merged staff working hours
     */
    public Map<Integer, Map<String, TimeRangeDto>> mergeStaffWorkingHours(
            Map<Integer, Map<String, TimeRangeDto>> staffWorkingHour,
            Map<Integer, BookOnlineStaffTimeDTO> staffIdToTime) {
        staffIdToTime.forEach((staffId, obTime) -> {
            List<String> disabledDays = new ArrayList<>();
            Map<String, TimeRangeDto> dateToRange = staffWorkingHour.get(staffId);
            if (CollectionUtils.isEmpty(dateToRange)) {
                return;
            }
            dateToRange.forEach((date, staffRange) -> {
                BookOnlineStaffTimeDTO staffTime = staffIdToTime.get(staffId);
                if (staffTime != null) {
                    Map<String, StaffTime> dayToTime = new LinkedCaseInsensitiveMap<>();
                    dayToTime.putAll(
                            Optional.ofNullable(staffTime.getStaffTimes()).orElseGet(Map::of));
                    StaffTime time =
                            dayToTime.get(LocalDate.parse(date).getDayOfWeek().name());
                    if (time == null) {
                        return;
                    }
                    // disabled
                    if (Boolean.FALSE.equals(time.getIsSelected())) {
                        disabledDays.add(date);
                        return;
                    }
                    if (!time.getTimeRange().isEmpty()) {
                        staffRange.setStartTime(time.getTimeRange().get(0).getStartTime());
                        staffRange.setEndTime(time.getTimeRange()
                                .get(time.getTimeRange().size() - 1)
                                .getEndTime());
                    }
                }
            });
            disabledDays.forEach(dateToRange::remove);
        });
        return Map.copyOf(staffWorkingHour);
    }
}
