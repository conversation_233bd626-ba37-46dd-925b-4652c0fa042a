package com.moego.client.api.v1.online_booking.server;

import com.moego.idl.client.online_booking.v1.GetFeedingMedicationChargeParams;
import com.moego.idl.client.online_booking.v1.GetFeedingMedicationChargeResult;
import com.moego.idl.client.online_booking.v1.ServiceChargeServiceGrpc;
import com.moego.idl.service.order.v1.GetFeedingMedicationChargeRequest;
import com.moego.idl.service.order.v1.GetFeedingMedicationChargeResponse;
import com.moego.idl.service.order.v1.ServiceChargeCompanyServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class ServiceChargeServer extends ServiceChargeServiceGrpc.ServiceChargeServiceImplBase {
    private final ServiceChargeCompanyServiceGrpc.ServiceChargeCompanyServiceBlockingStub serviceChargeStub;
    private final IGroomingOnlineBookingService onlineBookingService;

    @Override
    @Auth(AuthType.OB)
    public void getFeedingMedicationCharge(
            GetFeedingMedicationChargeParams params, StreamObserver<GetFeedingMedicationChargeResult> result) {
        OBBusinessDTO biz = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(params.getDomain()).setName(params.getName()));
        GetFeedingMedicationChargeResponse feedingMedicationCharge =
                serviceChargeStub.getFeedingMedicationCharge(GetFeedingMedicationChargeRequest.newBuilder()
                        .setCompanyId(biz.getCompanyId())
                        .setBusinessId(biz.getBusinessId())
                        .setScheduleType(params.getScheduleType())
                        .addAllFoodSourceIds(params.getFoodSourceIdsList())
                        .setServiceItemType(params.getServiceItemType())
                        .build());

        result.onNext(GetFeedingMedicationChargeResult.newBuilder()
                .addAllServiceCharges(feedingMedicationCharge.getServiceChargesList())
                .build());
        result.onCompleted();
    }
}
