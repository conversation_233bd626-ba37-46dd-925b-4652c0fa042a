package com.moego.client.api.v1.online_booking.converter;

import static com.moego.client.api.v1.shared.util.ProtobufUtil.toLocalDate;
import static com.moego.client.api.v1.shared.util.ProtobufUtil.toProtobufDate;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.idl.client.online_booking.v1.BoardingAddon;
import com.moego.idl.client.online_booking.v1.DaycareAddon;
import com.moego.idl.client.online_booking.v1.Feeding;
import com.moego.idl.client.online_booking.v1.GetAppointmentDetailResult;
import com.moego.idl.client.online_booking.v1.GroomingAddon;
import com.moego.idl.client.online_booking.v1.Medication;
import com.moego.idl.client.online_booking.v1.PetAndServicesSummaryItem;
import com.moego.idl.client.online_booking.v1.Service;
import com.moego.idl.models.appointment.v1.AppointmentPetMedicationScheduleDef;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.GroupClassModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceBundleSaleView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.online_booking.v1.BoardingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.DaycareAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.idl.service.online_booking.v1.CreateBoardingAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateBoardingServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.CreateDaycareAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateDaycareServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateDogWalkingServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateFeedingRequest;
import com.moego.idl.service.online_booking.v1.CreateGroomingAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateGroomingServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateGroupClassServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateMedicationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.JsonUtil;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE,
        imports = {ServiceItemType.class})
public interface ServiceConverter {

    ServiceConverter INSTANCE = Mappers.getMapper(ServiceConverter.class);

    @Mapping(target = "serviceName", source = "name")
    @Mapping(target = "careType", source = "serviceItemType")
    PetAndServicesSummaryItem.ServiceItem toServiceSummary(ServiceBriefView service);

    @Mapping(target = "serviceName", source = "aliasForOnlineBooking")
    @Mapping(target = "careType", expression = "java(ServiceItemType.EVALUATION)")
    PetAndServicesSummaryItem.ServiceItem toServiceSummary(EvaluationBriefView evaluation);

    default GetAppointmentDetailResult.ServiceDetailItem toServiceDetail(
            PetDetailModel peDetail, ServiceBriefView service) {
        var serviceDetailItem = GetAppointmentDetailResult.ServiceDetailItem.newBuilder()
                .setId(peDetail.getServiceId())
                .setPetDetailId(peDetail.getId())
                .setDateType(peDetail.getDateType())
                .addAllSpecificDates(JsonUtil.toList(peDetail.getSpecificDates(), String.class))
                .setStartDate(peDetail.getStartDate())
                .setStartTime(peDetail.getStartTime())
                .setEndDate(peDetail.getEndDate())
                .setEndTime(peDetail.getEndTime())
                .setServiceTime(peDetail.getServiceTime());
        if (service != null) {
            serviceDetailItem.setServiceName(service.getName());
            serviceDetailItem.setCareType(service.getServiceItemType());
            serviceDetailItem.setMaxDuration(service.getMaxDuration());
        }
        return serviceDetailItem.build();
    }

    default GetAppointmentDetailResult.ServiceDetailItem toServiceDetail(
            BookingRequestModel.GroomingService grooming, Map<Long, ServiceBriefView> serviceIdToInfoMap) {
        var groomingDetail = grooming.getService();
        var service = serviceIdToInfoMap.get(groomingDetail.getServiceId());

        var builder = GetAppointmentDetailResult.ServiceDetailItem.newBuilder();
        builder.setId(groomingDetail.getServiceId());
        builder.setPetDetailId(groomingDetail.getId());
        builder.setServiceTime(groomingDetail.getServiceTime());
        if (groomingDetail.hasStartDate()) {
            builder.setStartDate(groomingDetail.getStartDate());
        }
        if (groomingDetail.hasStartTime()) {
            builder.setStartTime(groomingDetail.getStartTime());
        }
        if (groomingDetail.hasEndDate()) {
            builder.setEndDate(groomingDetail.getEndDate());
        }
        if (groomingDetail.hasEndTime()) {
            builder.setEndTime(groomingDetail.getEndTime());
        }
        if (service != null) {
            builder.setServiceName(service.getName());
            builder.setCareType(service.getServiceItemType());
        }
        return builder.build();
    }

    default GetAppointmentDetailResult.ServiceDetailItem toServiceDetail(
            BookingRequestModel.DogWalkingService dogWalking, Map<Long, ServiceBriefView> serviceIdToInfoMap) {
        var dogWalkingDetail = dogWalking.getService();
        var service = serviceIdToInfoMap.get(dogWalkingDetail.getServiceId());
        var serviceDetailItem = GetAppointmentDetailResult.ServiceDetailItem.newBuilder()
                .setId(dogWalkingDetail.getServiceId())
                .setPetDetailId(dogWalkingDetail.getId());
        if (service != null) {
            serviceDetailItem.setServiceName(service.getName());
            serviceDetailItem.setCareType(service.getServiceItemType());
        }
        return serviceDetailItem.build();
    }

    default GetAppointmentDetailResult.ServiceDetailItem toServiceDetail(
            BookingRequestModel.BoardingService boarding, Map<Long, ServiceBriefView> serviceIdToInfoMap) {
        var boardingDetail = boarding.getService();
        var service = serviceIdToInfoMap.get(boardingDetail.getServiceId());
        var serviceDetailItem = GetAppointmentDetailResult.ServiceDetailItem.newBuilder()
                .setId(boardingDetail.getServiceId())
                .setPetDetailId(boardingDetail.getId())
                .setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT)
                .setStartDate(boardingDetail.getStartDate())
                .setStartTime(boardingDetail.getStartTime())
                .setEndDate(boardingDetail.getEndDate())
                .setEndTime(boardingDetail.getEndTime())
                .addAllFeedings(FeedingConverter.INSTANCE.toFeeding(boarding.getFeedingsList()))
                .addAllMedications(MedicationConverter.INSTANCE.toMedication(boarding.getMedicationsList()));
        if (service != null) {
            serviceDetailItem.setServiceName(service.getName());
            serviceDetailItem.setCareType(service.getServiceItemType());
        }
        return serviceDetailItem.build();
    }

    default GetAppointmentDetailResult.ServiceDetailItem toServiceDetail(
            BookingRequestModel.DaycareService daycare, Map<Long, ServiceBriefView> serviceIdToInfoMap) {
        var daycareDetail = daycare.getService();
        var service = serviceIdToInfoMap.get(daycareDetail.getServiceId());
        var serviceDetailItem = GetAppointmentDetailResult.ServiceDetailItem.newBuilder()
                .setId(daycareDetail.getServiceId())
                .setPetDetailId(daycareDetail.getId())
                .setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE)
                .addAllSpecificDates(daycareDetail.getSpecificDatesList())
                .setStartTime(daycareDetail.getStartTime())
                .setEndTime(daycareDetail.getEndTime())
                .addAllFeedings(FeedingConverter.INSTANCE.toFeeding(daycare.getFeedingsList()))
                .addAllMedications(MedicationConverter.INSTANCE.toMedication(daycare.getMedicationsList()));
        if (service != null) {
            serviceDetailItem.setServiceName(service.getName());
            serviceDetailItem.setCareType(service.getServiceItemType());
            serviceDetailItem.setMaxDuration(service.getMaxDuration());
        }
        return serviceDetailItem.build();
    }

    @Mapping(target = "serviceName", source = "aliasForOnlineBooking")
    @Mapping(target = "careType", expression = "java(ServiceItemType.EVALUATION)")
    GetAppointmentDetailResult.ServiceDetailItem toEvaluationDetail(EvaluationBriefView evaluation);

    default GetAppointmentDetailResult.AddOnDetailItem toAddOnItem(
            ServiceBriefView info,
            PetDetailModel addOn,
            Map<Long, Map<Long, PetDetailModel>> petIdToServiceIdMap,
            ServiceItemType mainCareType) {
        var builder = GetAppointmentDetailResult.AddOnDetailItem.newBuilder()
                .setId(addOn.getServiceId())
                .setAddOnName(info.getName())
                .setRequireDedicatedStaff(info.getRequireDedicatedStaff())
                .setCareType(addOn.getServiceItemType())
                .setPetDetailId(addOn.getId())
                .setStartTime(addOn.getStartTime())
                .setEndTime(addOn.getEndTime())
                .setServiceTime(addOn.getServiceTime())
                .setQuantityPerDay(addOn.getQuantityPerDay());

        builder.setDateType(addOn.getDateType());
        switch (addOn.getDateType()) {
            case PET_DETAIL_DATE_DATE_POINT -> {
                if (StringUtils.hasText(addOn.getStartDate())) {
                    builder.addSpecificDates(addOn.getStartDate());
                    builder.setStartDate(addOn.getStartDate());
                }
            }
            case PET_DETAIL_DATE_SPECIFIC_DATE -> builder.addAllSpecificDates(
                    JsonUtil.toList(addOn.getSpecificDates(), String.class));
            case PET_DETAIL_DATE_EVERYDAY, PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY -> {
                var serviceIdToInfo = petIdToServiceIdMap.get(addOn.getPetId());
                var serviceDetail = serviceIdToInfo.get(addOn.getAssociatedServiceId());
                if (serviceDetail == null) {
                    // 兼容无关联 service 的 add-on
                    serviceDetail = serviceIdToInfo.values().stream()
                            .filter(p -> Objects.equals(p.getServiceItemType(), mainCareType))
                            .findFirst()
                            .orElseThrow(() ->
                                    ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Associated service not found"));
                }
                if (addOn.getDateType() == PetDetailDateType.PET_DETAIL_DATE_EVERYDAY) {
                    builder.addAllSpecificDates(
                            generateAllDatesBetweenByNight(serviceDetail.getStartDate(), serviceDetail.getEndDate()));
                } else if (addOn.getDateType() == PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY) {
                    builder.addAllSpecificDates(
                            generateAllDatesBetweenByDay(serviceDetail.getStartDate(), serviceDetail.getEndDate()));
                }
            }
            default -> throw bizException(Code.CODE_SERVER_ERROR, "Invalid date type: " + addOn.getDateType());
        }

        return builder.build();
    }

    default GetAppointmentDetailResult.AddOnDetailItem toAddOnItem(
            ServiceBriefView info, BoardingAddOnDetailModel addOn, BoardingServiceDetailModel service) {
        var builder = GetAppointmentDetailResult.AddOnDetailItem.newBuilder()
                .setId(addOn.getAddOnId())
                .setAddOnName(info.getName())
                .setRequireDedicatedStaff(info.getRequireDedicatedStaff())
                .setCareType(ServiceItemType.BOARDING)
                .setPetDetailId(addOn.getId())
                .setQuantityPerDay(addOn.getQuantityPerDay())
                .setServiceTime(addOn.getDuration());
        builder.setDateType(addOn.getDateType());
        switch (addOn.getDateType()) {
            case PET_DETAIL_DATE_SPECIFIC_DATE -> builder.addAllSpecificDates(addOn.getSpecificDatesList());
            case PET_DETAIL_DATE_EVERYDAY -> builder.addAllSpecificDates(
                    generateAllDatesBetweenByNight(service.getStartDate(), service.getEndDate()));
            case PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY -> builder.addAllSpecificDates(
                    generateAllDatesBetweenByDay(service.getStartDate(), service.getEndDate()));
            case PET_DETAIL_DATE_DATE_POINT -> {
                if (addOn.hasStartDate()) {
                    builder.setStartDate(toLocalDate(addOn.getStartDate()).toString());
                    builder.addSpecificDates(
                            toLocalDate(addOn.getStartDate()).toString()); // for backward compatibility
                }
            }
            default -> {}
        }
        return builder.build();
    }

    default GetAppointmentDetailResult.AddOnDetailItem toAddOnItem(
            ServiceBriefView info, DaycareAddOnDetailModel addOn, DaycareServiceDetailModel service) {
        var builder = GetAppointmentDetailResult.AddOnDetailItem.newBuilder()
                .setId(addOn.getAddOnId())
                .setAddOnName(info.getName())
                .setRequireDedicatedStaff(info.getRequireDedicatedStaff())
                .setCareType(ServiceItemType.DAYCARE)
                .setPetDetailId(addOn.getId())
                .setQuantityPerDay(addOn.getQuantityPerDay())
                .setServiceTime(addOn.getDuration());
        if (addOn.getIsEveryday()) {
            builder.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY)
                    .addAllSpecificDates(service.getSpecificDatesList());
        } else if (!CollectionUtils.isEmpty(addOn.getSpecificDatesList())) {
            builder.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE)
                    .addAllSpecificDates(addOn.getSpecificDatesList());
        }
        return builder.build();
    }

    default GetAppointmentDetailResult.AddOnDetailItem toAddOnItem(
            ServiceBriefView info, GroomingAddOnDetailModel addOn) {
        return GetAppointmentDetailResult.AddOnDetailItem.newBuilder()
                .setId(addOn.getAddOnId())
                .setAddOnName(info.getName())
                .setRequireDedicatedStaff(info.getRequireDedicatedStaff())
                .setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT)
                .addSpecificDates(addOn.getStartDate())
                .setCareType(ServiceItemType.GROOMING)
                .setPetDetailId(addOn.getId())
                .setStartTime(addOn.getStartTime())
                .setEndTime(addOn.getEndTime())
                .setServiceTime(addOn.getServiceTime())
                .build();
    }

    static List<String> generateAllDatesBetweenByNight(String startDate, String endDate) {
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        if (start.isEqual(end)) {
            return List.of(start.toString());
        }
        List<String> dates = new ArrayList<>();
        for (LocalDate date = start; date.isBefore(end); date = date.plusDays(1)) {
            dates.add(date.toString());
        }
        return dates;
    }

    static List<String> generateAllDatesBetweenByDay(String startDate, String endDate) {
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        List<String> dates = new ArrayList<>();
        for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            dates.add(date.toString());
        }
        return dates;
    }

    static CreateBookingRequestRequest.DaycareService buildDaycareService(Service.Daycare requestDaycare, long petId) {
        var feedings = new ArrayList<>(requestDaycare.getFeedingsList());
        var medications = new ArrayList<>(requestDaycare.getMedicationsList());
        if (requestDaycare.hasFeeding() && requestDaycare.getFeeding().getTimeCount() > 0) {
            feedings.add(requestDaycare.getFeeding());
        }
        if (requestDaycare.hasMedication() && requestDaycare.getMedication().getTimeCount() > 0) {
            medications.add(requestDaycare.getMedication());
        }
        var builder = CreateBookingRequestRequest.DaycareService.newBuilder()
                .setService(toCreateDaycareServiceDetailRequest(requestDaycare, petId))
                .addAllAddons(toCreateDaycareAddOnDetailRequest(requestDaycare.getAddonsList(), petId))
                .addAllFeedings(ServiceConverter.toCreateFeedingRequest(feedings))
                .addAllMedications(ServiceConverter.toCreateMedicationRequest(medications));

        if (requestDaycare.hasWaitlist()) {
            builder.setWaitlist(requestDaycare.getWaitlist());
        }

        return builder.build();
    }

    static CreateDaycareServiceDetailRequest toCreateDaycareServiceDetailRequest(
            Service.Daycare requestDaycare, long petId) {
        var builder = CreateDaycareServiceDetailRequest.newBuilder();
        builder.setPetId(petId);
        builder.setServiceId(requestDaycare.getServiceId());
        builder.addAllSpecificDates(requestDaycare.getDatesList());
        builder.setServicePrice(requestDaycare.getServicePrice());
        builder.setTaxId(requestDaycare.getTaxId());
        builder.setMaxDuration(requestDaycare.getMaxDuration());
        builder.setStartTime(requestDaycare.getArrivalTime());
        if (requestDaycare.hasPickupTime()) {
            builder.setEndTime(requestDaycare.getPickupTime());
        } else {
            var endTime = requestDaycare.getArrivalTime() + requestDaycare.getMaxDuration();
            var limitEndTime = Math.min(endTime, 1439);

            builder.setEndTime(limitEndTime);
        }
        return builder.build();
    }

    static CreateDaycareAddOnDetailRequest toCreateDaycareAddOnDetailRequest(DaycareAddon addon, long petId) {
        var builder = CreateDaycareAddOnDetailRequest.newBuilder();
        builder.setPetId(petId);
        builder.setAddOnId(addon.getId());
        builder.addAllSpecificDates(addon.getDatesList());
        builder.setIsEveryday(addon.getIsEveryDay());
        builder.setServicePrice(addon.getServicePrice());
        builder.setTaxId(addon.getTaxId());
        builder.setDuration(addon.getDuration());
        builder.setQuantityPerDay(addon.getQuantityPerDay());
        return builder.build();
    }

    static List<CreateDaycareAddOnDetailRequest> toCreateDaycareAddOnDetailRequest(
            List<DaycareAddon> addons, long petId) {
        if (CollectionUtils.isEmpty(addons)) {
            return List.of();
        }
        return addons.stream()
                .map(e -> toCreateDaycareAddOnDetailRequest(e, petId))
                .toList();
    }

    static CreateFeedingRequest toCreateFeedingRequest(Feeding feeding) {
        var builder = CreateFeedingRequest.newBuilder();
        builder.addAllTime(feeding.getTimeList());
        if (feeding.hasAmount()) {
            builder.setAmount(feeding.getAmount());
        }
        if (feeding.hasUnit()) {
            builder.setUnit(feeding.getUnit());
        }
        if (feeding.hasFoodType()) {
            builder.setFoodType(feeding.getFoodType());
        }
        if (feeding.hasFoodSource()) {
            builder.setFoodSource(feeding.getFoodSource());
        }
        if (feeding.hasInstruction()) {
            builder.setInstruction(feeding.getInstruction());
        }
        if (feeding.hasNote()) {
            builder.setNote(feeding.getNote());
        }
        if (feeding.hasAmountStr()) {
            builder.setAmountStr(feeding.getAmountStr());
        }
        return builder.build();
    }

    static List<CreateFeedingRequest> toCreateFeedingRequest(List<Feeding> feedings) {
        if (CollectionUtils.isEmpty(feedings)) {
            return List.of();
        }
        return feedings.stream().map(ServiceConverter::toCreateFeedingRequest).toList();
    }

    static CreateMedicationRequest toCreateMedicationRequest(Medication medication) {
        var builder = CreateMedicationRequest.newBuilder();
        builder.addAllTime(medication.getTimeList());
        if (medication.hasAmount()) {
            builder.setAmount(medication.getAmount());
        }
        if (medication.hasUnit()) {
            builder.setUnit(medication.getUnit());
        }
        if (medication.hasMedicationName()) {
            builder.setMedicationName(medication.getMedicationName());
        }
        if (medication.hasNotes()) {
            builder.setNotes(medication.getNotes());
        }
        if (medication.hasAmountStr()) {
            builder.setAmountStr(medication.getAmountStr());
        }
        if (medication.hasSelectedDate()) {
            builder.setSelectedDate(AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder()
                    .setDateType(medication.getSelectedDate().getDateType())
                    .addAllSpecificDates(medication.getSelectedDate().getSpecificDatesList())
                    .build());
        }
        return builder.build();
    }

    static List<CreateMedicationRequest> toCreateMedicationRequest(List<Medication> medications) {
        if (CollectionUtils.isEmpty(medications)) {
            return List.of();
        }
        return medications.stream()
                .map(ServiceConverter::toCreateMedicationRequest)
                .toList();
    }

    static CreateBookingRequestRequest.BoardingService buildBoardingService(
            Service.Boarding requestBoarding, long petId) {
        var feedings = new ArrayList<>(requestBoarding.getFeedingsList());
        var medications = new ArrayList<>(requestBoarding.getMedicationsList());
        if (requestBoarding.hasFeeding() && requestBoarding.getFeeding().getTimeCount() > 0) {
            feedings.add(requestBoarding.getFeeding());
        }
        if (requestBoarding.hasMedication() && requestBoarding.getMedication().getTimeCount() > 0) {
            medications.add(requestBoarding.getMedication());
        }

        var builder = CreateBookingRequestRequest.BoardingService.newBuilder()
                .setService(toCreateBoardingServiceDetailRequest(requestBoarding, petId))
                .addAllAddons(toCreateBoardingAddOnDetailRequest(requestBoarding.getAddonsList(), petId))
                .addAllFeedings(ServiceConverter.toCreateFeedingRequest(feedings))
                .addAllMedications(ServiceConverter.toCreateMedicationRequest(medications));

        if (requestBoarding.hasWaitlist()) {
            builder.setWaitlist(requestBoarding.getWaitlist());
        }

        return builder.build();
    }

    static CreateBoardingServiceDetailRequest toCreateBoardingServiceDetailRequest(
            Service.Boarding requestBoarding, long petId) {
        var builder = CreateBoardingServiceDetailRequest.newBuilder();
        builder.setPetId(petId);
        builder.setServiceId(requestBoarding.getServiceId());
        builder.setServicePrice(requestBoarding.getServicePrice());
        builder.setTaxId(requestBoarding.getTaxId());
        if (requestBoarding.hasStartDate()) {
            builder.setStartDate(requestBoarding.getStartDate());
        }
        builder.setStartTime(requestBoarding.getArrivalTime());
        if (requestBoarding.hasEndDate()) {
            builder.setEndDate(requestBoarding.getEndDate());
        }
        builder.setEndTime(requestBoarding.getPickupTime());
        return builder.build();
    }

    static CreateBoardingAddOnDetailRequest toCreateBoardingAddOnDetailRequest(BoardingAddon addon, long petId) {
        if (!addon.hasDateType() && !addon.getIsEveryDay() && CollectionUtils.isEmpty(addon.getDatesList())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Boarding add-on specific dates should not be empty!");
        }
        if (Objects.equals(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE, addon.getDateType())
                && CollectionUtils.isEmpty(addon.getDatesList())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Boarding add-on specific dates should not be empty!");
        }
        var builder = CreateBoardingAddOnDetailRequest.newBuilder();
        builder.setPetId(petId);
        builder.setAddOnId(addon.getId());
        builder.addAllSpecificDates(addon.getDatesList());
        builder.setIsEveryday(addon.getIsEveryDay());
        builder.setServicePrice(addon.getServicePrice());
        builder.setTaxId(addon.getTaxId());
        builder.setDuration(addon.getDuration());
        builder.setQuantityPerDay(addon.getQuantityPerDay());
        if (addon.hasDateType()) {
            builder.setDateType(addon.getDateType());
        }
        if (addon.hasStartDate()) {
            builder.setStartDate(addon.getStartDate());
        } else if (addon.getDatesCount() > 0) {
            builder.setStartDate(toProtobufDate(addon.getDates(0)));
        }
        return builder.build();
    }

    static List<CreateBoardingAddOnDetailRequest> toCreateBoardingAddOnDetailRequest(
            List<BoardingAddon> addons, long petId) {
        if (CollectionUtils.isEmpty(addons)) {
            return List.of();
        }
        return addons.stream()
                .map(e -> toCreateBoardingAddOnDetailRequest(e, petId))
                .toList();
    }

    ServiceBundleSaleView toServiceBundleSaleView(ServiceBriefView serviceBriefView);

    static CreateBookingRequestRequest.DogWalkingService buildDogWalkingService(
            Service.DogWalking dogWalking, long petToId) {
        var builder = CreateBookingRequestRequest.DogWalkingService.newBuilder();
        builder.setService(toCreateDogWalkingServiceDetailRequest(dogWalking, petToId));
        // todo add-ons
        return builder.build();
    }

    static CreateDogWalkingServiceDetailRequest toCreateDogWalkingServiceDetailRequest(
            Service.DogWalking dogWalking, long petId) {
        var builder = CreateDogWalkingServiceDetailRequest.newBuilder();
        builder.setPetId(petId);
        builder.setStaffId(dogWalking.getStaffId());
        builder.setServiceId(dogWalking.getServiceId());
        builder.setServiceTime(dogWalking.getDuration());
        builder.setServicePrice(dogWalking.getServicePrice());
        builder.setTaxId(dogWalking.getTaxId());
        builder.setStartDate(dogWalking.getDate());
        builder.setStartTime(dogWalking.getTime());
        var endDateTime = buildEndDateTime(dogWalking);
        builder.setEndDate(endDateTime.toLocalDate().toString());
        builder.setEndTime(endDateTime.toLocalTime().toSecondOfDay() / 60);
        return builder.build();
    }

    static CreateBookingRequestRequest.GroupClassService buildGroupClassService(
            Service.GroupClass groupClass, long petToId, long serviceId) {
        var builder = CreateBookingRequestRequest.GroupClassService.newBuilder();
        builder.setService(toCreateGroupClassServiceDetailRequest(groupClass, petToId, serviceId));
        return builder.build();
    }

    static CreateGroupClassServiceDetailRequest toCreateGroupClassServiceDetailRequest(
            Service.GroupClass groupClass, long petId, long serviceId) {
        var builder = CreateGroupClassServiceDetailRequest.newBuilder();
        builder.setPetId(petId);
        builder.setStaffId(groupClass.getStaffId());
        builder.setServiceId(serviceId);
        builder.setServicePrice(groupClass.getServicePrice());
        builder.setClassInstanceId(groupClass.getGroupClassInstanceId());
        builder.addAllSpecificDates(groupClass.getDatesList());
        builder.setStartTime(groupClass.getStartTime());
        builder.setEndTime(groupClass.getEndTime());
        builder.setDurationPerSession(groupClass.getEndTime() - groupClass.getStartTime());
        return builder.build();
    }

    static GroupClassModel toGroupClassModel(ServiceModel service) {
        return GroupClassModel.newBuilder()
                .setId(service.getServiceId())
                .setName(service.getName())
                .setDescription(service.getDescription())
                .setPrice(service.getPrice())
                .setNumSessions(service.getNumSessions())
                .setDurationSessionMin(service.getDurationSessionMin())
                .build();
    }

    static LocalDateTime buildEndDateTime(Service.DogWalking dogWalking) {
        return LocalDate.parse(dogWalking.getDate())
                .atStartOfDay()
                .plusMinutes(dogWalking.getTime())
                .plusMinutes(dogWalking.getDuration());
    }

    static CreateBookingRequestRequest.GroomingService buildGroomingService(Service.Grooming grooming, long petId) {
        var builder = CreateBookingRequestRequest.GroomingService.newBuilder();
        builder.setService(toCreateGroomingServiceDetailRequest(grooming, petId));
        builder.addAllAddonsV2(toCreateGroomingAddOnDetailRequest(grooming.getAddonsList(), petId));
        return builder.build();
    }

    static List<CreateGroomingAddOnDetailRequest> toCreateGroomingAddOnDetailRequest(
            List<GroomingAddon> addonsList, long petId) {
        return addonsList.stream()
                .map(addon -> toCreateGroomingAddOnDetailRequest(addon, petId))
                .toList();
    }

    static CreateGroomingAddOnDetailRequest toCreateGroomingAddOnDetailRequest(GroomingAddon addon, long petId) {
        var builder = CreateGroomingAddOnDetailRequest.newBuilder();
        builder.setPetId(petId);
        builder.setAddOnId(addon.getId());
        return builder.build();
    }

    static CreateGroomingServiceDetailRequest toCreateGroomingServiceDetailRequest(
            Service.Grooming grooming, long petId) {
        var builder = CreateGroomingServiceDetailRequest.newBuilder();
        builder.setPetId(petId);
        builder.setServiceId(grooming.getServiceId());
        builder.setStartDate(grooming.getStartDate());
        return builder.build();
    }
}
