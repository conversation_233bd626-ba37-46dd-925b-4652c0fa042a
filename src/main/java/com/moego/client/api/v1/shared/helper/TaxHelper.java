package com.moego.client.api.v1.shared.helper;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.organization.v1.TaxRuleModel;
import com.moego.idl.service.organization.v1.BatchGetTaxRuleRequest;
import com.moego.idl.service.organization.v1.TaxRuleServiceGrpc;
import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2025/5/20
 */
@Component
@RequiredArgsConstructor
public class TaxHelper {

    private final TaxRuleServiceGrpc.TaxRuleServiceBlockingStub taxStub;

    /**
     * Get tax by ids.
     *
     * @param taxIds tax ids
     * @return tax id -> tax
     */
    public Map<Long, TaxRuleModel> listTax(Collection<Long> taxIds) {
        if (ObjectUtils.isEmpty(taxIds)) {
            return Map.of();
        }
        var ids = taxIds.stream().filter(CommonUtil::isNormal).collect(Collectors.toSet());
        return taxStub
                .batchGetTaxRule(
                        BatchGetTaxRuleRequest.newBuilder().addAllIds(ids).build())
                .getRulesList()
                .stream()
                .collect(Collectors.toMap(TaxRuleModel::getId, Function.identity(), (a, b) -> a));
    }
}
