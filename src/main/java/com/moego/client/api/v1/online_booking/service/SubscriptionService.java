package com.moego.client.api.v1.online_booking.service;

import static com.moego.idl.models.subscription.v1.Feature.Key.CREDIT_CREDIT_POINT;

import com.google.protobuf.TextFormat;
import com.moego.common.utils.RedisUtil;
import com.moego.idl.api.subscription.v1.ListCreditChangeHistoryResult.CreditChangeHistory;
import com.moego.idl.api.subscription.v1.UpdateCreditParams;
import com.moego.idl.client.online_booking.v1.ListCreditChangeHistoryResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.subscription.v1.Count;
import com.moego.idl.models.subscription.v1.Entitlement;
import com.moego.idl.models.subscription.v1.Feature;
import com.moego.idl.models.subscription.v1.License;
import com.moego.idl.models.subscription.v1.User;
import com.moego.idl.service.subscription.v1.CreateLicenseAndEntitlementsRequest;
import com.moego.idl.service.subscription.v1.ListEntitlementsRequest;
import com.moego.idl.service.subscription.v1.ListLicensesRequest;
import com.moego.idl.service.subscription.v1.ListRevisionsRequest;
import com.moego.idl.service.subscription.v1.SubscriptionServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.utils.StringUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service(value = "com.moego.client.api.v1.online_booking.service.SubscriptionService")
@Slf4j
@RequiredArgsConstructor
public class SubscriptionService {

    private final SubscriptionServiceGrpc.SubscriptionServiceBlockingStub subscriptionService;

    private final RedisUtil redisUtil;

    public ListCreditChangeHistoryResult listCreditChangeHistory(Long customerId, PaginationRequest page) {
        var resp = subscriptionService.listRevisions(ListRevisionsRequest.newBuilder()
                .putDetailKeys("updateEntitlementDetail.owner.id", String.valueOf(customerId))
                .setPagination(page)
                .build());
        if (Objects.isNull(resp) || resp.getRevisionsList().isEmpty()) {
            return ListCreditChangeHistoryResult.newBuilder()
                    .addAllHistories(List.of())
                    .build();
        }
        var histories = new ArrayList<CreditChangeHistory>();
        for (var revision : resp.getRevisionsList()) {
            var createTime = revision.getCreatedAt();
            var detail = revision.getDetail();
            if (detail.hasUpdateEntitlementDetail()) {
                var ue = detail.getUpdateEntitlementDetail();
                try {
                    // FIXME(jett): 图省事直接序列化成UpdateCreditParams了
                    UpdateCreditParams.Builder builder = UpdateCreditParams.newBuilder();
                    TextFormat.getParser().merge(ue.getDetails().trim(), builder);
                    var ucp = builder.build();
                    var history = CreditChangeHistory.newBuilder()
                            .setId(revision.getId())
                            .setCredit(ucp.getCredit())
                            .setUser(User.newBuilder()
                                    .setId(ue.getOperatorId())
                                    .setType(User.Type.STAFF)
                                    .build())
                            .setType(ucp.getType())
                            .setNote(ucp.getNote())
                            .setReason(ucp.getReason())
                            .setAssociation(CreditChangeHistory.Association.newBuilder()
                                    .setInvoiceId(ucp.getInvoiceId())
                                    .setAppointmentId(ucp.getAppointmentId())
                                    .build())
                            .setCreatedTime(createTime)
                            .build();

                    histories.add(history);
                } catch (TextFormat.ParseException e) {
                    log.error("parse revision error, id: {}", revision.getId(), e);
                }
            }
        }
        return ListCreditChangeHistoryResult.newBuilder()
                .addAllHistories(histories)
                .setPagination(resp.getPagination())
                .build();
    }

    public Long getCredit(Long customerId) {
        var user =
                User.newBuilder().setId(customerId).setType(User.Type.CUSTOMER).build();
        var licenses = subscriptionService
                .listLicenses(ListLicensesRequest.newBuilder()
                        .setFilter(ListLicensesRequest.Filter.newBuilder()
                                .addOwners(user)
                                .build())
                        .build())
                .getLicensesList();
        if (licenses.isEmpty()) {
            licenses = createLicenses(user);
        }

        var entitlements = subscriptionService.listEntitlements(ListEntitlementsRequest.newBuilder()
                .setFilter(ListEntitlementsRequest.Filter.newBuilder()
                        .addAllLicenceIds(licenses.stream().map(License::getId).toList())
                        .addAllFeatureKeys(List.of(CREDIT_CREDIT_POINT))
                        .build())
                .build());

        if (Objects.isNull(entitlements) || entitlements.getEntitlementsList().isEmpty()) {
            licenses = createLicenses(user);
            entitlements = subscriptionService.listEntitlements(ListEntitlementsRequest.newBuilder()
                    .setFilter(ListEntitlementsRequest.Filter.newBuilder()
                            .addAllLicenceIds(
                                    licenses.stream().map(License::getId).toList())
                            .addAllFeatureKeys(List.of(CREDIT_CREDIT_POINT))
                            .build())
                    .build());
        }

        var countStream = entitlements.getEntitlementsList().stream()
                .map(Entitlement::getFeature)
                .map(Feature::getSetting)
                .map(Feature.Setting::getCount)
                .toList();

        var total = countStream.stream().mapToLong(Count::getTotalAmount).sum();
        var used = countStream.stream().mapToLong(Count::getUsedAmount).sum();
        // 剩余
        return total - used;
    }

    private List<License> createLicenses(User owner) {
        var resp = subscriptionService.createLicenseAndEntitlements(CreateLicenseAndEntitlementsRequest.newBuilder()
                .setBuyer(owner)
                .addFeatureIds(getCreditID())
                .setLicenseStatus(License.Status.VALID)
                .build());

        log.info("create license response: {}", resp);
        var license = subscriptionService.listLicenses(ListLicensesRequest.newBuilder()
                .setFilter(ListLicensesRequest.Filter.newBuilder()
                        .addOwners(owner)
                        .addStatuses(License.Status.VALID)
                        .build())
                .build());
        return license.getLicensesList();
    }

    private Long getCreditID() {
        String CREDIT_CREDIT_POINT_FEATURE_ID = "CREDIT_CREDIT_POINT_FEATURE_ID";
        var id = redisUtil.get(CREDIT_CREDIT_POINT_FEATURE_ID);
        if (StringUtils.isBlank(id))
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "credit feature id not found");
        return Long.parseLong(id);
    }
}
