package com.moego.client.api.v1.membership.controller;

import com.moego.client.api.v1.customer.service.CustomerService;
import com.moego.client.api.v1.membership.converter.MembershipConverter;
import com.moego.idl.client.membership.v1.CreateSellLinkForAppParams;
import com.moego.idl.client.membership.v1.CreateSellLinkForAppResult;
import com.moego.idl.client.membership.v1.CreateSellLinkParams;
import com.moego.idl.client.membership.v1.CreateSellLinkResult;
import com.moego.idl.client.membership.v1.ListMembershipsForSaleForAppParams;
import com.moego.idl.client.membership.v1.ListMembershipsForSaleForAppResult;
import com.moego.idl.client.membership.v1.ListMembershipsForSaleParams;
import com.moego.idl.client.membership.v1.ListMembershipsForSaleResult;
import com.moego.idl.client.membership.v1.ListMembershipsParams;
import com.moego.idl.client.membership.v1.ListMembershipsResult;
import com.moego.idl.client.membership.v1.MembershipServiceGrpc;
import com.moego.idl.models.business_customer.v1.BusinessPetCoatTypeModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.membership.v1.PetFilter;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetCoatTypeServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetSizeServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListPetCoatTypeRequest;
import com.moego.idl.service.business_customer.v1.ListPetCoatTypeResponse;
import com.moego.idl.service.business_customer.v1.ListPetSizeRequest;
import com.moego.idl.service.business_customer.v1.ListPetSizeResponse;
import com.moego.idl.service.membership.v1.ListMembershipsForSaleRequest;
import com.moego.idl.service.membership.v1.ListMembershipsForSaleResponse;
import com.moego.idl.service.membership.v1.SubscriptionServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.customer.api.ICustomerProfileRequestService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.util.Objects;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@GrpcService
@Slf4j
public class MembershipController extends MembershipServiceGrpc.MembershipServiceImplBase {
    private final MembershipConverter membershipConverter;
    private final ICustomerProfileRequestService profileRequestService;
    private final BusinessPetCoatTypeServiceGrpc.BusinessPetCoatTypeServiceBlockingStub petCoatTypeService;
    private final BusinessPetSizeServiceGrpc.BusinessPetSizeServiceBlockingStub petSizeService;
    private final com.moego.idl.service.membership.v1.MembershipServiceGrpc.MembershipServiceBlockingStub
            membershipService;
    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerService;
    private final SubscriptionServiceGrpc.SubscriptionServiceBlockingStub subscriptionService;
    private final IGroomingOnlineBookingService onlineBookingService;
    private final CustomerService customerService;

    @Override
    @Auth(AuthType.OB)
    public void listMemberships(ListMembershipsParams request, StreamObserver<ListMembershipsResult> responseObserver) {
        OBBusinessDTO dto = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        final var input = membershipConverter.listMembershipsRequest(dto.getCompanyId(), request);
        final var output = membershipService.listMemberships(input);
        final var result = membershipConverter.listMembershipsResult(output);

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB)
    public void createSellLink(CreateSellLinkParams request, StreamObserver<CreateSellLinkResult> responseObserver) {
        OBBusinessDTO dto = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        Long customerId = AuthContext.get().customerId();
        if (Objects.isNull(customerId)) {
            // new customer, need to create
            final var createCustomerInput = membershipConverter.createCustomerRequest(
                    dto.getCompanyId(), dto.getBusinessId().longValue(), request.getCustomerWithPetInfo());
            final var createCustomerOutput =
                    businessCustomerService.createCustomerWithAdditionalInfo(createCustomerInput);
            customerId = createCustomerOutput.getCustomerId();
        }

        final var input = membershipConverter.createSellLinkRequest(dto, request, customerId);
        final var output = subscriptionService.createSellLink(input);
        final var result = membershipConverter.createSellLinkResult(output);

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB)
    public void listMembershipsForSale(
            ListMembershipsForSaleParams request, StreamObserver<ListMembershipsForSaleResult> responseObserver) {
        OBBusinessDTO dto = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        ListMembershipsForSaleRequest.Builder builder = ListMembershipsForSaleRequest.newBuilder();
        ListMembershipsForSaleRequest.Filter.Builder filterBuilder = ListMembershipsForSaleRequest.Filter.newBuilder();
        filterBuilder.setCompanyId(dto.getCompanyId());

        filterBuilder.setOnlyOnlineBookingEnabled(true);

        if (request.hasPagination()) {
            builder.setPagination(request.getPagination());
        }

        // 添加宠物过滤逻辑：调用 addPetFilters 方法从 RPC 获取映射数据后构造 PetFilter 列表
        if (request.hasFilter()) {
            addPetFilters(request.getFilter(), filterBuilder, dto.getCompanyId());
        }
        if (request.hasStatus()) {
            filterBuilder.addStatusIn(request.getStatus());
        }
        if (request.hasName()) {
            filterBuilder.setNameLike(request.getNameLike());
        }

        builder.setFilter(filterBuilder);

        ListMembershipsForSaleResponse response = membershipService.listMembershipsForSale(builder.build());

        responseObserver.onNext(ListMembershipsForSaleResult.newBuilder()
                .addAllMemberships(response.getMembershipsList())
                .setPagination(response.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void createSellLinkForApp(
            CreateSellLinkForAppParams request, StreamObserver<CreateSellLinkForAppResult> responseObserver) {

        var customer = customerService.getLinkCustomer(
                request.getCompanyId(), AuthContext.get().accountId());

        Long customerId =
                customer.getCustomerId() != null ? customer.getCustomerId().longValue() : null;

        if (Objects.isNull(customerId)) {
            // new customer, need to create
            final var createCustomerInput = membershipConverter.createCustomerForAppRequest(
                    request.getCompanyId(), request.getBusinessId(), request.getCustomerWithPetInfo());
            final var createCustomerOutput =
                    businessCustomerService.createCustomerWithAdditionalInfo(createCustomerInput);
            customerId = createCustomerOutput.getCustomerId();
        }

        final var input = membershipConverter.createSellLinkForAppRequest(request, customerId);
        final var output = subscriptionService.createSellLink(input);
        final var result = membershipConverter.createSellLinkForAppResult(output);

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void listMembershipsForSaleForApp(
            ListMembershipsForSaleForAppParams request,
            StreamObserver<ListMembershipsForSaleForAppResult> responseObserver) {

        ListMembershipsForSaleRequest.Builder builder = ListMembershipsForSaleRequest.newBuilder();
        ListMembershipsForSaleRequest.Filter.Builder filterBuilder = ListMembershipsForSaleRequest.Filter.newBuilder();
        filterBuilder.setCompanyId(request.getCompanyId());

        if (request.hasPagination()) {
            builder.setPagination(request.getPagination());
        }

        // 添加宠物过滤逻辑：调用 addPetFilters 方法从 RPC 获取映射数据后构造 PetFilter 列表
        if (request.hasFilter()) {
            addPetFilters(request.getFilter(), filterBuilder, request.getCompanyId());
        }
        if (request.hasStatus()) {
            filterBuilder.addStatusIn(request.getStatus());
        }

        builder.setFilter(filterBuilder);

        ListMembershipsForSaleResponse response = membershipService.listMembershipsForSale(builder.build());

        responseObserver.onNext(ListMembershipsForSaleForAppResult.newBuilder()
                .addAllMemberships(response.getMembershipsList())
                .addAllMembershipDiscountBenefits(response.getMembershipDiscountBenefitsList())
                .addAllMembershipQuantityBenefits(response.getMembershipQuantityBenefitsList())
                .setPagination(response.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    private void addPetFilters(
            ListMembershipsForSaleForAppParams.Filter filter,
            ListMembershipsForSaleRequest.Filter.Builder filterBuilder,
            Long companyId) {
        if (filter == null || filter.getPetsList().isEmpty()) {
            return;
        }

        // 获取宠物体型和毛色映射数据
        NavigableMap<Integer, BusinessPetSizeModel> petSizeMap = getPetSize(companyId);
        Map<String, BusinessPetCoatTypeModel> coatTypeMap = getPetCoatType(companyId);

        List<PetFilter> petFilters = filter.getPetsList().stream()
                .map(pet -> {
                    PetFilter.Builder petFilterBuilder = PetFilter.newBuilder();
                    petFilterBuilder.setPetType(pet.getPetType());
                    petFilterBuilder.setPetBreed(pet.getBreed());

                    if (pet.hasWeight()) {
                        double weightValue = pet.getWeight();
                        int roundedWeight = (int) Math.round(weightValue);
                        Map.Entry<Integer, BusinessPetSizeModel> entry = petSizeMap.floorEntry(roundedWeight);
                        // 若找到对应区间且 roundedWeight 在区间内，则设置 petSizeId
                        if (entry != null && roundedWeight <= entry.getValue().getWeightHigh()) {
                            petFilterBuilder.setPetSizeId(entry.getValue().getId());
                        }
                    }

                    if (pet.hasCoatType()) {
                        if (!pet.getCoatType().isEmpty()) {
                            BusinessPetCoatTypeModel coatModel = coatTypeMap.get(pet.getCoatType());
                            if (coatModel != null) {
                                petFilterBuilder.setPetCoatId(coatModel.getId());
                            }
                        }
                    }
                    return petFilterBuilder.build();
                })
                .collect(Collectors.toList());

        filterBuilder.addAllPetFilters(petFilters);
    }

    private void addPetFilters(
            ListMembershipsForSaleParams.Filter filter,
            ListMembershipsForSaleRequest.Filter.Builder filterBuilder,
            Long companyId) {
        if (filter == null || filter.getPetsList().isEmpty()) {
            return;
        }

        // 获取宠物体型和毛色映射数据
        NavigableMap<Integer, BusinessPetSizeModel> petSizeMap = getPetSize(companyId);
        Map<String, BusinessPetCoatTypeModel> coatTypeMap = getPetCoatType(companyId);

        List<PetFilter> petFilters = filter.getPetsList().stream()
                .map(pet -> {
                    PetFilter.Builder petFilterBuilder = PetFilter.newBuilder();
                    petFilterBuilder.setPetType(pet.getPetType());
                    petFilterBuilder.setPetBreed(pet.getBreed());

                    if (pet.hasWeight()) {
                        double weightValue = pet.getWeight();
                        int roundedWeight = (int) Math.round(weightValue);
                        Map.Entry<Integer, BusinessPetSizeModel> entry = petSizeMap.floorEntry(roundedWeight);
                        // 若找到对应区间且 roundedWeight 在区间内，则设置 petSizeId
                        if (entry != null && roundedWeight <= entry.getValue().getWeightHigh()) {
                            petFilterBuilder.setPetSizeId(entry.getValue().getId());
                        }
                    }

                    if (pet.hasCoatType()) {
                        if (!pet.getCoatType().isEmpty()) {
                            BusinessPetCoatTypeModel coatModel = coatTypeMap.get(pet.getCoatType());
                            if (coatModel != null) {
                                petFilterBuilder.setPetCoatId(coatModel.getId());
                            }
                        }
                    }
                    return petFilterBuilder.build();
                })
                .collect(Collectors.toList());

        filterBuilder.addAllPetFilters(petFilters);
    }

    /**
     * 调用 petCoatTypeService 拉取宠物毛色类型数据，并返回一个 key 为毛色名称的映射
     */
    private Map<String, BusinessPetCoatTypeModel> getPetCoatType(Long companyId) {
        ListPetCoatTypeResponse response = petCoatTypeService.listPetCoatType(
                ListPetCoatTypeRequest.newBuilder().setCompanyId(companyId).build());
        return response.getCoatTypesList().stream()
                .collect(Collectors.toMap(BusinessPetCoatTypeModel::getName, Function.identity(), (a, b) -> a));
    }

    /**
     * 调用 petSizeService 拉取宠物体型数据，并返回一个以体型下限（weightLow）为 key 的 NavigableMap
     */
    private NavigableMap<Integer, BusinessPetSizeModel> getPetSize(Long companyId) {
        ListPetSizeResponse response = petSizeService.listPetSize(
                ListPetSizeRequest.newBuilder().setCompanyId(companyId).build());
        return response.getSizesList().stream()
                .collect(Collectors.toMap(
                        BusinessPetSizeModel::getWeightLow, Function.identity(), (a, b) -> a, TreeMap::new));
    }
}
