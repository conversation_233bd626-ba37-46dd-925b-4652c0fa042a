package com.moego.client.api.v1.appointment;

import com.moego.idl.client.appointment.v1.DailyReportServiceGrpc;
import com.moego.idl.client.appointment.v1.ListDailyReportConfigParams;
import com.moego.idl.client.appointment.v1.ListDailyReportConfigResult;
import com.moego.idl.models.appointment.v1.ListDailyReportConfigFilter;
import com.moego.idl.models.appointment.v1.ReportCardStatus;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigByFilterRequest;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigByFilterResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class DailyReportController extends DailyReportServiceGrpc.DailyReportServiceImplBase {

    private final com.moego.idl.service.appointment.v1.DailyReportServiceGrpc.DailyReportServiceBlockingStub
            dailyReportServiceBlockingStub;
    private final IGroomingOnlineBookingService onlineBookingService;

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listDailyReportConfig(
            ListDailyReportConfigParams request, StreamObserver<ListDailyReportConfigResult> responseObserver) {
        OBBusinessDTO biz = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        ListDailyReportConfigByFilterResponse listDailyReportConfigByFilterResponse =
                dailyReportServiceBlockingStub.listDailyReportConfigByFilter(
                        ListDailyReportConfigByFilterRequest.newBuilder()
                                .setBusinessId(biz.getBusinessId())
                                .setCompanyId(biz.getCompanyId())
                                .setFilter(ListDailyReportConfigFilter.newBuilder()
                                        .setPetId(request.getPetId())
                                        .setStatus(ReportCardStatus.REPORT_CARD_SENT)
                                        .build())
                                .setPagination(request.getPagination())
                                .build());

        ListDailyReportConfigResult result = ListDailyReportConfigResult.newBuilder()
                .setPagination(listDailyReportConfigByFilterResponse.getPagination())
                .addAllReportConfigs(listDailyReportConfigByFilterResponse.getReportConfigsList())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }
}
