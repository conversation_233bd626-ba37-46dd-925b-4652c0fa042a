package com.moego.client.api.v1.online_booking.utils;

import com.google.type.Date;
import java.time.Clock;
import java.time.LocalDate;
import java.util.Comparator;

public class DateComparator implements Comparator<Date> {
    public static final DateComparator INSTANCE = new DateComparator();

    @Override
    public int compare(Date date1, Date date2) {
        var diff = date1.getYear() - date2.getYear();
        if (diff != 0) {
            return diff;
        }
        diff = date1.getMonth() - date2.getMonth();
        if (diff != 0) {
            return diff;
        }
        return date1.getDay() - date2.getDay();
    }

    public static Date getMaxDate(Date date1, Date date2) {
        return INSTANCE.compare(date1, date2) > 0 ? date1 : date2;
    }

    public static boolean beforeToday(Date date) {
        var now = LocalDate.now(Clock.systemUTC());
        return INSTANCE.compare(
                        date,
                        Date.newBuilder()
                                .setYear(now.getYear())
                                .setMonth(now.getMonthValue())
                                .setDay(now.getDayOfMonth())
                                .build())
                < 0;
    }
}
