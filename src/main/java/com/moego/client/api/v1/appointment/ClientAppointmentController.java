package com.moego.client.api.v1.appointment;

import static com.moego.idl.models.order.v1.OrderSourceType.BOOKING_REQUEST;
import static java.lang.Math.toIntExact;

import com.google.protobuf.Timestamp;
import com.google.type.Interval;
import com.moego.client.api.v1.appointment.converter.AppointmentConverter;
import com.moego.client.api.v1.appointment.service.AccountAppointmentService;
import com.moego.client.api.v1.appointment.service.AppointmentCompositeService;
import com.moego.client.api.v1.branded_app.BrandedAppService;
import com.moego.client.api.v1.business.mapper.BusinessMapper;
import com.moego.client.api.v1.converter.DateConverter;
import com.moego.client.api.v1.customer.service.CustomerService;
import com.moego.client.api.v1.enterprise.service.CompanyService;
import com.moego.client.api.v1.grooming.mapper.BookOnlineMapper;
import com.moego.client.api.v1.grooming.mapper.MessageMapper;
import com.moego.client.api.v1.online_booking.service.BookingService;
import com.moego.client.api.v1.online_booking.service.OrganizationService;
import com.moego.client.api.v1.payment.mapper.PaymentMethodMapper;
import com.moego.common.constant.CommonConstant;
import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.common.dto.PaymentSummary;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.PaymentSettingConst;
import com.moego.common.enums.PaymentStatusEnum;
import com.moego.common.enums.PreAuthStatusEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.enums.order.DiscountType;
import com.moego.common.enums.order.OrderItemType;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.idl.client.appointment.v1.AllowedOperation;
import com.moego.idl.client.appointment.v1.AppointmentDetailItem;
import com.moego.idl.client.appointment.v1.AppointmentDetailView;
import com.moego.idl.client.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.client.appointment.v1.CancelAppointmentParams;
import com.moego.idl.client.appointment.v1.CancelAppointmentResult;
import com.moego.idl.client.appointment.v1.ClientAppointmentStatus;
import com.moego.idl.client.appointment.v1.ConfirmAppointmentParams;
import com.moego.idl.client.appointment.v1.ConfirmAppointmentResult;
import com.moego.idl.client.appointment.v1.GetAppointmentParams;
import com.moego.idl.client.appointment.v1.GetAppointmentResult;
import com.moego.idl.client.appointment.v1.GetLastFinishedAppointmentParams;
import com.moego.idl.client.appointment.v1.GetLastFinishedAppointmentResult;
import com.moego.idl.client.appointment.v1.ListAppointmentsParams;
import com.moego.idl.client.appointment.v1.ListAppointmentsResult;
import com.moego.idl.client.appointment.v1.ListPendingDayAppointmentsParams;
import com.moego.idl.client.appointment.v1.ListPendingDayAppointmentsResult;
import com.moego.idl.client.appointment.v1.ListTodayAppointmentsParams;
import com.moego.idl.client.appointment.v1.ListTodayAppointmentsResult;
import com.moego.idl.client.appointment.v1.ListUpcomingDayAppointmentsParams;
import com.moego.idl.client.appointment.v1.ListUpcomingDayAppointmentsResult;
import com.moego.idl.client.appointment.v1.OrderApplyPackage;
import com.moego.idl.client.appointment.v1.OrderDiscount;
import com.moego.idl.client.appointment.v1.OrderPayment;
import com.moego.idl.client.appointment.v1.PaymentDetailView;
import com.moego.idl.client.appointment.v1.RescheduleAppointmentParams;
import com.moego.idl.client.appointment.v1.RescheduleAppointmentResult;
import com.moego.idl.models.activity_log.v1.Resource;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentNoShowStatus;
import com.moego.idl.models.appointment.v1.AppointmentPetFeedingScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentPetMedicationScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentPetScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.AppointmentUpdatedBy;
import com.moego.idl.models.appointment.v1.CalendarCardType;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.GroomingRescheduleDef;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.PetDetailModelClientView;
import com.moego.idl.models.appointment.v1.PetEvaluationDetailClientView;
import com.moego.idl.models.appointment.v1.PetScheduleDef;
import com.moego.idl.models.appointment.v1.WaitListStatus;
import com.moego.idl.models.branded_app.v1.BrandedAppConfigModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.marketing.v1.DiscountCodeCompositeView;
import com.moego.idl.models.marketing.v1.DiscountCodeType;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.online_booking.v1.FeedingModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.MedicationModel;
import com.moego.idl.models.online_booking.v1.PaymentType;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderLineDiscountModel;
import com.moego.idl.models.order.v1.OrderLineItemModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.organization.v1.CompanyPreferenceSettingModel;
import com.moego.idl.models.payment.v1.PaymentMethod;
import com.moego.idl.models.payment.v1.PaymentModule;
import com.moego.idl.models.payment.v1.StripePaymentMethod;
import com.moego.idl.service.activity_log.v1.ActivityLogServiceGrpc.ActivityLogServiceBlockingStub;
import com.moego.idl.service.activity_log.v1.CreateActivityLogRequest;
import com.moego.idl.service.appointment.v1.AppointmentDateType;
import com.moego.idl.service.appointment.v1.AppointmentScheduleServiceGrpc.AppointmentScheduleServiceBlockingStub;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc.AppointmentServiceBlockingStub;
import com.moego.idl.service.appointment.v1.CancelAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentListRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailListRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentsForCustomersRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentsRequest;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc.PetDetailServiceBlockingStub;
import com.moego.idl.service.appointment.v1.RescheduleGroomingServiceRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.GetCustomerInfoRequest;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc.BookingRequestServiceBlockingStub;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsRequest;
import com.moego.idl.service.order.v1.CreateOrderRequest;
import com.moego.idl.service.order.v1.GetOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc.OrderServiceBlockingStub;
import com.moego.idl.service.order.v1.UpdateOrderRequest;
import com.moego.idl.utils.v2.OrderBy;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.api.IGroomingAppointmentService;
import com.moego.server.grooming.api.IOBService;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.dto.CustomerAppointmentNumInfoDTO;
import com.moego.server.grooming.dto.PackageServiceDTO;
import com.moego.server.grooming.dto.appointment.history.CancelLogDTO;
import com.moego.server.grooming.dto.appointment.history.ChangeTimeLogDTO;
import com.moego.server.grooming.dto.appointment.history.CustomerReplyLogDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.params.ConfirmParams;
import com.moego.server.message.enums.ClientReplyTypeEnum;
import com.moego.server.message.enums.MessageMethodTypeEnum;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.client.IPaymentSettingClient;
import com.moego.server.payment.dto.ConvenienceFeeDTO;
import com.moego.server.payment.dto.PreAuthDTO;
import com.moego.server.payment.params.CreatePaymentParams;
import io.grpc.stub.StreamObserver;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/6/18
 */
@GrpcService
@RequiredArgsConstructor
public class ClientAppointmentController extends AppointmentServiceGrpc.AppointmentServiceImplBase {

    private final CustomerService customerService;

    private final BusinessMapper businessMapper;
    private final BookOnlineMapper bookOnlineMapper;
    private final MessageMapper messageMapper;
    private final AppointmentConverter appointmentConverter;
    private final PaymentMethodMapper paymentMethodMapper;

    private final AppointmentServiceBlockingStub appointmentStub;
    private final PetDetailServiceBlockingStub petDetailService;
    private final BookingRequestServiceBlockingStub bookingRequestService;
    private final CompanyService companyService;
    private final AppointmentScheduleServiceBlockingStub appointmentScheduleService;
    private final BookingService bookingService;
    private final AccountAppointmentService accountAppointmentService;
    private final OrderServiceBlockingStub orderService;
    private final IPaymentPaymentClient paymentClient;
    private final IPaymentSettingClient paymentSettingClient;
    private final BusinessCustomerServiceBlockingStub businessCustomerService;
    private final ActivityLogServiceBlockingStub activityLogService;
    private final BrandedAppService brandedAppService;
    private final FutureService futureService;
    private final IGroomingAppointmentService groomingAppointmentService;
    private final OrganizationService organizationService;
    private final AppointmentCompositeService appointmentCompositeService;

    private static final Map<ListAppointmentsParams.AppointmentSortField, String> APPOINTMENT_SORT_FIELD_MAP = Map.of(
            ListAppointmentsParams.AppointmentSortField.APPOINTMENT_DATE, "appointmentDate",
            ListAppointmentsParams.AppointmentSortField.APPOINTMENT_START_TIME, "appointmentStartTime");
    private static final Map<ListAppointmentsParams.AppointmentSortField, String> BOOKING_REQUEST_SORT_FIELD_MAP =
            Map.of(
                    ListAppointmentsParams.AppointmentSortField.APPOINTMENT_DATE, "startDate",
                    ListAppointmentsParams.AppointmentSortField.APPOINTMENT_START_TIME, "startTime");
    private static final int MAX_VIEW_DAYS = 180;

    @Override
    @Auth(AuthType.ACCOUNT)
    public void listAppointments(
            ListAppointmentsParams request, StreamObserver<ListAppointmentsResult> responseObserver) {
        var customer = customerService.getLinkCustomer(
                request.getCompanyId(), AuthContext.get().accountId());

        var countFuture =
                futureService.listAppointmentCounts(customer.getCompanyId(), List.of(customer.getCustomerId()));

        var listType = request.getFilter().getListType();
        ListAppointmentsResult result;
        if (Objects.equals(listType, ListAppointmentsParams.AppointmentListType.PENDING)) {
            List<OrderBy> orderBys = request.getSortsList().stream()
                    .map(sort -> OrderBy.newBuilder()
                            .setFieldName(BOOKING_REQUEST_SORT_FIELD_MAP.get(sort.getField()))
                            .setAsc(sort.getAsc())
                            .build())
                    .toList();
            result = buildBookingRequestsResult(customer, orderBys, request.getPagination());
        } else {
            List<OrderBy> orderBys = request.getSortsList().stream()
                    .map(sort -> OrderBy.newBuilder()
                            .setFieldName(APPOINTMENT_SORT_FIELD_MAP.get(sort.getField()))
                            .setAsc(sort.getAsc())
                            .build())
                    .toList();
            result = buildAppointmentsResult(customer, orderBys, request.getPagination(), listType);
        }

        var totalCount = Optional.ofNullable(countFuture.join().get(customer.getCustomerId()))
                .map(CustomerAppointmentNumInfoDTO::getTotalApptAndRequestsCount)
                .orElse(0);
        responseObserver.onNext(result.toBuilder().setTotalCount(totalCount).build());
        responseObserver.onCompleted();
    }

    private List<AppointmentModel> getAppointments(
            BaseBusinessCustomerIdDTO customer,
            List<OrderBy> orderBys,
            PaginationRequest pagination,
            ListAppointmentsParams.AppointmentListType listType,
            PaginationResponse.Builder paginationResponse) {

        return switch (listType) {
            case ONGOING -> {
                var request = buildOngoingPaginationRequest(customer, orderBys, pagination);
                var response = appointmentStub.listAppointments(request);
                paginationResponse.setTotal(response.getPagination().getTotal());
                yield response.getAppointmentsList();
            }
            case FINISHED -> {
                var request = buildFinishedPaginationRequest(customer, orderBys, pagination);
                var response = appointmentStub.listAppointments(request);
                paginationResponse.setTotal(response.getPagination().getTotal());
                yield response.getAppointmentsList();
            }
            case CANCELED -> {
                var request = buildCanceledPaginationRequest(customer, orderBys, pagination);
                var response = appointmentStub.listAppointments(request);
                paginationResponse.setTotal(response.getPagination().getTotal());
                yield response.getAppointmentsList();
            }
            case PAST -> {
                var finishedRequest = buildFinishedRequest(customer);
                var delayedRequest = buildDelayedRequest(customer);
                var response = appointmentCompositeService.listPastAppointments(
                        finishedRequest, delayedRequest, orderBys, pagination);
                paginationResponse.setTotal(response.getPagination().getTotal());
                yield response.getAppointmentsList();
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid list type");
        };
    }

    private ListAppointmentsRequest.Filter.Builder buildGroomingAppointmentsFilter(BaseBusinessCustomerIdDTO customer) {
        return ListAppointmentsRequest.Filter.newBuilder()
                .setFilterBookingRequest(true)
                .addAllServiceTypeIncludes(ServiceItemEnum.getBitValueListByServiceItems(List.of(
                        ServiceItemEnum.GROOMING,
                        ServiceItemEnum.BOARDING,
                        ServiceItemEnum.DAYCARE,
                        ServiceItemEnum.EVALUATION)))
                .addCustomerIds(customer.getCustomerId())
                .addAllWaitListStatuses(List.of(WaitListStatus.APPTONLY, WaitListStatus.APPTANDWAITLIST))
                .setStartTimeRange(Interval.newBuilder()
                        .setStartTime(DateConverter.INSTANCE.toTimestamp(LocalDateTime.now()
                                .minusDays(MAX_VIEW_DAYS)
                                .toInstant(ZoneOffset.UTC)
                                .getEpochSecond()))
                        .build());
    }

    private ListAppointmentsRequest buildOngoingPaginationRequest(
            BaseBusinessCustomerIdDTO customer, List<OrderBy> orderBys, PaginationRequest pagination) {
        long todayStartSecond = getTodayStartSecond(customer);
        return ListAppointmentsRequest.newBuilder()
                .setCompanyId(customer.getCompanyId())
                .addAllOrderBys(orderBys)
                .setPagination(pagination)
                .setFilter(buildGroomingAppointmentsFilter(customer)
                        .addAllStatus(List.of(
                                AppointmentStatus.UNCONFIRMED,
                                AppointmentStatus.CONFIRMED,
                                AppointmentStatus.CHECKED_IN,
                                AppointmentStatus.READY))
                        .setEndTimeRange(Interval.newBuilder()
                                .setStartTime(Timestamp.newBuilder().setSeconds(todayStartSecond))))
                .setPriorityOrderType(ListAppointmentsRequest.PriorityOrderType.UNEXPIRED_UNCONFIRMED)
                .build();
    }

    private ListAppointmentsRequest buildFinishedPaginationRequest(
            BaseBusinessCustomerIdDTO customer, List<OrderBy> orderBys, PaginationRequest pagination) {
        return ListAppointmentsRequest.newBuilder()
                .setCompanyId(customer.getCompanyId())
                .addAllOrderBys(orderBys)
                .setPagination(pagination)
                .setFilter(buildGroomingAppointmentsFilter(customer).addStatus(AppointmentStatus.FINISHED))
                .build();
    }

    private ListAppointmentsRequest buildCanceledPaginationRequest(
            BaseBusinessCustomerIdDTO customer, List<OrderBy> orderBys, PaginationRequest pagination) {
        return ListAppointmentsRequest.newBuilder()
                .setCompanyId(customer.getCompanyId())
                .addAllOrderBys(orderBys)
                .setPagination(pagination)
                .setFilter(buildGroomingAppointmentsFilter(customer).addStatus(AppointmentStatus.CANCELED))
                .build();
    }

    private ListAppointmentsRequest buildFinishedRequest(BaseBusinessCustomerIdDTO customer) {
        return ListAppointmentsRequest.newBuilder()
                .setCompanyId(customer.getCompanyId())
                .setFilter(buildGroomingAppointmentsFilter(customer).addStatus(AppointmentStatus.FINISHED))
                .build();
    }

    private ListAppointmentsRequest buildDelayedRequest(BaseBusinessCustomerIdDTO customer) {
        long todayStartSecond = getTodayStartSecond(customer);
        return ListAppointmentsRequest.newBuilder()
                .setCompanyId(customer.getCompanyId())
                .setFilter(buildGroomingAppointmentsFilter(customer)
                        .addAllStatus(List.of(
                                AppointmentStatus.UNCONFIRMED,
                                AppointmentStatus.CONFIRMED,
                                AppointmentStatus.CHECKED_IN,
                                AppointmentStatus.READY))
                        .setEndTimeRange(Interval.newBuilder()
                                .setEndTime(Timestamp.newBuilder().setSeconds(todayStartSecond))))
                .build();
    }

    private long getTodayStartSecond(BaseBusinessCustomerIdDTO customer) {
        var zoneId = companyService.getZoneId(customer.getCompanyId());
        return LocalDate.now(zoneId).atStartOfDay(zoneId).toEpochSecond();
    }

    private ListAppointmentsResult buildAppointmentsResult(
            BaseBusinessCustomerIdDTO customer,
            List<OrderBy> orderBys,
            PaginationRequest pagination,
            ListAppointmentsParams.AppointmentListType listType) {
        var paginationResponse = PaginationResponse.newBuilder()
                .setPageNum(pagination.getPageNum())
                .setPageSize(pagination.getPageSize());

        var appointments = getAppointments(customer, orderBys, pagination, listType, paginationResponse);

        if (CollectionUtils.isEmpty(appointments)) {
            return ListAppointmentsResult.newBuilder()
                    .setPagination(paginationResponse)
                    .build();
        }

        var appointmentIds = appointments.stream().map(AppointmentModel::getId).toList();
        var businessIds = appointments.stream()
                .map(AppointmentModel::getBusinessId)
                .distinct()
                .toList();

        var petDetailsFuture = futureService.listPetDetails(customer.getCompanyId(), appointmentIds);
        var servicesFuture = futureService.listServices(petDetailsFuture);
        var petsFuture = futureService.listPets(petDetailsFuture);
        var evaluationServiceFuture = futureService.listEvaluations(petDetailsFuture);
        var ordersFuture = futureService.listOrders(appointmentIds, OrderSourceType.APPOINTMENT);
        var orderProcessingFeesFuture = futureService.listProcessingFees(ordersFuture);
        CompletableFuture<Map<Long, OrderDetailModel>> noShowOrdersFuture = null;
        CompletableFuture<Map<Long, ConvenienceFeeDTO>> noShowProcessingFeesFuture = null;
        if (Objects.equals(listType, ListAppointmentsParams.AppointmentListType.CANCELED)) {
            noShowOrdersFuture = futureService.listOrders(appointmentIds, OrderSourceType.NO_SHOW);
            noShowProcessingFeesFuture = futureService.listProcessingFees(noShowOrdersFuture);
        }
        var depositsFuture = futureService.listBookOnlineDeposits(appointmentIds);
        var arrivalWindowsFuture = futureService.listArrivalWindowSettings(businessIds);
        var appointmentTrackingFuture = futureService.listAppointmentTracking(appointmentIds);
        var appointmentPetFeedingMedicationFuture =
                futureService.listAppointmentPetScheduleView(customer.getCompanyId(), appointmentIds);

        var petDetailMap = petDetailsFuture.join().getPetDetailsList().stream()
                .collect(Collectors.groupingBy(PetDetailModel::getGroomingId));
        var petMap = petsFuture.join();
        var orderMap = ordersFuture.join();
        var processingFeeMap = filterProcessingFeePayByClient(orderProcessingFeesFuture.join());
        Map<Long, OrderDetailModel> noShowMap = noShowOrdersFuture != null ? noShowOrdersFuture.join() : Map.of();
        Map<Long, BigDecimal> noShowProcessingFeeMap = noShowProcessingFeesFuture != null
                ? filterProcessingFeePayByClient(noShowProcessingFeesFuture.join())
                : Map.of();
        var depositMap = depositsFuture.join();
        var appointmentTrackingMap = appointmentTrackingFuture.join();
        var serviceMap = servicesFuture.join();
        Map<Long /*appointment id */, List<EvaluationServiceModel>> appointmentEvaluationDetailMap =
                petDetailsFuture.join().getPetEvaluationsList().stream()
                        .collect(Collectors.groupingBy(EvaluationServiceModel::getAppointmentId));
        Map<Long /*evaluation id*/, EvaluationBriefView> evaluationMap = evaluationServiceFuture.join();
        var appointmentPetFeedingMedicationMap = appointmentPetFeedingMedicationFuture.join();

        var appointmentItems = appointments.stream()
                .map(appointment -> {
                    var builder = ListAppointmentsResult.AppointmentItem.newBuilder()
                            .setBusinessId(appointment.getBusinessId())
                            .setAppointmentId(appointment.getId())
                            .setStartDate(appointment.getAppointmentDate())
                            .setStartTime(appointment.getAppointmentStartTime())
                            .setEndDate(appointment.getAppointmentEndDate())
                            .setEndTime(appointment.getAppointmentEndTime())
                            .setStatus(AppointmentConverter.STATUS_MAP.get(appointment.getStatus()))
                            .setServiceTypeInclude(appointment.getServiceTypeInclude())
                            .addAllServicePets(petDetailMap.getOrDefault(appointment.getId(), List.of()).stream()
                                    .collect(Collectors.groupingBy(PetDetailModel::getPetId))
                                    .keySet()
                                    .stream()
                                    .map(petId -> {
                                        var pet = petMap.get(petId);
                                        if (pet == null) {
                                            throw ExceptionUtil.bizException(Code.CODE_PET_NOT_FOUND);
                                        }
                                        return ListAppointmentsResult.ServicePetItem.newBuilder()
                                                .setPetId(pet.getId())
                                                .setPetName(pet.getPetName())
                                                .setAvatarPath(pet.getAvatarPath())
                                                .setPetType(pet.getPetType())
                                                .build();
                                    })
                                    .toList())
                            .addAllServicePets(
                                    appointmentEvaluationDetailMap.getOrDefault(appointment.getId(), List.of()).stream()
                                            .collect(Collectors.groupingBy(EvaluationServiceModel::getPetId))
                                            .keySet()
                                            .stream()
                                            .map(petId -> {
                                                var pet = petMap.get(petId);
                                                if (pet == null) {
                                                    throw ExceptionUtil.bizException(Code.CODE_PET_NOT_FOUND);
                                                }
                                                return ListAppointmentsResult.ServicePetItem.newBuilder()
                                                        .setPetId(pet.getId())
                                                        .setPetName(pet.getPetName())
                                                        .setAvatarPath(pet.getAvatarPath())
                                                        .setPetType(pet.getPetType())
                                                        .build();
                                            })
                                            .toList())
                            .setTotalAmount(calculateTotalAmount(
                                    AppointmentConverter.STATUS_MAP.get(appointment.getStatus()),
                                    orderMap.get(appointment.getId()),
                                    depositMap.get(appointment.getId()),
                                    processingFeeMap,
                                    noShowMap.get(appointment.getId()),
                                    noShowProcessingFeeMap))
                            .addAllServices(petDetailMap.getOrDefault(appointment.getId(), List.of()).stream()
                                    .collect(Collectors.groupingBy(PetDetailModel::getServiceId))
                                    .keySet()
                                    .stream()
                                    .map(serviceMap::get)
                                    .filter(Objects::nonNull)
                                    .map(appointmentConverter::toView)
                                    .toList())
                            .addAllEvaluationServices(
                                    appointmentEvaluationDetailMap.getOrDefault(appointment.getId(), List.of()).stream()
                                            .collect(Collectors.groupingBy(EvaluationServiceModel::getServiceId))
                                            .keySet()
                                            .stream()
                                            .map(evaluationMap::get)
                                            .filter(Objects::nonNull)
                                            .map(appointmentConverter::toView)
                                            .toList());
                    if (appointmentTrackingMap.get(appointment.getId()) != null) {
                        builder.setAppointmentTracking(
                                appointmentConverter.toView(appointmentTrackingMap.get(appointment.getId())));
                    }
                    if (appointmentPetFeedingMedicationMap.get(appointment.getId()) != null) {
                        builder.addAllSchedules(appointmentPetFeedingMedicationMap
                                .getOrDefault(appointment.getId(), AppointmentPetScheduleDef.getDefaultInstance())
                                .getSchedulesList());
                    }
                    return builder.build();
                })
                .toList();
        return ListAppointmentsResult.newBuilder()
                .addAllAppointments(appointmentItems)
                .addAllArrivalWindows(arrivalWindowsFuture.join().values().stream()
                        .map(messageMapper::dtoToView)
                        .toList())
                .setPagination(paginationResponse)
                .build();
    }

    private ListAppointmentsResult buildBookingRequestsResult(
            BaseBusinessCustomerIdDTO customer, List<OrderBy> orderBys, PaginationRequest pagination) {
        var response = bookingRequestService.listBookingRequests(ListBookingRequestsRequest.newBuilder()
                .addAssociatedModels(BookingRequestAssociatedModel.SERVICE)
                .addAssociatedModels(BookingRequestAssociatedModel.ADD_ON)
                .setCompanyId(customer.getCompanyId())
                .addCustomerId(customer.getCustomerId())
                .addAllServiceTypeIncludes(ServiceItemEnum.getBitValueListByServiceItems(List.of(
                        ServiceItemEnum.GROOMING,
                        ServiceItemEnum.BOARDING,
                        ServiceItemEnum.DAYCARE,
                        ServiceItemEnum.EVALUATION)))
                .addAllStatuses(List.of(BookingRequestStatus.SUBMITTED, BookingRequestStatus.WAIT_LIST))
                .addSources(BookingRequestModel.Source.OB)
                .addSources(BookingRequestModel.Source.MEMBERSHIP)
                .setStartDate(DateConverter.INSTANCE.toDate(LocalDate.now().minusDays(MAX_VIEW_DAYS)))
                .setPagination(pagination)
                .addAllOrderBys(orderBys)
                .addAllPaymentStatuses(List.of(
                        BookingRequestModel.PaymentStatus.NO_PAYMENT,
                        BookingRequestModel.PaymentStatus.PROCESSING,
                        BookingRequestModel.PaymentStatus.SUCCESS))
                .addAllAssociatedModels(List.of(
                        BookingRequestAssociatedModel.SERVICE,
                        BookingRequestAssociatedModel.ADD_ON,
                        BookingRequestAssociatedModel.FEEDING,
                        BookingRequestAssociatedModel.MEDICATION,
                        BookingRequestAssociatedModel.AUTO_ASSIGN))
                .build());
        if (response.getBookingRequestsCount() == 0) {
            return ListAppointmentsResult.newBuilder()
                    .setPagination(response.getPagination())
                    .build();
        }

        var bookingRequests = response.getBookingRequestsList();
        var onlyGroomingRequests = bookingRequests.stream()
                .filter(bookingRequest ->
                        ServiceItemEnum.GROOMING.getBitValue().equals(bookingRequest.getServiceTypeInclude()))
                .toList();
        var excludeOnlyGroomingRequests = bookingRequests.stream()
                .filter(bookingRequest ->
                        !ServiceItemEnum.GROOMING.getBitValue().equals(bookingRequest.getServiceTypeInclude()))
                .toList();

        var onlyGroomingRequestIDToAppointmentItemFuture = CompletableFuture.supplyAsync(
                () -> buildOnlyGroomingRequestItem(customer.getCompanyId(), onlyGroomingRequests),
                ThreadPool.getSubmitExecutor());
        var excludeOnlyGroomingRequestIDToAppointmentItemFuture = CompletableFuture.supplyAsync(
                () -> buildExcludeOnlyGroomingRequestItem(excludeOnlyGroomingRequests), ThreadPool.getSubmitExecutor());

        var onlyGroomingRequestIDToAppointmentItem = onlyGroomingRequestIDToAppointmentItemFuture.join();
        var excludeOnlyGroomingRequestIDToAppointmentItem = excludeOnlyGroomingRequestIDToAppointmentItemFuture.join();

        var businessIds = bookingRequests.stream()
                .map(BookingRequestModel::getBusinessId)
                .distinct()
                .toList();
        var arrivalWindowsFuture = futureService.listArrivalWindowSettings(businessIds);
        List<ListAppointmentsResult.AppointmentItem> appointmentItems = bookingRequests.stream()
                .map(bookingRequest -> {
                    if (ServiceItemEnum.GROOMING.getBitValue().equals(bookingRequest.getServiceTypeInclude())) {
                        return onlyGroomingRequestIDToAppointmentItem.get(bookingRequest.getId());
                    }
                    return excludeOnlyGroomingRequestIDToAppointmentItem.get(bookingRequest.getId());
                })
                .filter(Objects::nonNull)
                .toList();

        return ListAppointmentsResult.newBuilder()
                .addAllAppointments(appointmentItems)
                .setPagination(response.getPagination())
                .addAllArrivalWindows(arrivalWindowsFuture.join().values().stream()
                        .map(messageMapper::dtoToView)
                        .toList())
                .build();
    }

    private Map<Long, ListAppointmentsResult.AppointmentItem> buildOnlyGroomingRequestItem(
            long companyId, List<BookingRequestModel> bookingRequests) {
        if (CollectionUtils.isEmpty(bookingRequests)) {
            return Map.of();
        }
        // Service pets
        var appointmentPetsMap = bookingRequests.stream()
                .collect(Collectors.toMap(
                        BookingRequestModel::getAppointmentId,
                        bookingRequest -> bookingRequest.getServicesList().stream()
                                .filter(BookingRequestModel.Service::hasGrooming)
                                .flatMap(service -> Stream.concat(
                                        Stream.of(service.getGrooming()
                                                .getService()
                                                .getPetId()),
                                        service.getGrooming().getAddonsList().stream()
                                                .map(GroomingAddOnDetailModel::getPetId)))
                                .collect(Collectors.toSet())));
        var appointmentIds = bookingRequests.stream()
                .map(BookingRequestModel::getAppointmentId)
                .toList();
        var petIds = appointmentPetsMap.values().stream()
                .flatMap(Set::stream)
                .distinct()
                .toList();
        var petsFuture = futureService.listPets(petIds);
        var petDetailsFuture = futureService.listPetDetails(companyId, appointmentIds);
        var servicesFuture = futureService.listServices(petDetailsFuture);
        Map<Long, List<EvaluationServiceModel>> appointmentEvaluationDetailMap =
                petDetailsFuture.join().getPetEvaluationsList().stream()
                        .collect(Collectors.groupingBy(EvaluationServiceModel::getAppointmentId));
        var evaluationsFuture = futureService.listEvaluations(petDetailsFuture);
        var ordersFuture = futureService.listOrders(appointmentIds, OrderSourceType.APPOINTMENT);
        var orderProcessingFeesFuture = futureService.listProcessingFees(ordersFuture);
        var depositsFuture = futureService.listBookOnlineDeposits(appointmentIds);

        var petMap = petsFuture.join();
        var orderMap = ordersFuture.join();
        var processingFeeMap = filterProcessingFeePayByClient(orderProcessingFeesFuture.join());
        var depositMap = depositsFuture.join();
        var petDetailMap = petDetailsFuture.join().getPetDetailsList().stream()
                .collect(Collectors.groupingBy(PetDetailModel::getGroomingId));
        var serviceMap = servicesFuture.join();
        var evaluationServiceMap = evaluationsFuture.join();
        return bookingRequests.stream()
                .collect(Collectors.toMap(
                        BookingRequestModel::getId,
                        bookingRequest -> ListAppointmentsResult.AppointmentItem.newBuilder()
                                .setBusinessId(bookingRequest.getBusinessId())
                                .setAppointmentId(bookingRequest.getAppointmentId())
                                .setStartDate(bookingRequest.getStartDate())
                                .setStartTime(bookingRequest.getStartTime())
                                .setEndDate(bookingRequest.getEndDate())
                                .setEndTime(bookingRequest.getEndTime())
                                .setStatus(ClientAppointmentStatus.PENDING)
                                .addAllServicePets(appointmentPetsMap.get(bookingRequest.getAppointmentId()).stream()
                                        .map(petId -> {
                                            BusinessCustomerPetInfoModel pet = petMap.get(petId);
                                            if (pet == null) {
                                                throw ExceptionUtil.bizException(Code.CODE_PET_NOT_FOUND);
                                            }
                                            return ListAppointmentsResult.ServicePetItem.newBuilder()
                                                    .setPetId(petId)
                                                    .setPetName(pet.getPetName())
                                                    .setAvatarPath(pet.getAvatarPath())
                                                    .setPetType(pet.getPetType())
                                                    .build();
                                        })
                                        .toList())
                                .setTotalAmount(calculateTotalAmount(
                                        ClientAppointmentStatus.PENDING,
                                        orderMap.get(bookingRequest.getAppointmentId()),
                                        depositMap.get(bookingRequest.getAppointmentId()),
                                        processingFeeMap,
                                        null,
                                        null))
                                .addAllServices(
                                        petDetailMap.getOrDefault(bookingRequest.getAppointmentId(), List.of()).stream()
                                                .collect(Collectors.groupingBy(PetDetailModel::getServiceId))
                                                .keySet()
                                                .stream()
                                                .map(serviceMap::get)
                                                .filter(Objects::nonNull)
                                                .map(appointmentConverter::toView)
                                                .toList())
                                .addAllEvaluationServices(appointmentEvaluationDetailMap
                                        .getOrDefault(bookingRequest.getAppointmentId(), List.of())
                                        .stream()
                                        .collect(Collectors.groupingBy(EvaluationServiceModel::getId))
                                        .keySet()
                                        .stream()
                                        .map(evaluationServiceMap::get)
                                        .filter(Objects::nonNull)
                                        .map(appointmentConverter::toView)
                                        .toList())
                                .build()));
    }

    private Map<Long, ListAppointmentsResult.AppointmentItem> buildExcludeOnlyGroomingRequestItem(
            List<BookingRequestModel> bookingRequests) {
        if (CollectionUtils.isEmpty(bookingRequests)) {
            return Map.of();
        }

        List<Long> petIds = bookingRequests.stream()
                .flatMap(bookingRequest -> bookingRequest.getServicesList().stream())
                .flatMap(service -> Stream.of(
                        service.hasGrooming()
                                ? service.getGrooming().getService().getPetId()
                                : null,
                        service.hasBoarding()
                                ? service.getBoarding().getService().getPetId()
                                : null,
                        service.hasDaycare() ? service.getDaycare().getService().getPetId() : null,
                        service.hasEvaluation()
                                ? service.getEvaluation().getService().getPetId()
                                : null))
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        var petsFuture = futureService.listPets(petIds);

        List<Long> bookingRequestIds =
                bookingRequests.stream().map(BookingRequestModel::getId).toList();
        List<Long> serviceIds = bookingRequests.stream()
                .flatMap(bookingRequest -> bookingRequest.getServicesList().stream())
                .flatMap(service -> Stream.of(
                        service.hasGrooming()
                                ? service.getGrooming().getService().getServiceId()
                                : null,
                        service.hasBoarding()
                                ? service.getBoarding().getService().getServiceId()
                                : null,
                        service.hasDaycare() ? service.getDaycare().getService().getServiceId() : null))
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        List<Long> evaluationBookingDetailIds = bookingRequests.stream()
                .flatMap(bookingRequest -> bookingRequest.getServicesList().stream())
                .flatMap(service -> Stream.of(
                        service.hasEvaluation()
                                ? service.getEvaluation().getService().getEvaluationId()
                                : null))
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        var evaluationFuture = futureService.listEvaluations(evaluationBookingDetailIds);
        var servicesFuture = futureService.listServices(serviceIds);
        var ordersFuture = futureService.listOrders(
                bookingRequestIds, BOOKING_REQUEST.name().toLowerCase());
        var orderProcessingFeesFuture = futureService.listProcessingFees(ordersFuture);
        var depositsFuture = futureService.listBookOnlineDepositsByRequestIds(bookingRequestIds);

        var petMap = petsFuture.join();
        var orderMap = ordersFuture.join();
        var processingFeeMap = filterProcessingFeePayByClient(orderProcessingFeesFuture.join());
        var depositMap = depositsFuture.join();
        var serviceMap = servicesFuture.join();
        var evaluationMap = evaluationFuture.join();
        return bookingRequests.stream()
                .collect(Collectors.toMap(
                        BookingRequestModel::getId,
                        bookingRequest -> ListAppointmentsResult.AppointmentItem.newBuilder()
                                .setBusinessId(bookingRequest.getBusinessId())
                                .setBookingRequestId(bookingRequest.getId())
                                .setStartDate(bookingRequest.getStartDate())
                                .setStartTime(bookingRequest.getStartTime())
                                .setEndDate(bookingRequest.getEndDate())
                                .setEndTime(bookingRequest.getEndTime())
                                .setStatus(ClientAppointmentStatus.PENDING)
                                .addAllServicePets(bookingRequest.getServicesList().stream()
                                        .flatMap(service -> Stream.of(
                                                service.hasGrooming()
                                                        ? service.getGrooming()
                                                                .getService()
                                                                .getPetId()
                                                        : null,
                                                service.hasBoarding()
                                                        ? service.getBoarding()
                                                                .getService()
                                                                .getPetId()
                                                        : null,
                                                service.hasDaycare()
                                                        ? service.getDaycare()
                                                                .getService()
                                                                .getPetId()
                                                        : null,
                                                service.hasEvaluation()
                                                        ? service.getEvaluation()
                                                                .getService()
                                                                .getPetId()
                                                        : null))
                                        .filter(Objects::nonNull)
                                        .map(petId -> {
                                            BusinessCustomerPetInfoModel pet = petMap.get(petId);
                                            if (pet == null) {
                                                throw ExceptionUtil.bizException(Code.CODE_PET_NOT_FOUND);
                                            }
                                            return ListAppointmentsResult.ServicePetItem.newBuilder()
                                                    .setPetId(petId)
                                                    .setPetName(pet.getPetName())
                                                    .setAvatarPath(pet.getAvatarPath())
                                                    .setPetType(pet.getPetType())
                                                    .build();
                                        })
                                        .toList())
                                .setTotalAmount(calculateTotalAmount(
                                        ClientAppointmentStatus.PENDING,
                                        orderMap.get(bookingRequest.getId()),
                                        depositMap.get(bookingRequest.getId()),
                                        processingFeeMap,
                                        null,
                                        null))
                                .addAllServices(bookingRequest.getServicesList().stream()
                                        .flatMap(service -> Stream.of(
                                                service.hasGrooming()
                                                        ? service.getGrooming()
                                                                .getService()
                                                                .getServiceId()
                                                        : null,
                                                service.hasBoarding()
                                                        ? service.getBoarding()
                                                                .getService()
                                                                .getServiceId()
                                                        : null,
                                                service.hasDaycare()
                                                        ? service.getDaycare()
                                                                .getService()
                                                                .getServiceId()
                                                        : null)) // evaluation 没有 service id
                                        .filter(Objects::nonNull)
                                        .map(serviceId -> appointmentConverter.toView(serviceMap.get(serviceId)))
                                        .filter(Objects::nonNull)
                                        .toList())
                                .addAllEvaluationServices(bookingRequest.getServicesList().stream()
                                        .filter(BookingRequestModel.Service::hasEvaluation)
                                        .map(service ->
                                                appointmentConverter.toView(evaluationMap.get(service.getEvaluation()
                                                        .getService()
                                                        .getEvaluationId())))
                                        .filter(Objects::nonNull)
                                        .toList())
                                .addAllSchedules(getPetScheduleDefs(bookingRequest))
                                .build()));
    }

    /**
     * PENDING: total amount = deposit amount + booking fee
     * CANCELLED: no show fee + processing fee
     * OTHER: total amount = order total amount + (booking fee + processing fee)
     * order total amount = service total + service charge amount + tax amount + tip amount
     *
     * @param orderDetail order detail
     * @param deposit     booking deposit
     * @return total amount
     */
    private double calculateTotalAmount(
            ClientAppointmentStatus status,
            OrderDetailModel orderDetail,
            BookOnlineDepositDTO deposit,
            Map<Long, BigDecimal> processingFeeMap,
            OrderDetailModel noShowOrderDetail,
            Map<Long, BigDecimal> noShowProcessingFeeMap) {
        if (Objects.equals(status, ClientAppointmentStatus.PENDING) && deposit != null) {
            // Pre-auth 情况下 amount 已经包含了 booking fee。pre pay 需要手动加一笔 booking fee :(
            var total = getTotalAmount(
                    deposit, processingFeeMap.get(orderDetail.getOrder().getId()), orderDetail.getOrder());
            return total.doubleValue();
        }
        if (Objects.equals(status, ClientAppointmentStatus.CANCELLED) && noShowOrderDetail != null) {
            var order = noShowOrderDetail.getOrder();
            var convenienceFee = noShowProcessingFeeMap.get(order.getId());
            return BigDecimal.valueOf(order.getTotalAmount())
                    .add(convenienceFee)
                    .doubleValue();
        }
        double totalAmount = Optional.ofNullable(orderDetail)
                .filter(OrderDetailModel::hasOrder)
                .map(OrderDetailModel::getOrder)
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_INVOICE_NOT_FOUND))
                .getTotalAmount();
        var bookingFee = Optional.ofNullable(deposit)
                .map(BookOnlineDepositDTO::getBookingFee)
                .orElse(BigDecimal.ZERO);
        var convenienceFee = processingFeeMap.get(orderDetail.getOrder().getId());
        return BigDecimal.valueOf(totalAmount)
                .add(bookingFee)
                .add(convenienceFee)
                .doubleValue();
    }

    private BigDecimal getTotalAmount(BookOnlineDepositDTO deposit, BigDecimal processingFee, OrderModel order) {
        var total = Optional.ofNullable(deposit.getAmount()).orElse(BigDecimal.ZERO);
        var discount = Optional.ofNullable(deposit.getDiscountAmount()).orElse(BigDecimal.ZERO);
        var bookingFee = Optional.ofNullable(deposit.getBookingFee()).orElse(BigDecimal.ZERO);
        if (Objects.equals(deposit.getDepositType(), DepositPaymentTypeEnum.PrePay)) {
            var taxAndFees = BigDecimal.valueOf(order.getTaxAmount())
                    // unpaid processing fee
                    .add(processingFee)
                    .add(bookingFee);
            total = BigDecimal.valueOf(order.getSubTotalAmount())
                    .add(taxAndFees)
                    .add(Optional.ofNullable(deposit.getTipsAmount()).orElse(BigDecimal.ZERO))
                    .subtract(discount);
        } else if (Objects.equals(deposit.getDepositType(), DepositPaymentTypeEnum.PreAuth)) {
            total = total.subtract(discount).add(processingFee);
        }
        return total;
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getLastFinishedAppointment(
            GetLastFinishedAppointmentParams request,
            StreamObserver<GetLastFinishedAppointmentResult> responseObserver) {
        var customer = customerService.getLinkCustomer(
                request.getCompanyId(), AuthContext.get().accountId());

        var response = appointmentStub.listAppointmentsForCustomers(ListAppointmentsForCustomersRequest.newBuilder()
                .setCompanyId(customer.getCompanyId())
                .addCustomerIds(customer.getCustomerId())
                .setFilter(ListAppointmentsForCustomersRequest.Filter.newBuilder()
                        .setDateType(AppointmentDateType.LAST)
                        .addAllServiceTypeIncludes(ServiceItemEnum.getBitValueListByServiceItems(List.of(
                                ServiceItemEnum.GROOMING,
                                ServiceItemEnum.BOARDING,
                                ServiceItemEnum.DAYCARE,
                                ServiceItemEnum.EVALUATION)))
                        .addStatuses(AppointmentStatus.FINISHED)
                        .build())
                .build());

        var items = buildAppointmentDetailItems(request.getCompanyId(), response.getAppointmentsList());
        if (CollectionUtils.isEmpty(items)) {
            responseObserver.onNext(GetLastFinishedAppointmentResult.getDefaultInstance());
        } else {
            responseObserver.onNext(GetLastFinishedAppointmentResult.newBuilder()
                    .setAppointment(CollectionUtils.firstElement(items))
                    .build());
        }

        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void listTodayAppointments(
            ListTodayAppointmentsParams request, StreamObserver<ListTodayAppointmentsResult> responseObserver) {
        var customer = customerService.getLinkCustomer(
                request.getCompanyId(), AuthContext.get().accountId());

        var todayAppointments = listTodayAppointments(customer);
        var todayRequests = listTodayBookingRequests(customer);

        var appointmentDetails = buildAppointmentDetailItems(request.getCompanyId(), todayAppointments);
        var bookingRequestDetails = buildDetailItemsFromBookingRequests(request.getCompanyId(), todayRequests);

        responseObserver.onNext(ListTodayAppointmentsResult.newBuilder()
                .addAllAppointments(appointmentDetails)
                .addAllAppointments(bookingRequestDetails)
                .build());
        responseObserver.onCompleted();
    }

    private List<AppointmentModel> listTodayAppointments(BaseBusinessCustomerIdDTO customer) {
        return appointmentStub
                .listAppointmentsForCustomers(ListAppointmentsForCustomersRequest.newBuilder()
                        .setCompanyId(customer.getCompanyId())
                        .addCustomerIds(customer.getCustomerId())
                        .setFilter(ListAppointmentsForCustomersRequest.Filter.newBuilder()
                                .setDateType(AppointmentDateType.TODAY)
                                .addAllStatuses(List.of(
                                        AppointmentStatus.UNCONFIRMED,
                                        AppointmentStatus.CONFIRMED,
                                        AppointmentStatus.FINISHED,
                                        AppointmentStatus.READY,
                                        AppointmentStatus.CHECKED_IN))
                                .addAllServiceTypeIncludes(ServiceItemEnum.getBitValueListByServiceItems(List.of(
                                        ServiceItemEnum.GROOMING,
                                        ServiceItemEnum.BOARDING,
                                        ServiceItemEnum.DAYCARE,
                                        ServiceItemEnum.EVALUATION)))
                                .build())
                        .build())
                .getAppointmentsList();
    }

    private List<BookingRequestModel> listTodayBookingRequests(BaseBusinessCustomerIdDTO customer) {
        return bookingRequestService
                .listBookingRequests(ListBookingRequestsRequest.newBuilder()
                        .setCompanyId(customer.getCompanyId())
                        .addCustomerId(customer.getCustomerId())
                        .addStatuses(BookingRequestStatus.SUBMITTED)
                        .setStartDate(DateConverter.INSTANCE.toDate(LocalDate.now()))
                        .setEndDate(DateConverter.INSTANCE.toDate(LocalDate.now()))
                        .addAllPaymentStatuses(List.of(
                                BookingRequestModel.PaymentStatus.NO_PAYMENT,
                                BookingRequestModel.PaymentStatus.PROCESSING,
                                BookingRequestModel.PaymentStatus.SUCCESS))
                        .addAllServiceTypeIncludes(ServiceItemEnum.getBitValueListByServiceItems(List.of(
                                ServiceItemEnum.GROOMING,
                                ServiceItemEnum.BOARDING,
                                ServiceItemEnum.DAYCARE,
                                ServiceItemEnum.EVALUATION)))
                        .addAllAssociatedModels(List.of(
                                BookingRequestAssociatedModel.SERVICE,
                                BookingRequestAssociatedModel.ADD_ON,
                                BookingRequestAssociatedModel.FEEDING,
                                BookingRequestAssociatedModel.MEDICATION,
                                BookingRequestAssociatedModel.AUTO_ASSIGN))
                        .build())
                .getBookingRequestsList();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void listUpcomingDayAppointments(
            ListUpcomingDayAppointmentsParams request,
            StreamObserver<ListUpcomingDayAppointmentsResult> responseObserver) {
        var customer = customerService.getLinkCustomer(
                request.getCompanyId(), AuthContext.get().accountId());

        var response = appointmentStub.listAppointmentsForCustomers(ListAppointmentsForCustomersRequest.newBuilder()
                .setCompanyId(customer.getCompanyId())
                .addCustomerIds(customer.getCustomerId())
                .setFilter(ListAppointmentsForCustomersRequest.Filter.newBuilder()
                        .addAllServiceTypeIncludes(ServiceItemEnum.getBitValueListByServiceItems(List.of(
                                ServiceItemEnum.GROOMING,
                                ServiceItemEnum.BOARDING,
                                ServiceItemEnum.DAYCARE,
                                ServiceItemEnum.EVALUATION)))
                        .setDateType(AppointmentDateType.NEXT)
                        .addAllStatuses(List.of(AppointmentStatus.UNCONFIRMED, AppointmentStatus.CONFIRMED))
                        .build())
                .build());

        var items = buildAppointmentDetailItems(request.getCompanyId(), response.getAppointmentsList());

        responseObserver.onNext(ListUpcomingDayAppointmentsResult.newBuilder()
                .addAllAppointments(items)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void listPendingDayAppointments(
            ListPendingDayAppointmentsParams request,
            StreamObserver<ListPendingDayAppointmentsResult> responseObserver) {
        var customer = customerService.getLinkCustomer(
                request.getCompanyId(), AuthContext.get().accountId());

        var zoneId = companyService.getZoneId(customer.getCompanyId());
        LocalDate today = LocalDate.now(zoneId);

        var upcomingBookingRequests = bookingRequestService
                .listBookingRequests(ListBookingRequestsRequest.newBuilder()
                        .setCompanyId(customer.getCompanyId())
                        .addCustomerId(customer.getCustomerId())
                        .addStatuses(BookingRequestStatus.SUBMITTED)
                        .addAllServiceTypeIncludes(ServiceItemEnum.getBitValueListByServiceItems(List.of(
                                ServiceItemEnum.GROOMING,
                                ServiceItemEnum.BOARDING,
                                ServiceItemEnum.DAYCARE,
                                ServiceItemEnum.EVALUATION)))
                        .setStartDate(DateConverter.INSTANCE.toDate(today.plusDays(1)))
                        .addOrderBys(OrderBy.newBuilder()
                                .setFieldName("startDate")
                                .setAsc(true)
                                .build())
                        .addAllPaymentStatuses(List.of(
                                BookingRequestModel.PaymentStatus.NO_PAYMENT,
                                BookingRequestModel.PaymentStatus.PROCESSING,
                                BookingRequestModel.PaymentStatus.SUCCESS))
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1)
                                .build())
                        .build())
                .getBookingRequestsList();
        if (CollectionUtils.isEmpty(upcomingBookingRequests)) {
            responseObserver.onNext(ListPendingDayAppointmentsResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        BookingRequestModel upcomingBookingRequest = upcomingBookingRequests.get(0);
        var bookingRequests = bookingRequestService
                .listBookingRequests(ListBookingRequestsRequest.newBuilder()
                        .setCompanyId(customer.getCompanyId())
                        .addCustomerId(customer.getCustomerId())
                        .addStatuses(BookingRequestStatus.SUBMITTED)
                        .setStartDate(DateConverter.INSTANCE.toDate(upcomingBookingRequest.getStartDate()))
                        .setEndDate(DateConverter.INSTANCE.toDate(upcomingBookingRequest.getStartDate()))
                        .addAllServiceTypeIncludes(ServiceItemEnum.getBitValueListByServiceItems(List.of(
                                ServiceItemEnum.GROOMING,
                                ServiceItemEnum.BOARDING,
                                ServiceItemEnum.DAYCARE,
                                ServiceItemEnum.EVALUATION)))
                        .addAllPaymentStatuses(List.of(
                                BookingRequestModel.PaymentStatus.NO_PAYMENT,
                                BookingRequestModel.PaymentStatus.PROCESSING,
                                BookingRequestModel.PaymentStatus.SUCCESS))
                        .addOrderBys(OrderBy.newBuilder()
                                .setFieldName("startDate")
                                .setAsc(true)
                                .build())
                        .addAllAssociatedModels(List.of(
                                BookingRequestAssociatedModel.SERVICE,
                                BookingRequestAssociatedModel.ADD_ON,
                                BookingRequestAssociatedModel.FEEDING,
                                BookingRequestAssociatedModel.MEDICATION,
                                BookingRequestAssociatedModel.AUTO_ASSIGN))
                        .build())
                .getBookingRequestsList();

        var items = buildDetailItemsFromBookingRequests(request.getCompanyId(), bookingRequests);

        responseObserver.onNext(ListPendingDayAppointmentsResult.newBuilder()
                .addAllAppointments(items)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getAppointment(GetAppointmentParams request, StreamObserver<GetAppointmentResult> responseObserver) {
        List<AppointmentDetailItem> items;
        if (request.getAppointmentId() == 0 && !request.hasBookingRequestId()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid params");
        }
        if (request.hasBookingRequestId()) {
            var bookingRequest = accountAppointmentService.getAccountBelongsBookingRequest(
                    AuthContext.get().accountId(), request.getBookingRequestId());
            items = buildDetailItemsFromBookingRequests(bookingRequest.getCompanyId(), List.of(bookingRequest));
        } else {
            var appointment = accountAppointmentService.getAccountBelongsAppointment(
                    AuthContext.get().accountId(), request.getAppointmentId());
            items = buildAppointmentDetailItems(appointment.getCompanyId(), List.of(appointment));
        }

        AppointmentDetailItem item = CollectionUtils.firstElement(items);
        responseObserver.onNext(
                GetAppointmentResult.newBuilder().setAppointment(item).build());
        responseObserver.onCompleted();
    }

    private List<AppointmentDetailItem> buildAppointmentDetailItems(
            long companyId, List<AppointmentModel> appointments) {
        var appointmentIds = appointments.stream().map(AppointmentModel::getId).toList();

        // Deposit, order, pet detail, agreement
        var bookingRequestsFuture = futureService.listBookingRequests(companyId, appointmentIds);
        var depositsFuture = futureService.listBookOnlineDeposits(appointmentIds);
        var ordersFuture = futureService.listOrders(appointmentIds, OrderSourceType.APPOINTMENT);
        var noShowOrdersFuture = futureService.listOrders(appointmentIds, OrderSourceType.NO_SHOW);
        var processingFeesFuture = futureService.listProcessingFees(ordersFuture);
        var noShowProcessingFeesFuture = futureService.listProcessingFees(noShowOrdersFuture);
        var petDetailsFuture = futureService.listPetDetails(companyId, appointmentIds);
        var agreementsFuture = futureService.listAgreements(companyId, appointmentIds);

        // Discount code, apply package, payment and refund info
        var discountCodesFuture = futureService.listDiscountCodes(companyId, ordersFuture);
        var applyPackagesFuture = futureService.listApplyPackages(ordersFuture);
        var paymentsFuture = futureService.listPayments(ordersFuture, noShowOrdersFuture);
        var preAuthRecordsFuture = futureService.listPreAuthRecords(companyId, appointmentIds);

        // Service info, staff info, pet info
        var servicesFuture = futureService.listServices(petDetailsFuture);
        var staffsFuture = futureService.listStaffs(petDetailsFuture);
        var petsFuture = futureService.listPets(petDetailsFuture);
        var evaluationServiceFuture = futureService.listEvaluations(petDetailsFuture);

        // Business info, book online config, twilio number, arrival window
        var businessIds = appointments.stream()
                .map(AppointmentModel::getBusinessId)
                .distinct()
                .toList();
        var businessInfoFuture = futureService.listBusinessesInfo(businessIds);
        var obConfigFuture = futureService.listBookOnlineConfig(businessIds);
        var twilioFuture = futureService.listBusinessTwilioNumbers(businessIds);
        var arrivalWindowFuture = futureService.listArrivalWindowSettings(businessIds);

        // Customer info, primary address
        var customerIds = appointments.stream()
                .map(AppointmentModel::getCustomerId)
                .distinct()
                .toList();
        var customerFuture = futureService.listBusinessCustomers(customerIds);
        var primaryAddressFuture = futureService.listPrimaryAddresses(customerIds);

        var preferenceSettingFuture = futureService.getCompanyPreferenceSetting(companyId);
        var brandedAppConfigFuture = futureService.getBrandedAppConfig(companyId);
        var appointmentsTrackingFuture = futureService.listAppointmentTracking(appointmentIds);
        var appointmentPetFeedingMedicationFuture =
                futureService.listAppointmentPetScheduleView(companyId, appointmentIds);
        CompletableFuture.allOf(
                        bookingRequestsFuture,
                        depositsFuture,
                        ordersFuture,
                        noShowOrdersFuture,
                        processingFeesFuture,
                        noShowProcessingFeesFuture,
                        petDetailsFuture,
                        agreementsFuture,
                        discountCodesFuture,
                        applyPackagesFuture,
                        paymentsFuture,
                        preAuthRecordsFuture,
                        servicesFuture,
                        staffsFuture,
                        petsFuture,
                        businessInfoFuture,
                        obConfigFuture,
                        twilioFuture,
                        arrivalWindowFuture,
                        customerFuture,
                        primaryAddressFuture,
                        preferenceSettingFuture,
                        brandedAppConfigFuture,
                        appointmentsTrackingFuture,
                        appointmentPetFeedingMedicationFuture,
                        evaluationServiceFuture)
                .join();
        var bookingRequestMap = bookingRequestsFuture.join();
        var depositMap = depositsFuture.join();
        var orderMap = ordersFuture.join();
        var noShowOrderMap = noShowOrdersFuture.join();
        var processingFeeMap = filterProcessingFeePayByClient(processingFeesFuture.join());
        var noShowProcessingFeeMap = filterProcessingFeePayByClient(noShowProcessingFeesFuture.join());
        var petDetailMap = petDetailsFuture.join().getPetDetailsList().stream()
                .collect(Collectors.groupingBy(PetDetailModel::getGroomingId));
        var evaluationDeatilMap = petDetailsFuture.join().getPetEvaluationsList().stream()
                .collect(Collectors.groupingBy(EvaluationServiceModel::getAppointmentId));
        var agreementMap = agreementsFuture.join();
        var discountCodeMap = discountCodesFuture.join();
        var applyPackageMap = applyPackagesFuture.join();
        var paymentMap = paymentsFuture.join();
        var preAuthRecordMap = preAuthRecordsFuture.join();
        var serviceMap = servicesFuture.join();
        var staffMap = staffsFuture.join();
        var petMap = petsFuture.join();
        var businessInfoMap = businessInfoFuture.join();
        var obConfigMap = obConfigFuture.join();
        var twilioMap = twilioFuture.join();
        var arrivalWindowMap = arrivalWindowFuture.join();
        var customerMap = customerFuture.join();
        var primaryAddressMap = primaryAddressFuture.join();
        var appointmentTrackingMap = appointmentsTrackingFuture.join();
        var appointmentPetFeedingMedicationMap = appointmentPetFeedingMedicationFuture.join();
        var evaluationServiceMap = evaluationServiceFuture.join();
        return appointments.stream()
                .map(appointment -> {
                    // Appointment related
                    AppointmentDetailItem.Builder builder = AppointmentDetailItem.newBuilder();
                    AppointmentDetailView view = appointmentConverter.toView(appointment);
                    Optional.ofNullable(bookingRequestMap.get(appointment.getId()))
                            .map(BookingRequestModel::getId)
                            .ifPresentOrElse(
                                    id -> builder.setAppointment(view.toBuilder()
                                            .setBookingRequestId(id)
                                            .build()),
                                    () -> builder.setAppointment(view));
                    builder.addAllSignedAgreements(agreementMap.getOrDefault(appointment.getId(), List.of()));
                    Set<Long> serviceIds = new HashSet<>();
                    Set<Long> staffIds = new HashSet<>();
                    Set<Long> petIds = new HashSet<>();
                    petDetailMap.getOrDefault(appointment.getId(), List.of()).forEach(petDetail -> {
                        builder.addAppointmentItems(appointmentConverter.toView(petDetail));
                        Optional.ofNullable(serviceMap.get(petDetail.getServiceId()))
                                .ifPresent(service -> {
                                    if (!serviceIds.contains(service.getId())) {
                                        serviceIds.add(service.getId());
                                        builder.addServices(appointmentConverter.toView(service));
                                    }
                                });
                        Optional.ofNullable(staffMap.get(petDetail.getStaffId()))
                                .ifPresent(staff -> {
                                    if (!staffIds.contains(staff.getId())) {
                                        staffIds.add(staff.getId());
                                        builder.addStaffs(appointmentConverter.toView(staff));
                                    }
                                });
                        Optional.ofNullable(petMap.get(petDetail.getPetId())).ifPresent(pet -> {
                            if (!petIds.contains(pet.getId())) {
                                petIds.add(pet.getId());
                                builder.addBusinessPets(appointmentConverter.toView(pet));
                            }
                        });
                    });
                    evaluationDeatilMap
                            .getOrDefault(appointment.getId(), List.of())
                            .forEach(evaluation -> {
                                builder.addAppointmentEvaluationItems(appointmentConverter.toView(evaluation));
                                Optional.ofNullable(evaluationServiceMap.get(evaluation.getServiceId()))
                                        .ifPresent(service -> {
                                            if (!serviceIds.contains(service.getId())) {
                                                serviceIds.add(service.getId());
                                                builder.addEvaluationServices(appointmentConverter.toView(service));
                                            }
                                        });
                                Optional.ofNullable(staffMap.get(evaluation.getStaffId()))
                                        .ifPresent(staff -> {
                                            if (!staffIds.contains(staff.getId())) {
                                                staffIds.add(staff.getId());
                                                builder.addStaffs(appointmentConverter.toView(staff));
                                            }
                                        });
                                Optional.ofNullable(petMap.get(evaluation.getPetId()))
                                        .ifPresent(pet -> {
                                            if (!petIds.contains(pet.getId())) {
                                                petIds.add(pet.getId());
                                                builder.addBusinessPets(appointmentConverter.toView(pet));
                                            }
                                        });
                            });

                    // Business related
                    Optional.ofNullable(businessInfoMap.get(appointment.getBusinessId()))
                            .ifPresent(business -> builder.setBusiness(businessMapper.bizDtoToClientView(business)));
                    Optional.ofNullable(obConfigMap.get(appointment.getBusinessId()))
                            .ifPresent(obConfig -> builder.setObConfig(bookOnlineMapper.dtoToClientView(obConfig)));
                    Optional.ofNullable(twilioMap.get(appointment.getBusinessId()))
                            .ifPresent(twilio -> builder.setTwilioNumber(messageMapper.dtoToView(twilio)));
                    Optional.ofNullable(arrivalWindowMap.get(appointment.getBusinessId()))
                            .ifPresent(
                                    arrivalWindow -> builder.setArrivalWindow(messageMapper.dtoToView(arrivalWindow)));
                    // Customer related
                    Optional.ofNullable(customerMap.get(appointment.getCustomerId()))
                            .ifPresent(customer -> builder.setCustomer(appointmentConverter.toView(customer)));
                    Optional.ofNullable(primaryAddressMap.get(appointment.getCustomerId()))
                            .ifPresent(address -> builder.setPrimaryAddress(appointmentConverter.toView(address)));
                    // Payment related。No show 存在时只需要计算 order，否则需要通过 deposit 取 booking fee
                    OrderDetailModel order;
                    BookOnlineDepositDTO deposit;
                    Map<Long, BigDecimal> feeMap;
                    if (noShowOrderMap.containsKey(appointment.getId())) {
                        order = noShowOrderMap.get(appointment.getId());
                        deposit = null;
                        feeMap = noShowProcessingFeeMap;
                    } else if (orderMap.containsKey(appointment.getId())) {
                        order = orderMap.get(appointment.getId());
                        deposit = depositMap.get(appointment.getId());
                        feeMap = processingFeeMap;
                    } else {
                        return builder.build();
                    }
                    var paymentBuilder =
                            buildPaymentDetail(appointment.getBookOnlineStatus(), order.getOrder(), deposit, feeMap);
                    paymentBuilder.addAllOrderDiscounts(buildDiscounts(order.getLineDiscountsList(), discountCodeMap));
                    paymentBuilder.addAllOrderItems(order.getLineItemsList().stream()
                            .map(appointmentConverter::toView)
                            .toList());
                    paymentBuilder.addAllOrderApplyPackages(buildApplyPackages(
                            order.getLineItemsList(),
                            applyPackageMap.getOrDefault(order.getOrder().getId(), List.of())));
                    paymentBuilder.addAllOrderPayments(buildPayments(
                            orderMap.get(appointment.getId()),
                            noShowOrderMap.get(appointment.getId()),
                            paymentMap,
                            depositMap.get(appointment.getId()),
                            preAuthRecordMap.get(appointment.getId())));
                    builder.setPaymentDetail(paymentBuilder.build());
                    // allowed operation
                    builder.setAllowedOperation(buildAllowedOperation(
                            appointment,
                            petDetailMap.getOrDefault(appointment.getId(), List.of()),
                            depositMap.get(appointment.getId()),
                            preAuthRecordMap.get(appointment.getId()),
                            preferenceSettingFuture.join(),
                            brandedAppConfigFuture.join()));
                    if (appointmentTrackingMap.get(appointment.getId()) != null) {
                        builder.setAppointmentTracking(
                                appointmentConverter.toView(appointmentTrackingMap.get(appointment.getId())));
                    }
                    if (appointmentPetFeedingMedicationMap.get(appointment.getId()) != null) {
                        builder.addAllSchedules(appointmentPetFeedingMedicationMap
                                .getOrDefault(appointment.getId(), AppointmentPetScheduleDef.getDefaultInstance())
                                .getSchedulesList());
                    }
                    return builder.build();
                })
                .toList();
    }

    private List<AppointmentDetailItem> buildDetailItemsFromBookingRequests(
            long companyId, List<BookingRequestModel> bookingRequests) {
        var onlyGroomingRequests = bookingRequests.stream()
                .filter(bookingRequest ->
                        ServiceItemEnum.GROOMING.getBitValue().equals(bookingRequest.getServiceTypeInclude()))
                .toList();
        var excludeOnlyGroomingRequests = bookingRequests.stream()
                .filter(bookingRequest ->
                        !ServiceItemEnum.GROOMING.getBitValue().equals(bookingRequest.getServiceTypeInclude()))
                .toList();
        var onlyGroomingRequestIDToAppointmentItemFuture = CompletableFuture.supplyAsync(
                () -> buildOnlyGroomingRequestDetail(companyId, onlyGroomingRequests), ThreadPool.getSubmitExecutor());
        var excludeOnlyGroomingRequestIDToAppointmentItemFuture = CompletableFuture.supplyAsync(
                () -> buildExcludeOnlyGroomingRequestDetail(companyId, excludeOnlyGroomingRequests),
                ThreadPool.getSubmitExecutor());
        var onlyGroomingRequestIDToAppointmentItem = onlyGroomingRequestIDToAppointmentItemFuture.join();
        var excludeOnlyGroomingRequestIDToAppointmentItem = excludeOnlyGroomingRequestIDToAppointmentItemFuture.join();

        return bookingRequests.stream()
                .map(bookingRequest -> {
                    if (ServiceItemEnum.GROOMING.getBitValue().equals(bookingRequest.getServiceTypeInclude())) {
                        return onlyGroomingRequestIDToAppointmentItem.get(bookingRequest.getId());
                    }
                    return excludeOnlyGroomingRequestIDToAppointmentItem.get(bookingRequest.getId());
                })
                .filter(Objects::nonNull)
                .toList();
    }

    private Map<Long /*bookingRequestId*/, AppointmentDetailItem> buildOnlyGroomingRequestDetail(
            long companyId, List<BookingRequestModel> bookingRequests) {
        if (CollectionUtils.isEmpty(bookingRequests)) {
            return Map.of();
        }
        var appointments = appointmentStub
                .getAppointmentList(GetAppointmentListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllAppointmentId(bookingRequests.stream()
                                .map(BookingRequestModel::getAppointmentId)
                                .toList())
                        .build())
                .getAppointmentsList();

        var items = buildAppointmentDetailItems(companyId, appointments);
        var appointmentMap = items.stream()
                .collect(Collectors.toMap(item -> item.getAppointment().getId(), Function.identity()));
        Map<Long, AppointmentDetailItem> result = new HashMap<>();
        for (BookingRequestModel bookingRequest : bookingRequests) {
            var appointmentItem = appointmentMap.get(bookingRequest.getAppointmentId());
            if (appointmentItem != null) {
                result.put(bookingRequest.getId(), appointmentItem);
            }
        }
        return result;
    }

    private Map<Long /*bookingRequestId*/, AppointmentDetailItem> buildExcludeOnlyGroomingRequestDetail(
            long companyId, List<BookingRequestModel> bookingRequests) {
        if (CollectionUtils.isEmpty(bookingRequests)) {
            return Map.of();
        }
        Set<Long> staffIdsSet = new HashSet<>();
        Set<Long> petIdsSet = new HashSet<>();
        Set<Long> businessIdsSet = new HashSet<>();
        Set<Long> bookingRequestIdsSet = new HashSet<>();
        Set<Long> customerIdsSet = new HashSet<>();
        Set<Long> serviceIdsSet = new HashSet<>();
        Set<Long> evaluationServiceIdsSet = new HashSet<>();

        Map<Long, Set<Long>> bkrToStaffIds = new HashMap<>();
        Map<Long, Set<Long>> bkrToPetIds = new HashMap<>();
        Map<Long, Set<Long>> bkrToServiceIds = new HashMap<>();
        Map<Long, Set<Long>> bkrToEvaluationServiceIds = new HashMap<>();

        for (BookingRequestModel bookingRequest : bookingRequests) {
            bookingRequestIdsSet.add(bookingRequest.getId());
            businessIdsSet.add(bookingRequest.getBusinessId());
            customerIdsSet.add(bookingRequest.getCustomerId());
            for (BookingRequestModel.Service service : bookingRequest.getServicesList()) {
                long staffId = 0, petId = 0, serviceId = 0, evaluationServiceId = 0;
                if (service.hasGrooming()) {
                    staffId = service.getGrooming().getService().getStaffId();
                    petId = service.getGrooming().getService().getPetId();
                    serviceId = service.getGrooming().getService().getServiceId();
                }
                if (service.hasBoarding()) {
                    petId = service.getBoarding().getService().getPetId();
                    serviceId = service.getBoarding().getService().getServiceId();
                }
                if (service.hasDaycare()) {
                    petId = service.getDaycare().getService().getPetId();
                    serviceId = service.getDaycare().getService().getServiceId();
                }
                if (service.hasEvaluation()) {
                    petId = service.getEvaluation().getService().getPetId();
                    evaluationServiceId = service.getEvaluation().getService().getEvaluationId();
                }
                if (staffId != 0) {
                    staffIdsSet.add(staffId);
                    bkrToStaffIds
                            .computeIfAbsent(bookingRequest.getId(), k -> new HashSet<>())
                            .add(staffId);
                }
                if (petId != 0) {
                    petIdsSet.add(petId);
                    bkrToPetIds
                            .computeIfAbsent(bookingRequest.getId(), k -> new HashSet<>())
                            .add(petId);
                }
                if (serviceId != 0) {
                    serviceIdsSet.add(serviceId);
                    bkrToServiceIds
                            .computeIfAbsent(bookingRequest.getId(), k -> new HashSet<>())
                            .add(serviceId);
                }
                if (evaluationServiceId != 0) {
                    evaluationServiceIdsSet.add(evaluationServiceId);
                    bkrToEvaluationServiceIds
                            .computeIfAbsent(bookingRequest.getId(), k -> new HashSet<>())
                            .add(evaluationServiceId);
                }
            }
        }
        var staffIds = staffIdsSet.stream().toList();
        var petIds = petIdsSet.stream().toList();
        var bookingRequestIds = bookingRequestIdsSet.stream().toList();
        var businessIds = businessIdsSet.stream().toList();
        var customerIds = customerIdsSet.stream().toList();
        var serviceIds = serviceIdsSet.stream().toList();
        var evaluationServiceIds = evaluationServiceIdsSet.stream().toList();

        // Deposit, order, pet detail, agreement
        var depositsFuture = futureService.listBookOnlineDepositsByRequestIds(bookingRequestIds);
        var ordersFuture = futureService.listOrders(
                bookingRequestIds, BOOKING_REQUEST.name().toLowerCase());
        var processingFeesFuture = futureService.listProcessingFees(ordersFuture);

        // Discount code, apply package, payment and refund info
        var discountCodesFuture = futureService.listDiscountCodes(companyId, ordersFuture);
        var applyPackagesFuture = futureService.listApplyPackages(ordersFuture);
        var paymentsFuture = futureService.listPayments(ordersFuture, CompletableFuture.completedFuture(Map.of()));

        //  branded app user can ignore pre-auth.
        // var preAuthRecordsFuture = futureService.listPreAuthRecords(companyId,appointmentIds);

        // Service info, staff info, pet info
        var serviceFuture = futureService.listServices(serviceIds);
        var evaluationServiceFuture = futureService.listEvaluations(evaluationServiceIds);
        var staffsFuture = futureService.listStaffs(staffIds);
        var petsFuture = futureService.listPets(petIds);

        // Business info, book online config, twilio number, arrival window

        var businessInfoFuture = futureService.listBusinessesInfo(businessIds);
        var obConfigFuture = futureService.listBookOnlineConfig(businessIds);
        var twilioFuture = futureService.listBusinessTwilioNumbers(businessIds);
        var arrivalWindowFuture = futureService.listArrivalWindowSettings(businessIds);

        // Customer info, primary address
        var customerFuture = futureService.listBusinessCustomers(customerIds);
        var primaryAddressFuture = futureService.listPrimaryAddresses(customerIds);

        CompletableFuture.allOf(
                        depositsFuture,
                        ordersFuture,
                        processingFeesFuture,
                        discountCodesFuture,
                        applyPackagesFuture,
                        paymentsFuture,
                        serviceFuture,
                        staffsFuture,
                        petsFuture,
                        businessInfoFuture,
                        obConfigFuture,
                        twilioFuture,
                        arrivalWindowFuture,
                        customerFuture,
                        primaryAddressFuture,
                        evaluationServiceFuture)
                .join();
        var depositMap = depositsFuture.join();
        var orderMap = ordersFuture.join();
        var processingFeeMap = filterProcessingFeePayByClient(processingFeesFuture.join());
        var discountCodeMap = discountCodesFuture.join();
        var applyPackageMap = applyPackagesFuture.join();
        var paymentMap = paymentsFuture.join();

        var serviceMap = serviceFuture.join();
        var evaluationServiceMap = evaluationServiceFuture.join();
        var staffMap = staffsFuture.join();
        var petMap = petsFuture.join();
        var businessInfoMap = businessInfoFuture.join();
        var obConfigMap = obConfigFuture.join();
        var twilioMap = twilioFuture.join();
        var arrivalWindowMap = arrivalWindowFuture.join();
        var customerMap = customerFuture.join();
        var primaryAddressMap = primaryAddressFuture.join();

        return bookingRequests.stream()
                .map(bookingRequest -> {
                    // Appointment related
                    AppointmentDetailItem.Builder builder = AppointmentDetailItem.newBuilder();
                    builder.setAppointment(appointmentConverter.toView(bookingRequest));
                    bookingRequest.getServicesList().forEach(service -> {
                        builder.addAllAppointmentItems(buildPetDetailModelClientView(service));
                    });
                    bookingRequest.getServicesList().forEach(service -> {
                        builder.addAllAppointmentEvaluationItems(buildPetEvaluationDetailClientView(service));
                    });

                    if (bkrToStaffIds.containsKey(bookingRequest.getId())) {
                        bkrToStaffIds.get(bookingRequest.getId()).forEach(staffId -> {
                            if (staffMap.containsKey(staffId)) {
                                builder.addStaffs(appointmentConverter.toView(staffMap.get(staffId)));
                            }
                        });
                    }
                    if (bkrToPetIds.containsKey(bookingRequest.getId())) {
                        bkrToPetIds.get(bookingRequest.getId()).forEach(petId -> {
                            if (petMap.containsKey(petId)) {
                                builder.addBusinessPets(appointmentConverter.toView(petMap.get(petId)));
                            }
                        });
                    }
                    if (bkrToServiceIds.containsKey(bookingRequest.getId())) {
                        bkrToServiceIds.get(bookingRequest.getId()).forEach(serviceId -> {
                            if (serviceMap.containsKey(serviceId)) {
                                builder.addServices(appointmentConverter.toView(serviceMap.get(serviceId)));
                            }
                        });
                    }
                    if (bkrToEvaluationServiceIds.containsKey(bookingRequest.getId())) {
                        bkrToEvaluationServiceIds.get(bookingRequest.getId()).forEach(evaluationServiceId -> {
                            if (evaluationServiceMap.containsKey(evaluationServiceId)) {
                                builder.addEvaluationServices(
                                        appointmentConverter.toView(evaluationServiceMap.get(evaluationServiceId)));
                            }
                        });
                    }

                    // Business related
                    Optional.ofNullable(businessInfoMap.get(bookingRequest.getBusinessId()))
                            .ifPresent(business -> builder.setBusiness(businessMapper.bizDtoToClientView(business)));
                    Optional.ofNullable(obConfigMap.get(bookingRequest.getBusinessId()))
                            .ifPresent(obConfig -> builder.setObConfig(bookOnlineMapper.dtoToClientView(obConfig)));
                    Optional.ofNullable(twilioMap.get(bookingRequest.getBusinessId()))
                            .ifPresent(twilio -> builder.setTwilioNumber(messageMapper.dtoToView(twilio)));
                    Optional.ofNullable(arrivalWindowMap.get(bookingRequest.getBusinessId()))
                            .ifPresent(
                                    arrivalWindow -> builder.setArrivalWindow(messageMapper.dtoToView(arrivalWindow)));
                    // Customer related
                    Optional.ofNullable(customerMap.get(bookingRequest.getCustomerId()))
                            .ifPresent(customer -> builder.setCustomer(appointmentConverter.toView(customer)));
                    Optional.ofNullable(primaryAddressMap.get(bookingRequest.getCustomerId()))
                            .ifPresent(address -> builder.setPrimaryAddress(appointmentConverter.toView(address)));
                    // Payment related。No show 存在时只需要计算 order，否则需要通过 deposit 取 booking fee
                    OrderDetailModel order;
                    BookOnlineDepositDTO deposit;
                    Map<Long, BigDecimal> feeMap;
                    if (orderMap.containsKey(bookingRequest.getId())) {
                        order = orderMap.get(bookingRequest.getId());
                        deposit = depositMap.get(bookingRequest.getId());
                        feeMap = processingFeeMap;
                    } else {
                        return builder.build();
                    }
                    var paymentBuilder =
                            buildPaymentDetail(CommonConstant.ENABLE.intValue(), order.getOrder(), deposit, feeMap);

                    paymentBuilder.addAllOrderDiscounts(buildDiscounts(order.getLineDiscountsList(), discountCodeMap));
                    paymentBuilder.addAllOrderItems(order.getLineItemsList().stream()
                            .map(appointmentConverter::toView)
                            .toList());
                    paymentBuilder.addAllOrderApplyPackages(buildApplyPackages(
                            order.getLineItemsList(),
                            applyPackageMap.getOrDefault(order.getOrder().getId(), List.of())));
                    paymentBuilder.addAllOrderPayments(buildPayments(
                            orderMap.get(bookingRequest.getId()),
                            null,
                            paymentMap,
                            depositMap.get(bookingRequest.getId()),
                            null));
                    builder.setPaymentDetail(paymentBuilder.build());
                    // allowed operation
                    builder.setAllowedOperation(buildAllowedOperation(bookingRequest));
                    builder.addAllSchedules(getPetScheduleDefs(bookingRequest));
                    return builder.build();
                })
                .collect(Collectors.toMap(
                        item -> item.getAppointment().getBookingRequestId(), Function.identity(), (a, b) -> a));
    }

    private List<PetScheduleDef> getPetScheduleDefs(BookingRequestModel bookingRequest) {
        Set<Long> petIds = new HashSet<>();
        Map<Long /*pet id*/, List<AppointmentPetFeedingScheduleDef>> petFeedings = new HashMap<>();
        Map<Long /*pet id*/, List<AppointmentPetMedicationScheduleDef>> petMedications = new HashMap<>();
        bookingRequest.getServicesList().forEach(service -> {
            List<FeedingModel> feedings = new ArrayList<>();
            List<MedicationModel> medications = new ArrayList<>();
            long petId = 0;
            if (service.hasGrooming() || service.hasEvaluation()) {
                return;
            }
            if (service.hasBoarding()) {
                var boarding = service.getBoarding();
                petId = boarding.getService().getPetId();
                feedings = boarding.getFeedingsList();
                medications = boarding.getMedicationsList();
            }
            if (service.hasDaycare()) {
                var daycare = service.getDaycare();
                petId = daycare.getService().getPetId();
                feedings = daycare.getFeedingsList();
                medications = daycare.getMedicationsList();
            }
            petIds.add(petId);
            petFeedings
                    .computeIfAbsent(petId, k -> new ArrayList<>())
                    .addAll(appointmentConverter.toAppointmentPetFeedingScheduleDefs(feedings));
            petMedications
                    .computeIfAbsent(petId, k -> new ArrayList<>())
                    .addAll(appointmentConverter.toAppointmentPetMedicationScheduleDefs(medications));
        });
        List<PetScheduleDef> petSchedules = new ArrayList<>();
        petIds.forEach(petId -> {
            PetScheduleDef def = PetScheduleDef.newBuilder()
                    .setPetId(petId)
                    .addAllFeedings(petFeedings.getOrDefault(petId, Collections.emptyList()))
                    .addAllMedications(petMedications.getOrDefault(petId, Collections.emptyList()))
                    .build();
            petSchedules.add(def);
        });
        return petSchedules;
    }

    private AllowedOperation buildAllowedOperation(
            AppointmentModel appointment,
            List<PetDetailModel> petDetails,
            BookOnlineDepositDTO deposit,
            PreAuthDTO preAuthDTO,
            CompanyPreferenceSettingModel preferenceSetting,
            BrandedAppConfigModel brandedAppConfig) {
        var isPending = Objects.equals(
                appointment.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB.intValue());
        var now = LocalDateTime.now(ZoneId.of(preferenceSetting.getTimeZone().getName()));
        // Pending 状态下可以任意 cancel 和 reschedule
        // Upcoming 状态下 cancel 需要在 appointment 时间之前，reschedule 需要在 threshold 时间之前
        var isAllowedCancellation = isPending || isAllowedCancellation(appointment, now, brandedAppConfig);
        var isAllowedReschedule = isPending || isAllowedReschedule(appointment, petDetails, now, brandedAppConfig);
        // 无 deposit，无 pre-auth 时允许跳转到 cancel 页面
        var isDirectCancellation = isPending || (deposit == null && preAuthDTO == null);
        // single staff 时允许跳转到 reschedule 页面
        var isDirectReschedule = isPending || isSingleStaff(petDetails);

        return AllowedOperation.newBuilder()
                .setIsAllowedCancellation(isAllowedCancellation)
                .setIsDirectCancellation(isDirectCancellation)
                .setIsAllowedReschedule(isAllowedReschedule)
                .setIsDirectReschedule(isDirectReschedule)
                .build();
    }

    private AllowedOperation buildAllowedOperation(BookingRequestModel bookingRequest) {
        return AllowedOperation.newBuilder()
                .setIsAllowedCancellation(true)
                .setIsDirectCancellation(true)
                .setIsAllowedReschedule(true)
                .setIsDirectReschedule(true)
                .build();
    }

    private List<PetDetailModelClientView> buildPetDetailModelClientView(BookingRequestModel.Service service) {
        var res = new ArrayList<PetDetailModelClientView>();
        if (service.hasGrooming()) {
            var s = service.getGrooming();
            res.add(PetDetailModelClientView.newBuilder()
                    .setPetId(s.getService().getPetId())
                    .setServiceId(s.getService().getServiceId())
                    .setServiceTime(s.getService().getServiceTime())
                    .setStaffId(s.getService().getStaffId())
                    .setServicePrice(s.getService().getServicePrice())
                    .build());
        }
        if (service.hasBoarding()) {
            var s = service.getBoarding();
            res.add(PetDetailModelClientView.newBuilder()
                    .setPetId(s.getService().getPetId())
                    .setServiceId(s.getService().getServiceId())
                    .setServiceTime(getServiceTime(
                            s.getService().getStartDate(),
                            s.getService().getStartTime(),
                            s.getService().getEndDate(),
                            s.getService().getEndTime()))
                    .setServicePrice(s.getService().getServicePrice())
                    .build());
            s.getAddonsList().forEach(addon -> {
                res.add(PetDetailModelClientView.newBuilder()
                        .setPetId(s.getService().getPetId())
                        .setAssociatedServiceId(addon.getServiceDetailId())
                        .setServiceTime(addon.getDuration())
                        .setServicePrice(addon.getServicePrice())
                        .setDateType(addon.getDateType())
                        .setQuantityPerDay(addon.getQuantityPerDay())
                        .addAllSpecificDates(addon.getSpecificDatesList())
                        .build());
            });
        }
        if (service.hasDaycare()) {
            var s = service.getDaycare();
            res.add(PetDetailModelClientView.newBuilder()
                    .setPetId(s.getService().getPetId())
                    .setServiceId(s.getService().getServiceId())
                    .setServiceTime(s.getService().getEndTime() - s.getService().getStartTime())
                    .setServicePrice(s.getService().getServicePrice())
                    .build());
            s.getAddonsList().forEach(addon -> {
                res.add(PetDetailModelClientView.newBuilder()
                        .setPetId(s.getService().getPetId())
                        .setAssociatedServiceId(addon.getServiceDetailId())
                        .setServiceTime(addon.getDuration())
                        .setServicePrice(addon.getServicePrice())
                        .setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY)
                        .setQuantityPerDay(addon.getQuantityPerDay())
                        .addAllSpecificDates(addon.getSpecificDatesList())
                        .build());
            });
        }

        return res;
    }

    private List<PetEvaluationDetailClientView> buildPetEvaluationDetailClientView(
            BookingRequestModel.Service service) {
        var res = new ArrayList<PetEvaluationDetailClientView>();
        if (service.hasEvaluation()) {
            var s = service.getEvaluation();
            res.add(appointmentConverter.toView(s));
        }
        return res;
    }

    private int getServiceTime(String startDate, int startTimeInMins, String endDate, int endTimeInMins) {
        var start = LocalDateTime.parse(startDate + "T00:00:00");
        var end = LocalDateTime.parse(endDate + "T00:00:00");
        return (int) Duration.between(start.plusMinutes(startTimeInMins), end.plusMinutes(endTimeInMins))
                .toMinutes();
    }

    private Map<Long, BigDecimal> filterProcessingFeePayByClient(Map<Long, ConvenienceFeeDTO> processingFeeMap) {
        return processingFeeMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> Objects.equals(
                                        entry.getValue().getProcessingFeePayBy(),
                                        PaymentSettingConst.PROCESSING_FEE_PAY_BY_CLIENT)
                                ? entry.getValue().getConvenienceFee()
                                : BigDecimal.ZERO));
    }

    private PaymentDetailView.Builder buildPaymentDetail(
            int bookOnlineStatus,
            OrderModel order,
            BookOnlineDepositDTO deposit,
            Map<Long, BigDecimal> processingFeeMap) {
        PaymentDetailView.Builder builder = PaymentDetailView.newBuilder().setSubtotal(order.getSubTotalAmount());

        BigDecimal bookingFee = BigDecimal.ZERO;
        if (deposit != null) {
            String guid = deposit.getGuid();
            builder.setPaymentType(bookOnlineMapper.guidToPaymentType(guid))
                    .setPrepayType(bookOnlineMapper.guidToPrepayType(guid));
            bookingFee = Optional.ofNullable(deposit.getBookingFee()).orElse(BigDecimal.ZERO);
        } else {
            builder.setPaymentType(PaymentType.PAYMENT_TYPE_DISABLE);
        }

        // 未 schedule 时并且有 deposit 需要从 deposit 中获取信息
        boolean needToGetFromDeposit =
                Objects.equals(bookOnlineStatus, CommonConstant.ENABLE.intValue()) && deposit != null;
        if (needToGetFromDeposit) {
            var taxAndFees = BigDecimal.valueOf(order.getTaxAmount())
                    // unpaid processing fee
                    .add(processingFeeMap.get(order.getId()))
                    .add(bookingFee);
            var tip = Optional.ofNullable(deposit.getTipsAmount()).orElse(BigDecimal.ZERO);
            var discount = Optional.ofNullable(deposit.getDiscountAmount()).orElse(BigDecimal.ZERO);
            BigDecimal total = BigDecimal.valueOf(order.getSubTotalAmount())
                    .add(taxAndFees)
                    .add(tip)
                    .subtract(discount);
            builder.setTaxAndFees(taxAndFees.doubleValue())
                    .setTip(tip.doubleValue())
                    .setDiscount(discount.doubleValue())
                    .setTotal(total.doubleValue());
        } else {
            var taxAndFees = BigDecimal.valueOf(order.getTaxAmount())
                    .add(BigDecimal.valueOf(order.getExtraFeeAmount()))
                    .add(bookingFee)
                    // unpaid processing fee
                    .add(processingFeeMap.get(order.getId()));
            BigDecimal total = BigDecimal.valueOf(order.getSubTotalAmount())
                    .add(taxAndFees)
                    .add(BigDecimal.valueOf(order.getTipsAmount()))
                    .subtract(BigDecimal.valueOf(order.getDiscountAmount()));
            builder.setTaxAndFees(taxAndFees.doubleValue())
                    .setTip(order.getTipsAmount())
                    .setDiscount(order.getDiscountAmount())
                    .setTotal(total.doubleValue());
        }

        return builder;
    }

    private List<OrderDiscount> buildDiscounts(
            List<OrderLineDiscountModel> orderDiscounts, Map<Long, DiscountCodeCompositeView> discountCodeMap) {
        return orderDiscounts.stream()
                .map(model -> {
                    OrderDiscount.Builder builder = OrderDiscount.newBuilder().setId(model.getId());
                    if (Objects.equals(model.getDiscountType(), DiscountType.AMOUNT.getType())) {
                        builder.setType(DiscountCodeType.DISCOUNT_CODE_TYPE_FIXED_AMOUNT);
                        builder.setAmount(model.getDiscountAmount());
                    } else if (Objects.equals(model.getDiscountType(), DiscountType.PERCENTAGE.getType())) {
                        builder.setType(DiscountCodeType.DISCOUNT_CODE_TYPE_PERCENTAGE);
                        builder.setAmount(model.getDiscountRate());
                    }
                    Optional.ofNullable(discountCodeMap.get(model.getDiscountCodeId()))
                            .ifPresent(discount -> builder.setDiscountCode(discount.getDiscountCode()));
                    return builder.build();
                })
                .toList();
    }

    private List<OrderApplyPackage> buildApplyPackages(
            List<OrderLineItemModel> items, List<PackageServiceDTO> applyPackages) {
        var itemMap = items.stream().collect(Collectors.toMap(OrderLineItemModel::getId, Function.identity()));

        return applyPackages.stream()
                .peek(dto -> Optional.ofNullable(
                                itemMap.get(dto.getInvoiceItemId().longValue()))
                        .ifPresent(item -> dto.setServicePrice(BigDecimal.valueOf(item.getUnitPrice()))))
                .map(appointmentConverter::toView)
                .toList();
    }

    private <T> List<T> buildPaymentDetails(
            OrderDetailModel order,
            Map<Long, PaymentSummary> paymentMap,
            Function<PaymentSummary, List<T>> getDetails) {
        if (order == null || !order.hasOrder()) {
            return List.of();
        }
        PaymentSummary paymentSummary = paymentMap.get(order.getOrder().getId());
        if (paymentSummary == null) {
            return List.of();
        }
        List<T> details = getDetails.apply(paymentSummary);
        if (CollectionUtils.isEmpty(details)) {
            return List.of();
        }
        return details;
    }

    private List<OrderPayment> buildPayments(
            OrderDetailModel order,
            OrderDetailModel noShowOrder,
            Map<Long, PaymentSummary> paymentSummaryMap,
            BookOnlineDepositDTO deposit,
            PreAuthDTO preAuth) {
        var payments = Stream.concat(
                        buildPaymentDetails(order, paymentSummaryMap, PaymentSummary::getPayments).stream(),
                        buildPaymentDetails(noShowOrder, paymentSummaryMap, PaymentSummary::getPayments).stream())
                .toList();
        var refunds = Stream.concat(
                        buildPaymentDetails(order, paymentSummaryMap, PaymentSummary::getRefunds).stream(),
                        buildPaymentDetails(noShowOrder, paymentSummaryMap, PaymentSummary::getRefunds).stream())
                .toList();
        if (CollectionUtils.isEmpty(payments) && CollectionUtils.isEmpty(refunds)) {
            return List.of();
        }
        BigDecimal bookingFee = getBookingFee(deposit, preAuth);
        // 未 capture 的 preAuth 优先显示
        if (isNotCapturePreAuth(preAuth)) {
            return List.of(OrderPayment.newBuilder()
                    .setId(preAuth.getPaymentId())
                    .setTransactionType(OrderPayment.TransactionType.PRE_AUTHORIZED)
                    .setAmount(preAuth.getPreAuthAmount().add(bookingFee).doubleValue())
                    .setPaymentMethod(paymentMethodMapper.dtoToView(preAuth))
                    .setPaymentTime(DateConverter.INSTANCE.toTimestamp(preAuth.getPreAuthTime()))
                    .build());
        }

        var paymentMap = payments.stream()
                .collect(Collectors.toMap(PaymentSummary.PaymentDto::getId, Function.identity(), (a, b) -> a));
        List<OrderPayment> sortedPayments = Stream.concat(
                        payments.stream()
                                .filter(payment -> isPaymentValid(payment, deposit))
                                .map(payment -> OrderPayment.newBuilder()
                                        .setId(payment.getId())
                                        .setTransactionType(buildTransactionType(payment, deposit))
                                        .setAmount(payment.getAmount().doubleValue())
                                        .setPaymentMethod(paymentMethodMapper.dtoToView(payment))
                                        .setPaymentTime(DateConverter.INSTANCE.toTimestamp(payment.getCreateTime()))
                                        .build()),
                        refunds.stream().map(refund -> OrderPayment.newBuilder()
                                .setId(refund.getOriginPaymentId())
                                .setTransactionType(OrderPayment.TransactionType.REFUND)
                                .setAmount(refund.getAmount().doubleValue())
                                .setPaymentMethod(
                                        paymentMethodMapper.dtoToView(paymentMap.get(refund.getOriginPaymentId())))
                                .setPaymentTime(DateConverter.INSTANCE.toTimestamp(refund.getCreateTime()))
                                .build()))
                .sorted(Comparator.comparingLong(
                                p -> ((OrderPayment) p).getPaymentTime().getSeconds())
                        .reversed())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sortedPayments)) {
            return List.of();
        }
        // 第一笔 pre-auth 或者 pre pay 的 payment 需要加上 booking fee
        OrderPayment earliestPayment = sortedPayments.get(sortedPayments.size() - 1);
        sortedPayments.set(
                sortedPayments.size() - 1,
                earliestPayment.toBuilder()
                        .setAmount(BigDecimal.valueOf(earliestPayment.getAmount())
                                .add(bookingFee)
                                .doubleValue())
                        .build());
        return sortedPayments;
    }

    private boolean isNotCapturePreAuth(PreAuthDTO preAuth) {
        return preAuth != null
                && Objects.equals(preAuth.getPreAuthStatus(), PreAuthStatusEnum.SUCCEED.getCode())
                && Objects.equals(preAuth.getIsCapture(), Boolean.FALSE);
    }

    private boolean isPaymentValid(PaymentSummary.PaymentDto payment, BookOnlineDepositDTO deposit) {
        return !(deposit == null && Objects.equals(payment.getStatus(), PaymentStatusEnum.FAILED_STR));
    }

    private BigDecimal getBookingFee(BookOnlineDepositDTO deposit, PreAuthDTO preAuth) {
        if (deposit == null || deposit.getBookingFee() == null) {
            return BigDecimal.ZERO;
        }
        if (preAuth == null && Objects.equals(deposit.getDepositType(), DepositPaymentTypeEnum.PreAuth)) {
            return BigDecimal.ZERO;
        }
        return deposit.getBookingFee();
    }

    private OrderPayment.TransactionType buildTransactionType(
            PaymentSummary.PaymentDto payment, BookOnlineDepositDTO deposit) {
        if (Objects.equals(payment.getStatus(), PaymentStatusEnum.PROCESSING_STR)) {
            return OrderPayment.TransactionType.PROCESSING_PAYMENT;
        }
        // Prepay 时 payment status 为 failed 时为取消
        if (deposit != null && Objects.equals(payment.getStatus(), PaymentStatusEnum.FAILED_STR)) {
            return OrderPayment.TransactionType.CANCELED_PAYMENT;
        }
        return Objects.equals(payment.getIsDeposit(), CommonConstant.ENABLE)
                ? OrderPayment.TransactionType.DEPOSIT
                : OrderPayment.TransactionType.COMPLETED_PAYMENT;
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void cancelAppointment(
            CancelAppointmentParams request, StreamObserver<CancelAppointmentResult> responseObserver) {
        var appointment = accountAppointmentService.getAccountBelongsAppointment(
                AuthContext.get().accountId(), request.getAppointmentId());
        if (Objects.equals(appointment.getStatus(), AppointmentStatus.CANCELED)) {
            responseObserver.onNext(CancelAppointmentResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        var zoneId = companyService.getZoneId(appointment.getCompanyId());
        var now = LocalDateTime.now(zoneId);
        var config = brandedAppService.mustGetBrandedAppConfig(AuthContext.get().brandedAppId());
        if (!isAllowedCancellation(appointment, now, config)) {
            throw ExceptionUtil.bizException(Code.CODE_CANCELLATION_NOT_ALLOWED);
        }
        boolean isFreeCancellation = isFreeCancellation(now, appointment, config);
        // 1. Cancel appointment
        if (isFreeCancellation) {
            var cancelAppointmentRequest = CancelAppointmentRequest.newBuilder()
                    .setAppointmentId(request.getAppointmentId())
                    .setCancelByType(AppointmentUpdatedBy.BY_PET_PARENT_APP)
                    .setCancelBy(appointment.getCustomerId())
                    .setCancelReason(GroomingAppointmentEnum.PPA_CANCEL_REASON)
                    .setNoShow(AppointmentNoShowStatus.NOT_NO_SHOW)
                    .build();
            appointmentStub.cancelAppointment(cancelAppointmentRequest);
        }

        if (!isFreeCancellation) {
            if (!request.hasPaymentMethodId()) {
                throw ExceptionUtil.bizException(Code.CODE_CANCELLATION_NOT_ALLOWED, "Payment method is required");
            }
            // 2. Create cancellation-fee order
            double cancellationFee = config.getCancellationFee();
            long orderId = createCancellationFeeOrder(appointment, cancellationFee);
            // 3. Create and confirm payment
            createAndConfirmPayment(appointment, request.getPaymentMethodId(), cancellationFee, orderId);
        }

        // 4. Activity log
        activityLogService.createActivityLog(CreateActivityLogRequest.newBuilder()
                .setCompanyId(appointment.getCompanyId())
                .setBusinessId(appointment.getBusinessId())
                .setOperatorId(String.valueOf(appointment.getCustomerId()))
                .setIsRoot(true)
                .setAction(AppointmentAction.CANCEL)
                .setResourceType(Resource.Type.APPOINTMENT)
                .setResourceId(String.valueOf(appointment.getId()))
                .setDetails(JsonUtil.toJson(new CancelLogDTO(
                        GroomingAppointmentEnum.PPA_CANCEL_REASON, AppointmentUpdatedBy.BY_PET_PARENT_APP)))
                .build());

        responseObserver.onNext(CancelAppointmentResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private boolean isAllowedCancellation(
            AppointmentModel appointment, LocalDateTime now, BrandedAppConfigModel config) {
        if (!config.getIsAllowCancel()) {
            return false;
        }
        var isUpcoming = Objects.equals(
                        AppointmentConverter.STATUS_MAP.get(appointment.getStatus()), ClientAppointmentStatus.UPCOMING)
                || Objects.equals(
                        AppointmentConverter.STATUS_MAP.get(appointment.getStatus()),
                        ClientAppointmentStatus.UNCONFIRMED);
        var isBeforeAppointmentDateTime = now.isBefore(convertAppointmentDateTime(appointment));
        return isUpcoming && isBeforeAppointmentDateTime;
    }

    private boolean isFreeCancellation(LocalDateTime now, AppointmentModel appointment, BrandedAppConfigModel config) {
        return config.getIsFreeCancellation()
                || config.getCancellationFee() == 0
                || now.plusHours(config.getCancellationThresholdHours())
                        .isBefore(convertAppointmentDateTime(appointment));
    }

    private long createCancellationFeeOrder(AppointmentModel appointment, double cancellationFee) {
        var orderDetail = orderService.getOrderDetail(GetOrderRequest.newBuilder()
                .setSourceId(appointment.getId())
                .setSourceType(OrderSourceType.NO_SHOW.getSource())
                .build());
        if (orderDetail.hasOrder()) {
            var order = orderDetail.getOrder();
            orderService.updateOrder(UpdateOrderRequest.newBuilder()
                    .setOrder(OrderModel.newBuilder()
                            .setId(order.getId())
                            .setTotalAmount(cancellationFee)
                            .build())
                    .addLineItems(buildOrderLineItem(cancellationFee))
                    .build());
            return order.getId();
        }
        return orderService
                .createOrder(CreateOrderRequest.newBuilder()
                        .setOrder(buildOrder(appointment, cancellationFee))
                        .addLineItems(buildOrderLineItem(cancellationFee))
                        .build())
                .getId();
    }

    private OrderModel buildOrder(AppointmentModel appointment, double cancellationFee) {
        return OrderModel.newBuilder()
                .setCompanyId(appointment.getCompanyId())
                .setBusinessId(appointment.getBusinessId())
                .setCustomerId(appointment.getCustomerId())
                .setStatus(InvoiceStatusEnum.INVOICE_STATUS_CREATED)
                .setSourceType(OrderSourceType.NO_SHOW.getSource())
                .setSourceId(appointment.getId())
                .setTotalAmount(cancellationFee)
                .build();
    }

    private OrderLineItemModel buildOrderLineItem(double cancellationFee) {
        return OrderLineItemModel.newBuilder()
                .setName("Cancellation Fee")
                .setType(OrderItemType.ITEM_TYPE_CANCELLATION_FEE.getType())
                .setQuantity(1)
                .setUnitPrice(cancellationFee)
                .build();
    }

    private void createAndConfirmPayment(
            AppointmentModel appointment, String paymentMethodId, double cancellationFee, long orderId) {
        var customer = businessCustomerService
                .getCustomerInfo(GetCustomerInfoRequest.newBuilder()
                        .setId(appointment.getCustomerId())
                        .build())
                .getCustomer();
        var paymentSetting = paymentSettingClient.getPaymentSetting(toIntExact(appointment.getBusinessId()));
        CreatePaymentParams params = new CreatePaymentParams();
        params.setAddProcessingFee(Objects.equals(
                paymentSetting.getProcessingFeePayBy(), PaymentSettingConst.PROCESSING_FEE_PAY_BY_CLIENT));
        params.setAmount(BigDecimal.valueOf(cancellationFee));
        params.setCustomerId(toIntExact(appointment.getCustomerId()));
        params.setChargeToken(paymentMethodId);
        params.setStripePaymentMethod((byte) StripePaymentMethod.STRIPE_PAYMENT_METHOD_COF_VALUE);
        params.setStripePaymentMethodId(paymentMethodId);
        params.setInvoiceId(toIntExact(orderId));
        params.setIsDeposit((byte) 0);
        params.setIsOnline(false);
        params.setMethodId(PaymentMethod.CREDIT_CARD_VALUE);
        params.setModule(PaymentModule.GROOMING.name().toLowerCase());
        params.setPaidBy(customer.getFirstName() + " " + customer.getLastName());
        params.setIsCancellationFee(Boolean.TRUE);
        paymentClient.createAndConfirmPayment(toIntExact(appointment.getBusinessId()), params);
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void rescheduleAppointment(
            RescheduleAppointmentParams request, StreamObserver<RescheduleAppointmentResult> responseObserver) {
        var config = brandedAppService.mustGetBrandedAppConfig(AuthContext.get().brandedAppId());
        var appointment = accountAppointmentService.getAccountBelongsAppointment(
                AuthContext.get().accountId(), request.getAppointmentId());
        var petDetails = getPetDetails(appointment.getCompanyId(), request.getAppointmentId());
        var zoneId = companyService.getZoneId(appointment.getCompanyId());
        var now = LocalDateTime.now(zoneId);
        if (!isAllowedSelfReschedule(appointment, petDetails, now, config)) {
            throw ExceptionUtil.bizException(Code.CODE_RESCHEDULE_NOT_ALLOWED);
        }

        // 1. Checks if auto-assign is needed
        String startDate = request.getGroomingReschedule().getStartDate();
        var autoAssignResult = needAutoAssign(request.getGroomingReschedule())
                ? bookingService.doAutoAssign(appointment, petDetails, request.getGroomingReschedule())
                : null;
        int startTime = Optional.ofNullable(autoAssignResult)
                .map(IOBService.AutoAssignResult::appointmentTime)
                .orElse(request.getGroomingReschedule().getStartTime());
        long staffId = Optional.ofNullable(autoAssignResult)
                .map(result -> result.staffId().longValue())
                .orElse(request.getGroomingReschedule().getStaffId());

        // 2. Reschedule appointment
        appointmentScheduleService.rescheduleGroomingService(RescheduleGroomingServiceRequest.newBuilder()
                .setAppointmentId(appointment.getId())
                .setId(appointment.getId())
                .setCardType(CalendarCardType.APPOINTMENT)
                .setStaffId(staffId)
                .setStartDate(startDate)
                .setStartTime(startTime)
                .setCompanyId(appointment.getCompanyId())
                .build());

        // 3. Activity log
        activityLogService.createActivityLog(CreateActivityLogRequest.newBuilder()
                .setCompanyId(appointment.getCompanyId())
                .setBusinessId(appointment.getBusinessId())
                .setOperatorId(String.valueOf(appointment.getCustomerId()))
                .setIsRoot(true)
                .setAction(AppointmentAction.RESCHEDULE)
                .setResourceType(Resource.Type.APPOINTMENT)
                .setResourceId(String.valueOf(appointment.getId()))
                .setDetails(JsonUtil.toJson(new ChangeTimeLogDTO(
                        DateConverter.INSTANCE.toTimestamp(
                                appointment.getAppointmentDate(), appointment.getAppointmentStartTime(), zoneId),
                        DateConverter.INSTANCE.toTimestamp(startDate, startTime, zoneId),
                        AppointmentUpdatedBy.BY_PET_PARENT_APP)))
                .build());

        responseObserver.onNext(RescheduleAppointmentResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private boolean needAutoAssign(GroomingRescheduleDef rescheduleDef) {
        return StringUtils.hasText(rescheduleDef.getStartDate())
                && (!rescheduleDef.hasStaffId() || !rescheduleDef.hasStartTime());
    }

    private List<PetDetailModel> getPetDetails(long companyId, long appointmentId) {
        return petDetailService
                .getPetDetailList(GetPetDetailListRequest.newBuilder()
                        .addAllAppointmentIds(List.of(appointmentId))
                        .setCompanyId(companyId)
                        .build())
                .getPetDetailsList();
    }

    private boolean isAllowedSelfReschedule(
            AppointmentModel appointment,
            List<PetDetailModel> petDetails,
            LocalDateTime now,
            BrandedAppConfigModel config) {
        return isAllowedReschedule(appointment, petDetails, now, config) && isSingleStaff(petDetails);
    }

    public boolean isAllowedReschedule(
            AppointmentModel appointment,
            List<PetDetailModel> petDetails,
            LocalDateTime now,
            BrandedAppConfigModel config) {
        if (CollectionUtils.isEmpty(petDetails) || !isGroomingOnly(petDetails)) return false;
        var isUnconfirmed = Objects.equals(
                AppointmentConverter.STATUS_MAP.get(appointment.getStatus()), ClientAppointmentStatus.UNCONFIRMED);
        var isUpcoming = Objects.equals(
                AppointmentConverter.STATUS_MAP.get(appointment.getStatus()), ClientAppointmentStatus.UPCOMING);
        var appointmentDateTime = convertAppointmentDateTime(appointment);
        var isBeforeThresholdHours =
                now.plusHours(config.getRescheduleThresholdHours()).isBefore(appointmentDateTime);
        return config.getIsAllowReschedule() && (isUpcoming || isUnconfirmed) && isBeforeThresholdHours;
    }

    private boolean isGroomingOnly(List<PetDetailModel> petDetails) {
        return petDetails.stream()
                .allMatch(petDetail -> Objects.equals(petDetail.getServiceItemType(), ServiceItemType.GROOMING));
    }

    private boolean isSingleStaff(List<PetDetailModel> petDetails) {
        List<Long> staffIds =
                petDetails.stream().map(PetDetailModel::getStaffId).distinct().toList();
        return staffIds.size() == 1;
    }

    private LocalDateTime convertAppointmentDateTime(AppointmentModel appointment) {
        return LocalDate.parse(appointment.getAppointmentDate())
                .atTime(appointment.getAppointmentStartTime() / 60, appointment.getAppointmentStartTime() % 60);
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void confirmAppointment(
            ConfirmAppointmentParams request, StreamObserver<ConfirmAppointmentResult> responseObserver) {
        var appointment = accountAppointmentService.getAccountBelongsAppointment(
                AuthContext.get().accountId(), request.getAppointmentId());
        if (!Objects.equals(appointment.getStatus(), AppointmentStatus.APPOINTMENT_STATUS_UNSPECIFIED)
                && !Objects.equals(appointment.getStatus(), AppointmentStatus.UNCONFIRMED)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "The current status of the appointment does not support being confirmed.");
        }
        String timezoneName = organizationService.getTimezone(appointment.getCompanyId());
        var now = LocalDateTime.now(ZoneId.of(timezoneName));
        var appointmentDateTime = convertAppointmentDateTime(appointment);
        if (now.isAfter(appointmentDateTime)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "The appointment has already passed the start time.");
        }

        ConfirmParams confirmParams = new ConfirmParams();
        confirmParams.setId((int) appointment.getId());
        confirmParams.setConfirmByType(GroomingAppointmentEnum.CONFIRM_TYPE_BY_CLIENT);
        confirmParams.setConfirmByMethod(MessageMethodTypeEnum.MESSAGE_METHOD_APP);
        groomingAppointmentService.confirm((int) appointment.getBusinessId(), 0, confirmParams);

        activityLogService.createActivityLog(CreateActivityLogRequest.newBuilder()
                .setCompanyId(appointment.getCompanyId())
                .setBusinessId(appointment.getBusinessId())
                .setOperatorId(String.valueOf(appointment.getCustomerId()))
                .setIsRoot(true)
                .setAction(AppointmentAction.CONFIRM)
                .setResourceType(Resource.Type.APPOINTMENT)
                .setResourceId(String.valueOf(appointment.getId()))
                .setDetails(JsonUtil.toJson(new CustomerReplyLogDTO(
                        true, MessageMethodTypeEnum.MESSAGE_METHOD_APP, ClientReplyTypeEnum.CONFIRM_APPOINTMENT)))
                .build());

        responseObserver.onNext(ConfirmAppointmentResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
