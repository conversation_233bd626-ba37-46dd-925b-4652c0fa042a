package com.moego.client.api.v1.grooming.controller;

import com.google.common.collect.Lists;
import com.moego.client.api.v1.grooming.converter.GroomingReportConverter;
import com.moego.client.api.v1.grooming.mapper.GroomingMapper;
import com.moego.client.api.v1.grooming.service.GroomingService;
import com.moego.common.enums.groomingreport.GroomingReportStatusEnum;
import com.moego.idl.client.grooming.v1.GroomingReportCardDef;
import com.moego.idl.client.grooming.v1.GetGroomingReportListRequest;
import com.moego.idl.client.grooming.v1.GetGroomingReportListResponse;
import com.moego.idl.client.grooming.v1.GroomingReportServiceGrpc;
import com.moego.idl.client.grooming.v1.ListGroomingReportCardParams;
import com.moego.idl.client.grooming.v1.ListGroomingReportCardResult;
import com.moego.idl.models.grooming.v1.GroomingReportStatus;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.api.IGroomingGroomingReportService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.client.IGroomingGroomingReportClient;
import com.moego.server.grooming.dto.AppointmentDetailClientPortalDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportDTO;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.groomingreport.GetGroomingReportCardListParams;
import com.moego.server.grooming.params.groomingreport.GroomingIdListParams;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.message.api.IGroomingReportSendService;
import com.moego.server.message.dto.GroomingReportSendLogDTO;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
@GrpcService
@RequiredArgsConstructor
public class GroomingReportController extends GroomingReportServiceGrpc.GroomingReportServiceImplBase {

    private final GroomingService groomingService;
    private final IGroomingGroomingReportClient groomingReportClient;
    private final GroomingMapper groomingMapper;
    private final IGroomingOnlineBookingService onlineBookingService;
    private final IGroomingGroomingReportService groomingGroomingReportService;
    private final IGroomingReportSendService groomingReportSendService;

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getGroomingReportList(
            GetGroomingReportListRequest request, StreamObserver<GetGroomingReportListResponse> responseObserver) {
        AppointmentDetailClientPortalDTO appt = groomingService.getAppointmentDetail(
                request.getAppointmentId(), AuthContext.get().accountId());
        List<GroomingReportDTO> reports = groomingReportClient.getGroomingReportListForClientApp(appt.getId());

        responseObserver.onNext(GetGroomingReportListResponse.newBuilder()
                .addAllReports(reports.stream().map(groomingMapper::dtoToView).toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listGroomingReportCard(
            ListGroomingReportCardParams request, StreamObserver<ListGroomingReportCardResult> responseObserver) {
        OBBusinessDTO biz = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        var listGroomingReportCardParamsBuilder = new GetGroomingReportCardListParams()
                .toBuilder()
                        .companyId(biz.getCompanyId())
                        .businessId(biz.getBusinessId().longValue())
                        .petId(request.getPetId());
        if (request.hasStatus()) {
            listGroomingReportCardParamsBuilder.status(convertToGroomingReportStatusEnum(request.getStatus()));
        }
        var reports = groomingGroomingReportService.listGroomingReportCardByFilter(
                listGroomingReportCardParamsBuilder.build());
        if (reports.isEmpty()) {
            responseObserver.onNext(ListGroomingReportCardResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }


        List<GroomingReportCardDef> reportConfigs;
        if (request.hasStatus() && request.getStatus().equals(GroomingReportStatus.GROOMING_REPORT_STATUS_SENT)) {
            // 查询report last send log，key 为 groomingId
            Map<Integer, List<GroomingReportSendLogDTO>> groomingLastReportSendLogsMap =
                groomingReportSendService.getGroomingLastReportSendLogsMap(new GroomingIdListParams(
                    biz.getBusinessId(),
                    reports.stream()
                        .map(GroomingReportDTO::getGroomingId)
                        .distinct()
                        .toList()));

            List<GroomingReportSendLogDTO> allSendLogs = groomingLastReportSendLogsMap.values().stream()
                .flatMap(List::stream)
                .toList();

            reportConfigs = GroomingReportConverter.INSTANCE.convertToGroomingReportCardDefList(
                reports, allSendLogs);
        } else  {
            reportConfigs = GroomingReportConverter.INSTANCE.convertToGroomingReportCardDefList(reports);
        }

        reportConfigs = sortGroomingReportCardDefList(reportConfigs, request.getStatus());

        int pageSize = request.getPagination().getPageSize();
        int pageNum = request.getPagination().getPageNum();

        List<List<GroomingReportCardDef>> partition = Lists.partition(reportConfigs, pageSize);
        List<GroomingReportCardDef> itemsPage =
            (pageNum > 0 && pageNum <= partition.size()) ? partition.get(pageNum - 1) : List.of();

        responseObserver.onNext(ListGroomingReportCardResult.newBuilder()
            .addAllGroomingReportCards(itemsPage)
            .setPagination(PaginationResponse.newBuilder()
                .setPageNum(pageNum)
                .setPageSize(pageSize)
                .setTotal(partition.size())
                .build())
            .build());
        responseObserver.onCompleted();
    }

    private GroomingReportStatusEnum convertToGroomingReportStatusEnum(GroomingReportStatus status) {
        if (status == null) {
            return null;
        }

        return switch (status) {
            case GROOMING_REPORT_STATUS_CREATED -> GroomingReportStatusEnum.created;
            case GROOMING_REPORT_STATUS_DRAFT -> GroomingReportStatusEnum.draft;
            case GROOMING_REPORT_STATUS_READY -> GroomingReportStatusEnum.submitted;
            case GROOMING_REPORT_STATUS_SENT -> GroomingReportStatusEnum.sent;
            case GROOMING_REPORT_STATUS_UNSPECIFIED, UNRECOGNIZED -> null;
        };
    }

    private List<GroomingReportCardDef> sortGroomingReportCardDefList(
        List<GroomingReportCardDef> groomingReportCardDefs, GroomingReportStatus status) {
        return groomingReportCardDefs.stream()
            .sorted((a, b) -> {
                if (status != null && status.equals(GroomingReportStatus.GROOMING_REPORT_STATUS_SENT)) {
                    // 按发送时间降序排序，处理空值情况
                    if (!a.hasSendTime() && !b.hasSendTime()) {
                        return 0;
                    }
                    if (!a.hasSendTime()) {
                        return 1;
                    }
                    if (!b.hasSendTime()) {
                        return -1;
                    }
                    return Long.compare(
                        b.getSendTime().getSeconds(), a.getSendTime().getSeconds());
                } else {
                     return Long.compare(
                        b.getReportCardId(), a.getReportCardId());
                }
            })
            .toList();
    }
}
