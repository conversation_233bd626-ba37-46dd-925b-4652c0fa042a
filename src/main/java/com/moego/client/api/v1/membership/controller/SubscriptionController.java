package com.moego.client.api.v1.membership.controller;

import static com.moego.idl.models.membership.v1.PaymentHistoryItemView.Status.PAYMENT_SUCCESS;
import static com.moego.lib.common.auth.AuthType.ANONYMOUS;

import com.moego.client.api.v1.customer.service.CustomerService;
import com.moego.client.api.v1.membership.converter.MembershipConverter;
import com.moego.idl.client.membership.v1.BuyMembershipParams;
import com.moego.idl.client.membership.v1.BuyMembershipResult;
import com.moego.idl.client.membership.v1.CreateOBRequestSettingParams;
import com.moego.idl.client.membership.v1.CreateOBRequestSettingResult;
import com.moego.idl.client.membership.v1.GetBuyResultParams;
import com.moego.idl.client.membership.v1.GetBuyResultResult;
import com.moego.idl.client.membership.v1.GetOBRequestSettingParams;
import com.moego.idl.client.membership.v1.GetOBRequestSettingResult;
import com.moego.idl.client.membership.v1.GetSellLinkPublicInfoParams;
import com.moego.idl.client.membership.v1.GetSellLinkPublicInfoResult;
import com.moego.idl.client.membership.v1.ListPaymentHistoryParams;
import com.moego.idl.client.membership.v1.ListPaymentHistoryResult;
import com.moego.idl.client.membership.v1.ListSubscriptionsForAppParams;
import com.moego.idl.client.membership.v1.ListSubscriptionsForAppResult;
import com.moego.idl.client.membership.v1.ListSubscriptionsParams;
import com.moego.idl.client.membership.v1.ListSubscriptionsResult;
import com.moego.idl.client.membership.v1.SubscriptionServiceGrpc;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.membership.v1.MembershipSubscriptionModel;
import com.moego.idl.models.membership.v1.PaymentHistoryItemFilter;
import com.moego.idl.models.membership.v1.SellLinkModel;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.membership.v1.CreateOBRequestSettingRequest;
import com.moego.idl.service.membership.v1.CreateOBRequestSettingResponse;
import com.moego.idl.service.membership.v1.GetOBRequestSettingRequest;
import com.moego.idl.service.membership.v1.GetOBRequestSettingResponse;
import com.moego.idl.service.membership.v1.GetSubscriptionRequest;
import com.moego.idl.service.membership.v1.ListPaymentHistoryRequest;
import com.moego.idl.service.membership.v1.ListPaymentHistoryResponse;
import com.moego.idl.service.membership.v1.MembershipServiceGrpc;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.TaxRuleServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.payment.client.IPaymentCreditCardClient;
import com.moego.server.payment.params.CustomerStripInfoRequest;
import io.grpc.stub.StreamObserver;
import java.util.Comparator;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@GrpcService
@Slf4j
public class SubscriptionController extends SubscriptionServiceGrpc.SubscriptionServiceImplBase {
    private final MembershipConverter membershipConverter;

    private final com.moego.idl.service.membership.v1.SubscriptionServiceGrpc.SubscriptionServiceBlockingStub
            subscriptionService;
    private final CompanyServiceGrpc.CompanyServiceBlockingStub companyService;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessService;
    private final MembershipServiceGrpc.MembershipServiceBlockingStub membershipService;
    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerService;
    private final TaxRuleServiceGrpc.TaxRuleServiceBlockingStub taxService;
    private final IPaymentCreditCardClient iPaymentCreditCardClient;
    private final IGroomingOnlineBookingService onlineBookingService;
    private final CustomerService customerService;

    @Override
    @Auth(ANONYMOUS)
    public void getSellLinkPublicInfo(
            GetSellLinkPublicInfoParams request, StreamObserver<GetSellLinkPublicInfoResult> responseObserver) {
        final var sellLinkInput = membershipConverter.getSellLinkRequest(request);
        final var sellLinkOutput = subscriptionService.getSellLink(sellLinkInput);
        final var companyInput = membershipConverter.getCompanyPreferenceSettingRequest(sellLinkOutput);
        final var companyOutput = companyService.getCompanyPreferenceSetting(companyInput);
        final var businessInput = membershipConverter.getLocationDetailRequest(sellLinkOutput);
        final var businessOutput = businessService.getLocationDetail(businessInput);
        final var membershipInput = membershipConverter.getMembershipRequest(sellLinkOutput);
        final var membershipOutput = membershipService.getMembership(membershipInput);
        final var taxInput = membershipConverter.getTaxRuleRequest(membershipOutput);
        final var taxOutput = taxService.getTaxRule(taxInput);
        final var customerInput = membershipConverter.getCustomerRequest(sellLinkOutput);
        final var customerOutput = businessCustomerService.getCustomer(customerInput);
        final var customerCards = iPaymentCreditCardClient.getCreditCardList(
                (int) sellLinkOutput.getSellLink().getBusinessId(),
                (int) sellLinkOutput.getSellLink().getCustomerId());
        final var usBankAccountsInput = iPaymentCreditCardClient.getUsBankAccounts(
                (int) sellLinkOutput.getSellLink().getCustomerId());
        final var subscriptionInput = membershipConverter.getNonCancelledSubscriptionRequest(sellLinkOutput);
        final var subscriptionOutput = subscriptionService.getSubscription(subscriptionInput);
        final var result = membershipConverter.getSellLinkResult(
                sellLinkOutput,
                companyOutput,
                businessOutput,
                membershipOutput,
                taxOutput,
                customerOutput,
                customerCards,
                usBankAccountsInput,
                subscriptionOutput);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void buyMembership(BuyMembershipParams request, StreamObserver<BuyMembershipResult> responseObserver) {
        final var sellLinkInput = membershipConverter.getSellLinkRequest(request);
        final var sellLinkOutput = subscriptionService.getSellLink(sellLinkInput);
        final var sellLink = sellLinkOutput.getSellLink();
        final var cardId =
                switch (request.getPaymentMethodCase()) {
                    case EXTERNAL_CARD_ID -> request.getExternalCardId();
                    case EXTERNAL_CARD_TOKEN -> saveCard(sellLink, request.getExternalCardToken(), false);
                    case EXTERNAL_ACH_ID -> request.getExternalAchId();
                    case EXTERNAL_ACH_TOKEN -> saveCard(sellLink, request.getExternalAchToken(), true);
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown card source type");
                };
        log.info(
                "buyMembership sellLink: request companyId={},getSellOperatorStaffId={}",
                sellLink.getCompanyId(),
                sellLink.getOperatorStaffId());
        final var createInput = membershipConverter.createSubscriptionRequest(request, sellLink, cardId);
        log.info(
                "buyMembership request: createInput companyId={},getSellOperatorStaffId={}",
                createInput.getCompanyId(),
                createInput.getSellOperatorStaffId());

        final var createOutput = subscriptionService.createSubscription(createInput);
        final var result = membershipConverter.buyMembershipResult(createOutput);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void getBuyResult(GetBuyResultParams request, StreamObserver<GetBuyResultResult> responseObserver) {
        final var sellLinkInput = membershipConverter.getSellLinkRequest(request);
        final var sellLinkOutput = subscriptionService.getSellLink(sellLinkInput);
        final var subscriptionInput = membershipConverter.getNonCancelledSubscriptionRequest(sellLinkOutput);
        final var subscriptionOutput = subscriptionService.getSubscription(subscriptionInput);
        final var result = membershipConverter.getBuyResult(subscriptionOutput);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listSubscriptions(
            ListSubscriptionsParams request, StreamObserver<ListSubscriptionsResult> responseObserver) {
        OBBusinessDTO dto = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        final var input = membershipConverter.listSubscriptionsRequest(
                dto.getCompanyId(), AuthContext.get().customerId(), request);
        final var output = subscriptionService.listSubscriptions(input);
        final var result = membershipConverter.listSubscriptionsResult(output);
        final var membershipSubscriptionList = result.getMembershipSubscriptionsList().stream()
                .sorted(Comparator.<MembershipSubscriptionModel>comparingInt(membershipSubscriptionModel ->
                                membershipConverter.statusToSort(membershipSubscriptionModel
                                        .getSubscription()
                                        .getStatus()))
                        .thenComparingLong(membershipSubscriptionModel -> membershipSubscriptionModel
                                .getSubscription()
                                .getCreatedAt()
                                .getSeconds()))
                .toList();
        responseObserver.onNext(result.toBuilder()
                .clearMembershipSubscriptions()
                .addAllMembershipSubscriptions(membershipSubscriptionList)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void listSubscriptionsForApp(
            ListSubscriptionsForAppParams request, StreamObserver<ListSubscriptionsForAppResult> responseObserver) {
        var customer = customerService.getLinkCustomer(
                request.getCompanyId(), AuthContext.get().accountId());
        final var input = membershipConverter.listSubscriptionsRequest(
                request.getCompanyId(), customer.getCustomerId().longValue(), request);
        final var output = subscriptionService.listSubscriptions(input);
        final var result = membershipConverter.listSubscriptionsForAppResult(output);
        final var membershipSubscriptionList = result.getMembershipSubscriptionsList().stream()
                .sorted(Comparator.<MembershipSubscriptionModel>comparingInt(membershipSubscriptionModel ->
                                membershipConverter.statusToSort(membershipSubscriptionModel
                                        .getSubscription()
                                        .getStatus()))
                        .thenComparingLong(membershipSubscriptionModel -> membershipSubscriptionModel
                                .getSubscription()
                                .getCreatedAt()
                                .getSeconds()))
                .toList();
        responseObserver.onNext(result.toBuilder()
                .clearMembershipSubscriptions()
                .addAllMembershipSubscriptions(membershipSubscriptionList)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void listPaymentHistory(
            ListPaymentHistoryParams request, StreamObserver<ListPaymentHistoryResult> responseObserver) {
        PaymentHistoryItemFilter.Builder builder = PaymentHistoryItemFilter.newBuilder();

        if (request.hasFilter()) {
            builder.setSubscriptionId(request.getFilter().getSubscriptionId());
            builder.setCompanyId(request.getFilter().getCompanyId());
            builder.setBusinessId(request.getFilter().getBusinessId());
            if (request.getFilter().hasSortBy()) {
                builder.setSortBy(request.getFilter().getSortBy());
            }
            if (request.getFilter().hasIsDesc()) {
                builder.setIsDesc(request.getFilter().getIsDesc());
            }
        }

        // 构建请求
        ListPaymentHistoryRequest listPaymentHistoryRequest = ListPaymentHistoryRequest.newBuilder()
                .setFilter(builder.build())
                .setPagination(request.getPagination())
                .build();

        ListPaymentHistoryResponse listPaymentHistoryResponse =
                subscriptionService.listPaymentHistory(listPaymentHistoryRequest);

        ListPaymentHistoryResult.Builder resultBuilder = ListPaymentHistoryResult.newBuilder();

        listPaymentHistoryResponse.getHistoryViewsList().stream()
                .filter(historyView -> historyView.getStatus() == PAYMENT_SUCCESS) // 只保留支付成功的记录
                .forEach(resultBuilder::addPaymentHistory);

        resultBuilder.setPagination(listPaymentHistoryResponse.getPagination());

        responseObserver.onNext(resultBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void createOBRequestSetting(
            CreateOBRequestSettingParams request, StreamObserver<CreateOBRequestSettingResult> responseObserver) {
        var customer = customerService.getLinkCustomer(
                request.getCompanyId(), AuthContext.get().accountId());
        var res = subscriptionService.getSubscription(GetSubscriptionRequest.newBuilder()
                .setCompanyId(request.getCompanyId())
                .setId(request.getSubscriptionId())
                .build());
        if (!res.hasSubscription()
                || !Objects.equals((int) res.getSubscription().getCustomerId(), customer.getCustomerId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Subscription not found");
        }
        CreateOBRequestSettingResponse response =
                subscriptionService.createOBRequestSetting(CreateOBRequestSettingRequest.newBuilder()
                        .setCompanyId(request.getCompanyId())
                        .setSubscriptionId(request.getSubscriptionId())
                        .setBusinessId(request.getBusinessId())
                        .addAllDaysOfWeek(request.getDaysOfWeekList())
                        .build());
        CreateOBRequestSettingResult.Builder resultBuilder =
                CreateOBRequestSettingResult.newBuilder().setObRequestSetting(response.getObRequestSetting());
        responseObserver.onNext(resultBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getOBRequestSetting(
            GetOBRequestSettingParams request, StreamObserver<GetOBRequestSettingResult> responseObserver) {
        var customer = customerService.getLinkCustomer(
                request.getCompanyId(), AuthContext.get().accountId());
        var res = subscriptionService.getSubscription(GetSubscriptionRequest.newBuilder()
                .setCompanyId(request.getCompanyId())
                .setId(request.getSubscriptionId())
                .build());
        if (!res.hasSubscription()
                || !Objects.equals((int) res.getSubscription().getCustomerId(), customer.getCustomerId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Subscription not found");
        }
        GetOBRequestSettingResponse obRequest =
                subscriptionService.getOBRequestSetting(GetOBRequestSettingRequest.newBuilder()
                        .setSubscriptionId(request.getSubscriptionId())
                        .build());
        GetOBRequestSettingResult.Builder resultBuilder = GetOBRequestSettingResult.newBuilder();
        if (obRequest.hasObRequestSetting()) {
            resultBuilder.setObRequestSetting(obRequest.getObRequestSetting());
        }
        responseObserver.onNext(resultBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * save new payment method to stripe customer
     * @param sellLink
     * @param token charge token or ach token
     * @return
     */
    private String saveCard(SellLinkModel sellLink, String token, boolean addACH) {
        final var cardInput = new CustomerStripInfoRequest();
        cardInput.setBusinessId((int) sellLink.getBusinessId());
        cardInput.setCustomerId((int) sellLink.getCustomerId());
        cardInput.setChargeToken(token);
        cardInput.setIgnoreDuplicateCard(true);
        cardInput.setAddAch(addACH);
        final var card = iPaymentCreditCardClient.saveNewCard((int) sellLink.getBusinessId(), cardInput);
        return card.getPaymentMethodId();
    }
}
