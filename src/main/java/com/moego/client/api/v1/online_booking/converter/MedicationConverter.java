package com.moego.client.api.v1.online_booking.converter;

import com.moego.idl.models.appointment.v1.AppointmentPetMedicationScheduleDef;
import com.moego.idl.models.business_customer.v1.BusinessPetMedicationScheduleDef;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleTimeDef;
import com.moego.idl.models.online_booking.v1.MedicationModel;
import com.moego.idl.service.online_booking.v1.CreateMedicationRequest;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface MedicationConverter {

    MedicationConverter INSTANCE = Mappers.getMapper(MedicationConverter.class);

    String LABEL = "label";

    @Mapping(target = "medicationAmount", expression = "java(toMedicationAmount(model))")
    @Mapping(target = "medicationUnit", source = "unit")
    @Mapping(target = "medicationName", source = "medicationName")
    @Mapping(target = "medicationNote", source = "notes")
    @Mapping(target = "medicationTimes", source = "time")
    AppointmentPetMedicationScheduleDef toMedication(MedicationModel model);

    default List<AppointmentPetMedicationScheduleDef> toMedication(List<MedicationModel> models) {
        if (CollectionUtils.isEmpty(models)) {
            return List.of();
        }
        return models.stream().map(this::toMedication).toList();
    }

    default String toMedicationAmount(MedicationModel model) {
        return model.hasAmountStr() ? model.getAmountStr() : String.valueOf(model.getAmount());
    }

    default BusinessPetScheduleTimeDef toMedicationTime(MedicationModel.MedicationSchedule schedule) {
        return BusinessPetScheduleTimeDef.newBuilder()
                .setScheduleTime(schedule.getTime())
                .putExtraJson("label", schedule.getLabel())
                .build();
    }

    static CreateMedicationRequest toCreateMedicationRequest(AppointmentPetMedicationScheduleDef medication) {
        var builder = CreateMedicationRequest.newBuilder();
        builder.addAllTime(medication.getMedicationTimesList().stream()
                .map(time -> MedicationModel.MedicationSchedule.newBuilder()
                        .setTime(time.getScheduleTime())
                        .setLabel(time.getExtraJsonOrDefault(LABEL, ""))
                        .build())
                .toList());
        builder.setAmountStr(medication.getMedicationAmount());
        builder.setUnit(medication.getMedicationUnit());
        builder.setMedicationName(medication.getMedicationName());
        if (medication.hasMedicationNote()) {
            builder.setNotes(medication.getMedicationNote());
        }
        if (medication.hasSelectedDate()) {
            builder.setSelectedDate(AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder()
                    .setDateType(medication.getSelectedDate().getDateType())
                    .addAllSpecificDates(medication.getSelectedDate().getSpecificDatesList())
                    .build());
        }
        return builder.build();
    }

    static BusinessPetMedicationScheduleDef toBusinessPetMedicationScheduleDef(
            Long petId, AppointmentPetMedicationScheduleDef def) {
        return BusinessPetMedicationScheduleDef.newBuilder()
                .setPetId(petId)
                .setMedicationAmount(def.getMedicationAmount())
                .setMedicationUnit(def.getMedicationUnit())
                .setMedicationName(def.getMedicationName())
                .setMedicationNote(def.getMedicationNote())
                .addAllMedicationTimes(def.getMedicationTimesList())
                .build();
    }
}
