package com.moego.client.api.v1.grooming.service;

import com.moego.client.api.v1.customer.service.CustomerService;
import com.moego.client.api.v1.grooming.dto.OnlineBookingDTO;
import com.moego.client.api.v1.grooming.mapper.BookOnlineMapper;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.online_booking.v1.SelectedPetServiceDef;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetTypeServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetCustomerAddressRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerPrimaryAddressRequest;
import com.moego.idl.service.business_customer.v1.ListPetTypeRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.grooming.api.IBookOnlineAcceptPetTypeService;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.client.IOnlineBookingClient;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.CompanyBusinessIdDTO;
import com.moego.server.grooming.dto.ob.AvailableStaffDTO;
import com.moego.server.grooming.dto.ob.BookOnlinePaymentBaseSettingDTO;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import com.moego.server.grooming.dto.ob.OBServiceDTO;
import com.moego.server.grooming.dto.ob.OBServiceListDto;
import com.moego.server.grooming.dto.ob.OBTimeSlotDTO;
import com.moego.server.grooming.dto.ob.SelectedPetServiceDTO;
import com.moego.server.grooming.dto.ob.ServiceAreaResultDTO;
import com.moego.server.grooming.dto.ob.StaffAvailableDateDTO;
import com.moego.server.grooming.dto.ob.StaffFirstAvailableDateDTO;
import com.moego.server.grooming.params.ob.AvailableStaffParams;
import com.moego.server.grooming.params.ob.ServiceAreaParams;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/9/27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OnlineBookingService {

    private final IGroomingOnlineBookingClient groomingOnlineBookingClient;
    private final IPetClient petClient;
    private final IOnlineBookingClient onlineBookingClient;

    private final IBookOnlineAcceptPetTypeService bookOnlineAcceptPetTypeService;
    private final BusinessPetTypeServiceGrpc.BusinessPetTypeServiceBlockingStub petTypeClient;
    private final BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub customerAddressClient;

    private final CustomerService customerService;
    private final BookOnlineMapper bookOnlineMapper;

    public Set<Integer> getAcceptPetTypeIds(boolean migrated, long companyId, long businessId) {
        var listPetTypeRequest =
                ListPetTypeRequest.newBuilder().setCompanyId(companyId).setIncludeUnavailable(false);
        if (!migrated) {
            listPetTypeRequest.setBusinessId(businessId);
        }

        // 查询 available pet type
        var petTypes = petTypeClient.listPetType(listPetTypeRequest.build()).getTypesList();
        if (CollectionUtils.isEmpty(petTypes)) {
            return Set.of();
        }

        // 查询 ob accept pet type, key 是 petTypeId, value 是 accepted or not
        var acceptPetTypeMap = bookOnlineAcceptPetTypeService
                .getAcceptPetType(new CompanyBusinessIdDTO(companyId, (int) businessId))
                .petTypeAcceptMap();

        // 返回 available 且 accept 的 pet type
        return petTypes.stream()
                .filter(petType ->
                        acceptPetTypeMap.getOrDefault(petType.getPetTypeId().getNumber(), true))
                .map(petType -> petType.getPetTypeId().getNumber())
                .collect(Collectors.toSet());
    }

    public OBServiceListDto getAvailableServiceList(
            Integer businessId, Integer linkCustomerId, List<CustomerPetDetailDTO> businessPets) {
        OBServiceDTO params = new OBServiceDTO();
        params.setBusinessId(businessId);
        params.setCustomerId(linkCustomerId);
        params.setPetDataList(businessPets.stream()
                .map(pet -> {
                    OBPetDataDTO dto = new OBPetDataDTO();
                    dto.setPetId(pet.getPetId());
                    dto.setPetTypeId(pet.getPetTypeId());
                    dto.setBreed(pet.getBreed());
                    dto.setWeight(pet.getWeight());
                    dto.setCoat(pet.getHairLength());
                    return dto;
                })
                .toList());
        return onlineBookingClient.getAvailableServiceList(params);
    }

    public AvailableStaffDTO getAvailableStaffList(OnlineBookingDTO onlineBookingDTO) {
        int businessId = Math.toIntExact(onlineBookingDTO.businessId());
        Integer linkCustomerId = customerService.getLinkCustomerId(businessId, onlineBookingDTO.accountId());
        AvailableStaffParams params = new AvailableStaffParams();
        params.setBusinessId(businessId);
        params.setCustomerId(linkCustomerId);
        List<SelectedPetServiceDTO> petServiceList = onlineBookingDTO.selectedPetServices().stream()
                .map(def -> {
                    SelectedPetServiceDTO dto = new SelectedPetServiceDTO();
                    OBPetDataDTO pet = new OBPetDataDTO();
                    pet.setPetId(Math.toIntExact(def.getPetId()));
                    dto.setPetDataDTO(pet);
                    List<Integer> serviceIds = Stream.concat(
                                    Stream.of(def.getServiceId()), def.getAddOnIdsList().stream())
                            .map(Long::intValue)
                            .toList();
                    dto.setServiceIdList(serviceIds);
                    return dto;
                })
                .toList();
        params.setPetServiceList(petServiceList);
        return onlineBookingClient.getAvailableStaffList(params);
    }

    public OBTimeSlotDTO buildOBTimeSlotDTO(OnlineBookingDTO onlineBookingDTO) {
        int businessId = Math.toIntExact(onlineBookingDTO.businessId());
        Integer linkCustomerId = customerService.getLinkCustomerId(businessId, onlineBookingDTO.accountId());
        List<Integer> selectedPetIds = onlineBookingDTO.selectedPetServices().stream()
                .map(SelectedPetServiceDef::getPetId)
                .map(Long::intValue)
                .toList();
        List<CustomerPetDetailDTO> businessPets = petClient.getCustomerPetListByIdList(selectedPetIds);
        List<Integer> allServiceIdList = onlineBookingDTO.selectedPetServices().stream()
                .flatMap(def -> Stream.concat(Stream.of(def.getServiceId()), def.getAddOnIdsList().stream())
                        .map(Long::intValue))
                .toList();
        Map<Integer, List<Integer>> petServices = onlineBookingDTO.selectedPetServices().stream()
                .collect(Collectors.toMap(def -> Math.toIntExact(def.getPetId()), def -> Stream.concat(
                                Stream.of(def.getServiceId()), def.getAddOnIdsList().stream())
                        .map(Long::intValue)
                        .toList()));

        OBTimeSlotDTO dto = new OBTimeSlotDTO();
        dto.setServiceIds(allServiceIdList);
        dto.setPetServices(petServices);
        dto.setStaffIdList(
                onlineBookingDTO.selectedStaffIds().stream().map(Long::intValue).toList());
        dto.setCustomerId(linkCustomerId);
        dto.setBusinessId(businessId);
        dto.setDate(
                StringUtils.hasText(onlineBookingDTO.date())
                        ? onlineBookingDTO.date()
                        : LocalDate.now().toString());
        dto.setQueryPerHalfDay(onlineBookingDTO.isHalfDay());
        // search range
        if (onlineBookingDTO.isEndOfMonth()) {
            dto.setQueryEndOfTheMonth(true);
        } else if (Objects.nonNull(onlineBookingDTO.searchDays())) {
            dto.setCount(onlineBookingDTO.searchDays());
        } else {
            dto.setCount(1);
        }

        var bookAddress = getBookAddress(onlineBookingDTO.selectedAddressId(), linkCustomerId);
        Optional.ofNullable(bookAddress).ifPresent(address -> {
            if (address.hasCoordinate()) {
                dto.setLat(String.valueOf(address.getCoordinate().getLatitude()));
                dto.setLng(String.valueOf(address.getCoordinate().getLongitude()));
            }
            dto.setZipcode(address.getZipcode());
        });
        Map<Integer, CustomerPetDetailDTO> petDeailMap =
                businessPets.stream().collect(Collectors.toMap(CustomerPetDetailDTO::getPetId, Function.identity()));
        List<OBPetDataDTO> petParamList = onlineBookingDTO.selectedPetServices().stream()
                .map(def -> {
                    CustomerPetDetailDTO pet = petDeailMap.get(Math.toIntExact(def.getPetId()));
                    if (Objects.isNull(pet)) {
                        log.error("pet not found, petId: {}", def.getPetId());
                        return null;
                    }
                    OBPetDataDTO petDto = new OBPetDataDTO();
                    petDto.setPetId(pet.getPetId());
                    petDto.setWeight(pet.getWeight());
                    petDto.setPetTypeId(pet.getPetTypeId());
                    petDto.setBreed(pet.getBreed());
                    petDto.setCoat(pet.getHairLength());
                    return petDto;
                })
                .filter(Objects::nonNull)
                .toList();
        dto.setPetParamList(petParamList);
        return dto;
    }

    private BusinessCustomerAddressModel getBookAddress(Long selectedAddressId, Integer linkCustomerId) {
        if (selectedAddressId != null) {
            var address = customerAddressClient
                    .getCustomerAddress(GetCustomerAddressRequest.newBuilder()
                            .setId(selectedAddressId)
                            .build())
                    .getAddress();
            if (!Objects.equals(address.getCustomerId(), linkCustomerId.longValue())) {
                throw ExceptionUtil.bizException(Code.CODE_ADDRESS_NOT_FOUND);
            }
            return address;
        } else {
            var response = customerAddressClient.getCustomerPrimaryAddress(GetCustomerPrimaryAddressRequest.newBuilder()
                    .setCustomerId(linkCustomerId)
                    .build());
            return response.hasAddress() ? response.getAddress() : null;
        }
    }

    public List<StaffFirstAvailableDateDTO> getStaffFirstAvailableDate(OnlineBookingDTO onlineBookingDTO) {
        OBTimeSlotDTO dto = buildOBTimeSlotDTO(onlineBookingDTO);
        // 最多查询 60 天
        dto.setQueryLimitDays(60);
        // 限制查询时间为 10 秒
        dto.setOverLimitDaysSeconds(10);
        // 限制每个员工查询日期数量为 1 天
        dto.setQueryCountPerStaff(1);
        return onlineBookingClient.getStaffFirstAvailableDateList(dto);
    }

    public List<StaffAvailableDateDTO> getStaffAvailableDateList(OnlineBookingDTO onlineBookingDTO) {
        OBTimeSlotDTO dto = buildOBTimeSlotDTO(onlineBookingDTO);
        return onlineBookingClient.getStaffAvailableDateList(dto);
    }

    public List<StaffAvailableDateDTO> getStaffAvailableTimeslotList(OnlineBookingDTO onlineBookingDTO) {
        OBTimeSlotDTO dto = buildOBTimeSlotDTO(onlineBookingDTO);
        return onlineBookingClient.getStaffAvailableDateList(dto);
    }

    public ServiceAreaResultDTO checkServiceArea(Integer businessId, BusinessCustomerAddressModel address) {
        ServiceAreaParams params = new ServiceAreaParams();
        params.setBusinessId(businessId);
        ServiceAreaParams.ClientAddressParams addressParams = new ServiceAreaParams.ClientAddressParams();
        addressParams.setAddressId((int) address.getId());
        addressParams.setLat(
                address.hasCoordinate() ? String.valueOf(address.getCoordinate().getLatitude()) : "");
        addressParams.setLng(
                address.hasCoordinate() ? String.valueOf(address.getCoordinate().getLongitude()) : "");
        addressParams.setZipcode(address.getZipcode());
        params.setAddressParamsList(List.of(addressParams));
        List<ServiceAreaResultDTO> results = groomingOnlineBookingClient.getServiceAreaResultList(params);
        ServiceAreaResultDTO result = new ServiceAreaResultDTO();
        result.setAddressId((int) address.getId());
        result.setOutOfArea(Boolean.FALSE);
        if (CollectionUtils.isEmpty(results)) {
            return result;
        }
        result.setOutOfArea(results.get(0).getOutOfArea());
        return result;
    }

    public BookOnlineDTO getPersonalizedOBSetting(Integer businessId, Integer customerId) {
        BookOnlineDTO obSetting = groomingOnlineBookingClient.getOBSetting(businessId);
        BookOnlinePaymentBaseSettingDTO groupSetting =
                groomingOnlineBookingClient.getOBClientPaymentSetting(businessId, customerId);
        bookOnlineMapper.mergeGroupToSetting(groupSetting, obSetting);
        return obSetting;
    }
}
