package com.moego.client.api.v1.online_booking.server;

import com.moego.client.api.v1.online_booking.service.ContextService;
import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.idl.client.online_booking.v1.ACHServiceGrpc;
import com.moego.idl.client.online_booking.v1.AddACHOnFileParams;
import com.moego.idl.client.online_booking.v1.AddACHOnFileResult;
import com.moego.idl.client.online_booking.v1.CreateACHSetupIntentParams;
import com.moego.idl.client.online_booking.v1.CreateACHSetupIntentResult;
import com.moego.idl.client.online_booking.v1.DeleteACHOnFileParams;
import com.moego.idl.client.online_booking.v1.DeleteACHOnFileResult;
import com.moego.idl.client.online_booking.v1.ListACHOnFilesParams;
import com.moego.idl.client.online_booking.v1.ListACHOnFilesResult;
import com.moego.idl.models.payment.v1.ACHView;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.payment.client.IPaymentCreditCardClient;
import com.moego.server.payment.dto.CustomerStripInfoSaveResponse;
import com.moego.server.payment.dto.SetupIntentDTO;
import com.moego.server.payment.dto.UsBankAccountDTO;
import com.moego.server.payment.params.CustomerStripInfoRequest;
import com.moego.server.payment.params.DeleteCustomerCardRequest;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class ACHServer extends ACHServiceGrpc.ACHServiceImplBase {

    private final ContextService contextService;
    private final IPaymentCreditCardClient creditCardClient;

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listACHOnFiles(ListACHOnFilesParams request, StreamObserver<ListACHOnFilesResult> responseObserver) {
        BaseBusinessCustomerIdDTO context =
                contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        List<UsBankAccountDTO> usBankAccounts = creditCardClient.getUsBankAccounts(context.getCustomerId());
        responseObserver.onNext(ListACHOnFilesResult.newBuilder()
                .addAllAchViews(usBankAccounts.stream()
                        .map(item -> ACHView.newBuilder()
                                .setId(item.getId())
                                .setBankName(item.getBankName())
                                .setLast4(item.getLast4())
                                .build())
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void addACHOnFile(AddACHOnFileParams request, StreamObserver<AddACHOnFileResult> responseObserver) {
        BaseBusinessCustomerIdDTO context =
                contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        CustomerStripInfoRequest req = new CustomerStripInfoRequest();
        req.setAddAch(true);
        req.setCustomerId(context.getCustomerId());
        req.setBusinessId(context.getBusinessId());
        req.setChargeToken(request.getPaymentMethodId());
        CustomerStripInfoSaveResponse resp = creditCardClient.saveStripeCard(context.getBusinessId(), req);
        responseObserver.onNext(AddACHOnFileResult.newBuilder()
                .setAchView(
                        ACHView.newBuilder().setId(resp.getPaymentMethodId()).build())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void deleteACHOnFile(DeleteACHOnFileParams request, StreamObserver<DeleteACHOnFileResult> responseObserver) {
        BaseBusinessCustomerIdDTO context =
                contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        DeleteCustomerCardRequest req = new DeleteCustomerCardRequest();
        req.setCustomerId(context.getCustomerId());
        req.setCardId(request.getPaymentMethodId());
        creditCardClient.deleteCustomerCard(req);
        responseObserver.onNext(DeleteACHOnFileResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void createACHSetupIntent(
            CreateACHSetupIntentParams request, StreamObserver<CreateACHSetupIntentResult> responseObserver) {
        BaseBusinessCustomerIdDTO context =
                contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        SetupIntentDTO achSetupIntent = creditCardClient.createACHSetupIntent(context.getCustomerId());
        responseObserver.onNext(CreateACHSetupIntentResult.newBuilder()
                .setSetupIntentId(achSetupIntent.getSetupIntentId())
                .setClientSecret(achSetupIntent.getClientSecret())
                .build());
        responseObserver.onCompleted();
    }
}
