package com.moego.client.api.v1.agreement.controller;

import com.moego.idl.client.agreement.v1.AgreementRecordServiceGrpc;
import com.moego.idl.client.agreement.v1.GetRecordListParams;
import com.moego.idl.client.agreement.v1.GetRecordListResult;
import com.moego.idl.models.agreement.v1.AgreementModelSimpleView;
import com.moego.idl.models.agreement.v1.AgreementRecordSimpleView;
import com.moego.idl.service.agreement.v1.AgreementServiceGrpc;
import com.moego.idl.service.agreement.v1.GetAgreementListRequest;
import com.moego.idl.service.agreement.v1.GetRecordListByCompanyRequest;
import com.moego.idl.service.agreement.v1.GetRecordListRequest;
import com.moego.idl.utils.v1.PaginationRequest;
import com.moego.idl.utils.v2.OrderBy;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class AgreementRecordController extends AgreementRecordServiceGrpc.AgreementRecordServiceImplBase {

    private final com.moego.idl.service.agreement.v1.AgreementRecordServiceGrpc.AgreementRecordServiceBlockingStub
            agreementRecordServiceBlockingStub;
    private final AgreementServiceGrpc.AgreementServiceBlockingStub agreementServiceBlockingStub;
    private final IGroomingOnlineBookingService onlineBookingService;

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void getRecordList(GetRecordListParams params, StreamObserver<GetRecordListResult> responseObserver) {
        var customerId = AuthContext.get().customerId();
        OBBusinessDTO biz = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(params.getDomain()).setName(params.getName()));

        var paginationBuilder = PaginationRequest.newBuilder();
        if (params.hasPagination()) {
            paginationBuilder
                    .setPageNo(params.getPagination().getPageNum() - 1)
                    .setPageSize(params.getPagination().getPageSize());
        } else {
            paginationBuilder.setPageNo(0).setPageSize(200);
        }

        List<OrderBy> orderBys = List.of(
                OrderBy.newBuilder().setFieldName("signed_time").setAsc(false).build(),
                OrderBy.newBuilder().setFieldName("create_time").setAsc(false).build());

        com.moego.idl.utils.v1.PaginationResponse pagination;
        List<AgreementRecordSimpleView> agreementRecordSimpleViews;
        if (params.getListCompanyRecord()) {
            GetRecordListByCompanyRequest.Builder requestBuilder = GetRecordListByCompanyRequest.newBuilder()
                    .setCompanyId(biz.getCompanyId())
                    .setCustomerId(customerId)
                    .setPagination(paginationBuilder.build())
                    .addAllOrderBys(orderBys);
            if (params.hasSignedStatus()) {
                requestBuilder.setSignedStatus(params.getSignedStatus());
            }
            var resp = agreementRecordServiceBlockingStub.getRecordListByCompany(requestBuilder.build());
            agreementRecordSimpleViews = resp.getAgreementRecordSimpleViewList();
            pagination = resp.getPagination();
        } else {
            GetRecordListRequest.Builder requestBuilder = GetRecordListRequest.newBuilder()
                    .setBusinessId(biz.getBusinessId())
                    .setCustomerId(customerId)
                    .setPagination(paginationBuilder.build())
                    .addAllOrderBys(orderBys);

            if (params.hasSignedStatus()) {
                requestBuilder.setSignedStatus(params.getSignedStatus());
            }
            var resp = agreementRecordServiceBlockingStub.getRecordList(requestBuilder.build());
            agreementRecordSimpleViews = resp.getAgreementRecordSimpleViewList();
            pagination = resp.getPagination();
        }

        List<AgreementModelSimpleView> agreementList = List.of();
        if (!agreementRecordSimpleViews.isEmpty()) {
            agreementList = agreementServiceBlockingStub
                    .getAgreementList(GetAgreementListRequest.newBuilder()
                            .setBusinessId(biz.getBusinessId())
                            .addAllIds(agreementRecordSimpleViews.stream()
                                    .map(AgreementRecordSimpleView::getAgreementId)
                                    .distinct()
                                    .toList())
                            .build())
                    .getAgreementSimpleViewList();
        }

        responseObserver.onNext(GetRecordListResult.newBuilder()
                .addAllAgreementRecordSimpleView(agreementRecordSimpleViews)
                .addAllAgreementSimpleView(agreementList)
                .setPagination(PaginationResponse.newBuilder()
                        .setTotal((int) pagination.getTotal())
                        .setPageNum((int) pagination.getPageNo() + 1)
                        .setPageSize((int) pagination.getPageSize())
                        .build())
                .build());
        responseObserver.onCompleted();
    }
}
