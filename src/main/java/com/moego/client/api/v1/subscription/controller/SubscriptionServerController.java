package com.moego.client.api.v1.subscription.controller;

import com.moego.client.api.v1.customer.service.CustomerService;
import com.moego.client.api.v1.subscription.service.SubscriptionService;
import com.moego.idl.client.subscription.v1.GetCreditParams;
import com.moego.idl.client.subscription.v1.GetCreditResult;
import com.moego.idl.client.subscription.v1.SubscriptionServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@GrpcService
@Slf4j
public class SubscriptionServerController extends SubscriptionServiceGrpc.SubscriptionServiceImplBase {
    private final SubscriptionService subscriptionService;
    private final CustomerService customerService;

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getCredit(GetCreditParams request, StreamObserver<GetCreditResult> responseObserver) {
        var customer = customerService.getLinkCustomer(
                request.getCompanyId(), AuthContext.get().accountId());
        var response = subscriptionService.getCredit(customer.getCustomerId().longValue());
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }
}
