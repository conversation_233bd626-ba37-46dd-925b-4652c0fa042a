package com.moego.client.api.v1.online_booking.server;

import static com.moego.client.api.v1.appointment.utils.AppointmentUtil.buildAppointmentOrderBy;
import static com.moego.client.api.v1.appointment.utils.AppointmentUtil.buildBaseFilter;
import static com.moego.client.api.v1.appointment.utils.AppointmentUtil.buildBookingRequestOrderBy;
import static com.moego.client.api.v1.appointment.utils.AppointmentUtil.buildFilterAndOrderBys;
import static com.moego.client.api.v1.appointment.utils.AppointmentUtil.buildFilterForCanceled;
import static com.moego.client.api.v1.appointment.utils.AppointmentUtil.buildFilterForDelayed;
import static com.moego.client.api.v1.appointment.utils.AppointmentUtil.buildFilterForFinished;
import static com.moego.client.api.v1.appointment.utils.AppointmentUtil.buildFilterForUpcoming;
import static com.moego.client.api.v1.appointment.utils.AppointmentUtil.buildPaginationRequest;
import static com.moego.client.api.v1.appointment.utils.AppointmentUtil.buildRequest;
import static com.moego.client.api.v1.online_booking.utils.BookingRequestUtil.buildListBookingRequestsRequest;
import static java.util.concurrent.CompletableFuture.supplyAsync;

import com.moego.client.api.v1.appointment.FutureService;
import com.moego.client.api.v1.appointment.service.AppointmentCompositeService;
import com.moego.client.api.v1.converter.DateConverter;
import com.moego.client.api.v1.enterprise.service.CompanyService;
import com.moego.client.api.v1.online_booking.converter.AppointmentConverter;
import com.moego.client.api.v1.online_booking.converter.BookingRequestConverter;
import com.moego.client.api.v1.online_booking.converter.BusinessPetConverter;
import com.moego.client.api.v1.online_booking.converter.ServiceConverter;
import com.moego.client.api.v1.online_booking.service.AppointmentService;
import com.moego.client.api.v1.online_booking.service.AvailabilityService;
import com.moego.client.api.v1.online_booking.service.BookingService;
import com.moego.client.api.v1.online_booking.service.ContextService;
import com.moego.client.api.v1.online_booking.service.FeedingMedicationService;
import com.moego.client.api.v1.online_booking.service.LodgingService;
import com.moego.client.api.v1.online_booking.utils.LodgingUtil;
import com.moego.client.api.v1.online_booking.utils.PaymentUtil;
import com.moego.client.api.v1.payment.service.OrderService;
import com.moego.client.api.v1.shared.util.ProtobufUtil;
import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.idl.client.online_booking.v1.AppointmentCardItem;
import com.moego.idl.client.online_booking.v1.AppointmentCardType;
import com.moego.idl.client.online_booking.v1.AppointmentPaymentItem;
import com.moego.idl.client.online_booking.v1.AppointmentServiceGrpc;
import com.moego.idl.client.online_booking.v1.AppointmentSummaryItem;
import com.moego.idl.client.online_booking.v1.CancelAppointmentParams;
import com.moego.idl.client.online_booking.v1.CancelAppointmentResult;
import com.moego.idl.client.online_booking.v1.GetAppointmentDetailParams;
import com.moego.idl.client.online_booking.v1.GetAppointmentDetailResult;
import com.moego.idl.client.online_booking.v1.GetPriorityAppointmentCardParams;
import com.moego.idl.client.online_booking.v1.GetPriorityAppointmentCardResult;
import com.moego.idl.client.online_booking.v1.IsAvailableForRescheduleParams;
import com.moego.idl.client.online_booking.v1.IsAvailableForRescheduleResult;
import com.moego.idl.client.online_booking.v1.ListAppointmentsParams;
import com.moego.idl.client.online_booking.v1.ListAppointmentsResult;
import com.moego.idl.client.online_booking.v1.ListEvaluationsParams;
import com.moego.idl.client.online_booking.v1.ListEvaluationsResult;
import com.moego.idl.client.online_booking.v1.PetAndServicesSummaryItem;
import com.moego.idl.client.online_booking.v1.ReschedulePetFeedingMedicationParams;
import com.moego.idl.client.online_booking.v1.ReschedulePetFeedingMedicationResult;
import com.moego.idl.client.online_booking.v1.UpdateAppointmentParams;
import com.moego.idl.client.online_booking.v1.UpdateAppointmentResult;
import com.moego.idl.models.activity_log.v1.Resource;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentNoShowStatus;
import com.moego.idl.models.appointment.v1.AppointmentPaymentStatus;
import com.moego.idl.models.appointment.v1.AppointmentPetFeedingScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentPetMedicationScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.AppointmentUpdatedBy;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.PetScheduleDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.online_booking.v1.BoardingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceAvailabilityModel;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.online_booking.v1.CapacityOverrideModel;
import com.moego.idl.models.online_booking.v1.DaycareAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceAvailabilityModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.LodgingAvailabilityDef;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.service.activity_log.v1.ActivityLogServiceGrpc;
import com.moego.idl.service.activity_log.v1.CreateActivityLogRequest;
import com.moego.idl.service.appointment.v1.AppointmentScheduleServiceGrpc.AppointmentScheduleServiceBlockingStub;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc.AppointmentServiceBlockingStub;
import com.moego.idl.service.appointment.v1.CancelAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentsRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentsResponse;
import com.moego.idl.service.offering.v1.GetServiceDetailRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc.BookingRequestServiceBlockingStub;
import com.moego.idl.service.online_booking.v1.GetBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestStatusRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.client.IBookOnlineDepositClient;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.dto.appointment.history.ChangeTimeLogDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/11/20
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class AppointmentServer extends AppointmentServiceGrpc.AppointmentServiceImplBase {

    private final ContextService contextService;

    private final AppointmentServiceBlockingStub appointmentStub;
    private final BookingRequestServiceBlockingStub bookingRequestStub;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceBlockingStub;
    private final AppointmentScheduleServiceBlockingStub appointmentScheduleService;

    private final FutureService futureService;

    private final CompanyService companyService;
    private final AppointmentCompositeService appointmentCompositeService;
    private final AppointmentService appointmentService;
    private final FeedingMedicationService feedingMedicationService;
    private final AvailabilityService availabilityService;
    private final LodgingService lodgingService;
    private final BookingService bookingService;
    private final OrderService orderService;
    private final IBookOnlineDepositClient bookOnlineDepositClient;
    private final ActivityLogServiceGrpc.ActivityLogServiceBlockingStub activityLogService;

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void getPriorityAppointmentCard(
            GetPriorityAppointmentCardParams request,
            StreamObserver<GetPriorityAppointmentCardResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());

        var inProgressResult =
                supplyAsync(() -> getPriorityAppointmentCardResult(ctx, AppointmentCardType.IN_PROGRESS));
        var upcomingResult = supplyAsync(() -> getPriorityAppointmentCardResult(ctx, AppointmentCardType.UPCOMING));
        var pendingResult = supplyAsync(() -> getPriorityPendingCardResult(ctx));
        var lastFinishedResult = supplyAsync(() -> getPriorityAppointmentCardResult(ctx, AppointmentCardType.LAST));

        CompletableFuture.allOf(inProgressResult, upcomingResult, pendingResult, lastFinishedResult)
                .join();

        var result = Stream.of(
                        Map.entry(AppointmentCardType.IN_PROGRESS, inProgressResult.join()),
                        Map.entry(AppointmentCardType.UPCOMING, upcomingResult.join()),
                        Map.entry(AppointmentCardType.PENDING, pendingResult.join()),
                        Map.entry(AppointmentCardType.LAST, lastFinishedResult.join()))
                .filter(entry -> entry.getValue().getCount() != 0)
                .sorted((e1, e2) ->
                        Integer.compare(e2.getKey().getNumber(), e1.getKey().getNumber()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(GetPriorityAppointmentCardResult.getDefaultInstance());

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    private GetPriorityAppointmentCardResult getPriorityAppointmentCardResult(
            BaseBusinessCustomerIdDTO ctx, AppointmentCardType cardType) {
        var zoneId = companyService.getZoneId(ctx.getCompanyId());
        var pairAndOrderBys = buildFilterAndOrderBys(ctx.getCustomerId(), cardType, zoneId);

        var filter = pairAndOrderBys.key();
        var orderBys = pairAndOrderBys.value();

        var pagination = buildSinglePagination();

        var request = buildPaginationRequest(ctx, orderBys, pagination, filter);

        var response = appointmentStub.listAppointments(request);

        var appointment = CollectionUtils.firstElement(response.getAppointmentsList());

        if (appointment == null) {
            return GetPriorityAppointmentCardResult.getDefaultInstance();
        }

        var allPetDetailResult = futureService
                .listAllPetDetails(ctx.getCompanyId(), List.of(appointment.getId()))
                .join();
        var allPetDetails = allPetDetailResult.key();
        var allEvaluations = allPetDetailResult.value();

        return GetPriorityAppointmentCardResult.newBuilder()
                .setCard(AppointmentCardItem.newBuilder()
                        .setCardType(cardType)
                        .setAppointment(AppointmentConverter.INSTANCE.toAppointmentSummary(appointment))
                        .addAllPetAndServices(
                                buildAppointmentIdToPetAndServices(List.of(appointment), allPetDetails, allEvaluations)
                                        .getOrDefault(appointment.getId(), List.of()))
                        .build())
                .setCount(response.getPagination().getTotal())
                .build();
    }

    private GetPriorityAppointmentCardResult getPriorityPendingCardResult(BaseBusinessCustomerIdDTO ctx) {
        var request =
                buildListBookingRequestsRequest(ctx, buildSinglePagination(), buildBookingRequestOrderBy(List.of()));
        var response = bookingRequestStub.listBookingRequests(request);
        var bookingRequest = CollectionUtils.firstElement(response.getBookingRequestsList());

        if (bookingRequest == null) {
            return GetPriorityAppointmentCardResult.getDefaultInstance();
        }

        return GetPriorityAppointmentCardResult.newBuilder()
                .setCard(AppointmentCardItem.newBuilder()
                        .setCardType(AppointmentCardType.PENDING)
                        .setAppointment(BookingRequestConverter.INSTANCE.toAppointmentSummary(bookingRequest))
                        .addAllPetAndServices(buildBookingRequestIdToPetAndServices(List.of(bookingRequest))
                                .getOrDefault(bookingRequest.getId(), List.of())))
                .setCount(response.getPagination().getTotal())
                .build();
    }

    private static PaginationRequest buildSinglePagination() {
        return PaginationRequest.newBuilder().setPageNum(1).setPageSize(1).build();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listAppointments(
            ListAppointmentsParams request, StreamObserver<ListAppointmentsResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());

        var result =
                switch (request.getFilter().getAppointmentType()) {
                    case PENDING -> listPendingAppointmentsResult(ctx, request);
                    case UPCOMING, PAST, CANCELED -> listAppointmentsResult(ctx, request);
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown appointment type");
                };

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    private ListAppointmentsResult listAppointmentsResult(
            BaseBusinessCustomerIdDTO ctx, ListAppointmentsParams params) {

        var response = listAppointments(ctx, params);

        if (response.getAppointmentsCount() == 0) {
            return ListAppointmentsResult.newBuilder()
                    .addAllAppointments(List.of())
                    .setPagination(response.getPagination())
                    .build();
        }

        var appointments = response.getAppointmentsList();

        var allPetDetailResult = futureService
                .listAllPetDetails(
                        ctx.getCompanyId(),
                        appointments.stream().map(AppointmentModel::getId).toList())
                .join();
        var allPetDetails = allPetDetailResult.key();
        var allEvaluations = allPetDetailResult.value();

        var appointmentIdToAppointmentMap = buildAppointmentIdToAppointment(appointments);

        var appointmentIdToPetAndServicesMap =
                buildAppointmentIdToPetAndServices(appointments, allPetDetails, allEvaluations);

        var appointmentIdToPaymentMap = buildAppointmentIdToPayment(appointments, allEvaluations);

        var cardItems = appointments.stream()
                .map(appointment -> ListAppointmentsResult.AppointmentListItem.newBuilder()
                        .setAppointment(appointmentIdToAppointmentMap.get(appointment.getId()))
                        .addAllPetAndServices(
                                appointmentIdToPetAndServicesMap.getOrDefault(appointment.getId(), List.of()))
                        .setPayment(appointmentIdToPaymentMap.getOrDefault(
                                appointment.getId(), AppointmentPaymentItem.getDefaultInstance()))
                        .build())
                .toList();

        return ListAppointmentsResult.newBuilder()
                .addAllAppointments(cardItems)
                .setPagination(response.getPagination())
                .build();
    }

    private ListAppointmentsResponse listAppointments(BaseBusinessCustomerIdDTO ctx, ListAppointmentsParams params) {
        var zoneId = companyService.getZoneId(ctx.getCompanyId());
        var orderBys = buildAppointmentOrderBy(params.getSortsList());
        var pagination = params.getPagination();

        return switch (params.getFilter().getAppointmentType()) {
            case UPCOMING -> {
                var filter = buildFilterForUpcoming(ctx.getCustomerId(), zoneId);
                var request = buildPaginationRequest(ctx, orderBys, pagination, filter);
                yield appointmentStub.listAppointments(request);
            }
            case PAST -> {
                var finishedRequest = buildRequest(ctx, buildFilterForFinished(ctx.getCustomerId()));
                var delayedRequest = buildRequest(ctx, buildFilterForDelayed(ctx.getCustomerId(), zoneId));
                yield appointmentCompositeService.listPastAppointments(
                        finishedRequest, delayedRequest, orderBys, pagination);
            }
            case CANCELED -> {
                var request =
                        buildPaginationRequest(ctx, orderBys, pagination, buildFilterForCanceled(ctx.getCustomerId()));
                yield appointmentStub.listAppointments(request);
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown appointment type");
        };
    }

    private static Map<Long, AppointmentSummaryItem> buildAppointmentIdToAppointment(
            List<AppointmentModel> appointments) {
        return appointments.stream()
                .map(AppointmentConverter.INSTANCE::toAppointmentSummary)
                .collect(Collectors.toMap(AppointmentSummaryItem::getAppointmentId, Function.identity(), (a, b) -> a));
    }

    private Map<Long, List<PetAndServicesSummaryItem>> buildAppointmentIdToPetAndServices(
            List<AppointmentModel> appointments,
            List<PetDetailModel> serviceDetails,
            List<EvaluationServiceModel> evaluationDetails) {
        var appointmentIds = extractDistinctIds(appointments, AppointmentModel::getId);

        var serviceIds = extractDistinctIds(serviceDetails, PetDetailModel::getServiceId);
        var evaluationIds = extractDistinctIds(evaluationDetails, EvaluationServiceModel::getServiceId);
        var petIds = extractPetIds(serviceDetails, evaluationDetails);

        var serviceResult = futureService.listServices(serviceIds);
        var evaluationResult = futureService.listEvaluations(evaluationIds);
        var petResult = futureService.listPets(petIds);

        var petIdToInfoMap = petResult.join();
        var serviceIdToInfoMap = serviceResult.join();
        var evaluationIdToInfoMap = evaluationResult.join();

        var appointmentIdToPetDetailsMap =
                serviceDetails.stream().collect(Collectors.groupingBy(PetDetailModel::getGroomingId));
        var appointmentIdToEvaluationsMap =
                evaluationDetails.stream().collect(Collectors.groupingBy(EvaluationServiceModel::getAppointmentId));

        return appointmentIds.stream().collect(Collectors.toMap(appointmentId -> appointmentId, appointmentId -> {
            var petDetails = appointmentIdToPetDetailsMap.getOrDefault(appointmentId, List.of());
            var evaluations = appointmentIdToEvaluationsMap.getOrDefault(appointmentId, List.of());

            return buildPetAndServicesSummaryItems(
                    petDetails, evaluations, petIdToInfoMap, serviceIdToInfoMap, evaluationIdToInfoMap);
        }));
    }

    private Map<Long, AppointmentPaymentItem> buildAppointmentIdToPayment(
            List<AppointmentModel> appointments, List<EvaluationServiceModel> evaluationServices) {
        var appointmentIds = extractDistinctIds(appointments, AppointmentModel::getId);

        var appointmentIdToOrderDetailMap = futureService
                .listOrders(appointmentIds, OrderSourceType.APPOINTMENT)
                .join();

        var appointmentIdToEvaluationServices =
                evaluationServices.stream().collect(Collectors.groupingBy(EvaluationServiceModel::getAppointmentId));

        return appointments.stream().collect(Collectors.toMap(AppointmentModel::getId, appointment -> {
            var orderDetail = appointmentIdToOrderDetailMap.get(appointment.getId());
            var builder = AppointmentPaymentItem.newBuilder().setPaymentStatus(appointment.getIsPaid());
            if (orderDetail == null) {
                // No order found for appointment, return with default payment status
                return builder.build();
            }
            var order = orderDetail.getOrder();
            var evaluations = appointmentIdToEvaluationServices.getOrDefault(appointment.getId(), List.of());
            return builder.setEstimatedTotalPrice(order.getSubTotalAmount())
                    .setTotalAmount(order.getTotalAmount())
                    .setEvaluationEstimatedTotalPrice(
                            calEvaluationPriceForAppointment(evaluations).doubleValue())
                    .build();
        }));
    }

    private static <T, R> List<R> extractDistinctIds(List<T> items, Function<T, R> mapper) {
        return items.stream().map(mapper).distinct().toList();
    }

    private static List<Long> extractPetIds(List<PetDetailModel> petDetails, List<EvaluationServiceModel> evaluations) {
        return Stream.of(
                        petDetails.stream().map(PetDetailModel::getPetId),
                        evaluations.stream().map(EvaluationServiceModel::getPetId))
                .flatMap(Function.identity())
                .distinct()
                .toList();
    }

    private static Map<Long, ServiceItemType> extractPetMainCareType(
            List<PetDetailModel> petDetails,
            List<EvaluationServiceModel> evaluations,
            Map<Long, ServiceBriefView> serviceIdToInfoMap) {
        Map<Long, List<Integer>> petCareTypes = new HashMap<>();
        for (var petDetail : petDetails) {
            var service = serviceIdToInfoMap.get(petDetail.getServiceId());
            if (service == null || service.getType() == ServiceType.ADDON) {
                continue;
            }
            petCareTypes
                    .computeIfAbsent(petDetail.getPetId(), k -> new ArrayList<>())
                    .add(petDetail.getServiceItemType().getNumber());
        }
        for (var evaluation : evaluations) {
            petCareTypes
                    .computeIfAbsent(evaluation.getPetId(), k -> new ArrayList<>())
                    .add(ServiceItemType.EVALUATION_VALUE);
        }
        return petCareTypes.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey, entry -> AppointmentConverter.INSTANCE.getMainCareType(entry.getValue())));
    }

    private static List<PetAndServicesSummaryItem> buildPetAndServicesSummaryItems(
            List<PetDetailModel> petDetails,
            List<EvaluationServiceModel> evaluations,
            Map<Long, BusinessCustomerPetInfoModel> petIdToInfoMap,
            Map<Long, ServiceBriefView> serviceIdToInfoMap,
            Map<Long, EvaluationBriefView> evaluationIdToInfoMap) {
        var petIdToSummaryMap = new HashMap<Long, PetAndServicesSummaryItem.Builder>();

        for (var petDetail : petDetails) {
            var builder =
                    petIdToSummaryMap.computeIfAbsent(petDetail.getPetId(), id -> PetAndServicesSummaryItem.newBuilder()
                            .setPet(BusinessPetConverter.INSTANCE.toPetSummary(petIdToInfoMap.get(id))));
            var serviceBriefView = serviceIdToInfoMap.get(petDetail.getServiceId());
            if (serviceBriefView != null) {
                var serviceSummaryBuilder = ServiceConverter.INSTANCE.toServiceSummary(serviceBriefView).toBuilder();
                if (StringUtils.hasText(petDetail.getStartDate())) {
                    serviceSummaryBuilder.setStartDate(ProtobufUtil.toProtobufDate(petDetail.getStartDate()));
                }
                if (StringUtils.hasText(petDetail.getEndDate())) {
                    serviceSummaryBuilder.setEndDate(ProtobufUtil.toProtobufDate(petDetail.getEndDate()));
                }
                serviceSummaryBuilder.setStartTime(petDetail.getStartTime());
                serviceSummaryBuilder.setEndTime(petDetail.getEndTime());

                builder.addServices(serviceSummaryBuilder.build());
            }
        }

        for (var evaluation : evaluations) {
            var builder = petIdToSummaryMap.computeIfAbsent(
                    evaluation.getPetId(), id -> PetAndServicesSummaryItem.newBuilder()
                            .setPet(BusinessPetConverter.INSTANCE.toPetSummary(petIdToInfoMap.get(id))));
            var evaluationView = evaluationIdToInfoMap.get(evaluation.getServiceId());
            if (evaluationView != null) {
                var serviceSummaryBuilder = ServiceConverter.INSTANCE.toServiceSummary(evaluationView).toBuilder();
                if (StringUtils.hasText(evaluation.getStartDate())) {
                    serviceSummaryBuilder.setStartDate(ProtobufUtil.toProtobufDate(evaluation.getStartDate()));
                }
                if (StringUtils.hasText(evaluation.getEndDate())) {
                    serviceSummaryBuilder.setEndDate(ProtobufUtil.toProtobufDate(evaluation.getEndDate()));
                }
                serviceSummaryBuilder.setStartTime(evaluation.getStartTime());
                serviceSummaryBuilder.setEndTime(evaluation.getEndTime());

                builder.addServices(serviceSummaryBuilder.build());
            }
        }

        // Filter main service for each pet
        return petIdToSummaryMap.values().stream()
                .map(item -> {
                    var mainService = getMainService(item.getServicesList());
                    return item.clearServices().addServices(mainService).build();
                })
                .toList();
    }

    private static PetAndServicesSummaryItem.ServiceItem getMainService(
            List<PetAndServicesSummaryItem.ServiceItem> serviceItems) {
        var serviceItemTypes = serviceItems.stream()
                .map(PetAndServicesSummaryItem.ServiceItem::getCareType)
                .map(ServiceItemType::getNumber)
                .collect(Collectors.toSet());

        var mainServiceItemType =
                ServiceItemEnum.getMainServiceItemType(ServiceItemEnum.convertBitValueList(serviceItemTypes));

        return serviceItems.stream()
                .filter(service -> Objects.equals(service.getCareTypeValue(), mainServiceItemType.getServiceItem()))
                .findFirst()
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "No service found"));
    }

    private ListAppointmentsResult listPendingAppointmentsResult(
            BaseBusinessCustomerIdDTO ctx, ListAppointmentsParams params) {
        var request = buildListBookingRequestsRequest(
                ctx, params.getPagination(), buildBookingRequestOrderBy(params.getSortsList()));
        var response = bookingRequestStub.listBookingRequests(request);

        var bookingRequests = response.getBookingRequestsList();

        var bookingRequestIdToAppointmentMap = buildBookingRequestIdToAppointment(bookingRequests);

        var bookingRequestIdToPetAndServicesMap = buildBookingRequestIdToPetAndServices(bookingRequests);

        var bookingRequestIdToPaymentMap = buildBookingRequestIdToPayment(bookingRequests);

        var appointments = bookingRequests.stream()
                .map(bookingRequest -> ListAppointmentsResult.AppointmentListItem.newBuilder()
                        .setAppointment(bookingRequestIdToAppointmentMap.get(bookingRequest.getId()))
                        .addAllPetAndServices(
                                bookingRequestIdToPetAndServicesMap.getOrDefault(bookingRequest.getId(), List.of()))
                        .setPayment(bookingRequestIdToPaymentMap.get(bookingRequest.getId()))
                        .build())
                .toList();

        return ListAppointmentsResult.newBuilder()
                .addAllAppointments(appointments)
                .setPagination(response.getPagination())
                .build();
    }

    private static Map<Long, AppointmentSummaryItem> buildBookingRequestIdToAppointment(
            List<BookingRequestModel> bookingRequests) {
        return bookingRequests.stream()
                .map(BookingRequestConverter.INSTANCE::toAppointmentSummary)
                .collect(Collectors.toMap(
                        AppointmentSummaryItem::getBookingRequestId, Function.identity(), (a, b) -> a));
    }

    private Map<Long, List<PetAndServicesSummaryItem>> buildBookingRequestIdToPetAndServices(
            List<BookingRequestModel> bookingRequests) {
        var petIds = extractPetIds(bookingRequests);
        var serviceIds = extractServiceIds(bookingRequests);
        var evaluationIds = extractEvaluationIds(bookingRequests);

        var petResult = futureService.listPets(petIds);
        var serviceResult = futureService.listServices(serviceIds);
        var evaluationResult = futureService.listEvaluations(evaluationIds);

        var petIdToInfoMap = petResult.join();
        var serviceIdToInfoMap = serviceResult.join();
        var evaluationIdToInfoMap = evaluationResult.join();

        return bookingRequests.stream()
                .collect(Collectors.toMap(
                        BookingRequestModel::getId,
                        bookingRequest -> buildPetAndServicesSummaryItems(
                                bookingRequest, petIdToInfoMap, serviceIdToInfoMap, evaluationIdToInfoMap)));
    }

    private static Map<Long, AppointmentPaymentItem> buildBookingRequestIdToPayment(
            List<BookingRequestModel> bookingRequests) {
        return bookingRequests.stream().collect(Collectors.toMap(BookingRequestModel::getId, bookingRequest -> {
            var total = PaymentUtil.calculateEstimatedTotalPrice(bookingRequest.getServicesList());
            return AppointmentPaymentItem.newBuilder()
                    .setEstimatedTotalPrice(total)
                    .setTotalAmount(total)
                    .setPaymentStatus(AppointmentPaymentStatus.UNPAID)
                    .setEvaluationEstimatedTotalPrice(
                            calEvaluationPriceForBookingRequest(bookingRequest.getServicesList())
                                    .doubleValue())
                    .build();
        }));
    }

    private static List<PetAndServicesSummaryItem> buildPetAndServicesSummaryItems(
            BookingRequestModel bookingRequest,
            Map<Long, BusinessCustomerPetInfoModel> petIdToInfoMap,
            Map<Long, ServiceBriefView> serviceIdToInfoMap,
            Map<Long, EvaluationBriefView> evaluationIdToInfoMap) {

        var petIdToMainServiceMap = getMainServicesByPet(bookingRequest);

        return petIdToMainServiceMap.entrySet().stream()
                .map(entry -> {
                    var petId = entry.getKey();
                    var mainService = entry.getValue();
                    return switch (mainService.getServiceCase()) {
                        case BOARDING -> {
                            var serviceId =
                                    mainService.getBoarding().getService().getServiceId();
                            yield buildPetAndServicesSummaryItem(
                                    petIdToInfoMap.get(petId),
                                    serviceIdToInfoMap.get(serviceId),
                                    getServiceDetail(bookingRequest, petId, serviceId));
                        }
                        case DAYCARE -> {
                            var serviceId =
                                    mainService.getDaycare().getService().getServiceId();
                            yield buildPetAndServicesSummaryItem(
                                    petIdToInfoMap.get(petId),
                                    serviceIdToInfoMap.get(serviceId),
                                    getServiceDetail(bookingRequest, petId, serviceId));
                        }
                        case GROOMING -> {
                            var serviceId =
                                    mainService.getGrooming().getService().getServiceId();
                            yield buildPetAndServicesSummaryItem(
                                    petIdToInfoMap.get(petId),
                                    serviceIdToInfoMap.get(serviceId),
                                    getServiceDetail(bookingRequest, petId, serviceId));
                        }
                        case EVALUATION -> {
                            var evaluationId =
                                    mainService.getEvaluation().getService().getEvaluationId();
                            yield buildPetAndServicesSummaryItem(
                                    petIdToInfoMap.get(petId),
                                    evaluationIdToInfoMap.get(evaluationId),
                                    getEvaluationDetail(bookingRequest, petId, evaluationId));
                        }
                        case DOG_WALKING -> {
                            var serviceId =
                                    mainService.getDogWalking().getService().getServiceId();
                            yield buildPetAndServicesSummaryItem(
                                    petIdToInfoMap.get(petId),
                                    serviceIdToInfoMap.get(serviceId),
                                    getServiceDetail(bookingRequest, petId, serviceId));
                        }
                        default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown care type");
                    };
                })
                .toList();
    }

    @Nullable
    private static BookingRequestModel.Service getServiceDetail(
            BookingRequestModel bookingRequest, long petId, long serviceId) {
        return bookingRequest.getServicesList().stream()
                .filter(service -> switch (service.getServiceCase()) {
                    case GROOMING -> service.getGrooming().getService().getPetId() == petId
                            && service.getGrooming().getService().getServiceId() == serviceId;
                    case BOARDING -> service.getBoarding().getService().getPetId() == petId
                            && service.getBoarding().getService().getServiceId() == serviceId;
                    case DAYCARE -> service.getDaycare().getService().getPetId() == petId
                            && service.getDaycare().getService().getServiceId() == serviceId;
                    case DOG_WALKING -> service.getDogWalking().getService().getPetId() == petId
                            && service.getDogWalking().getService().getServiceId() == serviceId;
                    case GROUP_CLASS -> service.getGroupClass().getService().getPetId() == petId
                            && service.getGroupClass().getService().getServiceId() == serviceId;
                    default -> false;
                })
                .findFirst()
                .orElse(null);
    }

    @Nullable
    private static BookingRequestModel.Service getEvaluationDetail(
            BookingRequestModel bookingRequest, long petId, long evaluationId) {
        return bookingRequest.getServicesList().stream()
                .filter(service -> service.hasEvaluation()
                        && service.getEvaluation().getService().getPetId() == petId
                        && service.getEvaluation().getService().getEvaluationId() == evaluationId)
                .findFirst()
                .orElse(null);
    }

    private static Map<Long, BookingRequestModel.Service> getMainServicesByPet(BookingRequestModel bookingRequest) {
        // Group services by pet ID first
        var petIdToServices = bookingRequest.getServicesList().stream()
                .collect(Collectors.groupingBy(AppointmentServer::extractPetId));

        // For each pet, find its main service
        return petIdToServices.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> findMainService(entry.getValue())));
    }

    private static BookingRequestModel.Service findMainService(List<BookingRequestModel.Service> serviceDetails) {
        return serviceDetails.stream()
                .min(Comparator.comparingInt(AppointmentServer::getServicePriority))
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "No main service found"));
    }

    /**
     * 优先级参考 {@link ServiceItemEnum#getMainServiceItemType(Integer)}
     */
    private static int getServicePriority(BookingRequestModel.Service service) {
        return switch (service.getServiceCase()) {
            case BOARDING -> 10;
            case DAYCARE -> 20;
            case EVALUATION -> 30;
            case DOG_WALKING -> 40;
            case GROUP_CLASS -> 50;
            case GROOMING -> 60;
            default -> Integer.MAX_VALUE;
        };
    }

    private static PetAndServicesSummaryItem buildPetAndServicesSummaryItem(
            BusinessCustomerPetInfoModel petInfo,
            ServiceBriefView service,
            @Nullable BookingRequestModel.Service serviceDetail) {
        var serviceSummaryBuilder = ServiceConverter.INSTANCE.toServiceSummary(service).toBuilder();
        if (serviceDetail != null) {
            setDateAndTimeForBookingRequest(serviceDetail, serviceSummaryBuilder);
        }
        return PetAndServicesSummaryItem.newBuilder()
                .setPet(BusinessPetConverter.INSTANCE.toPetSummary(petInfo))
                .addServices(serviceSummaryBuilder.build())
                .build();
    }

    private static PetAndServicesSummaryItem buildPetAndServicesSummaryItem(
            BusinessCustomerPetInfoModel petInfo,
            EvaluationBriefView evaluation,
            @Nullable BookingRequestModel.Service evaluationDetail) {
        var serviceSummaryBuilder = ServiceConverter.INSTANCE.toServiceSummary(evaluation).toBuilder();
        if (evaluationDetail != null) {
            setDateAndTimeForBookingRequest(evaluationDetail, serviceSummaryBuilder);
        }
        return PetAndServicesSummaryItem.newBuilder()
                .setPet(BusinessPetConverter.INSTANCE.toPetSummary(petInfo))
                .addServices(serviceSummaryBuilder.build())
                .build();
    }

    private static void setDateAndTimeForBookingRequest(
            BookingRequestModel.Service evaluationDetail,
            PetAndServicesSummaryItem.ServiceItem.Builder serviceSummaryBuilder) {
        switch (evaluationDetail.getServiceCase()) {
            case GROOMING -> {
                var serviceDetail = evaluationDetail.getGrooming().getService();
                if (StringUtils.hasText(serviceDetail.getStartDate())) {
                    serviceSummaryBuilder.setStartDate(ProtobufUtil.toProtobufDate(serviceDetail.getStartDate()));
                }
                if (StringUtils.hasText(serviceDetail.getEndDate())) {
                    serviceSummaryBuilder.setEndDate(ProtobufUtil.toProtobufDate(serviceDetail.getEndDate()));
                }
                if (serviceDetail.hasStartTime()) {
                    serviceSummaryBuilder.setStartTime(serviceDetail.getStartTime());
                }
                if (serviceDetail.hasEndTime()) {
                    serviceSummaryBuilder.setEndTime(serviceDetail.getEndTime());
                }
            }
            case BOARDING -> {
                var serviceDetail = evaluationDetail.getBoarding().getService();
                if (StringUtils.hasText(serviceDetail.getStartDate())) {
                    serviceSummaryBuilder.setStartDate(ProtobufUtil.toProtobufDate(serviceDetail.getStartDate()));
                }
                if (StringUtils.hasText(serviceDetail.getEndDate())) {
                    serviceSummaryBuilder.setEndDate(ProtobufUtil.toProtobufDate(serviceDetail.getEndDate()));
                }
                serviceSummaryBuilder.setStartTime(serviceDetail.getStartTime());
                serviceSummaryBuilder.setEndTime(serviceDetail.getEndTime());
            }
            case DAYCARE -> {
                var serviceDetail = evaluationDetail.getDaycare().getService();
                if (serviceDetail.getSpecificDatesCount() > 0) {
                    var dates = serviceDetail.getSpecificDatesList().stream()
                            .sorted()
                            .toList();
                    serviceSummaryBuilder.setStartDate(ProtobufUtil.toProtobufDate(dates.get(0)));
                    serviceSummaryBuilder.setEndDate(ProtobufUtil.toProtobufDate(dates.get(dates.size() - 1)));
                }
                serviceSummaryBuilder.setStartTime(serviceDetail.getStartTime());
                serviceSummaryBuilder.setEndTime(serviceDetail.getEndTime());
            }
            case EVALUATION -> {
                var serviceDetail = evaluationDetail.getEvaluation().getService();
                if (StringUtils.hasText(serviceDetail.getStartDate())) {
                    serviceSummaryBuilder.setStartDate(ProtobufUtil.toProtobufDate(serviceDetail.getStartDate()));
                }
                if (StringUtils.hasText(serviceDetail.getEndDate())) {
                    serviceSummaryBuilder.setEndDate(ProtobufUtil.toProtobufDate(serviceDetail.getEndDate()));
                }
                serviceSummaryBuilder.setStartTime(serviceDetail.getStartTime());
                serviceSummaryBuilder.setEndTime(serviceDetail.getEndTime());
            }
            case DOG_WALKING -> {
                var serviceDetail = evaluationDetail.getDogWalking().getService();
                if (StringUtils.hasText(serviceDetail.getStartDate())) {
                    serviceSummaryBuilder.setStartDate(ProtobufUtil.toProtobufDate(serviceDetail.getStartDate()));
                }
                if (StringUtils.hasText(serviceDetail.getEndDate())) {
                    serviceSummaryBuilder.setEndDate(ProtobufUtil.toProtobufDate(serviceDetail.getEndDate()));
                }
                serviceSummaryBuilder.setStartTime(serviceDetail.getStartTime());
                serviceSummaryBuilder.setEndTime(serviceDetail.getEndTime());
            }
            case GROUP_CLASS -> {
                var serviceDetail = evaluationDetail.getGroupClass().getService();
                if (serviceDetail.getSpecificDatesCount() > 0) {
                    var dates = serviceDetail.getSpecificDatesList().stream()
                            .sorted()
                            .toList();
                    serviceSummaryBuilder.setStartDate(ProtobufUtil.toProtobufDate(dates.get(0)));
                    serviceSummaryBuilder.setEndDate(ProtobufUtil.toProtobufDate(dates.get(dates.size() - 1)));
                }
                serviceSummaryBuilder.setStartTime(serviceDetail.getStartTime());
                serviceSummaryBuilder.setEndTime(serviceDetail.getEndTime());
            }
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Unsupported service type: " + evaluationDetail.getServiceCase());
        }
    }

    private static List<Long> extractPetIds(List<BookingRequestModel> bookingRequests) {
        return bookingRequests.stream()
                .flatMap(model -> model.getServicesList().stream())
                .map(AppointmentServer::extractPetId)
                .distinct()
                .toList();
    }

    private static Long extractPetId(BookingRequestModel.Service service) {
        return switch (service.getServiceCase()) {
            case GROOMING -> service.getGrooming().getService().getPetId();
            case BOARDING -> service.getBoarding().getService().getPetId();
            case DAYCARE -> service.getDaycare().getService().getPetId();
            case EVALUATION -> service.getEvaluation().getService().getPetId();
            case DOG_WALKING -> service.getDogWalking().getService().getPetId();
            case GROUP_CLASS -> service.getGroupClass().getService().getPetId();
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown care type");
        };
    }

    private static Integer extractServiceItemType(BookingRequestModel.Service service) {
        return switch (service.getServiceCase()) {
            case GROOMING -> ServiceItemType.GROOMING_VALUE;
            case BOARDING -> ServiceItemType.BOARDING_VALUE;
            case DAYCARE -> ServiceItemType.DAYCARE_VALUE;
            case EVALUATION -> ServiceItemType.EVALUATION_VALUE;
            case DOG_WALKING -> ServiceItemType.DOG_WALKING_VALUE;
            case GROUP_CLASS -> ServiceItemType.GROUP_CLASS_VALUE;
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown care type");
        };
    }

    private static List<Long> extractServiceIds(List<BookingRequestModel> bookingRequests) {
        return bookingRequests.stream()
                .flatMap(model -> model.getServicesList().stream())
                .flatMap(service -> switch (service.getServiceCase()) {
                    case GROOMING -> Stream.concat(
                            Stream.of(service.getGrooming().getService().getServiceId()),
                            service.getGrooming().getAddonsList().stream().map(GroomingAddOnDetailModel::getAddOnId));
                    case BOARDING -> Stream.concat(
                            Stream.of(service.getBoarding().getService().getServiceId()),
                            service.getBoarding().getAddonsList().stream().map(BoardingAddOnDetailModel::getAddOnId));
                    case DAYCARE -> Stream.concat(
                            Stream.of(service.getDaycare().getService().getServiceId()),
                            service.getDaycare().getAddonsList().stream().map(DaycareAddOnDetailModel::getAddOnId));
                    case DOG_WALKING -> Stream.of(
                            service.getDogWalking().getService().getServiceId()); // TODO dog walking add on
                    default -> Stream.empty();
                })
                .toList();
    }

    private static List<Long> extractEvaluationIds(List<BookingRequestModel> bookingRequests) {
        return bookingRequests.stream()
                .flatMap(model -> model.getServicesList().stream())
                .map(service -> service.getServiceCase() == BookingRequestModel.Service.ServiceCase.EVALUATION
                        ? service.getEvaluation().getService().getEvaluationId()
                        : null)
                .filter(Objects::nonNull)
                .toList();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void getAppointmentDetail(
            GetAppointmentDetailParams request, StreamObserver<GetAppointmentDetailResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());

        var result =
                switch (request.getIdCase()) {
                    case APPOINTMENT_ID -> getAppointmentDetailByAppointmentId(ctx, request.getAppointmentId());
                    case BOOKING_REQUEST_ID -> getAppointmentDetailByBookingRequestId(
                            ctx.getBusinessId(), request.getBookingRequestId());
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown id case");
                };

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    private GetAppointmentDetailResult getAppointmentDetailByAppointmentId(
            BaseBusinessCustomerIdDTO ctx, long appointmentId) {
        var appointment = appointmentStub
                .getAppointment(GetAppointmentRequest.newBuilder()
                        .setAppointmentId(appointmentId)
                        .build())
                .getAppointment();

        var feedingMedicationResult = futureService.listFeedingMedication(ctx.getCompanyId(), appointmentId);
        var allPetDetailResult = futureService
                .listAllPetDetails(ctx.getCompanyId(), List.of(appointmentId))
                .join();

        var allPetDetails = allPetDetailResult.key();
        var allEvaluations = allPetDetailResult.value();
        var serviceIds = extractDistinctIds(allPetDetails, PetDetailModel::getServiceId);
        var evaluationIds = extractDistinctIds(allEvaluations, EvaluationServiceModel::getServiceId);
        var petIds = extractPetIds(allPetDetails, allEvaluations);

        var serviceResult = futureService.listServices(serviceIds);
        var evaluationResult = futureService.listEvaluations(evaluationIds);
        var petResult = futureService.listPetFullInfos(petIds);

        var orderResult = futureService.listOrders(List.of(appointmentId), OrderSourceType.APPOINTMENT);

        var petIdToInfoMap = petResult.join();
        var serviceIdToInfoMap = serviceResult.join();
        var evaluationIdToInfoMap = evaluationResult.join();
        var orderIdToDetailMap = orderResult.join();

        BookOnlineDepositDTO obDeposit = bookOnlineDepositClient.getOBDepositByGroomingId(
                Math.toIntExact(ctx.getBusinessId()), (int) appointmentId);

        var appointmentIdToPetDetailsMap =
                allPetDetails.stream().collect(Collectors.groupingBy(PetDetailModel::getGroomingId));
        var appointmentIdToEvaluationsMap =
                allEvaluations.stream().collect(Collectors.groupingBy(EvaluationServiceModel::getAppointmentId));

        var petDetails = appointmentIdToPetDetailsMap.getOrDefault(appointmentId, List.of());
        var evaluations = appointmentIdToEvaluationsMap.getOrDefault(appointmentId, List.of());

        var petIdToFeedingsMap = feedingMedicationResult.join().key().stream()
                .collect(Collectors.toMap(PetScheduleDef::getPetId, PetScheduleDef::getFeedingsList));
        var petIdToMedicationMap = feedingMedicationResult.join().key().stream()
                .collect(Collectors.toMap(PetScheduleDef::getPetId, PetScheduleDef::getMedicationsList));

        var appointmentView = AppointmentConverter.INSTANCE.toAppointmentDetail(appointment);
        // 存在多只 pet 的 main care type 分别为 Daycare、Grooming 的情况。此时将预约的 main care type 设置为 Grooming
        if (appointmentView.getMainCareType() == ServiceItemType.DAYCARE) {
            boolean hasGroomingCareType =
                    extractPetMainCareType(petDetails, evaluations, serviceIdToInfoMap).values().stream()
                            .anyMatch(type -> type == ServiceItemType.GROOMING);
            if (hasGroomingCareType) {
                appointmentView = appointmentView.toBuilder()
                        .setMainCareType(ServiceItemType.GROOMING)
                        .build();
            }
        }

        return GetAppointmentDetailResult.newBuilder()
                .setAppointment(appointmentView)
                .addAllPetAndServices(buildPetAndServicesDetailItems(
                        petDetails,
                        evaluations,
                        petIdToInfoMap,
                        serviceIdToInfoMap,
                        evaluationIdToInfoMap,
                        petIdToFeedingsMap,
                        petIdToMedicationMap))
                .setPayment(buildPaymentItem(
                        appointment, allPetDetailResult.value(), orderIdToDetailMap.get(appointmentId), obDeposit))
                .build();
    }

    private static List<GetAppointmentDetailResult.PetAndServicesDetailItem> buildPetAndServicesDetailItems(
            List<PetDetailModel> petDetails,
            List<EvaluationServiceModel> evaluations,
            Map<Long, BusinessCustomerPetModel> petIdToInfoMap,
            Map<Long, ServiceBriefView> serviceIdToInfoMap,
            Map<Long, EvaluationBriefView> evaluationIdToInfoMap,
            Map<Long, List<AppointmentPetFeedingScheduleDef>> petIdToFeedingsMap,
            Map<Long, List<AppointmentPetMedicationScheduleDef>> petIdToMedicationsMap) {
        var petIdToDetailItemBuilderMap =
                new HashMap<Long, GetAppointmentDetailResult.PetAndServicesDetailItem.Builder>();
        var mainCareType = AppointmentConverter.INSTANCE.getMainCareType(
                petDetails.stream().map(PetDetailModel::getServiceItemTypeValue).toList());

        var petIdToServiceIdMap = petDetails.stream()
                .filter(p -> isBoardingDaycareService(serviceIdToInfoMap.get(p.getServiceId())))
                .collect(Collectors.groupingBy(
                        PetDetailModel::getPetId, Collectors.toMap(PetDetailModel::getServiceId, Function.identity())));
        petDetails.forEach(petDetail -> {
            var builder = petIdToDetailItemBuilderMap.computeIfAbsent(
                    petDetail.getPetId(), id -> GetAppointmentDetailResult.PetAndServicesDetailItem.newBuilder()
                            .setPet(BusinessPetConverter.INSTANCE.toPetDetail(petIdToInfoMap.get(id))));
            var service = serviceIdToInfoMap.get(petDetail.getServiceId());
            var serviceType = service.getType();
            switch (serviceType) {
                case SERVICE -> {
                    var serviceDetailItem = ServiceConverter.INSTANCE.toServiceDetail(petDetail, service);
                    if (service.getServiceItemType() == mainCareType) {
                        builder.addServices(serviceDetailItem.toBuilder()
                                .addAllFeedings(petIdToFeedingsMap.getOrDefault(petDetail.getPetId(), List.of()))
                                .addAllMedications(petIdToMedicationsMap.getOrDefault(petDetail.getPetId(), List.of()))
                                .build());
                    } else {
                        builder.addServices(serviceDetailItem);
                    }
                }
                case ADDON -> builder.addAddOns(
                        ServiceConverter.INSTANCE.toAddOnItem(service, petDetail, petIdToServiceIdMap, mainCareType));
                default -> {}
            }
        });

        for (var evaluationServiceDetail : evaluations) {
            var petId = evaluationServiceDetail.getPetId();
            var evaluationId = evaluationServiceDetail.getServiceId();
            var pet = petIdToInfoMap.get(petId);
            if (pet == null) {
                continue;
            }
            var evaluation = evaluationIdToInfoMap.get(evaluationId);
            if (evaluation == null) {
                continue;
            }

            var builder = petIdToDetailItemBuilderMap.computeIfAbsent(
                    petId, id -> GetAppointmentDetailResult.PetAndServicesDetailItem.newBuilder()
                            .setPet(BusinessPetConverter.INSTANCE.toPetDetail(pet)));

            var serviceDetailItemBuilder = ServiceConverter.INSTANCE.toEvaluationDetail(evaluation).toBuilder();
            if (StringUtils.hasText(evaluationServiceDetail.getStartDate())) {
                serviceDetailItemBuilder.setStartDate(evaluationServiceDetail.getStartDate());
            }
            if (StringUtils.hasText(evaluationServiceDetail.getEndDate())) {
                serviceDetailItemBuilder.setEndDate(evaluationServiceDetail.getEndDate());
            }
            serviceDetailItemBuilder.setStartTime(evaluationServiceDetail.getStartTime());
            serviceDetailItemBuilder.setEndTime(evaluationServiceDetail.getEndTime());

            builder.addServices(serviceDetailItemBuilder.build());
        }

        return petIdToDetailItemBuilderMap.values().stream()
                .map(GetAppointmentDetailResult.PetAndServicesDetailItem.Builder::build)
                .toList();
    }

    private static boolean isBoardingDaycareService(ServiceBriefView service) {
        return Objects.equals(service.getType(), ServiceType.SERVICE)
                && (Objects.equals(service.getServiceItemType(), ServiceItemType.BOARDING)
                        || Objects.equals(service.getServiceItemType(), ServiceItemType.DAYCARE));
    }

    private static List<GetAppointmentDetailResult.PetAndServicesDetailItem> buildPetAndServiceDetailItems(
            List<EvaluationServiceModel> evaluations,
            Map<Long, EvaluationBriefView> evaluationIdToInfoMap,
            Map<Long, BusinessCustomerPetModel> petIdToInfoMap) {
        return evaluations.stream()
                .map(evaluation -> GetAppointmentDetailResult.PetAndServicesDetailItem.newBuilder()
                        .setPet(BusinessPetConverter.INSTANCE.toPetDetail(petIdToInfoMap.get(evaluation.getPetId())))
                        .addServices(ServiceConverter.INSTANCE.toEvaluationDetail(
                                evaluationIdToInfoMap.get(evaluation.getServiceId())))
                        .build())
                .toList();
    }

    private static GetAppointmentDetailResult.PaymentItem buildPaymentItem(
            AppointmentModel appointment,
            List<EvaluationServiceModel> evaluationServiceModels,
            OrderDetailModel orderDetail,
            BookOnlineDepositDTO obDeposit) {
        var builder = GetAppointmentDetailResult.PaymentItem.newBuilder().setPaymentStatus(appointment.getIsPaid());
        if (orderDetail != null) {
            builder.setEstimatedTotalPrice(orderDetail.getOrder().getSubTotalAmount())
                    .setTotalAmount(orderDetail.getOrder().getTotalAmount())
                    .setPaidAmount(orderDetail.getOrder().getPaidAmount())
                    .setEvaluationEstimatedTotalPrice(calEvaluationPriceForAppointment(evaluationServiceModels)
                            .doubleValue());
        }
        if (obDeposit != null) {
            if (DepositPaymentTypeEnum.PrePay.equals(obDeposit.getDepositType())) {
                builder.setPrePayAmount(obDeposit.getAmount().doubleValue());
                builder.setPreAuthEnable(false);
            } else {
                builder.setPreAuthEnable(true);
            }
        }
        return builder.build();
    }

    private static BigDecimal calEvaluationPriceForAppointment(List<EvaluationServiceModel> evaluationServiceModels) {
        return evaluationServiceModels.stream()
                .map(e -> BigDecimal.valueOf(e.getServicePrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static BigDecimal calEvaluationPriceForBookingRequest(List<BookingRequestModel.Service> services) {
        return services.stream()
                .filter(BookingRequestModel.Service::hasEvaluation)
                .map(service ->
                        BigDecimal.valueOf(service.getEvaluation().getService().getServicePrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private GetAppointmentDetailResult getAppointmentDetailByBookingRequestId(long businessId, long bookingRequestId) {
        var bookingRequest = bookingRequestStub
                .getBookingRequest(GetBookingRequestRequest.newBuilder()
                        .setId(bookingRequestId)
                        .addAllAssociatedModels(List.of(
                                BookingRequestAssociatedModel.SERVICE,
                                BookingRequestAssociatedModel.ADD_ON,
                                BookingRequestAssociatedModel.FEEDING,
                                BookingRequestAssociatedModel.MEDICATION))
                        .build())
                .getBookingRequest();

        var order = orderService.getOrderDetail(
                businessId,
                bookingRequestId,
                com.moego.idl.models.order.v1.OrderSourceType.BOOKING_REQUEST
                        .name()
                        .toLowerCase());
        BookOnlineDepositDTO obDeposit =
                bookOnlineDepositClient.getOBDepositByBookingRequestId(Math.toIntExact(businessId), bookingRequestId);

        return GetAppointmentDetailResult.newBuilder()
                .setAppointment(buildAppointmentItem(bookingRequest))
                .addAllPetAndServices(buildPetAndServicesDetailItems(bookingRequest))
                .setPayment(buildPaymentItem(bookingRequest.getServicesList(), order, obDeposit))
                .build();
    }

    private static GetAppointmentDetailResult.AppointmentItem buildAppointmentItem(BookingRequestModel bookingRequest) {
        var builder = BookingRequestConverter.INSTANCE.toAppointmentDetail(bookingRequest).toBuilder();
        var checkInOutDateTimes =
                switch (builder.getMainCareType()) {
                    case DAYCARE -> buildDaycareCheckInOutDateTimes(bookingRequest.getServicesList());
                    case BOARDING -> List.of(buildBoardingCheckInOutDateTime(bookingRequest.getServicesList()));
                    case GROOMING -> List.of(buildGroomingCheckInOutDateTime(bookingRequest.getServicesList()));
                    case EVALUATION -> List.of(buildEvaluationCheckInOutDateTime(bookingRequest.getServicesList()));
                    case DOG_WALKING -> List.of(buildDogWalkingCheckInOutDateTime(bookingRequest.getServicesList()));
                    default -> new ArrayList<GetAppointmentDetailResult.CheckInOutDateTime>();
                };
        return builder.addAllCheckInOutDateTimes(checkInOutDateTimes).build();
    }

    private static List<GetAppointmentDetailResult.CheckInOutDateTime> buildDaycareCheckInOutDateTimes(
            List<BookingRequestModel.Service> services) {
        return services.stream()
                .filter(service ->
                        Objects.equals(service.getServiceCase(), BookingRequestModel.Service.ServiceCase.DAYCARE))
                .findFirst()
                .map(BookingRequestModel.Service::getDaycare)
                .map(BookingRequestModel.DaycareService::getService)
                .map(service -> service.getSpecificDatesList().stream()
                        .map(date -> GetAppointmentDetailResult.CheckInOutDateTime.newBuilder()
                                .setCheckInDate(date)
                                .setCheckInTime(service.getStartTime())
                                .setCheckOutDate(date)
                                .setCheckOutTime(service.getEndTime())
                                .build())
                        .toList())
                .orElse(List.of());
    }

    private static GetAppointmentDetailResult.CheckInOutDateTime buildBoardingCheckInOutDateTime(
            List<BookingRequestModel.Service> services) {
        return services.stream()
                .filter(service ->
                        Objects.equals(service.getServiceCase(), BookingRequestModel.Service.ServiceCase.BOARDING))
                .findFirst()
                .map(BookingRequestModel.Service::getBoarding)
                .map(BookingRequestModel.BoardingService::getService)
                .map(service -> GetAppointmentDetailResult.CheckInOutDateTime.newBuilder()
                        .setCheckInDate(service.getStartDate())
                        .setCheckInTime(service.getStartTime())
                        .setCheckOutDate(service.getEndDate())
                        .setCheckOutTime(service.getEndTime())
                        .build())
                .orElse(GetAppointmentDetailResult.CheckInOutDateTime.getDefaultInstance());
    }

    private static GetAppointmentDetailResult.CheckInOutDateTime buildGroomingCheckInOutDateTime(
            List<BookingRequestModel.Service> services) {
        return services.stream()
                .filter(service ->
                        Objects.equals(service.getServiceCase(), BookingRequestModel.Service.ServiceCase.GROOMING))
                .findFirst()
                .map(BookingRequestModel.Service::getGrooming)
                .map(BookingRequestModel.GroomingService::getService)
                .map(service -> GetAppointmentDetailResult.CheckInOutDateTime.newBuilder()
                        .setCheckInDate(service.getStartDate())
                        .setCheckInTime(service.getStartTime())
                        .setCheckOutDate(service.getEndDate())
                        .setCheckOutTime(service.getEndTime())
                        .build())
                .orElse(GetAppointmentDetailResult.CheckInOutDateTime.getDefaultInstance());
    }

    private static GetAppointmentDetailResult.CheckInOutDateTime buildDogWalkingCheckInOutDateTime(
            List<BookingRequestModel.Service> services) {
        return services.stream()
                .filter(service ->
                        Objects.equals(service.getServiceCase(), BookingRequestModel.Service.ServiceCase.DOG_WALKING))
                .findFirst()
                .map(BookingRequestModel.Service::getDogWalking)
                .map(BookingRequestModel.DogWalkingService::getService)
                .map(service -> GetAppointmentDetailResult.CheckInOutDateTime.newBuilder()
                        .setCheckInDate(service.getStartDate())
                        .setCheckInTime(service.getStartTime())
                        .setCheckOutDate(service.getEndDate())
                        .setCheckOutTime(service.getEndTime())
                        .build())
                .orElse(GetAppointmentDetailResult.CheckInOutDateTime.getDefaultInstance());
    }

    private static GetAppointmentDetailResult.CheckInOutDateTime buildEvaluationCheckInOutDateTime(
            List<BookingRequestModel.Service> services) {
        return services.stream()
                .filter(service ->
                        Objects.equals(service.getServiceCase(), BookingRequestModel.Service.ServiceCase.EVALUATION))
                .findFirst()
                .map(BookingRequestModel.Service::getEvaluation)
                .map(BookingRequestModel.EvaluationService::getService)
                .map(service -> GetAppointmentDetailResult.CheckInOutDateTime.newBuilder()
                        .setCheckInDate(service.getStartDate())
                        .setCheckInTime(service.getStartTime())
                        .setCheckOutDate(service.getEndDate())
                        .setCheckOutTime(service.getEndTime())
                        .build())
                .orElse(GetAppointmentDetailResult.CheckInOutDateTime.getDefaultInstance());
    }

    private List<GetAppointmentDetailResult.PetAndServicesDetailItem> buildPetAndServicesDetailItems(
            BookingRequestModel bookingRequest) {
        var bookingRequests = List.of(bookingRequest);
        var petIds = extractPetIds(bookingRequests);
        var serviceIds = extractServiceIds(bookingRequests);
        var evaluationIds = extractEvaluationIds(bookingRequests);

        var petResult = futureService.listPetFullInfos(petIds);
        var serviceResult = futureService.listServices(serviceIds);
        var evaluationResult = futureService.listEvaluations(evaluationIds);

        var petIdToInfoMap = petResult.join();
        var serviceIdToInfoMap = serviceResult.join();
        var evaluationIdToInfoMap = evaluationResult.join();

        return buildPetAndServicesDetailItems(
                bookingRequest, petIdToInfoMap, serviceIdToInfoMap, evaluationIdToInfoMap);
    }

    private static List<GetAppointmentDetailResult.PetAndServicesDetailItem> buildPetAndServicesDetailItems(
            BookingRequestModel bookingRequest,
            Map<Long, BusinessCustomerPetModel> petIdToInfoMap,
            Map<Long, ServiceBriefView> serviceIdToInfoMap,
            Map<Long, EvaluationBriefView> evaluationIdToInfoMap) {

        var petIdToSummaryItemBuilderMap =
                new HashMap<Long, GetAppointmentDetailResult.PetAndServicesDetailItem.Builder>();

        bookingRequest.getServicesList().forEach(service -> {
            var builder = petIdToSummaryItemBuilderMap.computeIfAbsent(
                    extractPetId(service), id -> GetAppointmentDetailResult.PetAndServicesDetailItem.newBuilder()
                            .setPet(BusinessPetConverter.INSTANCE.toPetDetail(petIdToInfoMap.get(id))));

            if (service.getServiceCase() == BookingRequestModel.Service.ServiceCase.EVALUATION) {
                var evaluationServiceDetail = service.getEvaluation().getService();

                var evaluationDetailBuilder = ServiceConverter.INSTANCE
                        .toEvaluationDetail(evaluationIdToInfoMap.get(evaluationServiceDetail.getEvaluationId()))
                        .toBuilder();

                if (StringUtils.hasText(evaluationServiceDetail.getStartDate())) {
                    evaluationDetailBuilder.setStartDate(evaluationServiceDetail.getStartDate());
                }
                if (StringUtils.hasText(evaluationServiceDetail.getEndDate())) {
                    evaluationDetailBuilder.setEndDate(evaluationServiceDetail.getEndDate());
                }
                evaluationDetailBuilder.setStartTime(evaluationServiceDetail.getStartTime());
                evaluationDetailBuilder.setEndTime(evaluationServiceDetail.getEndTime());

                builder.addServices(evaluationDetailBuilder.build());
            } else {
                builder.addServices(buildServiceDetail(service, serviceIdToInfoMap))
                        .addAllAddOns(buildAddOnDetails(service, serviceIdToInfoMap));
            }
        });

        return petIdToSummaryItemBuilderMap.values().stream()
                .map(GetAppointmentDetailResult.PetAndServicesDetailItem.Builder::build)
                .toList();
    }

    private static GetAppointmentDetailResult.ServiceDetailItem buildServiceDetail(
            BookingRequestModel.Service service, Map<Long, ServiceBriefView> serviceIdToInfoMap) {
        return switch (service.getServiceCase()) {
            case GROOMING -> ServiceConverter.INSTANCE.toServiceDetail(service.getGrooming(), serviceIdToInfoMap);
            case BOARDING -> ServiceConverter.INSTANCE.toServiceDetail(service.getBoarding(), serviceIdToInfoMap);
            case DAYCARE -> ServiceConverter.INSTANCE.toServiceDetail(service.getDaycare(), serviceIdToInfoMap);
            case DOG_WALKING -> ServiceConverter.INSTANCE.toServiceDetail(service.getDogWalking(), serviceIdToInfoMap);
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown care type");
        };
    }

    private static List<GetAppointmentDetailResult.AddOnDetailItem> buildAddOnDetails(
            BookingRequestModel.Service service, Map<Long, ServiceBriefView> serviceIdToInfoMap) {
        return switch (service.getServiceCase()) {
            case GROOMING -> service.getGrooming().getAddonsList().stream()
                    .map(addOn ->
                            ServiceConverter.INSTANCE.toAddOnItem(serviceIdToInfoMap.get(addOn.getAddOnId()), addOn))
                    .toList();
            case BOARDING -> service.getBoarding().getAddonsList().stream()
                    .map(addOn -> ServiceConverter.INSTANCE.toAddOnItem(
                            serviceIdToInfoMap.get(addOn.getAddOnId()),
                            addOn,
                            service.getBoarding().getService()))
                    .toList();
            case DAYCARE -> service.getDaycare().getAddonsList().stream()
                    .map(addOn -> ServiceConverter.INSTANCE.toAddOnItem(
                            serviceIdToInfoMap.get(addOn.getAddOnId()),
                            addOn,
                            service.getDaycare().getService()))
                    .toList();
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown add-on type");
        };
    }

    private static GetAppointmentDetailResult.PaymentItem buildPaymentItem(
            List<BookingRequestModel.Service> services, OrderDetailModel orderDetail, BookOnlineDepositDTO obDeposit) {
        var total = PaymentUtil.calculateEstimatedTotalPrice(services);
        var builder = GetAppointmentDetailResult.PaymentItem.newBuilder()
                .setEstimatedTotalPrice(total)
                .setTotalAmount(total)
                .setPaymentStatus(AppointmentPaymentStatus.UNPAID)
                .setEvaluationEstimatedTotalPrice(
                        calEvaluationPriceForBookingRequest(services).doubleValue());
        if (orderDetail != null) {
            builder.setPaidAmount(orderDetail.getOrder().getPaidAmount());
        }
        if (obDeposit != null) {
            if (DepositPaymentTypeEnum.PrePay.equals(obDeposit.getDepositType())) {
                builder.setPrePayAmount(obDeposit.getAmount().doubleValue());
                builder.setPreAuthEnable(false);
            } else {
                builder.setPreAuthEnable(true);
            }
        }
        return builder.build();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void reschedulePetFeedingMedication(
            ReschedulePetFeedingMedicationParams request,
            StreamObserver<ReschedulePetFeedingMedicationResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());

        switch (request.getIdCase()) {
            case APPOINTMENT_ID -> reschedulePetFeedingMedicationForAppointment(ctx, request);
            case BOOKING_REQUEST_ID -> reschedulePetFeedingMedicationForOnlineBooking(request);
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown id case");
        }

        responseObserver.onNext(ReschedulePetFeedingMedicationResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    void reschedulePetFeedingMedicationForAppointment(
            BaseBusinessCustomerIdDTO ctx, ReschedulePetFeedingMedicationParams request) {
        var existPetDetailsMap =
                appointmentService
                        .listAllPetDetails(ctx.getCompanyId(), List.of(request.getAppointmentId()))
                        .key()
                        .stream()
                        .collect(Collectors.toMap(PetDetailModel::getId, k -> k, (k1, k2) -> k1));
        // 在 appointment， feeding&medication 是记在预约中 pet 上
        var req = AppointmentConverter.INSTANCE.toRescheduleRequest(
                ctx.getCompanyId(),
                ctx.getBusinessId(),
                request.getAppointmentId(),
                request.getSchedulesList(),
                existPetDetailsMap);
        appointmentScheduleService.reschedulePetFeedingMedication(req);
        feedingMedicationService.syncPetScheduleDef(ctx.getCompanyId(), req.getSchedulesList());
    }

    void reschedulePetFeedingMedicationForOnlineBooking(ReschedulePetFeedingMedicationParams request) {
        var bookingRequestId = request.getBookingRequestId();
        var bookingRequest = bookingRequestStub
                .getBookingRequest(GetBookingRequestRequest.newBuilder()
                        .setId(bookingRequestId)
                        .addAllAssociatedModels(List.of(
                                BookingRequestAssociatedModel.SERVICE,
                                BookingRequestAssociatedModel.ADD_ON,
                                BookingRequestAssociatedModel.FEEDING,
                                BookingRequestAssociatedModel.MEDICATION))
                        .build())
                .getBookingRequest();
        // 在 ob 中， feeding&medication 是记在预约中 pet 的 service 上
        var req = BookingRequestConverter.toUpdateBookingRequestRequest(request, bookingRequest);
        bookingRequestStub.updateBookingRequest(req);
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void cancelAppointment(
            CancelAppointmentParams request, StreamObserver<CancelAppointmentResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());

        switch (request.getIdCase()) {
            case APPOINTMENT_ID -> cancelForAppointment(ctx.getCustomerId().longValue(), request.getAppointmentId());
            case BOOKING_REQUEST_ID -> cancelForBookingRequest(
                    ctx.getCustomerId().longValue(), request.getBookingRequestId());
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown id case");
        }

        responseObserver.onNext(CancelAppointmentResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    void cancelForBookingRequest(Long customerId, Long bookingRequestId) {
        var bookingRequest = bookingRequestStub
                .getBookingRequest(GetBookingRequestRequest.newBuilder()
                        .setId(bookingRequestId)
                        .build())
                .getBookingRequest();
        if (!Objects.equals(bookingRequest.getCustomerId(), customerId)
                || bookingRequest.getStatus() != BookingRequestStatus.SUBMITTED) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid booking request id");
        }

        bookingRequestStub.updateBookingRequestStatus(UpdateBookingRequestStatusRequest.newBuilder()
                .setId(bookingRequest.getId())
                .setStatus(BookingRequestStatus.DECLINED)
                .build());
    }

    void cancelForAppointment(Long customerId, Long appointmentId) {
        var appointment = appointmentStub
                .getAppointment(GetAppointmentRequest.newBuilder()
                        .setAppointmentId(appointmentId)
                        .build())
                .getAppointment();
        if (!Objects.equals(appointment.getCustomerId(), customerId)
                || appointment.getStatus() == AppointmentStatus.FINISHED) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid appointment id");
        }

        var cancelAppointmentRequest = CancelAppointmentRequest.newBuilder()
                .setAppointmentId(appointmentId)
                .setCancelByType(AppointmentUpdatedBy.BY_CLIENT_PORTAL)
                .setCancelBy(appointment.getCustomerId())
                .setCancelReason(BookingRequestServer.CANCEL_REASON)
                .setNoShow(AppointmentNoShowStatus.NOT_NO_SHOW)
                .build();
        appointmentStub.cancelAppointment(cancelAppointmentRequest);
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void updateAppointment(
            UpdateAppointmentParams request, StreamObserver<UpdateAppointmentResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        switch (request.getIdCase()) {
            case APPOINTMENT_ID -> updateForAppointment(ctx, request);
            case BOOKING_REQUEST_ID -> updateForOnlineBooking(
                    ctx.getCustomerId().longValue(), request);
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown id case");
        }

        responseObserver.onNext(UpdateAppointmentResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    void updateForAppointment(BaseBusinessCustomerIdDTO ctx, UpdateAppointmentParams request) {
        var appointmentId = request.getAppointmentId();
        var zoneId = companyService.getZoneId(ctx.getCompanyId());
        var oldAppointment = appointmentStub
                .getAppointment(GetAppointmentRequest.newBuilder()
                        .setAppointmentId(appointmentId)
                        .build())
                .getAppointment();
        if (!Objects.equals(oldAppointment.getCustomerId(), ctx.getCustomerId().longValue())
                || oldAppointment.getStatus() == AppointmentStatus.FINISHED) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid appointment id");
        }

        appointmentStub.updateAppointmentSelective(
                AppointmentConverter.INSTANCE.toUpdateAppointmentSelectiveRequest(request));

        var newAppointment = appointmentStub
                .getAppointment(GetAppointmentRequest.newBuilder()
                        .setAppointmentId(appointmentId)
                        .build())
                .getAppointment();

        activityLogService.createActivityLog(CreateActivityLogRequest.newBuilder()
                .setCompanyId(ctx.getCompanyId())
                .setBusinessId(ctx.getBusinessId())
                .setOperatorId(String.valueOf(ctx.getCustomerId()))
                .setIsRoot(true)
                .setAction(AppointmentAction.RESCHEDULE)
                .setResourceType(Resource.Type.APPOINTMENT)
                .setResourceId(String.valueOf(request.getAppointmentId()))
                .setDetails(JsonUtil.toJson(new ChangeTimeLogDTO(
                        DateConverter.INSTANCE.toTimestamp(
                                oldAppointment.getAppointmentDate(), oldAppointment.getAppointmentStartTime(), zoneId),
                        DateConverter.INSTANCE.toTimestamp(
                                newAppointment.getAppointmentDate(), newAppointment.getAppointmentStartTime(), zoneId),
                        AppointmentUpdatedBy.BY_CLIENT_PORTAL)))
                .build());
    }

    void updateForOnlineBooking(Long customerId, UpdateAppointmentParams request) {
        var bookingRequestId = request.getBookingRequestId();
        var bookingRequest = bookingRequestStub
                .getBookingRequest(GetBookingRequestRequest.newBuilder()
                        .setId(bookingRequestId)
                        .addAllAssociatedModels(
                                List.of(BookingRequestAssociatedModel.SERVICE, BookingRequestAssociatedModel.ADD_ON))
                        .build())
                .getBookingRequest();
        if (!Objects.equals(bookingRequest.getCustomerId(), customerId)
                || bookingRequest.getStatus() != BookingRequestStatus.SUBMITTED) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid booking request id");
        }

        bookingRequestStub.updateBookingRequest(
                BookingRequestConverter.toUpdateBookingRequestRequest(request, bookingRequest));
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void isAvailableForReschedule(
            IsAvailableForRescheduleParams request, StreamObserver<IsAvailableForRescheduleResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        long companyId = ctx.getCompanyId();
        long businessId = ctx.getBusinessId().longValue();
        boolean isOBRequest = request.getIdCase() == IsAvailableForRescheduleParams.IdCase.BOOKING_REQUEST_ID;
        LocalDate startDate = DateConverter.INSTANCE.toLocalDate(request.getStartDate());
        LocalDate endDate = DateConverter.INSTANCE.toLocalDate(request.getEndDate());

        LodgingAvailabilityDef lodgingAvailabilityDef;
        List<CapacityOverrideModel> capacityOverrides = new ArrayList<>();
        switch (request.getServiceItemType()) {
            case BOARDING:
                BoardingServiceAvailabilityModel boardingLodgingAvailability =
                        availabilityService.getBoardingLodgingAvailability(companyId, businessId);
                lodgingAvailabilityDef = boardingLodgingAvailability.getLodgingAvailability();
                break;
            case DAYCARE:
                DaycareServiceAvailabilityModel daycareLodgingAvailability =
                        availabilityService.getDaycareLodgingAvailability(companyId, businessId);
                lodgingAvailabilityDef = daycareLodgingAvailability.getLodgingAvailability();
                capacityOverrides = daycareLodgingAvailability.getCapacityOverridesList();
                break;
            default:
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "unsupported service item type");
        }

        // 拉取 lodging 信息
        List<LodgingUnitModel> lodgingUnits = lodgingService.getAllLodgingUnitInBusiness(companyId, businessId);
        List<LodgingTypeModel> lodgingTypeList = lodgingService.getLodgingTypeByIds(
                lodgingUnits.stream().map(LodgingUnitModel::getLodgingTypeId).toList());
        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits =
                lodgingUnits.stream().collect(Collectors.groupingBy(LodgingUnitModel::getLodgingTypeId));

        // 拉取 lodging 使用信息与 ob request 信息，用于判断 service 是否有 available lodging
        List<BookingRequestModel> bookingRequests = bookingService.getSubmittedBookingRequestsByTimeRange(
                List.of(businessId),
                List.of(request.getServiceItemType()),
                startDate,
                endDate,
                List.of(BookingRequestAssociatedModel.SERVICE));
        List<LodgingAssignInfo> appointmentLodgingAssigns =
                appointmentService.getLodgingAssignInfo(companyId, businessId, startDate, endDate);

        // 过滤掉本身
        if (isOBRequest) {
            bookingRequests = bookingRequests.stream()
                    .filter(k -> k.getId() != request.getBookingRequestId())
                    .toList();
        } else {
            appointmentLodgingAssigns = appointmentLodgingAssigns.stream()
                    .map(k -> LodgingAssignInfo.newBuilder()
                            .setLodgingId(k.getLodgingId())
                            .addAllAppointments(k.getAppointmentsList().stream()
                                    .filter(v -> v.getId() != request.getAppointmentId())
                                    .toList())
                            .build())
                    .toList();
        }
        Map<Long, Map<LocalDate, Integer>> petCntPerLodgingPerDay =
                LodgingUtil.calPetCntPerLodgingPerDay(startDate, endDate, appointmentLodgingAssigns);
        Map<Long, Map<LocalDate, Integer>> petCntPerServicePerDay =
                LodgingUtil.calPetCntPerService(startDate, endDate, bookingRequests);

        var serviceDetail = serviceBlockingStub
                .getServiceDetail(GetServiceDetailRequest.newBuilder()
                        .setServiceId(request.getServiceId())
                        .build())
                .getService();

        // 获取适用于该 service 的 lodgingType
        List<LodgingTypeModel> availableLodgingTypes = lodgingTypeList;
        if (serviceDetail.getLodgingFilter()) {
            availableLodgingTypes = availableLodgingTypes.stream()
                    .filter(k -> serviceDetail.getCustomizedLodgingsList().contains(k.getId()))
                    .toList();
        }

        var isAvailable = LodgingUtil.isAnyLodgingAvailable(
                startDate.datesUntil(endDate.plusDays(1)).toList(),
                1,
                availableLodgingTypes,
                lodgingTypeToUnits,
                lodgingAvailabilityDef,
                capacityOverrides,
                petCntPerLodgingPerDay,
                petCntPerServicePerDay.getOrDefault(request.getServiceId(), Map.of()));

        responseObserver.onNext(IsAvailableForRescheduleResult.newBuilder()
                .setIsAvailable(isAvailable)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listEvaluations(ListEvaluationsParams request, StreamObserver<ListEvaluationsResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        var obRsp = bookingRequestStub.listBookingRequests(ListBookingRequestsRequest.newBuilder()
                .setBusinessId(ctx.getBusinessId())
                .addCustomerId(ctx.getCustomerId())
                .addAllStatuses(List.of(BookingRequestStatus.SUBMITTED, BookingRequestStatus.WAIT_LIST))
                .addAllServiceItems(List.of(ServiceItemType.EVALUATION))
                .addAllPaymentStatuses(List.of(
                        BookingRequestModel.PaymentStatus.NO_PAYMENT,
                        BookingRequestModel.PaymentStatus.PROCESSING,
                        BookingRequestModel.PaymentStatus.SUCCESS))
                .setPagination(request.getPagination())
                .addAllOrderBys(buildBookingRequestOrderBy(List.of()))
                .build());
        var obEvas = obRsp.getBookingRequestsList().stream()
                .map(BookingRequestConverter.INSTANCE::toAppointmentSummary)
                .toList();
        var apptRsp = appointmentStub.listAppointments(ListAppointmentsRequest.newBuilder()
                .setCompanyId(ctx.getCompanyId())
                .addBusinessIds(ctx.getBusinessId())
                .addAllOrderBys(buildAppointmentOrderBy(List.of()))
                .setPagination(request.getPagination())
                .setFilter(buildBaseFilter(ctx.getCustomerId())
                        .addAllStatus(List.of(
                                AppointmentStatus.UNCONFIRMED,
                                AppointmentStatus.CONFIRMED,
                                AppointmentStatus.CHECKED_IN,
                                AppointmentStatus.READY))
                        .addAllServiceTypeIncludes(
                                ServiceItemEnum.getBitValueListByServiceItem(ServiceItemEnum.EVALUATION))
                        .build())
                .build());
        var apptEvas = apptRsp.getAppointmentsList().stream()
                .map(AppointmentConverter.INSTANCE::toAppointmentSummary)
                .toList();
        responseObserver.onNext(ListEvaluationsResult.newBuilder()
                .addAllAppointments(
                        Stream.of(obEvas, apptEvas).flatMap(List::stream).toList())
                .setPagination(PaginationResponse.newBuilder()
                        .setTotal(obRsp.getPagination().getTotal()
                                + apptRsp.getPagination().getTotal())
                        .build())
                .build());
        responseObserver.onCompleted();
    }
}
