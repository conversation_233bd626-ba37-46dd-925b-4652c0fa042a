package com.moego.client.api.v1.appointment.utils;

import com.google.type.Interval;
import com.moego.client.api.v1.converter.DateConverter;
import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.idl.client.online_booking.v1.AppointmentCardType;
import com.moego.idl.client.online_booking.v1.ListAppointmentsParams;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.WaitListStatus;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.appointment.v1.ListAppointmentsRequest;
import com.moego.idl.utils.v2.OrderBy;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.utils.model.Pair;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2024/11/27
 */
public class AppointmentUtil {

    public static ListAppointmentsRequest buildRequest(
            BaseBusinessCustomerIdDTO ctx, ListAppointmentsRequest.Filter filter) {
        return ListAppointmentsRequest.newBuilder()
                .setCompanyId(ctx.getCompanyId())
                .addBusinessIds(ctx.getBusinessId())
                .setFilter(filter)
                .build();
    }

    public static ListAppointmentsRequest buildPaginationRequest(
            BaseBusinessCustomerIdDTO ctx,
            List<OrderBy> orderBys,
            PaginationRequest pagination,
            ListAppointmentsRequest.Filter filter) {
        return ListAppointmentsRequest.newBuilder()
                .setCompanyId(ctx.getCompanyId())
                .addBusinessIds(ctx.getBusinessId())
                .addAllOrderBys(orderBys)
                .setPagination(pagination)
                .setFilter(filter)
                .build();
    }

    /**
     * 180 天内的所有预约
     * in_progress definition: <br>
     * start_date_time < now < end_date_time <br>
     * status in (UNCONFIRMED, CONFIRMED, CHECKED_IN, READY) <br>
     *
     * @param customerId customer id
     * @param zoneId     company zone id
     * @return filter
     */
    public static ListAppointmentsRequest.Filter buildFilterForInProgress(long customerId, ZoneId zoneId) {
        var currentTimestamp = DateConverter.INSTANCE.buildCurrentTimestamp(zoneId);
        return buildBaseFilter(customerId)
                .addAllStatus(List.of(
                        AppointmentStatus.UNCONFIRMED,
                        AppointmentStatus.CONFIRMED,
                        AppointmentStatus.CHECKED_IN,
                        AppointmentStatus.READY))
                // in progress
                .setStartTimeRange(Interval.newBuilder().setEndTime(currentTimestamp))
                .setEndTimeRange(Interval.newBuilder().setStartTime(currentTimestamp))
                .build();
    }

    /**
     * 180 天内的所有预约
     * upcoming(ongoing) definition 含当天: <br>
     * end_date_time >= today start <br>
     * status in (UNCONFIRMED, CONFIRMED, CHECKED_IN, READY) <br>
     *
     * @param customerId customer id
     * @param zoneId company zone id
     * @return upcoming(ongoing) filter
     */
    public static ListAppointmentsRequest.Filter buildFilterForUpcoming(long customerId, ZoneId zoneId) {
        return buildBaseFilter(customerId)
                .addAllStatus(List.of(
                        AppointmentStatus.UNCONFIRMED,
                        AppointmentStatus.CONFIRMED,
                        AppointmentStatus.CHECKED_IN,
                        AppointmentStatus.READY))
                // end_datetime >= today start 包含当天
                .setEndTimeRange(
                        Interval.newBuilder().setStartTime(DateConverter.INSTANCE.buildTodayStartTimestamp(zoneId)))
                .build();
    }

    /**
     * 180 天内的所有预约
     * next_upcoming definition 未开始: <br>
     * start_date_time >= now <br>
     * status in (UNCONFIRMED, CONFIRMED, CHECKED_IN, READY) <br>
     *
     * @param customerId customer id
     * @param zoneId company zone id
     * @return next upcoming filter
     */
    public static ListAppointmentsRequest.Filter buildFilterForNextUpcoming(long customerId, ZoneId zoneId) {
        return buildBaseFilter(customerId)
                .addAllStatus(List.of(
                        AppointmentStatus.UNCONFIRMED,
                        AppointmentStatus.CONFIRMED,
                        AppointmentStatus.CHECKED_IN,
                        AppointmentStatus.READY))
                .setStartTimeRange(
                        Interval.newBuilder().setStartTime(DateConverter.INSTANCE.buildCurrentTimestamp(zoneId)))
                .build();
    }

    /**
     * 180 天内的所有预约
     * last_finished definition: <br>
     * end_date_time < now <br>
     * status = FINISHED <br>
     *
     * @param customerId  customer id
     * @param zoneId company zone id
     * @return last finished filter
     */
    public static ListAppointmentsRequest.Filter buildFilterForLastFinished(long customerId, ZoneId zoneId) {
        return buildBaseFilter(customerId)
                .addStatus(AppointmentStatus.FINISHED)
                .setEndTimeRange(Interval.newBuilder().setEndTime(DateConverter.INSTANCE.buildCurrentTimestamp(zoneId)))
                .build();
    }

    /**
     * 180 天内的所有预约
     * canceled definition: <br>
     * status = CANCELED <br>
     *
     * @param customerId customer id
     * @return canceled filter
     */
    public static ListAppointmentsRequest.Filter buildFilterForCanceled(long customerId) {
        return buildBaseFilter(customerId).addStatus(AppointmentStatus.CANCELED).build();
    }

    /**
     * 180 天内的所有预约
     * finished definition: <br>
     * status = FINISHED <br>
     *
     * @param customerId customer id
     * @return finished filter
     */
    public static ListAppointmentsRequest.Filter buildFilterForFinished(long customerId) {
        return buildBaseFilter(customerId).addStatus(AppointmentStatus.FINISHED).build();
    }

    /**
     * 180 天内的所有预约
     * delayed definition 时间结束但未完成: <br>
     * end_date_time < today start <br>
     * status in (UNCONFIRMED, CONFIRMED, CHECKED_IN, READY) <br>
     *
     * @param customerId customer id
     * @param zoneId company zone id
     * @return delayed filter
     */
    public static ListAppointmentsRequest.Filter buildFilterForDelayed(long customerId, ZoneId zoneId) {
        return buildBaseFilter(customerId)
                .addAllStatus(List.of(
                        AppointmentStatus.UNCONFIRMED,
                        AppointmentStatus.CONFIRMED,
                        AppointmentStatus.CHECKED_IN,
                        AppointmentStatus.READY))
                // end_datetime < today start
                .setEndTimeRange(
                        Interval.newBuilder().setEndTime(DateConverter.INSTANCE.buildTodayStartTimestamp(zoneId)))
                .build();
    }

    public static ListAppointmentsRequest.Filter.Builder buildBaseFilter(long customerId) {
        return ListAppointmentsRequest.Filter.newBuilder()
                .setFilterBookingRequest(true)
                .addCustomerIds(customerId)
                .addAllWaitListStatuses(List.of(WaitListStatus.APPTONLY, WaitListStatus.APPTANDWAITLIST))
                // 最大限制 180 天查询范围
                .setStartTimeRange(Interval.newBuilder()
                        .setStartTime(DateConverter.INSTANCE.toDaysAgoTimestamp(180))
                        .build());
    }

    public static List<OrderBy> buildBookingRequestOrderBy(List<ListAppointmentsParams.AppointmentSortDef> sorts) {
        return buildOrderBys(sorts, "startDate", "startTime");
    }

    public static List<OrderBy> buildAppointmentOrderBy(List<ListAppointmentsParams.AppointmentSortDef> sorts) {
        return buildOrderBys(sorts, "appointmentDate", "appointmentStartTime");
    }

    private static List<OrderBy> buildOrderBys(
            List<ListAppointmentsParams.AppointmentSortDef> sorts, String dateField, String timeField) {
        List<OrderBy> orderBys = new ArrayList<>();
        sorts.forEach(sort -> {
            if (sort.getField() == ListAppointmentsParams.AppointmentSortField.DATE_TIME) {
                orderBys.add(OrderBy.newBuilder()
                        .setFieldName(dateField)
                        .setAsc(sort.getAsc())
                        .build());
                orderBys.add(OrderBy.newBuilder()
                        .setFieldName(timeField)
                        .setAsc(sort.getAsc())
                        .build());
            }
        });
        if (CollectionUtils.isEmpty(orderBys)) {
            orderBys.add(
                    OrderBy.newBuilder().setFieldName(dateField).setAsc(true).build());
            orderBys.add(
                    OrderBy.newBuilder().setFieldName(timeField).setAsc(true).build());
        }
        return orderBys;
    }

    public static Pair<ListAppointmentsRequest.Filter, List<OrderBy>> buildFilterAndOrderBys(
            long customerId, AppointmentCardType cardType, ZoneId zoneId) {
        switch (cardType) {
            case IN_PROGRESS -> {
                var filter = buildFilterForInProgress(customerId, zoneId);
                var orderBys = buildAppointmentOrderBy(List.of());
                return Pair.of(filter, orderBys);
            }
            case UPCOMING -> {
                var filter = buildFilterForNextUpcoming(customerId, zoneId);
                var orderBys = buildAppointmentOrderBy(List.of(ListAppointmentsParams.AppointmentSortDef.newBuilder()
                        .setField(ListAppointmentsParams.AppointmentSortField.DATE_TIME)
                        .setAsc(true)
                        .build()));
                return Pair.of(filter, orderBys);
            }
            case LAST -> {
                var filter = buildFilterForLastFinished(customerId, zoneId);
                var orderBys = buildAppointmentOrderBy(List.of(ListAppointmentsParams.AppointmentSortDef.newBuilder()
                        .setField(ListAppointmentsParams.AppointmentSortField.DATE_TIME)
                        .setAsc(false)
                        .build()));
                return Pair.of(filter, orderBys);
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown card type");
        }
    }
}
