package com.moego.client.api.v1.subscription.service;

import static com.moego.idl.models.subscription.v1.Feature.Key.CREDIT_CREDIT_POINT;

import com.moego.common.utils.RedisUtil;
import com.moego.idl.client.subscription.v1.GetCreditResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.subscription.v1.Count;
import com.moego.idl.models.subscription.v1.Entitlement;
import com.moego.idl.models.subscription.v1.Feature;
import com.moego.idl.models.subscription.v1.License;
import com.moego.idl.models.subscription.v1.User;
import com.moego.idl.service.subscription.v1.CreateLicenseAndEntitlementsRequest;
import com.moego.idl.service.subscription.v1.ListEntitlementsRequest;
import com.moego.idl.service.subscription.v1.ListLicensesRequest;
import com.moego.idl.service.subscription.v1.SubscriptionServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.utils.StringUtils;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class SubscriptionService {

    private final SubscriptionServiceGrpc.SubscriptionServiceBlockingStub subscriptionServiceBlockingStub;

    private final RedisUtil redisUtil;

    public GetCreditResult getCredit(Long customerId) {
        var owner =
                User.newBuilder().setId(customerId).setType(User.Type.CUSTOMER).build();
        var licenses = subscriptionServiceBlockingStub
                .listLicenses(ListLicensesRequest.newBuilder()
                        .setFilter(ListLicensesRequest.Filter.newBuilder()
                                .addOwners(owner)
                                .build())
                        .build())
                .getLicensesList();
        if (licenses.isEmpty()) {
            licenses = createLicenses(owner);
        }

        var entitlements = subscriptionServiceBlockingStub.listEntitlements(ListEntitlementsRequest.newBuilder()
                .setFilter(ListEntitlementsRequest.Filter.newBuilder()
                        .addAllLicenceIds(licenses.stream().map(License::getId).toList())
                        .addAllFeatureKeys(List.of(CREDIT_CREDIT_POINT))
                        .build())
                .build());

        if (Objects.isNull(entitlements) || entitlements.getEntitlementsList().isEmpty()) {
            licenses = createLicenses(owner);
            entitlements = subscriptionServiceBlockingStub.listEntitlements(ListEntitlementsRequest.newBuilder()
                    .setFilter(ListEntitlementsRequest.Filter.newBuilder()
                            .addAllLicenceIds(
                                    licenses.stream().map(License::getId).toList())
                            .addAllFeatureKeys(List.of(CREDIT_CREDIT_POINT))
                            .build())
                    .build());
        }

        var countStream = entitlements.getEntitlementsList().stream()
                .map(Entitlement::getFeature)
                .map(Feature::getSetting)
                .map(Feature.Setting::getCount)
                .toList();

        var total = countStream.stream().mapToLong(Count::getTotalAmount).sum();
        var used = countStream.stream().mapToLong(Count::getUsedAmount).sum();
        // 剩余
        return GetCreditResult.newBuilder().setCredit(total - used).build();
    }

    private List<License> createLicenses(User owner) {
        var resp = subscriptionServiceBlockingStub.createLicenseAndEntitlements(
                CreateLicenseAndEntitlementsRequest.newBuilder()
                        .setBuyer(owner)
                        .addFeatureIds(getCreditID())
                        .setLicenseStatus(License.Status.VALID)
                        .build());

        log.info("create license response: {}", resp);
        var license = subscriptionServiceBlockingStub.listLicenses(ListLicensesRequest.newBuilder()
                .setFilter(ListLicensesRequest.Filter.newBuilder()
                        .addOwners(owner)
                        .addStatuses(License.Status.VALID)
                        .build())
                .build());
        return license.getLicensesList();
    }

    private Long getCreditID() {
        String CREDIT_CREDIT_POINT_FEATURE_ID = "CREDIT_CREDIT_POINT_FEATURE_ID";
        var id = redisUtil.get(CREDIT_CREDIT_POINT_FEATURE_ID);
        if (StringUtils.isBlank(id))
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "credit feature id not found");
        return Long.parseLong(id);
    }
}
