package com.moego.client.api.v1.online_booking.server;

import com.moego.client.api.v1.online_booking.service.ContextService;
import com.moego.client.api.v1.shared.helper.OfferingHelper;
import com.moego.idl.client.online_booking.v1.ListAvailableMembershipsParams;
import com.moego.idl.client.online_booking.v1.ListAvailableMembershipsResult;
import com.moego.idl.client.online_booking.v1.ListMembershipPerksParams;
import com.moego.idl.client.online_booking.v1.ListMembershipPerksResult;
import com.moego.idl.client.online_booking.v1.ListRedeemHistoryParams;
import com.moego.idl.client.online_booking.v1.ListRedeemHistoryResult;
import com.moego.idl.client.online_booking.v1.ListSubscriptionsParams;
import com.moego.idl.client.online_booking.v1.ListSubscriptionsResult;
import com.moego.idl.client.online_booking.v1.MembershipServiceGrpc;
import com.moego.idl.models.membership.v1.DiscountBenefitModel;
import com.moego.idl.models.membership.v1.DiscountUnit;
import com.moego.idl.models.membership.v1.MembershipDiscountBenefitsDef;
import com.moego.idl.models.membership.v1.MembershipQuantityBenefitsDef;
import com.moego.idl.models.membership.v1.MembershipSubscriptionModel;
import com.moego.idl.models.membership.v1.QualityBenefitModel;
import com.moego.idl.models.membership.v1.TargetType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.membership.v1.GetPerkUsageDetailRequest;
import com.moego.idl.service.membership.v1.GetRedeemHistoryRequest;
import com.moego.idl.service.membership.v1.ListAllPerkCycleRequest;
import com.moego.idl.service.membership.v1.ListMembershipsForCustomerRequest;
import com.moego.idl.service.membership.v1.ListMembershipsForCustomerResponse;
import com.moego.idl.service.membership.v1.ListSubscriptionsRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class MembershipServer extends MembershipServiceGrpc.MembershipServiceImplBase {

    private final IGroomingOnlineBookingService onlineBookingService;
    private final com.moego.idl.service.membership.v1.MembershipServiceGrpc.MembershipServiceBlockingStub
            membershipService;
    private final com.moego.idl.service.membership.v1.SubscriptionServiceGrpc.SubscriptionServiceBlockingStub
            subscriptionService;
    private final ContextService contextService;
    private final OfferingHelper offeringHelper;

    @Override
    @Auth(AuthType.OB)
    public void listAvailableMemberships(
            ListAvailableMembershipsParams request, StreamObserver<ListAvailableMembershipsResult> responseObserver) {
        Integer customerId = AuthContext.get().getCustomerId();
        if (Objects.isNull(customerId)) {
            responseObserver.onNext(ListAvailableMembershipsResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        OBBusinessDTO dto = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        ListMembershipsForCustomerResponse membershipsResponse =
                membershipService.listMembershipsForCustomer(ListMembershipsForCustomerRequest.newBuilder()
                        .setCompanyId(dto.getCompanyId())
                        .setBusinessId(dto.getBusinessId())
                        .setCustomerId(customerId)
                        .build());

        if (CollectionUtils.isEmpty(membershipsResponse.getMembershipsList())) {
            responseObserver.onNext(ListAvailableMembershipsResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        Map<Long /* service id */, ServiceModel> serviceMap = getServiceMap(dto.getCompanyId());

        Map<Long /* service id */, ListAvailableMembershipsResult.BenefitDetail> resultMap = new HashMap<>();

        List<QualityBenefitModel> quantities = membershipsResponse.getMembershipQuantityBenefitsList().stream()
                .map(MembershipQuantityBenefitsDef::getQuantityDefsList)
                .flatMap(List::stream)
                .filter(quantity -> isServiceOrAddOn(quantity.getTargetType()))
                .filter(quantity -> serviceMap.containsKey(quantity.getTargetId()))
                .toList();

        quantities.forEach(quantity -> {
            resultMap.computeIfAbsent(
                    quantity.getTargetId(), k -> ListAvailableMembershipsResult.BenefitDetail.newBuilder()
                            .setServiceId(quantity.getTargetId())
                            .setType(getTargetType(serviceMap.get(k)))
                            .setIsFree(true)
                            .build());
        });

        List<DiscountBenefitModel> discounts = membershipsResponse.getMembershipDiscountBenefitsList().stream()
                .map(MembershipDiscountBenefitsDef::getDiscountsList)
                .flatMap(List::stream)
                .filter(quantity -> isServiceOrAddOn(quantity.getTargetType()))
                .toList();

        for (DiscountBenefitModel discount : discounts) {
            List<Long> targetIds = discount.getTargetIdsList();
            if (CollectionUtils.isEmpty(targetIds)) {
                if (TargetType.SERVICE.equals(discount.getTargetType())) {
                    targetIds = serviceMap.values().stream()
                            .filter(service -> ServiceType.SERVICE.equals(service.getType()))
                            .map(ServiceModel::getServiceId)
                            .toList();
                } else if (TargetType.ADDON.equals(discount.getTargetType())) {
                    targetIds = serviceMap.values().stream()
                            .filter(service -> ServiceType.ADDON.equals(service.getType()))
                            .map(ServiceModel::getServiceId)
                            .toList();
                }
            }

            targetIds.stream()
                    .filter(serviceMap::containsKey)
                    .forEach(serviceId -> getBenefitDetail(discount, serviceId, resultMap, serviceMap));
        }

        responseObserver.onNext(ListAvailableMembershipsResult.newBuilder()
                .addAllBenefits(resultMap.values())
                .build());
        responseObserver.onCompleted();
    }

    private static void getBenefitDetail(
            DiscountBenefitModel discount,
            Long serviceId,
            Map<Long, ListAvailableMembershipsResult.BenefitDetail> resultMap,
            Map<Long, ServiceModel> serviceMap) {
        resultMap.compute(serviceId, (k, prev) -> {
            // If the service is not in the map, add it
            if (Objects.isNull(prev)) {
                return ListAvailableMembershipsResult.BenefitDetail.newBuilder()
                        .setServiceId(serviceId)
                        .setIsFree(false)
                        .setType(getTargetType(serviceMap.get(k)))
                        .setDiscountUnit(discount.getDiscountUnit())
                        .setDiscountValue(discount.getDiscountValue())
                        .build();
            }

            // If the service is already free, no need to update it
            if (prev.getIsFree()) {
                return prev;
            }

            ServiceModel serviceModel = serviceMap.get(serviceId);
            // If the service is not in the service map, keep the old discount
            if (Objects.isNull(serviceModel)) {
                return prev;
            }

            // If the new discount is not better, keep the old one
            if (getDiscountValue(serviceModel.getPrice(), discount.getDiscountUnit(), discount.getDiscountValue())
                    < getDiscountValue(serviceModel.getPrice(), prev.getDiscountUnit(), prev.getDiscountValue())) {
                return prev;
            }

            // If the new discount is better, update it
            return prev.toBuilder()
                    .setDiscountUnit(discount.getDiscountUnit())
                    .setDiscountValue(discount.getDiscountValue())
                    .build();
        });
    }

    private static boolean isServiceOrAddOn(TargetType targetType) {
        return TargetType.SERVICE.equals(targetType) || TargetType.ADDON.equals(targetType);
    }

    private static double getDiscountValue(double servicePrice, DiscountUnit discountUnit, double discountValue) {
        if (DiscountUnit.PERCENT.equals(discountUnit)) {
            return servicePrice * discountValue / 100;
        } else {
            return discountValue;
        }
    }

    @NotNull
    private Map<Long, ServiceModel> getServiceMap(Long companyId) {
        return offeringHelper.getAllService(companyId).stream()
                .collect(Collectors.toMap(ServiceModel::getServiceId, Function.identity()));
    }

    private static TargetType getTargetType(ServiceModel serviceModel) {
        return ServiceType.SERVICE.equals(serviceModel.getType()) ? TargetType.SERVICE : TargetType.ADDON;
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listSubscriptions(
            ListSubscriptionsParams request, StreamObserver<ListSubscriptionsResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        var rsp = subscriptionService.listSubscriptions(ListSubscriptionsRequest.newBuilder()
                .setTenant(Tenant.newBuilder()
                        .setCompanyId(ctx.getCompanyId())
                        .setBusinessId(ctx.getBusinessId())
                        .build())
                .addAllCustomerIdIn(List.of(ctx.getCustomerId().longValue()))
                .addAllStatusIn(request.getStatusInList())
                .setPagination(request.getPagination())
                .build());
        var membershipSubscriptionList = rsp.getResult().getMembershipSubscriptionsList().stream()
                .sorted(Comparator.<MembershipSubscriptionModel>comparingInt(
                                membershipSubscriptionModel -> switch (membershipSubscriptionModel
                                        .getSubscription()
                                        .getStatus()) {
                                    case ACTIVE -> 1;
                                    case PENDING -> 2;
                                    case CANCELLED -> 3;
                                    default -> 4;
                                })
                        .thenComparingLong(membershipSubscriptionModel -> membershipSubscriptionModel
                                .getSubscription()
                                .getCreatedAt()
                                .getSeconds()))
                .toList();
        responseObserver.onNext(ListSubscriptionsResult.newBuilder()
                .addAllMembershipSubscriptions(membershipSubscriptionList)
                .setPagination(rsp.getResult().getPagination())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listMembershipPerks(
            ListMembershipPerksParams request, StreamObserver<ListMembershipPerksResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        var rsp = membershipService.listAllPerkCycle(ListAllPerkCycleRequest.newBuilder()
                .setCompanyId(ctx.getCompanyId())
                .setMembershipId(request.getMembershipId())
                .setCustomerId(ctx.getCustomerId())
                .build());
        var result = ListMembershipPerksResult.newBuilder();
        if (CollectionUtils.isEmpty(rsp.getPerkCycleItemList())) {
            var detailRsp = membershipService.getPerkUsageDetail(GetPerkUsageDetailRequest.newBuilder()
                    .setCompanyId(ctx.getCompanyId())
                    .setMembershipId(request.getMembershipId())
                    .setCustomerId(ctx.getCustomerId())
                    .build());
            responseObserver.onNext(result.addAllMembershipPerks(detailRsp.getDiscountBenefitsList())
                    .build());
            responseObserver.onCompleted();
            return;
        }
        rsp.getPerkCycleItemList().forEach(perkCycleItem -> {
            var detailRsp = membershipService.getPerkUsageDetail(GetPerkUsageDetailRequest.newBuilder()
                    .setCompanyId(ctx.getCompanyId())
                    .setMembershipId(request.getMembershipId())
                    .setCustomerId(ctx.getCustomerId())
                    .setFilter(GetPerkUsageDetailRequest.Filter.newBuilder()
                            .setValidityStartTime(perkCycleItem.getValidityStartTime())
                            .build())
                    .build());
            result.addPerkCycleItem(perkCycleItem.toBuilder()
                            .clearPerks()
                            .addAllPerks(detailRsp.getIncludedBenefitsList())
                            .build())
                    .clearMembershipPerks()
                    .addAllMembershipPerks(detailRsp.getDiscountBenefitsList());
        });
        responseObserver.onNext(result.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listRedeemHistory(
            ListRedeemHistoryParams request, StreamObserver<ListRedeemHistoryResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        var rsp = membershipService.getRedeemHistory(GetRedeemHistoryRequest.newBuilder()
                .setCompanyId(ctx.getCompanyId())
                .setBusinessId(ctx.getBusinessId())
                .setMembershipId(request.getMembershipId())
                .setCustomerId(ctx.getCustomerId())
                .setPagination(request.getPagination())
                .build());
        responseObserver.onNext(ListRedeemHistoryResult.newBuilder()
                .addAllRedeemHistory(rsp.getRedeemHistoryList())
                .setPagination(rsp.getPagination())
                .build());
        responseObserver.onCompleted();
    }
}
