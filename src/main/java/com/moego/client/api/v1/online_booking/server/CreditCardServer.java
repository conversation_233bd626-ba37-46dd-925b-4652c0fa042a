package com.moego.client.api.v1.online_booking.server;

import com.moego.client.api.v1.online_booking.service.ContextService;
import com.moego.client.api.v1.online_booking.service.SubscriptionService;
import com.moego.client.api.v1.payment.mapper.CreditCardMapper;
import com.moego.idl.client.online_booking.v1.AddCreditCardParams;
import com.moego.idl.client.online_booking.v1.AddCreditCardResult;
import com.moego.idl.client.online_booking.v1.CreditCardServiceGrpc;
import com.moego.idl.client.online_booking.v1.DeleteCreditCardParams;
import com.moego.idl.client.online_booking.v1.DeleteCreditCardResult;
import com.moego.idl.client.online_booking.v1.GetCreditParams;
import com.moego.idl.client.online_booking.v1.GetCreditResult;
import com.moego.idl.client.online_booking.v1.ListCreditCardsParams;
import com.moego.idl.client.online_booking.v1.ListCreditCardsResult;
import com.moego.idl.client.online_booking.v1.ListCreditChangeHistoryParams;
import com.moego.idl.client.online_booking.v1.ListCreditChangeHistoryResult;
import com.moego.idl.models.payment.v1.CreditCardModelPublicView;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.payment.client.IPaymentCreditCardClient;
import com.moego.server.payment.dto.CardDTO;
import com.moego.server.payment.params.CustomerStripInfoRequest;
import com.moego.server.payment.params.DeleteCustomerCardRequest;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class CreditCardServer extends CreditCardServiceGrpc.CreditCardServiceImplBase {
    private final ContextService contextService;
    private final CreditCardMapper creditCardMapper;
    private final IPaymentCreditCardClient creditCardClient;
    private final SubscriptionService subscriptionService;

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listCreditCards(ListCreditCardsParams request, StreamObserver<ListCreditCardsResult> responseObserver) {
        // TODO: add white list
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        int businessId = Math.toIntExact(ctx.getBusinessId());
        List<CardDTO> cardList = creditCardClient.getCreditCardList(businessId, ctx.getCustomerId());

        responseObserver.onNext(ListCreditCardsResult.newBuilder()
                .addAllCards(creditCardMapper.toView(cardList))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void addCreditCard(AddCreditCardParams request, StreamObserver<AddCreditCardResult> responseObserver) {
        // TODO: add white list
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        var r = new CustomerStripInfoRequest();
        r.setAddAch(false);
        r.setCustomerId(ctx.getCustomerId());
        r.setBusinessId(ctx.getBusinessId());
        r.setChargeToken(request.getChargeToken());
        var res = creditCardClient.saveStripeCard(ctx.getBusinessId(), r);
        responseObserver.onNext(AddCreditCardResult.newBuilder()
                .setCard(CreditCardModelPublicView.newBuilder()
                        .setId(res.getPaymentMethodId())
                        .build())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void deleteCreditCard(
            DeleteCreditCardParams request, StreamObserver<DeleteCreditCardResult> responseObserver) {
        // TODO: add white list
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        var r = new DeleteCustomerCardRequest();
        r.setCustomerId(ctx.getCustomerId());
        r.setCardId(request.getCardId());
        creditCardClient.deleteCustomerCard(r);
        responseObserver.onNext(DeleteCreditCardResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void getCredit(GetCreditParams request, StreamObserver<GetCreditResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        var credit = subscriptionService.getCredit(ctx.getCustomerId().longValue());
        responseObserver.onNext(GetCreditResult.newBuilder().setCredit(credit).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listCreditChangeHistory(
            ListCreditChangeHistoryParams request, StreamObserver<ListCreditChangeHistoryResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        var response =
                subscriptionService.listCreditChangeHistory(ctx.getCustomerId().longValue(), request.getPagination());
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }
}
