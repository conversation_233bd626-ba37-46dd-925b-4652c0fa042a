// @since 2023-09-05 17:03:16
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/risk_control/v1/recaptcha_enums.proto

package riskcontrolpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// recaptcha version
type RecaptchaVersion int32

const (
	// unspecified
	RecaptchaVersion_RECAPTCHA_VERSION_UNSPECIFIED RecaptchaVersion = 0
	// V2
	RecaptchaVersion_RECAPTCHA_VERSION_V2 RecaptchaVersion = 2
	// V3
	RecaptchaVersion_RECAPTCHA_VERSION_V3 RecaptchaVersion = 3
)

// Enum value maps for RecaptchaVersion.
var (
	RecaptchaVersion_name = map[int32]string{
		0: "RECAPTCHA_VERSION_UNSPECIFIED",
		2: "RECAPTCHA_VERSION_V2",
		3: "RECAPTCHA_VERSION_V3",
	}
	RecaptchaVersion_value = map[string]int32{
		"RECAPTCHA_VERSION_UNSPECIFIED": 0,
		"RECAPTCHA_VERSION_V2":          2,
		"RECAPTCHA_VERSION_V3":          3,
	}
)

func (x RecaptchaVersion) Enum() *RecaptchaVersion {
	p := new(RecaptchaVersion)
	*p = x
	return p
}

func (x RecaptchaVersion) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecaptchaVersion) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_risk_control_v1_recaptcha_enums_proto_enumTypes[0].Descriptor()
}

func (RecaptchaVersion) Type() protoreflect.EnumType {
	return &file_moego_models_risk_control_v1_recaptcha_enums_proto_enumTypes[0]
}

func (x RecaptchaVersion) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecaptchaVersion.Descriptor instead.
func (RecaptchaVersion) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_risk_control_v1_recaptcha_enums_proto_rawDescGZIP(), []int{0}
}

// recaptcha v3 action
type RecaptchaAction int32

const (
	// unspecified
	RecaptchaAction_RECAPTCHA_ACTION_UNSPECIFIED RecaptchaAction = 0
	// intake form submit
	RecaptchaAction_RECAPTCHA_ACTION_IF_SUBMIT RecaptchaAction = 1
	// online booking 2.0 submit
	RecaptchaAction_RECAPTCHA_ACTION_OB_V2_SUBMIT RecaptchaAction = 2
	// online booking 3.0 submit
	RecaptchaAction_RECAPTCHA_ACTION_OB_V3_SUBMIT RecaptchaAction = 3
	// online booking 3.0 send verification code
	RecaptchaAction_RECAPTCHA_ACTION_OB_V3_SEND_CODE RecaptchaAction = 4
)

// Enum value maps for RecaptchaAction.
var (
	RecaptchaAction_name = map[int32]string{
		0: "RECAPTCHA_ACTION_UNSPECIFIED",
		1: "RECAPTCHA_ACTION_IF_SUBMIT",
		2: "RECAPTCHA_ACTION_OB_V2_SUBMIT",
		3: "RECAPTCHA_ACTION_OB_V3_SUBMIT",
		4: "RECAPTCHA_ACTION_OB_V3_SEND_CODE",
	}
	RecaptchaAction_value = map[string]int32{
		"RECAPTCHA_ACTION_UNSPECIFIED":     0,
		"RECAPTCHA_ACTION_IF_SUBMIT":       1,
		"RECAPTCHA_ACTION_OB_V2_SUBMIT":    2,
		"RECAPTCHA_ACTION_OB_V3_SUBMIT":    3,
		"RECAPTCHA_ACTION_OB_V3_SEND_CODE": 4,
	}
)

func (x RecaptchaAction) Enum() *RecaptchaAction {
	p := new(RecaptchaAction)
	*p = x
	return p
}

func (x RecaptchaAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecaptchaAction) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_risk_control_v1_recaptcha_enums_proto_enumTypes[1].Descriptor()
}

func (RecaptchaAction) Type() protoreflect.EnumType {
	return &file_moego_models_risk_control_v1_recaptcha_enums_proto_enumTypes[1]
}

func (x RecaptchaAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecaptchaAction.Descriptor instead.
func (RecaptchaAction) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_risk_control_v1_recaptcha_enums_proto_rawDescGZIP(), []int{1}
}

var File_moego_models_risk_control_v1_recaptcha_enums_proto protoreflect.FileDescriptor

var file_moego_models_risk_control_v1_recaptcha_enums_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x72,
	0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e,
	0x76, 0x31, 0x2a, 0x69, 0x0a, 0x10, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x43, 0x41, 0x50, 0x54,
	0x43, 0x48, 0x41, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x43,
	0x41, 0x50, 0x54, 0x43, 0x48, 0x41, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x56,
	0x32, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x43, 0x41, 0x50, 0x54, 0x43, 0x48, 0x41,
	0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x33, 0x10, 0x03, 0x2a, 0xbf, 0x01,
	0x0a, 0x0f, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x43, 0x41, 0x50, 0x54, 0x43, 0x48, 0x41, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x43, 0x41, 0x50, 0x54, 0x43, 0x48, 0x41,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x46, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49,
	0x54, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x43, 0x41, 0x50, 0x54, 0x43, 0x48, 0x41,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x42, 0x5f, 0x56, 0x32, 0x5f, 0x53, 0x55,
	0x42, 0x4d, 0x49, 0x54, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x43, 0x41, 0x50, 0x54,
	0x43, 0x48, 0x41, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x42, 0x5f, 0x56, 0x33,
	0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x10, 0x03, 0x12, 0x24, 0x0a, 0x20, 0x52, 0x45, 0x43,
	0x41, 0x50, 0x54, 0x43, 0x48, 0x41, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x42,
	0x5f, 0x56, 0x33, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x04, 0x42,
	0x89, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5f, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x3b, 0x72, 0x69,
	0x73, 0x6b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_models_risk_control_v1_recaptcha_enums_proto_rawDescOnce sync.Once
	file_moego_models_risk_control_v1_recaptcha_enums_proto_rawDescData = file_moego_models_risk_control_v1_recaptcha_enums_proto_rawDesc
)

func file_moego_models_risk_control_v1_recaptcha_enums_proto_rawDescGZIP() []byte {
	file_moego_models_risk_control_v1_recaptcha_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_risk_control_v1_recaptcha_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_risk_control_v1_recaptcha_enums_proto_rawDescData)
	})
	return file_moego_models_risk_control_v1_recaptcha_enums_proto_rawDescData
}

var file_moego_models_risk_control_v1_recaptcha_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_risk_control_v1_recaptcha_enums_proto_goTypes = []interface{}{
	(RecaptchaVersion)(0), // 0: moego.models.risk_control.v1.RecaptchaVersion
	(RecaptchaAction)(0),  // 1: moego.models.risk_control.v1.RecaptchaAction
}
var file_moego_models_risk_control_v1_recaptcha_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_risk_control_v1_recaptcha_enums_proto_init() }
func file_moego_models_risk_control_v1_recaptcha_enums_proto_init() {
	if File_moego_models_risk_control_v1_recaptcha_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_risk_control_v1_recaptcha_enums_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_risk_control_v1_recaptcha_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_risk_control_v1_recaptcha_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_risk_control_v1_recaptcha_enums_proto_enumTypes,
	}.Build()
	File_moego_models_risk_control_v1_recaptcha_enums_proto = out.File
	file_moego_models_risk_control_v1_recaptcha_enums_proto_rawDesc = nil
	file_moego_models_risk_control_v1_recaptcha_enums_proto_goTypes = nil
	file_moego_models_risk_control_v1_recaptcha_enums_proto_depIdxs = nil
}
