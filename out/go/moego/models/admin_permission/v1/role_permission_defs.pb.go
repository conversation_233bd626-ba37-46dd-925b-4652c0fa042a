// @since 2023-05-27 21:28:41
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/admin_permission/v1/role_permission_defs.proto

package adminpermissionpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// the eq condition
type EqConditionDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the target value
	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *EqConditionDef) Reset() {
	*x = EqConditionDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EqConditionDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EqConditionDef) ProtoMessage() {}

func (x *EqConditionDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EqConditionDef.ProtoReflect.Descriptor instead.
func (*EqConditionDef) Descriptor() ([]byte, []int) {
	return file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDescGZIP(), []int{0}
}

func (x *EqConditionDef) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// the in condition
type InConditionDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the target values
	Values []string `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *InConditionDef) Reset() {
	*x = InConditionDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InConditionDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InConditionDef) ProtoMessage() {}

func (x *InConditionDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InConditionDef.ProtoReflect.Descriptor instead.
func (*InConditionDef) Descriptor() ([]byte, []int) {
	return file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDescGZIP(), []int{1}
}

func (x *InConditionDef) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

// match regexp
type MatchConditionDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pattern
	Pattern string `protobuf:"bytes,1,opt,name=pattern,proto3" json:"pattern,omitempty"`
}

func (x *MatchConditionDef) Reset() {
	*x = MatchConditionDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchConditionDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchConditionDef) ProtoMessage() {}

func (x *MatchConditionDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchConditionDef.ProtoReflect.Descriptor instead.
func (*MatchConditionDef) Descriptor() ([]byte, []int) {
	return file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDescGZIP(), []int{2}
}

func (x *MatchConditionDef) GetPattern() string {
	if x != nil {
		return x.Pattern
	}
	return ""
}

// the filter model
type FilterDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the attribute
	Attribute string `protobuf:"bytes,1,opt,name=attribute,proto3" json:"attribute,omitempty"`
	// reverse condition result
	Reverse bool `protobuf:"varint,2,opt,name=reverse,proto3" json:"reverse,omitempty"`
	// condition
	//
	// Types that are assignable to Condition:
	//
	//	*FilterDef_Eq
	//	*FilterDef_In
	//	*FilterDef_Match
	Condition isFilterDef_Condition `protobuf_oneof:"condition"`
}

func (x *FilterDef) Reset() {
	*x = FilterDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterDef) ProtoMessage() {}

func (x *FilterDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterDef.ProtoReflect.Descriptor instead.
func (*FilterDef) Descriptor() ([]byte, []int) {
	return file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDescGZIP(), []int{3}
}

func (x *FilterDef) GetAttribute() string {
	if x != nil {
		return x.Attribute
	}
	return ""
}

func (x *FilterDef) GetReverse() bool {
	if x != nil {
		return x.Reverse
	}
	return false
}

func (m *FilterDef) GetCondition() isFilterDef_Condition {
	if m != nil {
		return m.Condition
	}
	return nil
}

func (x *FilterDef) GetEq() *EqConditionDef {
	if x, ok := x.GetCondition().(*FilterDef_Eq); ok {
		return x.Eq
	}
	return nil
}

func (x *FilterDef) GetIn() *InConditionDef {
	if x, ok := x.GetCondition().(*FilterDef_In); ok {
		return x.In
	}
	return nil
}

func (x *FilterDef) GetMatch() *MatchConditionDef {
	if x, ok := x.GetCondition().(*FilterDef_Match); ok {
		return x.Match
	}
	return nil
}

type isFilterDef_Condition interface {
	isFilterDef_Condition()
}

type FilterDef_Eq struct {
	// eq
	Eq *EqConditionDef `protobuf:"bytes,3,opt,name=eq,proto3,oneof"`
}

type FilterDef_In struct {
	// in
	In *InConditionDef `protobuf:"bytes,4,opt,name=in,proto3,oneof"`
}

type FilterDef_Match struct {
	// match
	Match *MatchConditionDef `protobuf:"bytes,5,opt,name=match,proto3,oneof"`
}

func (*FilterDef_Eq) isFilterDef_Condition() {}

func (*FilterDef_In) isFilterDef_Condition() {}

func (*FilterDef_Match) isFilterDef_Condition() {}

var File_moego_models_admin_permission_v1_role_permission_defs_proto protoreflect.FileDescriptor

var file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2f, 0x0a, 0x0e, 0x45, 0x71, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x1d, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x18, 0x32, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x38, 0x0a, 0x0e, 0x49, 0x6e, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x26, 0x0a, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0e, 0xfa, 0x42, 0x0b,
	0x92, 0x01, 0x08, 0x10, 0x64, 0x22, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x06, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x22, 0x2d, 0x0a, 0x11, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x74, 0x74,
	0x65, 0x72, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x6e, 0x22, 0xb5, 0x02, 0x0a, 0x09, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x44, 0x65, 0x66,
	0x12, 0x27, 0x0a, 0x09, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x09,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x76,
	0x65, 0x72, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x65, 0x76, 0x65,
	0x72, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x02, 0x65, 0x71, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x71, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x66, 0x48, 0x00, 0x52, 0x02, 0x65, 0x71, 0x12, 0x42, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x4b, 0x0a, 0x05, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x48,
	0x00, 0x52, 0x05, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x42, 0x10, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x95, 0x01, 0x0a, 0x28, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x67, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x3b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDescOnce sync.Once
	file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDescData = file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDesc
)

func file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDescGZIP() []byte {
	file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDescData)
	})
	return file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDescData
}

var file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_models_admin_permission_v1_role_permission_defs_proto_goTypes = []interface{}{
	(*EqConditionDef)(nil),    // 0: moego.models.admin_permission.v1.EqConditionDef
	(*InConditionDef)(nil),    // 1: moego.models.admin_permission.v1.InConditionDef
	(*MatchConditionDef)(nil), // 2: moego.models.admin_permission.v1.MatchConditionDef
	(*FilterDef)(nil),         // 3: moego.models.admin_permission.v1.FilterDef
}
var file_moego_models_admin_permission_v1_role_permission_defs_proto_depIdxs = []int32{
	0, // 0: moego.models.admin_permission.v1.FilterDef.eq:type_name -> moego.models.admin_permission.v1.EqConditionDef
	1, // 1: moego.models.admin_permission.v1.FilterDef.in:type_name -> moego.models.admin_permission.v1.InConditionDef
	2, // 2: moego.models.admin_permission.v1.FilterDef.match:type_name -> moego.models.admin_permission.v1.MatchConditionDef
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_admin_permission_v1_role_permission_defs_proto_init() }
func file_moego_models_admin_permission_v1_role_permission_defs_proto_init() {
	if File_moego_models_admin_permission_v1_role_permission_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EqConditionDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InConditionDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchConditionDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*FilterDef_Eq)(nil),
		(*FilterDef_In)(nil),
		(*FilterDef_Match)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_admin_permission_v1_role_permission_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_admin_permission_v1_role_permission_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_admin_permission_v1_role_permission_defs_proto_msgTypes,
	}.Build()
	File_moego_models_admin_permission_v1_role_permission_defs_proto = out.File
	file_moego_models_admin_permission_v1_role_permission_defs_proto_rawDesc = nil
	file_moego_models_admin_permission_v1_role_permission_defs_proto_goTypes = nil
	file_moego_models_admin_permission_v1_role_permission_defs_proto_depIdxs = nil
}
