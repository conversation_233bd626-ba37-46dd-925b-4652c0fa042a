// @since 2-23-12-14
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/pay_ops/v1/business_dispute_models.proto

package payopspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// stripe dispute model,
// corresponding to the `moe_payment`.`mm_stripe_dispute` table in MySQL.
type StripeDisputeModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Business identifier
	BusinessId int32 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Dispute identifier
	DisputeId string `protobuf:"bytes,3,opt,name=dispute_id,json=disputeId,proto3" json:"dispute_id,omitempty"`
	// Payment id
	PaymentId int32 `protobuf:"varint,4,opt,name=payment_id,json=paymentId,proto3" json:"payment_id,omitempty"`
	// Payment intent id
	PaymentIntentId string `protobuf:"bytes,5,opt,name=payment_intent_id,json=paymentIntentId,proto3" json:"payment_intent_id,omitempty"`
	// Payment method (com.stripe.model.Charge#paymentMethod)
	PaymentMethod string `protobuf:"bytes,6,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"`
	// Amount (com.stripe.model.Dispute#amount)
	Amount int64 `protobuf:"varint,7,opt,name=amount,proto3" json:"amount,omitempty"`
	// Currency (com.stripe.model.Dispute#currency)
	Currency string `protobuf:"bytes,8,opt,name=currency,proto3" json:"currency,omitempty"`
	// Status (com.stripe.model.Dispute#status)
	Status string `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
	// Reason for the dispute (com.stripe.model.Dispute#reason)
	Reason string `protobuf:"bytes,10,opt,name=reason,proto3" json:"reason,omitempty"`
	// Customer (com.stripe.model.Charge#customer)
	Customer string `protobuf:"bytes,11,opt,name=customer,proto3" json:"customer,omitempty"`
	// Charge creation time (com.stripe.model.Charge#created)
	ChargedOn int64 `protobuf:"varint,12,opt,name=charged_on,json=chargedOn,proto3" json:"charged_on,omitempty"`
	// Dispute creation time (com.stripe.model.Dispute#created)
	DisputedOn int64 `protobuf:"varint,13,opt,name=disputed_on,json=disputedOn,proto3" json:"disputed_on,omitempty"`
	// Dispute response due time (com.stripe.model.Dispute.EvidenceDetails#dueBy)
	RespondedOn int64 `protobuf:"varint,14,opt,name=responded_on,json=respondedOn,proto3" json:"responded_on,omitempty"`
	// Record creation timestamp
	CreatedAt int64 `protobuf:"varint,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Record update timestamp
	UpdatedAt int64 `protobuf:"varint,16,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Record deletion timestamp (nullable)
	DeletedAt int64 `protobuf:"varint,17,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// business email
	BusinessEmail string `protobuf:"bytes,18,opt,name=business_email,json=businessEmail,proto3" json:"business_email,omitempty"`
}

func (x *StripeDisputeModel) Reset() {
	*x = StripeDisputeModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_business_dispute_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeDisputeModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeDisputeModel) ProtoMessage() {}

func (x *StripeDisputeModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_business_dispute_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeDisputeModel.ProtoReflect.Descriptor instead.
func (*StripeDisputeModel) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDescGZIP(), []int{0}
}

func (x *StripeDisputeModel) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StripeDisputeModel) GetBusinessId() int32 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *StripeDisputeModel) GetDisputeId() string {
	if x != nil {
		return x.DisputeId
	}
	return ""
}

func (x *StripeDisputeModel) GetPaymentId() int32 {
	if x != nil {
		return x.PaymentId
	}
	return 0
}

func (x *StripeDisputeModel) GetPaymentIntentId() string {
	if x != nil {
		return x.PaymentIntentId
	}
	return ""
}

func (x *StripeDisputeModel) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *StripeDisputeModel) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *StripeDisputeModel) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *StripeDisputeModel) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *StripeDisputeModel) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *StripeDisputeModel) GetCustomer() string {
	if x != nil {
		return x.Customer
	}
	return ""
}

func (x *StripeDisputeModel) GetChargedOn() int64 {
	if x != nil {
		return x.ChargedOn
	}
	return 0
}

func (x *StripeDisputeModel) GetDisputedOn() int64 {
	if x != nil {
		return x.DisputedOn
	}
	return 0
}

func (x *StripeDisputeModel) GetRespondedOn() int64 {
	if x != nil {
		return x.RespondedOn
	}
	return 0
}

func (x *StripeDisputeModel) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *StripeDisputeModel) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *StripeDisputeModel) GetDeletedAt() int64 {
	if x != nil {
		return x.DeletedAt
	}
	return 0
}

func (x *StripeDisputeModel) GetBusinessEmail() string {
	if x != nil {
		return x.BusinessEmail
	}
	return ""
}

// stripe dispute info
type StripeDisputeInfoModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stripe dispute id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// dispute amount
	Amount int64 `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// associated charge id
	ChargeId string `protobuf:"bytes,3,opt,name=charge_id,json=chargeId,proto3" json:"charge_id,omitempty"`
	// currency code
	Currency string `protobuf:"bytes,4,opt,name=currency,proto3" json:"currency,omitempty"`
	// additional metadata
	Metadata string `protobuf:"bytes,5,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// dispute reason
	Reason string `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`
	// current status of the dispute
	Status string `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	// creation timestamp
	Created int64 `protobuf:"varint,8,opt,name=created,proto3" json:"created,omitempty"`
	// evidence details
	Evidence *EvidenceModel `protobuf:"bytes,9,opt,name=evidence,proto3" json:"evidence,omitempty"`
	// detailed evidence information
	EvidenceDetails *EvidenceDetailsModel `protobuf:"bytes,10,opt,name=evidence_details,json=evidenceDetails,proto3" json:"evidence_details,omitempty"`
}

func (x *StripeDisputeInfoModel) Reset() {
	*x = StripeDisputeInfoModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_business_dispute_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeDisputeInfoModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeDisputeInfoModel) ProtoMessage() {}

func (x *StripeDisputeInfoModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_business_dispute_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeDisputeInfoModel.ProtoReflect.Descriptor instead.
func (*StripeDisputeInfoModel) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDescGZIP(), []int{1}
}

func (x *StripeDisputeInfoModel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StripeDisputeInfoModel) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *StripeDisputeInfoModel) GetChargeId() string {
	if x != nil {
		return x.ChargeId
	}
	return ""
}

func (x *StripeDisputeInfoModel) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *StripeDisputeInfoModel) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *StripeDisputeInfoModel) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *StripeDisputeInfoModel) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *StripeDisputeInfoModel) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *StripeDisputeInfoModel) GetEvidence() *EvidenceModel {
	if x != nil {
		return x.Evidence
	}
	return nil
}

func (x *StripeDisputeInfoModel) GetEvidenceDetails() *EvidenceDetailsModel {
	if x != nil {
		return x.EvidenceDetails
	}
	return nil
}

// evidence details model
type EvidenceDetailsModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// deadline for submitting evidence
	DueBy int64 `protobuf:"varint,1,opt,name=due_by,json=dueBy,proto3" json:"due_by,omitempty"`
	// whether evidence has been submitted
	HasEvidence bool `protobuf:"varint,2,opt,name=has_evidence,json=hasEvidence,proto3" json:"has_evidence,omitempty"`
	// whether the submission was past the due date
	PastDue bool `protobuf:"varint,3,opt,name=past_due,json=pastDue,proto3" json:"past_due,omitempty"`
	// number of times evidence has been submitted
	SubmissionCount int64 `protobuf:"varint,4,opt,name=submission_count,json=submissionCount,proto3" json:"submission_count,omitempty"`
}

func (x *EvidenceDetailsModel) Reset() {
	*x = EvidenceDetailsModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_business_dispute_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvidenceDetailsModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceDetailsModel) ProtoMessage() {}

func (x *EvidenceDetailsModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_business_dispute_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceDetailsModel.ProtoReflect.Descriptor instead.
func (*EvidenceDetailsModel) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDescGZIP(), []int{2}
}

func (x *EvidenceDetailsModel) GetDueBy() int64 {
	if x != nil {
		return x.DueBy
	}
	return 0
}

func (x *EvidenceDetailsModel) GetHasEvidence() bool {
	if x != nil {
		return x.HasEvidence
	}
	return false
}

func (x *EvidenceDetailsModel) GetPastDue() bool {
	if x != nil {
		return x.PastDue
	}
	return false
}

func (x *EvidenceDetailsModel) GetSubmissionCount() int64 {
	if x != nil {
		return x.SubmissionCount
	}
	return 0
}

// evidence model
type EvidenceModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// server or activity logs showing customer access
	AccessActivityLog string `protobuf:"bytes,1,opt,name=access_activity_log,json=accessActivityLog,proto3" json:"access_activity_log,omitempty"`
	// customer's billing address
	BillingAddress string `protobuf:"bytes,2,opt,name=billing_address,json=billingAddress,proto3" json:"billing_address,omitempty"`
	// subscription cancellation policy document id
	CancellationPolicy string `protobuf:"bytes,3,opt,name=cancellation_policy,json=cancellationPolicy,proto3" json:"cancellation_policy,omitempty"`
	// explanation of how refund policy was shown
	CancellationPolicyDisclosure string `protobuf:"bytes,4,opt,name=cancellation_policy_disclosure,json=cancellationPolicyDisclosure,proto3" json:"cancellation_policy_disclosure,omitempty"`
	// justification for not canceling subscription
	CancellationRebuttal string `protobuf:"bytes,5,opt,name=cancellation_rebuttal,json=cancellationRebuttal,proto3" json:"cancellation_rebuttal,omitempty"`
	// communication with the customer
	CustomerCommunication string `protobuf:"bytes,6,opt,name=customer_communication,json=customerCommunication,proto3" json:"customer_communication,omitempty"`
	// customer's email address
	CustomerEmailAddress string `protobuf:"bytes,7,opt,name=customer_email_address,json=customerEmailAddress,proto3" json:"customer_email_address,omitempty"`
	// customer's name
	CustomerName string `protobuf:"bytes,8,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	// ip address used for purchase
	CustomerPurchaseIp string `protobuf:"bytes,9,opt,name=customer_purchase_ip,json=customerPurchaseIp,proto3" json:"customer_purchase_ip,omitempty"`
	// customer's signature document id
	CustomerSignature string `protobuf:"bytes,10,opt,name=customer_signature,json=customerSignature,proto3" json:"customer_signature,omitempty"`
	// documentation for duplicate charge
	DuplicateChargeDocumentation string `protobuf:"bytes,11,opt,name=duplicate_charge_documentation,json=duplicateChargeDocumentation,proto3" json:"duplicate_charge_documentation,omitempty"`
	// explanation of duplicate charge
	DuplicateChargeExplanation string `protobuf:"bytes,12,opt,name=duplicate_charge_explanation,json=duplicateChargeExplanation,proto3" json:"duplicate_charge_explanation,omitempty"`
	// id of the duplicate charge
	DuplicateChargeId string `protobuf:"bytes,13,opt,name=duplicate_charge_id,json=duplicateChargeId,proto3" json:"duplicate_charge_id,omitempty"`
	// description of the sold product or service
	ProductDescription string `protobuf:"bytes,14,opt,name=product_description,json=productDescription,proto3" json:"product_description,omitempty"`
	// receipt or notification sent to customer
	Receipt string `protobuf:"bytes,15,opt,name=receipt,proto3" json:"receipt,omitempty"`
	// refund policy document id
	RefundPolicy string `protobuf:"bytes,16,opt,name=refund_policy,json=refundPolicy,proto3" json:"refund_policy,omitempty"`
	// documentation of refund policy disclosure
	RefundPolicyDisclosure string `protobuf:"bytes,17,opt,name=refund_policy_disclosure,json=refundPolicyDisclosure,proto3" json:"refund_policy_disclosure,omitempty"`
	// justification for refusing a refund
	RefundRefusalExplanation string `protobuf:"bytes,18,opt,name=refund_refusal_explanation,json=refundRefusalExplanation,proto3" json:"refund_refusal_explanation,omitempty"`
	// date when service was received
	ServiceDate string `protobuf:"bytes,19,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
	// proof of service provided
	ServiceDocumentation string `protobuf:"bytes,20,opt,name=service_documentation,json=serviceDocumentation,proto3" json:"service_documentation,omitempty"`
	// address where product was shipped
	ShippingAddress string `protobuf:"bytes,21,opt,name=shipping_address,json=shippingAddress,proto3" json:"shipping_address,omitempty"`
	// carrier used for shipping
	ShippingCarrier string `protobuf:"bytes,22,opt,name=shipping_carrier,json=shippingCarrier,proto3" json:"shipping_carrier,omitempty"`
	// date when product was shipped
	ShippingDate string `protobuf:"bytes,23,opt,name=shipping_date,json=shippingDate,proto3" json:"shipping_date,omitempty"`
	// shipping documentation
	ShippingDocumentation string `protobuf:"bytes,24,opt,name=shipping_documentation,json=shippingDocumentation,proto3" json:"shipping_documentation,omitempty"`
	// tracking number for the shipment
	ShippingTrackingNumber string `protobuf:"bytes,25,opt,name=shipping_tracking_number,json=shippingTrackingNumber,proto3" json:"shipping_tracking_number,omitempty"`
	// additional evidence file id
	UncategorizedFile string `protobuf:"bytes,26,opt,name=uncategorized_file,json=uncategorizedFile,proto3" json:"uncategorized_file,omitempty"`
	// additional text evidence
	UncategorizedText string `protobuf:"bytes,27,opt,name=uncategorized_text,json=uncategorizedText,proto3" json:"uncategorized_text,omitempty"`
}

func (x *EvidenceModel) Reset() {
	*x = EvidenceModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_business_dispute_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvidenceModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceModel) ProtoMessage() {}

func (x *EvidenceModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_business_dispute_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceModel.ProtoReflect.Descriptor instead.
func (*EvidenceModel) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDescGZIP(), []int{3}
}

func (x *EvidenceModel) GetAccessActivityLog() string {
	if x != nil {
		return x.AccessActivityLog
	}
	return ""
}

func (x *EvidenceModel) GetBillingAddress() string {
	if x != nil {
		return x.BillingAddress
	}
	return ""
}

func (x *EvidenceModel) GetCancellationPolicy() string {
	if x != nil {
		return x.CancellationPolicy
	}
	return ""
}

func (x *EvidenceModel) GetCancellationPolicyDisclosure() string {
	if x != nil {
		return x.CancellationPolicyDisclosure
	}
	return ""
}

func (x *EvidenceModel) GetCancellationRebuttal() string {
	if x != nil {
		return x.CancellationRebuttal
	}
	return ""
}

func (x *EvidenceModel) GetCustomerCommunication() string {
	if x != nil {
		return x.CustomerCommunication
	}
	return ""
}

func (x *EvidenceModel) GetCustomerEmailAddress() string {
	if x != nil {
		return x.CustomerEmailAddress
	}
	return ""
}

func (x *EvidenceModel) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *EvidenceModel) GetCustomerPurchaseIp() string {
	if x != nil {
		return x.CustomerPurchaseIp
	}
	return ""
}

func (x *EvidenceModel) GetCustomerSignature() string {
	if x != nil {
		return x.CustomerSignature
	}
	return ""
}

func (x *EvidenceModel) GetDuplicateChargeDocumentation() string {
	if x != nil {
		return x.DuplicateChargeDocumentation
	}
	return ""
}

func (x *EvidenceModel) GetDuplicateChargeExplanation() string {
	if x != nil {
		return x.DuplicateChargeExplanation
	}
	return ""
}

func (x *EvidenceModel) GetDuplicateChargeId() string {
	if x != nil {
		return x.DuplicateChargeId
	}
	return ""
}

func (x *EvidenceModel) GetProductDescription() string {
	if x != nil {
		return x.ProductDescription
	}
	return ""
}

func (x *EvidenceModel) GetReceipt() string {
	if x != nil {
		return x.Receipt
	}
	return ""
}

func (x *EvidenceModel) GetRefundPolicy() string {
	if x != nil {
		return x.RefundPolicy
	}
	return ""
}

func (x *EvidenceModel) GetRefundPolicyDisclosure() string {
	if x != nil {
		return x.RefundPolicyDisclosure
	}
	return ""
}

func (x *EvidenceModel) GetRefundRefusalExplanation() string {
	if x != nil {
		return x.RefundRefusalExplanation
	}
	return ""
}

func (x *EvidenceModel) GetServiceDate() string {
	if x != nil {
		return x.ServiceDate
	}
	return ""
}

func (x *EvidenceModel) GetServiceDocumentation() string {
	if x != nil {
		return x.ServiceDocumentation
	}
	return ""
}

func (x *EvidenceModel) GetShippingAddress() string {
	if x != nil {
		return x.ShippingAddress
	}
	return ""
}

func (x *EvidenceModel) GetShippingCarrier() string {
	if x != nil {
		return x.ShippingCarrier
	}
	return ""
}

func (x *EvidenceModel) GetShippingDate() string {
	if x != nil {
		return x.ShippingDate
	}
	return ""
}

func (x *EvidenceModel) GetShippingDocumentation() string {
	if x != nil {
		return x.ShippingDocumentation
	}
	return ""
}

func (x *EvidenceModel) GetShippingTrackingNumber() string {
	if x != nil {
		return x.ShippingTrackingNumber
	}
	return ""
}

func (x *EvidenceModel) GetUncategorizedFile() string {
	if x != nil {
		return x.UncategorizedFile
	}
	return ""
}

func (x *EvidenceModel) GetUncategorizedText() string {
	if x != nil {
		return x.UncategorizedText
	}
	return ""
}

var File_moego_models_pay_ops_v1_business_dispute_models_proto protoreflect.FileDescriptor

var file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31,
	0x22, 0xbd, 0x04, 0x0a, 0x12, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x44, 0x69, 0x73, 0x70, 0x75,
	0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x70,
	0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x69,
	0x73, 0x70, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x4f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70,
	0x75, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x64,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x4f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x64, 0x4f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x22, 0xfd, 0x02, 0x0a, 0x16, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x44, 0x69, 0x73, 0x70, 0x75,
	0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1a, 0x0a, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x12, 0x42, 0x0a, 0x08, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x65, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x58, 0x0a, 0x10, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x0f, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x22, 0x96, 0x01, 0x0a, 0x14, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x75, 0x65,
	0x5f, 0x62, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x64, 0x75, 0x65, 0x42, 0x79,
	0x12, 0x21, 0x0a, 0x0c, 0x68, 0x61, 0x73, 0x5f, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x68, 0x61, 0x73, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x74, 0x5f, 0x64, 0x75, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65, 0x12, 0x29,
	0x0a, 0x10, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xc9, 0x0a, 0x0a, 0x0d, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2e, 0x0a, 0x13, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x6c,
	0x6f, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x12, 0x27, 0x0a, 0x0f, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x2f, 0x0a, 0x13, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x44, 0x0a, 0x1e, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x64, 0x69, 0x73,
	0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x63,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x33, 0x0a, 0x15, 0x63,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x62, 0x75,
	0x74, 0x74, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x62, 0x75, 0x74, 0x74, 0x61, 0x6c,
	0x12, 0x35, 0x0a, 0x16, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d,
	0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x15, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61,
	0x73, 0x65, 0x49, 0x70, 0x12, 0x2d, 0x0a, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x12, 0x44, 0x0a, 0x1e, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x64, 0x75, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x1c, 0x64, 0x75, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x65, 0x78,
	0x70, 0x6c, 0x61, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x1a, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x45, 0x78, 0x70, 0x6c, 0x61, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x64,
	0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x13, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x38, 0x0a, 0x18, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x64, 0x69, 0x73,
	0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x72, 0x65, 0x66, 0x75, 0x73, 0x61, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x61, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x52, 0x65, 0x66, 0x75, 0x73, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x15, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x73,
	0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65,
	0x72, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x16, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a,
	0x18, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x16, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x12, 0x75, 0x6e, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x75, 0x6e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a,
	0x65, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x75, 0x6e, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x75, 0x6e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65,
	0x64, 0x54, 0x65, 0x78, 0x74, 0x42, 0x7a, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x55, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61,
	0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6f, 0x70, 0x73, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDescOnce sync.Once
	file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDescData = file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDesc
)

func file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDescGZIP() []byte {
	file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDescOnce.Do(func() {
		file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDescData)
	})
	return file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDescData
}

var file_moego_models_pay_ops_v1_business_dispute_models_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_models_pay_ops_v1_business_dispute_models_proto_goTypes = []interface{}{
	(*StripeDisputeModel)(nil),     // 0: moego.models.pay_ops.v1.StripeDisputeModel
	(*StripeDisputeInfoModel)(nil), // 1: moego.models.pay_ops.v1.StripeDisputeInfoModel
	(*EvidenceDetailsModel)(nil),   // 2: moego.models.pay_ops.v1.EvidenceDetailsModel
	(*EvidenceModel)(nil),          // 3: moego.models.pay_ops.v1.EvidenceModel
}
var file_moego_models_pay_ops_v1_business_dispute_models_proto_depIdxs = []int32{
	3, // 0: moego.models.pay_ops.v1.StripeDisputeInfoModel.evidence:type_name -> moego.models.pay_ops.v1.EvidenceModel
	2, // 1: moego.models.pay_ops.v1.StripeDisputeInfoModel.evidence_details:type_name -> moego.models.pay_ops.v1.EvidenceDetailsModel
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_pay_ops_v1_business_dispute_models_proto_init() }
func file_moego_models_pay_ops_v1_business_dispute_models_proto_init() {
	if File_moego_models_pay_ops_v1_business_dispute_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_pay_ops_v1_business_dispute_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeDisputeModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_business_dispute_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeDisputeInfoModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_business_dispute_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvidenceDetailsModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_business_dispute_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvidenceModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_pay_ops_v1_business_dispute_models_proto_goTypes,
		DependencyIndexes: file_moego_models_pay_ops_v1_business_dispute_models_proto_depIdxs,
		MessageInfos:      file_moego_models_pay_ops_v1_business_dispute_models_proto_msgTypes,
	}.Build()
	File_moego_models_pay_ops_v1_business_dispute_models_proto = out.File
	file_moego_models_pay_ops_v1_business_dispute_models_proto_rawDesc = nil
	file_moego_models_pay_ops_v1_business_dispute_models_proto_goTypes = nil
	file_moego_models_pay_ops_v1_business_dispute_models_proto_depIdxs = nil
}
