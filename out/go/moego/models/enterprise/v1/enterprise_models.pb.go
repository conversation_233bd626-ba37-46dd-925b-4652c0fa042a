// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/enterprise_models.proto

package enterprisepb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// source
// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// source
type EnterpriseModel_Source int32

const (
	// normally add
	EnterpriseModel_NORMALLY_ADD EnterpriseModel_Source = 0
	// manually add
	EnterpriseModel_MANUALLY_ADD EnterpriseModel_Source = 1
	// split company
	EnterpriseModel_SPLIT_COMPANY EnterpriseModel_Source = 2
	// demo enterprise
	EnterpriseModel_DEMO_ENTERPRISE EnterpriseModel_Source = 3
)

// Enum value maps for EnterpriseModel_Source.
var (
	EnterpriseModel_Source_name = map[int32]string{
		0: "NORMALLY_ADD",
		1: "MANUALLY_ADD",
		2: "SPLIT_COMPANY",
		3: "DEMO_ENTERPRISE",
	}
	EnterpriseModel_Source_value = map[string]int32{
		"NORMALLY_ADD":    0,
		"MANUALLY_ADD":    1,
		"SPLIT_COMPANY":   2,
		"DEMO_ENTERPRISE": 3,
	}
)

func (x EnterpriseModel_Source) Enum() *EnterpriseModel_Source {
	p := new(EnterpriseModel_Source)
	*p = x
	return p
}

func (x EnterpriseModel_Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnterpriseModel_Source) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_enterprise_models_proto_enumTypes[0].Descriptor()
}

func (EnterpriseModel_Source) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_enterprise_models_proto_enumTypes[0]
}

func (x EnterpriseModel_Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnterpriseModel_Source.Descriptor instead.
func (EnterpriseModel_Source) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_enterprise_models_proto_rawDescGZIP(), []int{0, 0}
}

// EnterpriseModel
type EnterpriseModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// account_id
	AccountId int64 `protobuf:"varint,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// email
	Email string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	// theme color
	ThemeColor string `protobuf:"bytes,5,opt,name=theme_color,json=themeColor,proto3" json:"theme_color,omitempty"`
	// currency code
	CurrencyCode string `protobuf:"bytes,6,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// currency symbol
	CurrencySymbol string `protobuf:"bytes,7,opt,name=currency_symbol,json=currencySymbol,proto3" json:"currency_symbol,omitempty"`
	// logo path
	LogoPath string `protobuf:"bytes,8,opt,name=logo_path,json=logoPath,proto3" json:"logo_path,omitempty"`
	// date format
	DateFormatType DateFormat `protobuf:"varint,9,opt,name=date_format_type,json=dateFormatType,proto3,enum=moego.models.enterprise.v1.DateFormat" json:"date_format_type,omitempty"`
	// time format
	TimeFormatType TimeFormat `protobuf:"varint,10,opt,name=time_format_type,json=timeFormatType,proto3,enum=moego.models.enterprise.v1.TimeFormat" json:"time_format_type,omitempty"`
	// unit of weight
	UnitOfWeightType WeightUnit `protobuf:"varint,11,opt,name=unit_of_weight_type,json=unitOfWeightType,proto3,enum=moego.models.enterprise.v1.WeightUnit" json:"unit_of_weight_type,omitempty"`
	// unit of distance
	UnitOfDistanceType DistanceUnit `protobuf:"varint,12,opt,name=unit_of_distance_type,json=unitOfDistanceType,proto3,enum=moego.models.enterprise.v1.DistanceUnit" json:"unit_of_distance_type,omitempty"`
	// whether the notification sound is on
	NotificationSoundEnable bool `protobuf:"varint,13,opt,name=notification_sound_enable,json=notificationSoundEnable,proto3" json:"notification_sound_enable,omitempty"`
	// country
	Country *CountryDef `protobuf:"bytes,14,opt,name=country,proto3" json:"country,omitempty"`
	// timezone
	TimeZone *TimeZone `protobuf:"bytes,15,opt,name=time_zone,json=timeZone,proto3" json:"time_zone,omitempty"`
	// address
	Address *AddressDef `protobuf:"bytes,16,opt,name=address,proto3" json:"address,omitempty"`
	// source
	Source EnterpriseModel_Source `protobuf:"varint,17,opt,name=source,proto3,enum=moego.models.enterprise.v1.EnterpriseModel_Source" json:"source,omitempty"`
}

func (x *EnterpriseModel) Reset() {
	*x = EnterpriseModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_enterprise_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnterpriseModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnterpriseModel) ProtoMessage() {}

func (x *EnterpriseModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_enterprise_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnterpriseModel.ProtoReflect.Descriptor instead.
func (*EnterpriseModel) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_enterprise_models_proto_rawDescGZIP(), []int{0}
}

func (x *EnterpriseModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EnterpriseModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EnterpriseModel) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *EnterpriseModel) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *EnterpriseModel) GetThemeColor() string {
	if x != nil {
		return x.ThemeColor
	}
	return ""
}

func (x *EnterpriseModel) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *EnterpriseModel) GetCurrencySymbol() string {
	if x != nil {
		return x.CurrencySymbol
	}
	return ""
}

func (x *EnterpriseModel) GetLogoPath() string {
	if x != nil {
		return x.LogoPath
	}
	return ""
}

func (x *EnterpriseModel) GetDateFormatType() DateFormat {
	if x != nil {
		return x.DateFormatType
	}
	return DateFormat_DATE_FORMAT_UNSPECIFIED
}

func (x *EnterpriseModel) GetTimeFormatType() TimeFormat {
	if x != nil {
		return x.TimeFormatType
	}
	return TimeFormat_TIME_FORMAT_UNSPECIFIED
}

func (x *EnterpriseModel) GetUnitOfWeightType() WeightUnit {
	if x != nil {
		return x.UnitOfWeightType
	}
	return WeightUnit_WEIGHT_UNIT_UNSPECIFIED
}

func (x *EnterpriseModel) GetUnitOfDistanceType() DistanceUnit {
	if x != nil {
		return x.UnitOfDistanceType
	}
	return DistanceUnit_DISTANCE_UNIT_UNSPECIFIED
}

func (x *EnterpriseModel) GetNotificationSoundEnable() bool {
	if x != nil {
		return x.NotificationSoundEnable
	}
	return false
}

func (x *EnterpriseModel) GetCountry() *CountryDef {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *EnterpriseModel) GetTimeZone() *TimeZone {
	if x != nil {
		return x.TimeZone
	}
	return nil
}

func (x *EnterpriseModel) GetAddress() *AddressDef {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *EnterpriseModel) GetSource() EnterpriseModel_Source {
	if x != nil {
		return x.Source
	}
	return EnterpriseModel_NORMALLY_ADD
}

// definition of address
type AddressDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// address 1
	Address1 *string `protobuf:"bytes,1,opt,name=address1,proto3,oneof" json:"address1,omitempty"`
	// address 2
	Address2 *string `protobuf:"bytes,2,opt,name=address2,proto3,oneof" json:"address2,omitempty"`
	// city
	City *string `protobuf:"bytes,3,opt,name=city,proto3,oneof" json:"city,omitempty"`
	// state
	State *string `protobuf:"bytes,4,opt,name=state,proto3,oneof" json:"state,omitempty"`
	// zip code
	Zipcode *string `protobuf:"bytes,5,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// country
	Country *string `protobuf:"bytes,6,opt,name=country,proto3,oneof" json:"country,omitempty"`
	// latitude and longitude
	Coordinate *latlng.LatLng `protobuf:"bytes,7,opt,name=coordinate,proto3,oneof" json:"coordinate,omitempty"`
}

func (x *AddressDef) Reset() {
	*x = AddressDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_enterprise_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressDef) ProtoMessage() {}

func (x *AddressDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_enterprise_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressDef.ProtoReflect.Descriptor instead.
func (*AddressDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_enterprise_models_proto_rawDescGZIP(), []int{1}
}

func (x *AddressDef) GetAddress1() string {
	if x != nil && x.Address1 != nil {
		return *x.Address1
	}
	return ""
}

func (x *AddressDef) GetAddress2() string {
	if x != nil && x.Address2 != nil {
		return *x.Address2
	}
	return ""
}

func (x *AddressDef) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *AddressDef) GetState() string {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ""
}

func (x *AddressDef) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *AddressDef) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *AddressDef) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

var File_moego_models_enterprise_v1_enterprise_models_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_enterprise_models_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c, 0x61,
	0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f,
	0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xf3, 0x07, 0x0a, 0x0f, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1b, 0x0a,
	0x09, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x50, 0x61, 0x74, 0x68, 0x12, 0x50, 0x0a, 0x10, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x0e, 0x64, 0x61,
	0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x50, 0x0a, 0x10,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x0e,
	0x74, 0x69, 0x6d, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x55,
	0x0a, 0x13, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x55,
	0x6e, 0x69, 0x74, 0x52, 0x10, 0x75, 0x6e, 0x69, 0x74, 0x4f, 0x66, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5b, 0x0a, 0x15, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6f, 0x66,
	0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x12,
	0x75, 0x6e, 0x69, 0x74, 0x4f, 0x66, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x3a, 0x0a, 0x19, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x6e, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x40,
	0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x66, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x41, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a,
	0x6f, 0x6e, 0x65, 0x12, 0x40, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x66, 0x52, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x4a, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x22, 0x54, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x4e,
	0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a,
	0x0c, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x01, 0x12,
	0x11, 0x0a, 0x0d, 0x53, 0x50, 0x4c, 0x49, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59,
	0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x45, 0x4d, 0x4f, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52,
	0x50, 0x52, 0x49, 0x53, 0x45, 0x10, 0x03, 0x22, 0x92, 0x03, 0x0a, 0x0a, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x44, 0x65, 0x66, 0x12, 0x2b, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10,
	0x01, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31,
	0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff,
	0x01, 0x48, 0x01, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x88, 0x01, 0x01,
	0x12, 0x22, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x32, 0x48, 0x02, 0x52, 0x04, 0x63, 0x69, 0x74,
	0x79, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x32, 0x48, 0x03,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x07, 0x7a, 0x69,
	0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x00, 0x18, 0x32, 0x48, 0x04, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32,
	0x48, 0x05, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x38,
	0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x48, 0x06, 0x52, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64,
	0x69, 0x6e, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x31, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x32, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x42, 0x08, 0x0a, 0x06, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64,
	0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x42, 0x84, 0x01, 0x0a,
	0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_enterprise_models_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_enterprise_models_proto_rawDescData = file_moego_models_enterprise_v1_enterprise_models_proto_rawDesc
)

func file_moego_models_enterprise_v1_enterprise_models_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_enterprise_models_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_enterprise_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_enterprise_models_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_enterprise_models_proto_rawDescData
}

var file_moego_models_enterprise_v1_enterprise_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_enterprise_v1_enterprise_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_enterprise_v1_enterprise_models_proto_goTypes = []interface{}{
	(EnterpriseModel_Source)(0), // 0: moego.models.enterprise.v1.EnterpriseModel.Source
	(*EnterpriseModel)(nil),     // 1: moego.models.enterprise.v1.EnterpriseModel
	(*AddressDef)(nil),          // 2: moego.models.enterprise.v1.AddressDef
	(DateFormat)(0),             // 3: moego.models.enterprise.v1.DateFormat
	(TimeFormat)(0),             // 4: moego.models.enterprise.v1.TimeFormat
	(WeightUnit)(0),             // 5: moego.models.enterprise.v1.WeightUnit
	(DistanceUnit)(0),           // 6: moego.models.enterprise.v1.DistanceUnit
	(*CountryDef)(nil),          // 7: moego.models.enterprise.v1.CountryDef
	(*TimeZone)(nil),            // 8: moego.models.enterprise.v1.TimeZone
	(*latlng.LatLng)(nil),       // 9: google.type.LatLng
}
var file_moego_models_enterprise_v1_enterprise_models_proto_depIdxs = []int32{
	3, // 0: moego.models.enterprise.v1.EnterpriseModel.date_format_type:type_name -> moego.models.enterprise.v1.DateFormat
	4, // 1: moego.models.enterprise.v1.EnterpriseModel.time_format_type:type_name -> moego.models.enterprise.v1.TimeFormat
	5, // 2: moego.models.enterprise.v1.EnterpriseModel.unit_of_weight_type:type_name -> moego.models.enterprise.v1.WeightUnit
	6, // 3: moego.models.enterprise.v1.EnterpriseModel.unit_of_distance_type:type_name -> moego.models.enterprise.v1.DistanceUnit
	7, // 4: moego.models.enterprise.v1.EnterpriseModel.country:type_name -> moego.models.enterprise.v1.CountryDef
	8, // 5: moego.models.enterprise.v1.EnterpriseModel.time_zone:type_name -> moego.models.enterprise.v1.TimeZone
	2, // 6: moego.models.enterprise.v1.EnterpriseModel.address:type_name -> moego.models.enterprise.v1.AddressDef
	0, // 7: moego.models.enterprise.v1.EnterpriseModel.source:type_name -> moego.models.enterprise.v1.EnterpriseModel.Source
	9, // 8: moego.models.enterprise.v1.AddressDef.coordinate:type_name -> google.type.LatLng
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_enterprise_models_proto_init() }
func file_moego_models_enterprise_v1_enterprise_models_proto_init() {
	if File_moego_models_enterprise_v1_enterprise_models_proto != nil {
		return
	}
	file_moego_models_enterprise_v1_country_defs_proto_init()
	file_moego_models_enterprise_v1_enterprise_enums_proto_init()
	file_moego_models_enterprise_v1_time_zone_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_enterprise_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnterpriseModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_enterprise_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_enterprise_v1_enterprise_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_enterprise_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_enterprise_models_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_enterprise_models_proto_depIdxs,
		EnumInfos:         file_moego_models_enterprise_v1_enterprise_models_proto_enumTypes,
		MessageInfos:      file_moego_models_enterprise_v1_enterprise_models_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_enterprise_models_proto = out.File
	file_moego_models_enterprise_v1_enterprise_models_proto_rawDesc = nil
	file_moego_models_enterprise_v1_enterprise_models_proto_goTypes = nil
	file_moego_models_enterprise_v1_enterprise_models_proto_depIdxs = nil
}
