// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/configuration_models.proto

package enterprisepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// configuration template type
type ConfigurationTemplate_ConfigurationTemplateType int32

const (
	// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
	// Unspecified configuration template type
	ConfigurationTemplate_TYPE_UNSPECIFIED ConfigurationTemplate_ConfigurationTemplateType = 0
	// agreement
	ConfigurationTemplate_AGREEMENT ConfigurationTemplate_ConfigurationTemplateType = 1
	// intake form
	ConfigurationTemplate_INTAKE_FORM ConfigurationTemplate_ConfigurationTemplateType = 2
	// client & pet
	// client tag
	ConfigurationTemplate_CLIENT_AND_PET_CLIENT_TAG ConfigurationTemplate_ConfigurationTemplateType = 3
	// referral source
	ConfigurationTemplate_CLIENT_AND_PET_CLIENT_REFERRAL_SOURCE ConfigurationTemplate_ConfigurationTemplateType = 4
	// grooming frequency
	ConfigurationTemplate_CLIENT_AND_PET_CLIENT_GROOMING_FREQUENCY ConfigurationTemplate_ConfigurationTemplateType = 5
	// pet code
	ConfigurationTemplate_CLIENT_AND_PET_PET_CODE ConfigurationTemplate_ConfigurationTemplateType = 6
	// pet type & breed
	ConfigurationTemplate_CLIENT_AND_PET_PET_TYPE_AND_BREED ConfigurationTemplate_ConfigurationTemplateType = 7
	// pet size
	ConfigurationTemplate_CLIENT_AND_PET_PET_SIZE ConfigurationTemplate_ConfigurationTemplateType = 8
	// pet vaccine
	ConfigurationTemplate_CLIENT_AND_PET_PET_VACCINE ConfigurationTemplate_ConfigurationTemplateType = 9
	// pet coat type
	ConfigurationTemplate_CLIENT_AND_PET_PET_COAT_TYPE ConfigurationTemplate_ConfigurationTemplateType = 10
	// pet fixed
	ConfigurationTemplate_CLIENT_AND_PET_PET_FIXED ConfigurationTemplate_ConfigurationTemplateType = 11
	// pet behavior
	ConfigurationTemplate_CLIENT_AND_PET_PET_BEHAVIOR ConfigurationTemplate_ConfigurationTemplateType = 12
)

// Enum value maps for ConfigurationTemplate_ConfigurationTemplateType.
var (
	ConfigurationTemplate_ConfigurationTemplateType_name = map[int32]string{
		0:  "TYPE_UNSPECIFIED",
		1:  "AGREEMENT",
		2:  "INTAKE_FORM",
		3:  "CLIENT_AND_PET_CLIENT_TAG",
		4:  "CLIENT_AND_PET_CLIENT_REFERRAL_SOURCE",
		5:  "CLIENT_AND_PET_CLIENT_GROOMING_FREQUENCY",
		6:  "CLIENT_AND_PET_PET_CODE",
		7:  "CLIENT_AND_PET_PET_TYPE_AND_BREED",
		8:  "CLIENT_AND_PET_PET_SIZE",
		9:  "CLIENT_AND_PET_PET_VACCINE",
		10: "CLIENT_AND_PET_PET_COAT_TYPE",
		11: "CLIENT_AND_PET_PET_FIXED",
		12: "CLIENT_AND_PET_PET_BEHAVIOR",
	}
	ConfigurationTemplate_ConfigurationTemplateType_value = map[string]int32{
		"TYPE_UNSPECIFIED":                         0,
		"AGREEMENT":                                1,
		"INTAKE_FORM":                              2,
		"CLIENT_AND_PET_CLIENT_TAG":                3,
		"CLIENT_AND_PET_CLIENT_REFERRAL_SOURCE":    4,
		"CLIENT_AND_PET_CLIENT_GROOMING_FREQUENCY": 5,
		"CLIENT_AND_PET_PET_CODE":                  6,
		"CLIENT_AND_PET_PET_TYPE_AND_BREED":        7,
		"CLIENT_AND_PET_PET_SIZE":                  8,
		"CLIENT_AND_PET_PET_VACCINE":               9,
		"CLIENT_AND_PET_PET_COAT_TYPE":             10,
		"CLIENT_AND_PET_PET_FIXED":                 11,
		"CLIENT_AND_PET_PET_BEHAVIOR":              12,
	}
)

func (x ConfigurationTemplate_ConfigurationTemplateType) Enum() *ConfigurationTemplate_ConfigurationTemplateType {
	p := new(ConfigurationTemplate_ConfigurationTemplateType)
	*p = x
	return p
}

func (x ConfigurationTemplate_ConfigurationTemplateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConfigurationTemplate_ConfigurationTemplateType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_configuration_models_proto_enumTypes[0].Descriptor()
}

func (ConfigurationTemplate_ConfigurationTemplateType) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_configuration_models_proto_enumTypes[0]
}

func (x ConfigurationTemplate_ConfigurationTemplateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConfigurationTemplate_ConfigurationTemplateType.Descriptor instead.
func (ConfigurationTemplate_ConfigurationTemplateType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_configuration_models_proto_rawDescGZIP(), []int{0, 0}
}

// publish status
type ConfigurationTemplate_PublishStatus int32

const (
	// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
	// Unspecified publish status
	ConfigurationTemplate_STATUS_UNSPECIFIED ConfigurationTemplate_PublishStatus = 0
	// unpublished, never published
	ConfigurationTemplate_UNPUBLISHED ConfigurationTemplate_PublishStatus = 1
	// up to date
	ConfigurationTemplate_UP_TO_DATE ConfigurationTemplate_PublishStatus = 2
	// outdated
	ConfigurationTemplate_OUTDATED ConfigurationTemplate_PublishStatus = 3
)

// Enum value maps for ConfigurationTemplate_PublishStatus.
var (
	ConfigurationTemplate_PublishStatus_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "UNPUBLISHED",
		2: "UP_TO_DATE",
		3: "OUTDATED",
	}
	ConfigurationTemplate_PublishStatus_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"UNPUBLISHED":        1,
		"UP_TO_DATE":         2,
		"OUTDATED":           3,
	}
)

func (x ConfigurationTemplate_PublishStatus) Enum() *ConfigurationTemplate_PublishStatus {
	p := new(ConfigurationTemplate_PublishStatus)
	*p = x
	return p
}

func (x ConfigurationTemplate_PublishStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConfigurationTemplate_PublishStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_configuration_models_proto_enumTypes[1].Descriptor()
}

func (ConfigurationTemplate_PublishStatus) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_configuration_models_proto_enumTypes[1]
}

func (x ConfigurationTemplate_PublishStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConfigurationTemplate_PublishStatus.Descriptor instead.
func (ConfigurationTemplate_PublishStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_configuration_models_proto_rawDescGZIP(), []int{0, 1}
}

// publish result
type ConfigurationTemplatePublishRecord_PublishResult int32

const (
	// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
	// Unspecified
	ConfigurationTemplatePublishRecord_UNSPECIFIED ConfigurationTemplatePublishRecord_PublishResult = 0
	// success
	ConfigurationTemplatePublishRecord_SUCCESS ConfigurationTemplatePublishRecord_PublishResult = 1
	// failed
	ConfigurationTemplatePublishRecord_FAILED ConfigurationTemplatePublishRecord_PublishResult = 2
)

// Enum value maps for ConfigurationTemplatePublishRecord_PublishResult.
var (
	ConfigurationTemplatePublishRecord_PublishResult_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "SUCCESS",
		2: "FAILED",
	}
	ConfigurationTemplatePublishRecord_PublishResult_value = map[string]int32{
		"UNSPECIFIED": 0,
		"SUCCESS":     1,
		"FAILED":      2,
	}
)

func (x ConfigurationTemplatePublishRecord_PublishResult) Enum() *ConfigurationTemplatePublishRecord_PublishResult {
	p := new(ConfigurationTemplatePublishRecord_PublishResult)
	*p = x
	return p
}

func (x ConfigurationTemplatePublishRecord_PublishResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConfigurationTemplatePublishRecord_PublishResult) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_configuration_models_proto_enumTypes[2].Descriptor()
}

func (ConfigurationTemplatePublishRecord_PublishResult) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_configuration_models_proto_enumTypes[2]
}

func (x ConfigurationTemplatePublishRecord_PublishResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConfigurationTemplatePublishRecord_PublishResult.Descriptor instead.
func (ConfigurationTemplatePublishRecord_PublishResult) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_configuration_models_proto_rawDescGZIP(), []int{1, 0}
}

// configuration template
type ConfigurationTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// type
	Type ConfigurationTemplate_ConfigurationTemplateType `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.enterprise.v1.ConfigurationTemplate_ConfigurationTemplateType" json:"type,omitempty"`
	// enterprise_id
	EnterpriseId int64 `protobuf:"varint,3,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// last_published_at
	LastPublishedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=last_published_at,json=lastPublishedAt,proto3" json:"last_published_at,omitempty"`
	// impacted franchisees
	ImpactedFranchisees int64 `protobuf:"varint,6,opt,name=impacted_franchisees,json=impactedFranchisees,proto3" json:"impacted_franchisees,omitempty"`
	// publish status
	PublishStatus ConfigurationTemplate_PublishStatus `protobuf:"varint,7,opt,name=publish_status,json=publishStatus,proto3,enum=moego.models.enterprise.v1.ConfigurationTemplate_PublishStatus" json:"publish_status,omitempty"`
}

func (x *ConfigurationTemplate) Reset() {
	*x = ConfigurationTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_configuration_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigurationTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigurationTemplate) ProtoMessage() {}

func (x *ConfigurationTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_configuration_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigurationTemplate.ProtoReflect.Descriptor instead.
func (*ConfigurationTemplate) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_configuration_models_proto_rawDescGZIP(), []int{0}
}

func (x *ConfigurationTemplate) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ConfigurationTemplate) GetType() ConfigurationTemplate_ConfigurationTemplateType {
	if x != nil {
		return x.Type
	}
	return ConfigurationTemplate_TYPE_UNSPECIFIED
}

func (x *ConfigurationTemplate) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ConfigurationTemplate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ConfigurationTemplate) GetLastPublishedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastPublishedAt
	}
	return nil
}

func (x *ConfigurationTemplate) GetImpactedFranchisees() int64 {
	if x != nil {
		return x.ImpactedFranchisees
	}
	return 0
}

func (x *ConfigurationTemplate) GetPublishStatus() ConfigurationTemplate_PublishStatus {
	if x != nil {
		return x.PublishStatus
	}
	return ConfigurationTemplate_STATUS_UNSPECIFIED
}

// configuration template publish record
type ConfigurationTemplatePublishRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// template_id
	TemplateId int64 `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// enterprise_id
	EnterpriseId int64 `protobuf:"varint,3,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// published_at
	PublishedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=published_at,json=publishedAt,proto3" json:"published_at,omitempty"`
	// impacted franchisees
	ImpactedFranchiseesNum int64 `protobuf:"varint,5,opt,name=impacted_franchisees_num,json=impactedFranchiseesNum,proto3" json:"impacted_franchisees_num,omitempty"`
	// publish result
	PublishResult ConfigurationTemplatePublishRecord_PublishResult `protobuf:"varint,6,opt,name=publish_result,json=publishResult,proto3,enum=moego.models.enterprise.v1.ConfigurationTemplatePublishRecord_PublishResult" json:"publish_result,omitempty"`
}

func (x *ConfigurationTemplatePublishRecord) Reset() {
	*x = ConfigurationTemplatePublishRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_configuration_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigurationTemplatePublishRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigurationTemplatePublishRecord) ProtoMessage() {}

func (x *ConfigurationTemplatePublishRecord) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_configuration_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigurationTemplatePublishRecord.ProtoReflect.Descriptor instead.
func (*ConfigurationTemplatePublishRecord) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_configuration_models_proto_rawDescGZIP(), []int{1}
}

func (x *ConfigurationTemplatePublishRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ConfigurationTemplatePublishRecord) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *ConfigurationTemplatePublishRecord) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ConfigurationTemplatePublishRecord) GetPublishedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PublishedAt
	}
	return nil
}

func (x *ConfigurationTemplatePublishRecord) GetImpactedFranchiseesNum() int64 {
	if x != nil {
		return x.ImpactedFranchiseesNum
	}
	return 0
}

func (x *ConfigurationTemplatePublishRecord) GetPublishResult() ConfigurationTemplatePublishRecord_PublishResult {
	if x != nil {
		return x.PublishResult
	}
	return ConfigurationTemplatePublishRecord_UNSPECIFIED
}

var File_moego_models_enterprise_v1_configuration_models_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_configuration_models_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xaa, 0x07, 0x0a, 0x15, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x5f,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x11, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0f, 0x6c, 0x61, 0x73, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x31, 0x0a, 0x14, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x69, 0x73, 0x65, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13,
	0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x65, 0x64, 0x46, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x69, 0x73,
	0x65, 0x65, 0x73, 0x12, 0x66, 0x0a, 0x0e, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xab, 0x03, 0x0a, 0x19,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x41, 0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x0f,
	0x0a, 0x0b, 0x49, 0x4e, 0x54, 0x41, 0x4b, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x10, 0x02, 0x12,
	0x1d, 0x0a, 0x19, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x45,
	0x54, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x41, 0x47, 0x10, 0x03, 0x12, 0x29,
	0x0a, 0x25, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x45, 0x54,
	0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c,
	0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x10, 0x04, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x4c, 0x49,
	0x45, 0x4e, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x43, 0x4c, 0x49, 0x45,
	0x4e, 0x54, 0x5f, 0x47, 0x52, 0x4f, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x4e, 0x43, 0x59, 0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x4c, 0x49, 0x45, 0x4e,
	0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x43, 0x4f,
	0x44, 0x45, 0x10, 0x06, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x41,
	0x4e, 0x44, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x4e, 0x44, 0x5f, 0x42, 0x52, 0x45, 0x45, 0x44, 0x10, 0x07, 0x12, 0x1b, 0x0a, 0x17, 0x43,
	0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x50, 0x45,
	0x54, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x10, 0x08, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4c, 0x49, 0x45,
	0x4e, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x56,
	0x41, 0x43, 0x43, 0x49, 0x4e, 0x45, 0x10, 0x09, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4c, 0x49, 0x45,
	0x4e, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x43,
	0x4f, 0x41, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x0a, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4c,
	0x49, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x50, 0x45, 0x54,
	0x5f, 0x46, 0x49, 0x58, 0x45, 0x44, 0x10, 0x0b, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x4c, 0x49, 0x45,
	0x4e, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x42,
	0x45, 0x48, 0x41, 0x56, 0x49, 0x4f, 0x52, 0x10, 0x0c, 0x22, 0x56, 0x0a, 0x0d, 0x50, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x45,
	0x44, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x50, 0x5f, 0x54, 0x4f, 0x5f, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x55, 0x54, 0x44, 0x41, 0x54, 0x45, 0x44, 0x10,
	0x03, 0x22, 0xa3, 0x03, 0x0a, 0x22, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x3d,
	0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x41, 0x74, 0x12, 0x38, 0x0a,
	0x18, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x69, 0x73, 0x65, 0x65, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x16, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x65, 0x64, 0x46, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x69,
	0x73, 0x65, 0x65, 0x73, 0x4e, 0x75, 0x6d, 0x12, 0x73, 0x0a, 0x0e, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0d, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x39, 0x0a, 0x0d,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0f, 0x0a,
	0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76,
	0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_configuration_models_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_configuration_models_proto_rawDescData = file_moego_models_enterprise_v1_configuration_models_proto_rawDesc
)

func file_moego_models_enterprise_v1_configuration_models_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_configuration_models_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_configuration_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_configuration_models_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_configuration_models_proto_rawDescData
}

var file_moego_models_enterprise_v1_configuration_models_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_enterprise_v1_configuration_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_enterprise_v1_configuration_models_proto_goTypes = []interface{}{
	(ConfigurationTemplate_ConfigurationTemplateType)(0),  // 0: moego.models.enterprise.v1.ConfigurationTemplate.ConfigurationTemplateType
	(ConfigurationTemplate_PublishStatus)(0),              // 1: moego.models.enterprise.v1.ConfigurationTemplate.PublishStatus
	(ConfigurationTemplatePublishRecord_PublishResult)(0), // 2: moego.models.enterprise.v1.ConfigurationTemplatePublishRecord.PublishResult
	(*ConfigurationTemplate)(nil),                         // 3: moego.models.enterprise.v1.ConfigurationTemplate
	(*ConfigurationTemplatePublishRecord)(nil),            // 4: moego.models.enterprise.v1.ConfigurationTemplatePublishRecord
	(*timestamppb.Timestamp)(nil),                         // 5: google.protobuf.Timestamp
}
var file_moego_models_enterprise_v1_configuration_models_proto_depIdxs = []int32{
	0, // 0: moego.models.enterprise.v1.ConfigurationTemplate.type:type_name -> moego.models.enterprise.v1.ConfigurationTemplate.ConfigurationTemplateType
	5, // 1: moego.models.enterprise.v1.ConfigurationTemplate.last_published_at:type_name -> google.protobuf.Timestamp
	1, // 2: moego.models.enterprise.v1.ConfigurationTemplate.publish_status:type_name -> moego.models.enterprise.v1.ConfigurationTemplate.PublishStatus
	5, // 3: moego.models.enterprise.v1.ConfigurationTemplatePublishRecord.published_at:type_name -> google.protobuf.Timestamp
	2, // 4: moego.models.enterprise.v1.ConfigurationTemplatePublishRecord.publish_result:type_name -> moego.models.enterprise.v1.ConfigurationTemplatePublishRecord.PublishResult
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_configuration_models_proto_init() }
func file_moego_models_enterprise_v1_configuration_models_proto_init() {
	if File_moego_models_enterprise_v1_configuration_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_configuration_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigurationTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_configuration_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigurationTemplatePublishRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_configuration_models_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_configuration_models_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_configuration_models_proto_depIdxs,
		EnumInfos:         file_moego_models_enterprise_v1_configuration_models_proto_enumTypes,
		MessageInfos:      file_moego_models_enterprise_v1_configuration_models_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_configuration_models_proto = out.File
	file_moego_models_enterprise_v1_configuration_models_proto_rawDesc = nil
	file_moego_models_enterprise_v1_configuration_models_proto_goTypes = nil
	file_moego_models_enterprise_v1_configuration_models_proto_depIdxs = nil
}
