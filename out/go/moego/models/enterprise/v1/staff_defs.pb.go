// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/staff_defs.proto

package enterprisepb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// resource def
type ResourceDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is all
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// tenant ids
	TenantIds []int64 `protobuf:"varint,2,rep,packed,name=tenant_ids,json=tenantIds,proto3" json:"tenant_ids,omitempty"`
	// group ids
	GroupIds []int64 `protobuf:"varint,3,rep,packed,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`
}

func (x *ResourceDef) Reset() {
	*x = ResourceDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_staff_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceDef) ProtoMessage() {}

func (x *ResourceDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_staff_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceDef.ProtoReflect.Descriptor instead.
func (*ResourceDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_staff_defs_proto_rawDescGZIP(), []int{0}
}

func (x *ResourceDef) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *ResourceDef) GetTenantIds() []int64 {
	if x != nil {
		return x.TenantIds
	}
	return nil
}

func (x *ResourceDef) GetGroupIds() []int64 {
	if x != nil {
		return x.GroupIds
	}
	return nil
}

// staff profile
type CreateStaffProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// avator path
	AvatarPath *string `protobuf:"bytes,1,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// role_id
	RoleId int64 `protobuf:"varint,4,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// hired time
	HireTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=hire_time,json=hireTime,proto3" json:"hire_time,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,6,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// note
	Note *string `protobuf:"bytes,7,opt,name=note,proto3,oneof" json:"note,omitempty"`
	// email
	ProfileEmail *string `protobuf:"bytes,8,opt,name=profile_email,json=profileEmail,proto3,oneof" json:"profile_email,omitempty"`
}

func (x *CreateStaffProfile) Reset() {
	*x = CreateStaffProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_staff_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStaffProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStaffProfile) ProtoMessage() {}

func (x *CreateStaffProfile) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_staff_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStaffProfile.ProtoReflect.Descriptor instead.
func (*CreateStaffProfile) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_staff_defs_proto_rawDescGZIP(), []int{1}
}

func (x *CreateStaffProfile) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

func (x *CreateStaffProfile) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CreateStaffProfile) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CreateStaffProfile) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *CreateStaffProfile) GetHireTime() *timestamppb.Timestamp {
	if x != nil {
		return x.HireTime
	}
	return nil
}

func (x *CreateStaffProfile) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *CreateStaffProfile) GetNote() string {
	if x != nil && x.Note != nil {
		return *x.Note
	}
	return ""
}

func (x *CreateStaffProfile) GetProfileEmail() string {
	if x != nil && x.ProfileEmail != nil {
		return *x.ProfileEmail
	}
	return ""
}

// update staff profile
type UpdateStaffProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// avator path
	AvatarPath *string `protobuf:"bytes,1,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	// first name
	FirstName *string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3,oneof" json:"first_name,omitempty"`
	// last name
	LastName *string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3,oneof" json:"last_name,omitempty"`
	// role_id
	RoleId *int64 `protobuf:"varint,4,opt,name=role_id,json=roleId,proto3,oneof" json:"role_id,omitempty"`
	// hired time
	HireTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=hire_time,json=hireTime,proto3,oneof" json:"hire_time,omitempty"`
	// color code
	ColorCode *string `protobuf:"bytes,6,opt,name=color_code,json=colorCode,proto3,oneof" json:"color_code,omitempty"`
	// note
	Note *string `protobuf:"bytes,7,opt,name=note,proto3,oneof" json:"note,omitempty"`
	// email
	ProfileEmail *string `protobuf:"bytes,8,opt,name=profile_email,json=profileEmail,proto3,oneof" json:"profile_email,omitempty"`
}

func (x *UpdateStaffProfile) Reset() {
	*x = UpdateStaffProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_staff_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffProfile) ProtoMessage() {}

func (x *UpdateStaffProfile) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_staff_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffProfile.ProtoReflect.Descriptor instead.
func (*UpdateStaffProfile) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_staff_defs_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateStaffProfile) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

func (x *UpdateStaffProfile) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *UpdateStaffProfile) GetLastName() string {
	if x != nil && x.LastName != nil {
		return *x.LastName
	}
	return ""
}

func (x *UpdateStaffProfile) GetRoleId() int64 {
	if x != nil && x.RoleId != nil {
		return *x.RoleId
	}
	return 0
}

func (x *UpdateStaffProfile) GetHireTime() *timestamppb.Timestamp {
	if x != nil {
		return x.HireTime
	}
	return nil
}

func (x *UpdateStaffProfile) GetColorCode() string {
	if x != nil && x.ColorCode != nil {
		return *x.ColorCode
	}
	return ""
}

func (x *UpdateStaffProfile) GetNote() string {
	if x != nil && x.Note != nil {
		return *x.Note
	}
	return ""
}

func (x *UpdateStaffProfile) GetProfileEmail() string {
	if x != nil && x.ProfileEmail != nil {
		return *x.ProfileEmail
	}
	return ""
}

var File_moego_models_enterprise_v1_staff_defs_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_staff_defs_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x60, 0x0a, 0x0b, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44,
	0x65, 0x66, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x64, 0x73, 0x22, 0xa2, 0x03, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x0b,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52,
	0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x28,
	0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x09, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65,
	0x49, 0x64, 0x12, 0x37, 0x0a, 0x09, 0x68, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x08, 0x68, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0a, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x48,
	0x01, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x0d, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0x64, 0x60, 0x01, 0x48, 0x02, 0x52, 0x0c,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x42,
	0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x83, 0x04, 0x0a, 0x12, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x12, 0x30, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18,
	0xff, 0x01, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68,
	0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01,
	0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18,
	0xff, 0x01, 0x48, 0x02, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x25, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x03, 0x52, 0x06, 0x72,
	0x6f, 0x6c, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x3c, 0x0a, 0x09, 0x68, 0x69, 0x72, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x04, 0x52, 0x08, 0x68, 0x69, 0x72, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x14, 0x48, 0x05, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x48,
	0x06, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x0d, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0x64, 0x60, 0x01, 0x48, 0x07, 0x52, 0x0c,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c,
	0x0a, 0x0a, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0a, 0x0a, 0x08,
	0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x68, 0x69, 0x72,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x42, 0x10,
	0x0a, 0x0e, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_staff_defs_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_staff_defs_proto_rawDescData = file_moego_models_enterprise_v1_staff_defs_proto_rawDesc
)

func file_moego_models_enterprise_v1_staff_defs_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_staff_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_staff_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_staff_defs_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_staff_defs_proto_rawDescData
}

var file_moego_models_enterprise_v1_staff_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_enterprise_v1_staff_defs_proto_goTypes = []interface{}{
	(*ResourceDef)(nil),           // 0: moego.models.enterprise.v1.ResourceDef
	(*CreateStaffProfile)(nil),    // 1: moego.models.enterprise.v1.CreateStaffProfile
	(*UpdateStaffProfile)(nil),    // 2: moego.models.enterprise.v1.UpdateStaffProfile
	(*timestamppb.Timestamp)(nil), // 3: google.protobuf.Timestamp
}
var file_moego_models_enterprise_v1_staff_defs_proto_depIdxs = []int32{
	3, // 0: moego.models.enterprise.v1.CreateStaffProfile.hire_time:type_name -> google.protobuf.Timestamp
	3, // 1: moego.models.enterprise.v1.UpdateStaffProfile.hire_time:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_staff_defs_proto_init() }
func file_moego_models_enterprise_v1_staff_defs_proto_init() {
	if File_moego_models_enterprise_v1_staff_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_staff_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_staff_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStaffProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_staff_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_enterprise_v1_staff_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_staff_defs_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_staff_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_staff_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_staff_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_enterprise_v1_staff_defs_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_staff_defs_proto = out.File
	file_moego_models_enterprise_v1_staff_defs_proto_rawDesc = nil
	file_moego_models_enterprise_v1_staff_defs_proto_goTypes = nil
	file_moego_models_enterprise_v1_staff_defs_proto_depIdxs = nil
}
