// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/price_book_models.proto

package enterprisepb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// price book
type PriceBook struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *PriceBook) Reset() {
	*x = PriceBook{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceBook) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceBook) ProtoMessage() {}

func (x *PriceBook) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceBook.ProtoReflect.Descriptor instead.
func (*PriceBook) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{0}
}

func (x *PriceBook) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PriceBook) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// category
type ServiceCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// price book
	PriceBook *PriceBook `protobuf:"bytes,3,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
	// name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,5,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// sort
	Sort int64 `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
	// service type
	ServiceType v1.ServiceType `protobuf:"varint,7,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
}

func (x *ServiceCategory) Reset() {
	*x = ServiceCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCategory) ProtoMessage() {}

func (x *ServiceCategory) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCategory.ProtoReflect.Descriptor instead.
func (*ServiceCategory) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceCategory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceCategory) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ServiceCategory) GetPriceBook() *PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

func (x *ServiceCategory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceCategory) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *ServiceCategory) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *ServiceCategory) GetServiceType() v1.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v1.ServiceType(0)
}

// service model
// 原有 service 模型是企业级设计，这里先简化
type Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// price book
	PriceBook *PriceBook `protobuf:"bytes,3,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
	// name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,5,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// category
	Category *ServiceCategory `protobuf:"bytes,6,opt,name=category,proto3" json:"category,omitempty"`
	// description
	Description string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,8,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// color
	Color string `protobuf:"bytes,9,opt,name=color,proto3" json:"color,omitempty"`
	// sort
	Sort int64 `protobuf:"varint,10,opt,name=sort,proto3" json:"sort,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,11,opt,name=price,proto3" json:"price,omitempty"`
	// service price unit
	ServicePriceUnit v1.ServicePriceUnit `protobuf:"varint,12,opt,name=service_price_unit,json=servicePriceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"service_price_unit,omitempty"`
	// 万分位税率
	TaxRate int64 `protobuf:"varint,13,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	// duration
	Duration *durationpb.Duration `protobuf:"bytes,14,opt,name=duration,proto3" json:"duration,omitempty"`
	// max duration
	MaxDuration *durationpb.Duration `protobuf:"bytes,15,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// limitation
	Limitation *Service_Limitation `protobuf:"bytes,16,opt,name=limitation,proto3" json:"limitation,omitempty"`
	// service type
	ServiceType v1.ServiceType `protobuf:"varint,17,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
	// images
	Images []string `protobuf:"bytes,18,rep,name=images,proto3" json:"images,omitempty"`
}

func (x *Service) Reset() {
	*x = Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service) ProtoMessage() {}

func (x *Service) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service.ProtoReflect.Descriptor instead.
func (*Service) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{2}
}

func (x *Service) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Service) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *Service) GetPriceBook() *PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

func (x *Service) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Service) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *Service) GetCategory() *ServiceCategory {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *Service) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Service) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *Service) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *Service) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *Service) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *Service) GetServicePriceUnit() v1.ServicePriceUnit {
	if x != nil {
		return x.ServicePriceUnit
	}
	return v1.ServicePriceUnit(0)
}

func (x *Service) GetTaxRate() int64 {
	if x != nil {
		return x.TaxRate
	}
	return 0
}

func (x *Service) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *Service) GetMaxDuration() *durationpb.Duration {
	if x != nil {
		return x.MaxDuration
	}
	return nil
}

func (x *Service) GetLimitation() *Service_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

func (x *Service) GetServiceType() v1.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v1.ServiceType(0)
}

func (x *Service) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

// pet breed
type PetBreed struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type id
	PetTypeId v11.PetType `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type_id,omitempty"`
	// pet type name
	PetTypeName string `protobuf:"bytes,2,opt,name=pet_type_name,json=petTypeName,proto3" json:"pet_type_name,omitempty"`
	// pet breed name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *PetBreed) Reset() {
	*x = PetBreed{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetBreed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetBreed) ProtoMessage() {}

func (x *PetBreed) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetBreed.ProtoReflect.Descriptor instead.
func (*PetBreed) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{3}
}

func (x *PetBreed) GetPetTypeId() v11.PetType {
	if x != nil {
		return x.PetTypeId
	}
	return v11.PetType(0)
}

func (x *PetBreed) GetPetTypeName() string {
	if x != nil {
		return x.PetTypeName
	}
	return ""
}

func (x *PetBreed) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// pet type model
// Pet type can not be deleted, only can be set to unavailable.
type PetType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id, primary key of pet type record in database
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet type id
	PetTypeId v11.PetType `protobuf:"varint,2,opt,name=pet_type_id,json=petTypeId,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type_id,omitempty"`
	// pet type name, e.g. Dog, Cat, etc.
	PetTypeName string `protobuf:"bytes,3,opt,name=pet_type_name,json=petTypeName,proto3" json:"pet_type_name,omitempty"`
	// if the pet type is available
	IsAvailable bool `protobuf:"varint,4,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// pet type sort. The larger the sort number, the higher the priority.
	Sort int32 `protobuf:"varint,5,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *PetType) Reset() {
	*x = PetType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetType) ProtoMessage() {}

func (x *PetType) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetType.ProtoReflect.Descriptor instead.
func (*PetType) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{4}
}

func (x *PetType) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetType) GetPetTypeId() v11.PetType {
	if x != nil {
		return x.PetTypeId
	}
	return v11.PetType(0)
}

func (x *PetType) GetPetTypeName() string {
	if x != nil {
		return x.PetTypeName
	}
	return ""
}

func (x *PetType) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *PetType) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// weight range
type WeightRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// min weight
	Min *Weight `protobuf:"bytes,1,opt,name=min,proto3" json:"min,omitempty"`
	// max weight
	Max *Weight `protobuf:"bytes,2,opt,name=max,proto3" json:"max,omitempty"`
}

func (x *WeightRange) Reset() {
	*x = WeightRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeightRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeightRange) ProtoMessage() {}

func (x *WeightRange) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeightRange.ProtoReflect.Descriptor instead.
func (*WeightRange) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{5}
}

func (x *WeightRange) GetMin() *Weight {
	if x != nil {
		return x.Min
	}
	return nil
}

func (x *WeightRange) GetMax() *Weight {
	if x != nil {
		return x.Max
	}
	return nil
}

// weight
type Weight struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// weight
	Weight int64 `protobuf:"varint,1,opt,name=weight,proto3" json:"weight,omitempty"`
	// unit
	Unit WeightUnit `protobuf:"varint,2,opt,name=unit,proto3,enum=moego.models.enterprise.v1.WeightUnit" json:"unit,omitempty"`
}

func (x *Weight) Reset() {
	*x = Weight{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Weight) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Weight) ProtoMessage() {}

func (x *Weight) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Weight.ProtoReflect.Descriptor instead.
func (*Weight) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{6}
}

func (x *Weight) GetWeight() int64 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *Weight) GetUnit() WeightUnit {
	if x != nil {
		return x.Unit
	}
	return WeightUnit_WEIGHT_UNIT_UNSPECIFIED
}

// service change history
type ServiceChangeHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// impacted tenants
	ImpactedTenants int64 `protobuf:"varint,4,opt,name=impacted_tenants,json=impactedTenants,proto3" json:"impacted_tenants,omitempty"`
	// roll out at
	RollOutAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=roll_out_at,json=rollOutAt,proto3" json:"roll_out_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// impacted tenant ids
	ImpactedTenantIds []int64 `protobuf:"varint,7,rep,packed,name=impacted_tenant_ids,json=impactedTenantIds,proto3" json:"impacted_tenant_ids,omitempty"`
}

func (x *ServiceChangeHistory) Reset() {
	*x = ServiceChangeHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceChangeHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceChangeHistory) ProtoMessage() {}

func (x *ServiceChangeHistory) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceChangeHistory.ProtoReflect.Descriptor instead.
func (*ServiceChangeHistory) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{7}
}

func (x *ServiceChangeHistory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceChangeHistory) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ServiceChangeHistory) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ServiceChangeHistory) GetImpactedTenants() int64 {
	if x != nil {
		return x.ImpactedTenants
	}
	return 0
}

func (x *ServiceChangeHistory) GetRollOutAt() *timestamppb.Timestamp {
	if x != nil {
		return x.RollOutAt
	}
	return nil
}

func (x *ServiceChangeHistory) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *ServiceChangeHistory) GetImpactedTenantIds() []int64 {
	if x != nil {
		return x.ImpactedTenantIds
	}
	return nil
}

// service change
type ServiceChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// history id
	HistoryId int64 `protobuf:"varint,3,opt,name=history_id,json=historyId,proto3" json:"history_id,omitempty"`
	// target
	Target *TenantObject `protobuf:"bytes,4,opt,name=target,proto3" json:"target,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,5,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,6,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// details
	DetailCategories []*ServiceChange_DetailCategory `protobuf:"bytes,7,rep,name=detail_categories,json=detailCategories,proto3" json:"detail_categories,omitempty"`
}

func (x *ServiceChange) Reset() {
	*x = ServiceChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceChange) ProtoMessage() {}

func (x *ServiceChange) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceChange.ProtoReflect.Descriptor instead.
func (*ServiceChange) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{8}
}

func (x *ServiceChange) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceChange) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ServiceChange) GetHistoryId() int64 {
	if x != nil {
		return x.HistoryId
	}
	return 0
}

func (x *ServiceChange) GetTarget() *TenantObject {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *ServiceChange) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ServiceChange) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ServiceChange) GetDetailCategories() []*ServiceChange_DetailCategory {
	if x != nil {
		return x.DetailCategories
	}
	return nil
}

// limitation
type Service_Limitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// all pet types and breeds
	AllPetTypesAndBreeds bool `protobuf:"varint,1,opt,name=all_pet_types_and_breeds,json=allPetTypesAndBreeds,proto3" json:"all_pet_types_and_breeds,omitempty"`
	// required pet types
	PetTypeBreeds []*Service_Limitation_PetTypeBreeds `protobuf:"bytes,2,rep,name=pet_type_breeds,json=petTypeBreeds,proto3" json:"pet_type_breeds,omitempty"`
	// all pet sizes
	AllPetSizes bool `protobuf:"varint,3,opt,name=all_pet_sizes,json=allPetSizes,proto3" json:"all_pet_sizes,omitempty"`
	// pet weight range
	PetWeightRange *WeightRange `protobuf:"bytes,4,opt,name=pet_weight_range,json=petWeightRange,proto3" json:"pet_weight_range,omitempty"`
	// all coat types
	AllCoatTypes bool `protobuf:"varint,5,opt,name=all_coat_types,json=allCoatTypes,proto3" json:"all_coat_types,omitempty"`
	// required coat types
	RequiredCoatTypes []string `protobuf:"bytes,6,rep,name=required_coat_types,json=requiredCoatTypes,proto3" json:"required_coat_types,omitempty"`
}

func (x *Service_Limitation) Reset() {
	*x = Service_Limitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service_Limitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service_Limitation) ProtoMessage() {}

func (x *Service_Limitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service_Limitation.ProtoReflect.Descriptor instead.
func (*Service_Limitation) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Service_Limitation) GetAllPetTypesAndBreeds() bool {
	if x != nil {
		return x.AllPetTypesAndBreeds
	}
	return false
}

func (x *Service_Limitation) GetPetTypeBreeds() []*Service_Limitation_PetTypeBreeds {
	if x != nil {
		return x.PetTypeBreeds
	}
	return nil
}

func (x *Service_Limitation) GetAllPetSizes() bool {
	if x != nil {
		return x.AllPetSizes
	}
	return false
}

func (x *Service_Limitation) GetPetWeightRange() *WeightRange {
	if x != nil {
		return x.PetWeightRange
	}
	return nil
}

func (x *Service_Limitation) GetAllCoatTypes() bool {
	if x != nil {
		return x.AllCoatTypes
	}
	return false
}

func (x *Service_Limitation) GetRequiredCoatTypes() []string {
	if x != nil {
		return x.RequiredCoatTypes
	}
	return nil
}

// pet type breeds
type Service_Limitation_PetTypeBreeds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// all breeds
	AllBreeds bool `protobuf:"varint,1,opt,name=all_breeds,json=allBreeds,proto3" json:"all_breeds,omitempty"`
	// pet type id
	PetTypeId v11.PetType `protobuf:"varint,2,opt,name=pet_type_id,json=petTypeId,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type_id,omitempty"`
	// pet type name
	PetTypeName string `protobuf:"bytes,3,opt,name=pet_type_name,json=petTypeName,proto3" json:"pet_type_name,omitempty"`
	// required breeds
	PetBreeds []*PetBreed `protobuf:"bytes,4,rep,name=pet_breeds,json=petBreeds,proto3" json:"pet_breeds,omitempty"`
}

func (x *Service_Limitation_PetTypeBreeds) Reset() {
	*x = Service_Limitation_PetTypeBreeds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service_Limitation_PetTypeBreeds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service_Limitation_PetTypeBreeds) ProtoMessage() {}

func (x *Service_Limitation_PetTypeBreeds) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service_Limitation_PetTypeBreeds.ProtoReflect.Descriptor instead.
func (*Service_Limitation_PetTypeBreeds) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{2, 0, 0}
}

func (x *Service_Limitation_PetTypeBreeds) GetAllBreeds() bool {
	if x != nil {
		return x.AllBreeds
	}
	return false
}

func (x *Service_Limitation_PetTypeBreeds) GetPetTypeId() v11.PetType {
	if x != nil {
		return x.PetTypeId
	}
	return v11.PetType(0)
}

func (x *Service_Limitation_PetTypeBreeds) GetPetTypeName() string {
	if x != nil {
		return x.PetTypeName
	}
	return ""
}

func (x *Service_Limitation_PetTypeBreeds) GetPetBreeds() []*PetBreed {
	if x != nil {
		return x.PetBreeds
	}
	return nil
}

// detail
type ServiceChange_Detail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// field name
	FieldName string `protobuf:"bytes,1,opt,name=field_name,json=fieldName,proto3" json:"field_name,omitempty"`
	// field type
	Type v2.Field_Type `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.reporting.v2.Field_Type" json:"type,omitempty"`
	// old value
	Old *v2.Value `protobuf:"bytes,3,opt,name=old,proto3" json:"old,omitempty"`
	// new value
	New *v2.Value `protobuf:"bytes,4,opt,name=new,proto3" json:"new,omitempty"`
	// is changed
	IsChanged bool `protobuf:"varint,5,opt,name=is_changed,json=isChanged,proto3" json:"is_changed,omitempty"`
}

func (x *ServiceChange_Detail) Reset() {
	*x = ServiceChange_Detail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceChange_Detail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceChange_Detail) ProtoMessage() {}

func (x *ServiceChange_Detail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceChange_Detail.ProtoReflect.Descriptor instead.
func (*ServiceChange_Detail) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ServiceChange_Detail) GetFieldName() string {
	if x != nil {
		return x.FieldName
	}
	return ""
}

func (x *ServiceChange_Detail) GetType() v2.Field_Type {
	if x != nil {
		return x.Type
	}
	return v2.Field_Type(0)
}

func (x *ServiceChange_Detail) GetOld() *v2.Value {
	if x != nil {
		return x.Old
	}
	return nil
}

func (x *ServiceChange_Detail) GetNew() *v2.Value {
	if x != nil {
		return x.New
	}
	return nil
}

func (x *ServiceChange_Detail) GetIsChanged() bool {
	if x != nil {
		return x.IsChanged
	}
	return false
}

// detail category
type ServiceChange_DetailCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// details
	Details []*ServiceChange_Detail `protobuf:"bytes,2,rep,name=details,proto3" json:"details,omitempty"`
}

func (x *ServiceChange_DetailCategory) Reset() {
	*x = ServiceChange_DetailCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceChange_DetailCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceChange_DetailCategory) ProtoMessage() {}

func (x *ServiceChange_DetailCategory) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceChange_DetailCategory.ProtoReflect.Descriptor instead.
func (*ServiceChange_DetailCategory) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{8, 1}
}

func (x *ServiceChange_DetailCategory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceChange_DetailCategory) GetDetails() []*ServiceChange_Detail {
	if x != nil {
		return x.Details
	}
	return nil
}

var File_moego_models_enterprise_v1_price_book_models_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_price_book_models_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x2f, 0x0a, 0x09, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0xdf, 0x02, 0x0a, 0x0f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0a, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74,
	0x12, 0x52, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x22, 0xbd, 0x0b, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x58, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74,
	0x61, 0x78, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74,
	0x61, 0x78, 0x52, 0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a,
	0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b,
	0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x0a, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18,
	0x12, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x1a, 0xd4, 0x04,
	0x0a, 0x0a, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x18,
	0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x5f, 0x61, 0x6e,
	0x64, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14,
	0x61, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x41, 0x6e, 0x64, 0x42, 0x72,
	0x65, 0x65, 0x64, 0x73, 0x12, 0x64, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x52, 0x0d, 0x70, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x6c,
	0x6c, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x73, 0x12, 0x51,
	0x0a, 0x10, 0x70, 0x65, 0x74, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x72, 0x61, 0x6e,
	0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x61, 0x6c, 0x6c, 0x43, 0x6f,
	0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x43, 0x6f,
	0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x1a, 0xda, 0x01, 0x0a, 0x0d, 0x50, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6c, 0x6c,
	0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x61,
	0x6c, 0x6c, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x12, 0x41, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x09, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x70,
	0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x43, 0x0a, 0x0a, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x52, 0x09, 0x70, 0x65, 0x74, 0x42, 0x72,
	0x65, 0x65, 0x64, 0x73, 0x22, 0x85, 0x01, 0x0a, 0x08, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65,
	0x64, 0x12, 0x41, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x70, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xb7, 0x01, 0x0a,
	0x07, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x41, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x09, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x70,
	0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x22, 0x79, 0x0a, 0x0b, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12, 0x34, 0x0a, 0x03, 0x6d,
	0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x03, 0x6d, 0x61,
	0x78, 0x22, 0x5c, 0x0a, 0x06, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x77,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x12, 0x3a, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x22,
	0xbc, 0x02, 0x0a, 0x14, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10,
	0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x65, 0x64,
	0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x12, 0x3a, 0x0a, 0x0b, 0x72, 0x6f, 0x6c, 0x6c, 0x5f,
	0x6f, 0x75, 0x74, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x72, 0x6f, 0x6c, 0x6c, 0x4f, 0x75,
	0x74, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2e,
	0x0a, 0x13, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x52, 0x11, 0x69, 0x6d, 0x70,
	0x61, 0x63, 0x74, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0xac,
	0x05, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x68, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x06,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x65, 0x0a, 0x11, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x10, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x1a,
	0xe9, 0x01, 0x0a, 0x06, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x03, 0x6f, 0x6c, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x03, 0x6f, 0x6c, 0x64, 0x12, 0x32, 0x0a, 0x03, 0x6e, 0x65, 0x77, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x03, 0x6e, 0x65, 0x77, 0x12, 0x1d, 0x0a, 0x0a,
	0x69, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x69, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x1a, 0x70, 0x0a, 0x0e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x4a, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x84, 0x01,
	0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_price_book_models_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_price_book_models_proto_rawDescData = file_moego_models_enterprise_v1_price_book_models_proto_rawDesc
)

func file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_price_book_models_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_price_book_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_price_book_models_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescData
}

var file_moego_models_enterprise_v1_price_book_models_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_moego_models_enterprise_v1_price_book_models_proto_goTypes = []interface{}{
	(*PriceBook)(nil),                        // 0: moego.models.enterprise.v1.PriceBook
	(*ServiceCategory)(nil),                  // 1: moego.models.enterprise.v1.ServiceCategory
	(*Service)(nil),                          // 2: moego.models.enterprise.v1.Service
	(*PetBreed)(nil),                         // 3: moego.models.enterprise.v1.PetBreed
	(*PetType)(nil),                          // 4: moego.models.enterprise.v1.PetType
	(*WeightRange)(nil),                      // 5: moego.models.enterprise.v1.WeightRange
	(*Weight)(nil),                           // 6: moego.models.enterprise.v1.Weight
	(*ServiceChangeHistory)(nil),             // 7: moego.models.enterprise.v1.ServiceChangeHistory
	(*ServiceChange)(nil),                    // 8: moego.models.enterprise.v1.ServiceChange
	(*Service_Limitation)(nil),               // 9: moego.models.enterprise.v1.Service.Limitation
	(*Service_Limitation_PetTypeBreeds)(nil), // 10: moego.models.enterprise.v1.Service.Limitation.PetTypeBreeds
	(*ServiceChange_Detail)(nil),             // 11: moego.models.enterprise.v1.ServiceChange.Detail
	(*ServiceChange_DetailCategory)(nil),     // 12: moego.models.enterprise.v1.ServiceChange.DetailCategory
	(v1.ServiceItemType)(0),                  // 13: moego.models.offering.v1.ServiceItemType
	(v1.ServiceType)(0),                      // 14: moego.models.offering.v1.ServiceType
	(*money.Money)(nil),                      // 15: google.type.Money
	(v1.ServicePriceUnit)(0),                 // 16: moego.models.offering.v1.ServicePriceUnit
	(*durationpb.Duration)(nil),              // 17: google.protobuf.Duration
	(v11.PetType)(0),                         // 18: moego.models.customer.v1.PetType
	(WeightUnit)(0),                          // 19: moego.models.enterprise.v1.WeightUnit
	(*timestamppb.Timestamp)(nil),            // 20: google.protobuf.Timestamp
	(*TenantObject)(nil),                     // 21: moego.models.enterprise.v1.TenantObject
	(v2.Field_Type)(0),                       // 22: moego.models.reporting.v2.Field.Type
	(*v2.Value)(nil),                         // 23: moego.models.reporting.v2.Value
}
var file_moego_models_enterprise_v1_price_book_models_proto_depIdxs = []int32{
	0,  // 0: moego.models.enterprise.v1.ServiceCategory.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	13, // 1: moego.models.enterprise.v1.ServiceCategory.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	14, // 2: moego.models.enterprise.v1.ServiceCategory.service_type:type_name -> moego.models.offering.v1.ServiceType
	0,  // 3: moego.models.enterprise.v1.Service.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	13, // 4: moego.models.enterprise.v1.Service.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	1,  // 5: moego.models.enterprise.v1.Service.category:type_name -> moego.models.enterprise.v1.ServiceCategory
	15, // 6: moego.models.enterprise.v1.Service.price:type_name -> google.type.Money
	16, // 7: moego.models.enterprise.v1.Service.service_price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	17, // 8: moego.models.enterprise.v1.Service.duration:type_name -> google.protobuf.Duration
	17, // 9: moego.models.enterprise.v1.Service.max_duration:type_name -> google.protobuf.Duration
	9,  // 10: moego.models.enterprise.v1.Service.limitation:type_name -> moego.models.enterprise.v1.Service.Limitation
	14, // 11: moego.models.enterprise.v1.Service.service_type:type_name -> moego.models.offering.v1.ServiceType
	18, // 12: moego.models.enterprise.v1.PetBreed.pet_type_id:type_name -> moego.models.customer.v1.PetType
	18, // 13: moego.models.enterprise.v1.PetType.pet_type_id:type_name -> moego.models.customer.v1.PetType
	6,  // 14: moego.models.enterprise.v1.WeightRange.min:type_name -> moego.models.enterprise.v1.Weight
	6,  // 15: moego.models.enterprise.v1.WeightRange.max:type_name -> moego.models.enterprise.v1.Weight
	19, // 16: moego.models.enterprise.v1.Weight.unit:type_name -> moego.models.enterprise.v1.WeightUnit
	20, // 17: moego.models.enterprise.v1.ServiceChangeHistory.roll_out_at:type_name -> google.protobuf.Timestamp
	20, // 18: moego.models.enterprise.v1.ServiceChangeHistory.updated_at:type_name -> google.protobuf.Timestamp
	21, // 19: moego.models.enterprise.v1.ServiceChange.target:type_name -> moego.models.enterprise.v1.TenantObject
	12, // 20: moego.models.enterprise.v1.ServiceChange.detail_categories:type_name -> moego.models.enterprise.v1.ServiceChange.DetailCategory
	10, // 21: moego.models.enterprise.v1.Service.Limitation.pet_type_breeds:type_name -> moego.models.enterprise.v1.Service.Limitation.PetTypeBreeds
	5,  // 22: moego.models.enterprise.v1.Service.Limitation.pet_weight_range:type_name -> moego.models.enterprise.v1.WeightRange
	18, // 23: moego.models.enterprise.v1.Service.Limitation.PetTypeBreeds.pet_type_id:type_name -> moego.models.customer.v1.PetType
	3,  // 24: moego.models.enterprise.v1.Service.Limitation.PetTypeBreeds.pet_breeds:type_name -> moego.models.enterprise.v1.PetBreed
	22, // 25: moego.models.enterprise.v1.ServiceChange.Detail.type:type_name -> moego.models.reporting.v2.Field.Type
	23, // 26: moego.models.enterprise.v1.ServiceChange.Detail.old:type_name -> moego.models.reporting.v2.Value
	23, // 27: moego.models.enterprise.v1.ServiceChange.Detail.new:type_name -> moego.models.reporting.v2.Value
	11, // 28: moego.models.enterprise.v1.ServiceChange.DetailCategory.details:type_name -> moego.models.enterprise.v1.ServiceChange.Detail
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_price_book_models_proto_init() }
func file_moego_models_enterprise_v1_price_book_models_proto_init() {
	if File_moego_models_enterprise_v1_price_book_models_proto != nil {
		return
	}
	file_moego_models_enterprise_v1_enterprise_enums_proto_init()
	file_moego_models_enterprise_v1_tenant_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceBook); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetBreed); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeightRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Weight); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceChangeHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service_Limitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service_Limitation_PetTypeBreeds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceChange_Detail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceChange_DetailCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_price_book_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_price_book_models_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_price_book_models_proto_depIdxs,
		MessageInfos:      file_moego_models_enterprise_v1_price_book_models_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_price_book_models_proto = out.File
	file_moego_models_enterprise_v1_price_book_models_proto_rawDesc = nil
	file_moego_models_enterprise_v1_price_book_models_proto_goTypes = nil
	file_moego_models_enterprise_v1_price_book_models_proto_depIdxs = nil
}
