// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/location_defs.proto

package organizationpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create location definition
type CreateLocationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the location
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// business type
	BusinessType BusinessType `protobuf:"varint,2,opt,name=business_type,json=businessType,proto3,enum=moego.models.organization.v1.BusinessType" json:"business_type,omitempty"`
	// contact email
	ContactEmail string `protobuf:"bytes,3,opt,name=contact_email,json=contactEmail,proto3" json:"contact_email,omitempty"`
	// address
	Address *AddressDef `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	// website
	Website *string `protobuf:"bytes,5,opt,name=website,proto3,oneof" json:"website,omitempty"`
	// source from
	SourceFrom BusinessSourceFromType `protobuf:"varint,6,opt,name=source_from,json=sourceFrom,proto3,enum=moego.models.organization.v1.BusinessSourceFromType" json:"source_from,omitempty"`
}

func (x *CreateLocationDef) Reset() {
	*x = CreateLocationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_location_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLocationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLocationDef) ProtoMessage() {}

func (x *CreateLocationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_location_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLocationDef.ProtoReflect.Descriptor instead.
func (*CreateLocationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_location_defs_proto_rawDescGZIP(), []int{0}
}

func (x *CreateLocationDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateLocationDef) GetBusinessType() BusinessType {
	if x != nil {
		return x.BusinessType
	}
	return BusinessType_MOBILE
}

func (x *CreateLocationDef) GetContactEmail() string {
	if x != nil {
		return x.ContactEmail
	}
	return ""
}

func (x *CreateLocationDef) GetAddress() *AddressDef {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *CreateLocationDef) GetWebsite() string {
	if x != nil && x.Website != nil {
		return *x.Website
	}
	return ""
}

func (x *CreateLocationDef) GetSourceFrom() BusinessSourceFromType {
	if x != nil {
		return x.SourceFrom
	}
	return BusinessSourceFromType_BUSINESS_SOURCE_FROM_UNSPECIFIED
}

// update location definition
type UpdateLocationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the location
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name of the location
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// business type
	BusinessType *BusinessType `protobuf:"varint,3,opt,name=business_type,json=businessType,proto3,enum=moego.models.organization.v1.BusinessType,oneof" json:"business_type,omitempty"`
	// contact email
	ContactEmail *string `protobuf:"bytes,4,opt,name=contact_email,json=contactEmail,proto3,oneof" json:"contact_email,omitempty"`
	// phone number
	ContactPhoneNumber *string `protobuf:"bytes,5,opt,name=contact_phone_number,json=contactPhoneNumber,proto3,oneof" json:"contact_phone_number,omitempty"`
	// address
	Address *AddressDef `protobuf:"bytes,6,opt,name=address,proto3,oneof" json:"address,omitempty"`
	// avatar path
	AvatarPath *string `protobuf:"bytes,7,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
}

func (x *UpdateLocationDef) Reset() {
	*x = UpdateLocationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_location_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLocationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLocationDef) ProtoMessage() {}

func (x *UpdateLocationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_location_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLocationDef.ProtoReflect.Descriptor instead.
func (*UpdateLocationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_location_defs_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateLocationDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateLocationDef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateLocationDef) GetBusinessType() BusinessType {
	if x != nil && x.BusinessType != nil {
		return *x.BusinessType
	}
	return BusinessType_MOBILE
}

func (x *UpdateLocationDef) GetContactEmail() string {
	if x != nil && x.ContactEmail != nil {
		return *x.ContactEmail
	}
	return ""
}

func (x *UpdateLocationDef) GetContactPhoneNumber() string {
	if x != nil && x.ContactPhoneNumber != nil {
		return *x.ContactPhoneNumber
	}
	return ""
}

func (x *UpdateLocationDef) GetAddress() *AddressDef {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *UpdateLocationDef) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

// update online preference definition
type UpdateOnlinePreferenceDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the location
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// website
	Website *string `protobuf:"bytes,2,opt,name=website,proto3,oneof" json:"website,omitempty"`
	// facebook link
	FacebookLink *string `protobuf:"bytes,3,opt,name=facebook_link,json=facebookLink,proto3,oneof" json:"facebook_link,omitempty"`
	// instagram link
	InstagramLink *string `protobuf:"bytes,4,opt,name=instagram_link,json=instagramLink,proto3,oneof" json:"instagram_link,omitempty"`
	// google link
	GoogleLink *string `protobuf:"bytes,5,opt,name=google_link,json=googleLink,proto3,oneof" json:"google_link,omitempty"`
	// yelp link
	YelpLink *string `protobuf:"bytes,6,opt,name=yelp_link,json=yelpLink,proto3,oneof" json:"yelp_link,omitempty"`
	// tiktok link
	TiktokLink *string `protobuf:"bytes,7,opt,name=tiktok_link,json=tiktokLink,proto3,oneof" json:"tiktok_link,omitempty"`
}

func (x *UpdateOnlinePreferenceDef) Reset() {
	*x = UpdateOnlinePreferenceDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_location_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOnlinePreferenceDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOnlinePreferenceDef) ProtoMessage() {}

func (x *UpdateOnlinePreferenceDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_location_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOnlinePreferenceDef.ProtoReflect.Descriptor instead.
func (*UpdateOnlinePreferenceDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_location_defs_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateOnlinePreferenceDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateOnlinePreferenceDef) GetWebsite() string {
	if x != nil && x.Website != nil {
		return *x.Website
	}
	return ""
}

func (x *UpdateOnlinePreferenceDef) GetFacebookLink() string {
	if x != nil && x.FacebookLink != nil {
		return *x.FacebookLink
	}
	return ""
}

func (x *UpdateOnlinePreferenceDef) GetInstagramLink() string {
	if x != nil && x.InstagramLink != nil {
		return *x.InstagramLink
	}
	return ""
}

func (x *UpdateOnlinePreferenceDef) GetGoogleLink() string {
	if x != nil && x.GoogleLink != nil {
		return *x.GoogleLink
	}
	return ""
}

func (x *UpdateOnlinePreferenceDef) GetYelpLink() string {
	if x != nil && x.YelpLink != nil {
		return *x.YelpLink
	}
	return ""
}

func (x *UpdateOnlinePreferenceDef) GetTiktokLink() string {
	if x != nil && x.TiktokLink != nil {
		return *x.TiktokLink
	}
	return ""
}

// location date time def
type LocationDateTimeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// local date time
	LocalDateTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=local_date_time,json=localDateTime,proto3" json:"local_date_time,omitempty"`
	// current date
	CurrentDate string `protobuf:"bytes,4,opt,name=current_date,json=currentDate,proto3" json:"current_date,omitempty"`
	// current minutes
	CurrentMinutes int32 `protobuf:"varint,5,opt,name=current_minutes,json=currentMinutes,proto3" json:"current_minutes,omitempty"`
	// timezone name
	TimezoneName string `protobuf:"bytes,6,opt,name=timezone_name,json=timezoneName,proto3" json:"timezone_name,omitempty"`
}

func (x *LocationDateTimeDef) Reset() {
	*x = LocationDateTimeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_location_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationDateTimeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationDateTimeDef) ProtoMessage() {}

func (x *LocationDateTimeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_location_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationDateTimeDef.ProtoReflect.Descriptor instead.
func (*LocationDateTimeDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_location_defs_proto_rawDescGZIP(), []int{3}
}

func (x *LocationDateTimeDef) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *LocationDateTimeDef) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *LocationDateTimeDef) GetLocalDateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LocalDateTime
	}
	return nil
}

func (x *LocationDateTimeDef) GetCurrentDate() string {
	if x != nil {
		return x.CurrentDate
	}
	return ""
}

func (x *LocationDateTimeDef) GetCurrentMinutes() int32 {
	if x != nil {
		return x.CurrentMinutes
	}
	return 0
}

func (x *LocationDateTimeDef) GetTimezoneName() string {
	if x != nil {
		return x.TimezoneName
	}
	return ""
}

var File_moego_models_organization_v1_location_defs_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_location_defs_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa4,
	0x03, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x59, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x2e, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x64, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x4c, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x29, 0x0a,
	0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x07, 0x77, 0x65,
	0x62, 0x73, 0x69, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x5f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x77, 0x65,
	0x62, 0x73, 0x69, 0x74, 0x65, 0x22, 0x85, 0x04, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x00,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x5e, 0x0a, 0x0d, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x01, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x0d, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x48, 0x02, 0x52, 0x0c, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x40,
	0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x32, 0x48, 0x03, 0x52, 0x12, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x47, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x66, 0x48, 0x04, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x0b, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x05, 0x52, 0x0a, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x22, 0xbe, 0x03,
	0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x66, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff,
	0x01, 0x48, 0x00, 0x52, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x34, 0x0a, 0x0d, 0x66, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18,
	0xe8, 0x07, 0x48, 0x01, 0x52, 0x0c, 0x66, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x4c, 0x69,
	0x6e, 0x6b, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x0e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x67, 0x72,
	0x61, 0x6d, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xe8, 0x07, 0x48, 0x02, 0x52, 0x0d, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x4c, 0x69, 0x6e, 0x6b, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a,
	0x0b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xe8, 0x07, 0x48, 0x03,
	0x52, 0x0a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x88, 0x01, 0x01, 0x12,
	0x2c, 0x0a, 0x09, 0x79, 0x65, 0x6c, 0x70, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xe8, 0x07, 0x48, 0x04,
	0x52, 0x08, 0x79, 0x65, 0x6c, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a,
	0x0b, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xe8, 0x07, 0x48, 0x05,
	0x52, 0x0a, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x4c, 0x69, 0x6e, 0x6b, 0x88, 0x01, 0x01, 0x42,
	0x0a, 0x0a, 0x08, 0x5f, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f,
	0x66, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x42, 0x11, 0x0a,
	0x0f, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x6c, 0x69, 0x6e, 0x6b,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b,
	0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x79, 0x65, 0x6c, 0x70, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0x8a,
	0x02, 0x0a, 0x13, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4d,
	0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74,
	0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x8a, 0x01, 0x0a, 0x24,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_organization_v1_location_defs_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_location_defs_proto_rawDescData = file_moego_models_organization_v1_location_defs_proto_rawDesc
)

func file_moego_models_organization_v1_location_defs_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_location_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_location_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_location_defs_proto_rawDescData)
	})
	return file_moego_models_organization_v1_location_defs_proto_rawDescData
}

var file_moego_models_organization_v1_location_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_models_organization_v1_location_defs_proto_goTypes = []interface{}{
	(*CreateLocationDef)(nil),         // 0: moego.models.organization.v1.CreateLocationDef
	(*UpdateLocationDef)(nil),         // 1: moego.models.organization.v1.UpdateLocationDef
	(*UpdateOnlinePreferenceDef)(nil), // 2: moego.models.organization.v1.UpdateOnlinePreferenceDef
	(*LocationDateTimeDef)(nil),       // 3: moego.models.organization.v1.LocationDateTimeDef
	(BusinessType)(0),                 // 4: moego.models.organization.v1.BusinessType
	(*AddressDef)(nil),                // 5: moego.models.organization.v1.AddressDef
	(BusinessSourceFromType)(0),       // 6: moego.models.organization.v1.BusinessSourceFromType
	(*timestamppb.Timestamp)(nil),     // 7: google.protobuf.Timestamp
}
var file_moego_models_organization_v1_location_defs_proto_depIdxs = []int32{
	4, // 0: moego.models.organization.v1.CreateLocationDef.business_type:type_name -> moego.models.organization.v1.BusinessType
	5, // 1: moego.models.organization.v1.CreateLocationDef.address:type_name -> moego.models.organization.v1.AddressDef
	6, // 2: moego.models.organization.v1.CreateLocationDef.source_from:type_name -> moego.models.organization.v1.BusinessSourceFromType
	4, // 3: moego.models.organization.v1.UpdateLocationDef.business_type:type_name -> moego.models.organization.v1.BusinessType
	5, // 4: moego.models.organization.v1.UpdateLocationDef.address:type_name -> moego.models.organization.v1.AddressDef
	7, // 5: moego.models.organization.v1.LocationDateTimeDef.local_date_time:type_name -> google.protobuf.Timestamp
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_location_defs_proto_init() }
func file_moego_models_organization_v1_location_defs_proto_init() {
	if File_moego_models_organization_v1_location_defs_proto != nil {
		return
	}
	file_moego_models_organization_v1_address_defs_proto_init()
	file_moego_models_organization_v1_location_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_organization_v1_location_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLocationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_location_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLocationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_location_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOnlinePreferenceDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_location_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationDateTimeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_organization_v1_location_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_organization_v1_location_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_organization_v1_location_defs_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_location_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_location_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_location_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_organization_v1_location_defs_proto_msgTypes,
	}.Build()
	File_moego_models_organization_v1_location_defs_proto = out.File
	file_moego_models_organization_v1_location_defs_proto_rawDesc = nil
	file_moego_models_organization_v1_location_defs_proto_goTypes = nil
	file_moego_models_organization_v1_location_defs_proto_depIdxs = nil
}
