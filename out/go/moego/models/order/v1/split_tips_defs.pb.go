// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/order/v1/split_tips_defs.proto

package orderpb

import (
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// customized tip config
type CustomizedTipConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// tips amount
	Amount float64 `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// tips percentage
	Percentage int32 `protobuf:"varint,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *CustomizedTipConfig) Reset() {
	*x = CustomizedTipConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_split_tips_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizedTipConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizedTipConfig) ProtoMessage() {}

func (x *CustomizedTipConfig) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_split_tips_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizedTipConfig.ProtoReflect.Descriptor instead.
func (*CustomizedTipConfig) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_split_tips_defs_proto_rawDescGZIP(), []int{0}
}

func (x *CustomizedTipConfig) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CustomizedTipConfig) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CustomizedTipConfig) GetPercentage() int32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

// edit staff
type EditStaffDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// order def
	EditStaffOrderItemDefs []*EditStaffOrderItemDef `protobuf:"bytes,2,rep,name=edit_staff_order_item_defs,json=editStaffOrderItemDefs,proto3" json:"edit_staff_order_item_defs,omitempty"`
}

func (x *EditStaffDef) Reset() {
	*x = EditStaffDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_split_tips_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditStaffDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditStaffDef) ProtoMessage() {}

func (x *EditStaffDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_split_tips_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditStaffDef.ProtoReflect.Descriptor instead.
func (*EditStaffDef) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_split_tips_defs_proto_rawDescGZIP(), []int{1}
}

func (x *EditStaffDef) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *EditStaffDef) GetEditStaffOrderItemDefs() []*EditStaffOrderItemDef {
	if x != nil {
		return x.EditStaffOrderItemDefs
	}
	return nil
}

// edit staff order item commission
type EditStaffOrderItemDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order item id
	OrderItemId int64 `protobuf:"varint,1,opt,name=order_item_id,json=orderItemId,proto3" json:"order_item_id,omitempty"`
	// staff id list
	Staffs []*EditStaffOrderItemDef_StaffOperationDef `protobuf:"bytes,2,rep,name=staffs,proto3" json:"staffs,omitempty"`
}

func (x *EditStaffOrderItemDef) Reset() {
	*x = EditStaffOrderItemDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_split_tips_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditStaffOrderItemDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditStaffOrderItemDef) ProtoMessage() {}

func (x *EditStaffOrderItemDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_split_tips_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditStaffOrderItemDef.ProtoReflect.Descriptor instead.
func (*EditStaffOrderItemDef) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_split_tips_defs_proto_rawDescGZIP(), []int{2}
}

func (x *EditStaffOrderItemDef) GetOrderItemId() int64 {
	if x != nil {
		return x.OrderItemId
	}
	return 0
}

func (x *EditStaffOrderItemDef) GetStaffs() []*EditStaffOrderItemDef_StaffOperationDef {
	if x != nil {
		return x.Staffs
	}
	return nil
}

// staff tip config
type StaffTipConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// tips amount
	Amount *money.Money `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// tips percentage
	Percentage float64 `protobuf:"fixed64,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *StaffTipConfig) Reset() {
	*x = StaffTipConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_split_tips_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffTipConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffTipConfig) ProtoMessage() {}

func (x *StaffTipConfig) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_split_tips_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffTipConfig.ProtoReflect.Descriptor instead.
func (*StaffTipConfig) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_split_tips_defs_proto_rawDescGZIP(), []int{3}
}

func (x *StaffTipConfig) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *StaffTipConfig) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *StaffTipConfig) GetPercentage() float64 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

// multi staff operation
// ref: moego/models/appointment/v1/service_operation_defs.proto:12
type EditStaffOrderItemDef_StaffOperationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *EditStaffOrderItemDef_StaffOperationDef) Reset() {
	*x = EditStaffOrderItemDef_StaffOperationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_split_tips_defs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditStaffOrderItemDef_StaffOperationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditStaffOrderItemDef_StaffOperationDef) ProtoMessage() {}

func (x *EditStaffOrderItemDef_StaffOperationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_split_tips_defs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditStaffOrderItemDef_StaffOperationDef.ProtoReflect.Descriptor instead.
func (*EditStaffOrderItemDef_StaffOperationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_split_tips_defs_proto_rawDescGZIP(), []int{2, 0}
}

func (x *EditStaffOrderItemDef_StaffOperationDef) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

var File_moego_models_order_v1_split_tips_defs_proto protoreflect.FileDescriptor

var file_moego_models_order_v1_split_tips_defs_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x74, 0x69,
	0x70, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x68, 0x0a,
	0x13, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x54, 0x69, 0x70, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x0c, 0x45, 0x64, 0x69, 0x74,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x66, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x68, 0x0a, 0x1a, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x64, 0x65, 0x66,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74,
	0x65, 0x6d, 0x44, 0x65, 0x66, 0x52, 0x16, 0x65, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x73, 0x22, 0xc3, 0x01,
	0x0a, 0x15, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x12, 0x22, 0x0a, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x56, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x1a, 0x2e, 0x0a, 0x11, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x22, 0x77, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x69, 0x70, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x42, 0x75, 0x0a, 0x1d,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_order_v1_split_tips_defs_proto_rawDescOnce sync.Once
	file_moego_models_order_v1_split_tips_defs_proto_rawDescData = file_moego_models_order_v1_split_tips_defs_proto_rawDesc
)

func file_moego_models_order_v1_split_tips_defs_proto_rawDescGZIP() []byte {
	file_moego_models_order_v1_split_tips_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_order_v1_split_tips_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_order_v1_split_tips_defs_proto_rawDescData)
	})
	return file_moego_models_order_v1_split_tips_defs_proto_rawDescData
}

var file_moego_models_order_v1_split_tips_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_models_order_v1_split_tips_defs_proto_goTypes = []interface{}{
	(*CustomizedTipConfig)(nil),                     // 0: moego.models.order.v1.CustomizedTipConfig
	(*EditStaffDef)(nil),                            // 1: moego.models.order.v1.EditStaffDef
	(*EditStaffOrderItemDef)(nil),                   // 2: moego.models.order.v1.EditStaffOrderItemDef
	(*StaffTipConfig)(nil),                          // 3: moego.models.order.v1.StaffTipConfig
	(*EditStaffOrderItemDef_StaffOperationDef)(nil), // 4: moego.models.order.v1.EditStaffOrderItemDef.StaffOperationDef
	(*money.Money)(nil),                             // 5: google.type.Money
}
var file_moego_models_order_v1_split_tips_defs_proto_depIdxs = []int32{
	2, // 0: moego.models.order.v1.EditStaffDef.edit_staff_order_item_defs:type_name -> moego.models.order.v1.EditStaffOrderItemDef
	4, // 1: moego.models.order.v1.EditStaffOrderItemDef.staffs:type_name -> moego.models.order.v1.EditStaffOrderItemDef.StaffOperationDef
	5, // 2: moego.models.order.v1.StaffTipConfig.amount:type_name -> google.type.Money
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_order_v1_split_tips_defs_proto_init() }
func file_moego_models_order_v1_split_tips_defs_proto_init() {
	if File_moego_models_order_v1_split_tips_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_order_v1_split_tips_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizedTipConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_split_tips_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditStaffDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_split_tips_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditStaffOrderItemDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_split_tips_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffTipConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_split_tips_defs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditStaffOrderItemDef_StaffOperationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_order_v1_split_tips_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_order_v1_split_tips_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_order_v1_split_tips_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_order_v1_split_tips_defs_proto_msgTypes,
	}.Build()
	File_moego_models_order_v1_split_tips_defs_proto = out.File
	file_moego_models_order_v1_split_tips_defs_proto_rawDesc = nil
	file_moego_models_order_v1_split_tips_defs_proto_goTypes = nil
	file_moego_models_order_v1_split_tips_defs_proto_depIdxs = nil
}
