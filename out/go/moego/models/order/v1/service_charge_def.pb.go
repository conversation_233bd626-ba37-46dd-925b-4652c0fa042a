// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/order/v1/service_charge_def.proto

package orderpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// food source id list
type FoodSourceDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// food source ids
	FoodSourceIds []int64 `protobuf:"varint,1,rep,packed,name=food_source_ids,json=foodSourceIds,proto3" json:"food_source_ids,omitempty"`
	// is all food source
	IsAllFoodSource *bool `protobuf:"varint,2,opt,name=is_all_food_source,json=isAllFoodSource,proto3,oneof" json:"is_all_food_source,omitempty"`
}

func (x *FoodSourceDef) Reset() {
	*x = FoodSourceDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_service_charge_def_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FoodSourceDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FoodSourceDef) ProtoMessage() {}

func (x *FoodSourceDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_service_charge_def_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FoodSourceDef.ProtoReflect.Descriptor instead.
func (*FoodSourceDef) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_def_proto_rawDescGZIP(), []int{0}
}

func (x *FoodSourceDef) GetFoodSourceIds() []int64 {
	if x != nil {
		return x.FoodSourceIds
	}
	return nil
}

func (x *FoodSourceDef) GetIsAllFoodSource() bool {
	if x != nil && x.IsAllFoodSource != nil {
		return *x.IsAllFoodSource
	}
	return false
}

var File_moego_models_order_v1_service_charge_def_proto protoreflect.FileDescriptor

var file_moego_models_order_v1_service_charge_def_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x92, 0x01, 0x0a, 0x0d, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44,
	0x65, 0x66, 0x12, 0x38, 0x0a, 0x0f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d,
	0x92, 0x01, 0x0a, 0x08, 0x00, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x66,
	0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x12,
	0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0f, 0x69, 0x73, 0x41, 0x6c,
	0x6c, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x42, 0x15,
	0x0a, 0x13, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x75, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_order_v1_service_charge_def_proto_rawDescOnce sync.Once
	file_moego_models_order_v1_service_charge_def_proto_rawDescData = file_moego_models_order_v1_service_charge_def_proto_rawDesc
)

func file_moego_models_order_v1_service_charge_def_proto_rawDescGZIP() []byte {
	file_moego_models_order_v1_service_charge_def_proto_rawDescOnce.Do(func() {
		file_moego_models_order_v1_service_charge_def_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_order_v1_service_charge_def_proto_rawDescData)
	})
	return file_moego_models_order_v1_service_charge_def_proto_rawDescData
}

var file_moego_models_order_v1_service_charge_def_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_order_v1_service_charge_def_proto_goTypes = []interface{}{
	(*FoodSourceDef)(nil), // 0: moego.models.order.v1.FoodSourceDef
}
var file_moego_models_order_v1_service_charge_def_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_order_v1_service_charge_def_proto_init() }
func file_moego_models_order_v1_service_charge_def_proto_init() {
	if File_moego_models_order_v1_service_charge_def_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_order_v1_service_charge_def_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FoodSourceDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_order_v1_service_charge_def_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_order_v1_service_charge_def_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_order_v1_service_charge_def_proto_goTypes,
		DependencyIndexes: file_moego_models_order_v1_service_charge_def_proto_depIdxs,
		MessageInfos:      file_moego_models_order_v1_service_charge_def_proto_msgTypes,
	}.Build()
	File_moego_models_order_v1_service_charge_def_proto = out.File
	file_moego_models_order_v1_service_charge_def_proto_rawDesc = nil
	file_moego_models_order_v1_service_charge_def_proto_goTypes = nil
	file_moego_models_order_v1_service_charge_def_proto_depIdxs = nil
}
