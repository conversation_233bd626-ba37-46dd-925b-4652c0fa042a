// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/order/v1/refund_order_enums.proto

package orderpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Refund Order 的退款模式.
type RefundMode int32

const (
	// 未指定模式.
	RefundMode_REFUND_MODE_UNSPECIFIED RefundMode = 0
	// 基于 Order Item 进行退款.
	RefundMode_REFUND_MODE_BY_ITEM RefundMode = 1
	// 基于 Payment 进行退款.
	RefundMode_REFUND_MODE_BY_PAYMENT RefundMode = 2
)

// Enum value maps for RefundMode.
var (
	RefundMode_name = map[int32]string{
		0: "REFUND_MODE_UNSPECIFIED",
		1: "REFUND_MODE_BY_ITEM",
		2: "REFUND_MODE_BY_PAYMENT",
	}
	RefundMode_value = map[string]int32{
		"REFUND_MODE_UNSPECIFIED": 0,
		"REFUND_MODE_BY_ITEM":     1,
		"REFUND_MODE_BY_PAYMENT":  2,
	}
)

func (x RefundMode) Enum() *RefundMode {
	p := new(RefundMode)
	*p = x
	return p
}

func (x RefundMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RefundMode) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_refund_order_enums_proto_enumTypes[0].Descriptor()
}

func (RefundMode) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_refund_order_enums_proto_enumTypes[0]
}

func (x RefundMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RefundMode.Descriptor instead.
func (RefundMode) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_refund_order_enums_proto_rawDescGZIP(), []int{0}
}

// Refund Order 的状态.
type RefundOrderStatus int32

const (
	// 未定义状态.
	RefundOrderStatus_REFUND_ORDER_STATUS_UNSPECIFIED RefundOrderStatus = 0
	// Refund Order 经创建.
	RefundOrderStatus_REFUND_ORDER_STATUS_CREATED RefundOrderStatus = 100
	// Refund Order 退款交易已经发起.
	RefundOrderStatus_REFUND_ORDER_STATUS_TRANSACTION_CREATED RefundOrderStatus = 200
	// 终态 - 已退款.
	RefundOrderStatus_REFUND_ORDER_STATUS_COMPLETED RefundOrderStatus = 300
)

// Enum value maps for RefundOrderStatus.
var (
	RefundOrderStatus_name = map[int32]string{
		0:   "REFUND_ORDER_STATUS_UNSPECIFIED",
		100: "REFUND_ORDER_STATUS_CREATED",
		200: "REFUND_ORDER_STATUS_TRANSACTION_CREATED",
		300: "REFUND_ORDER_STATUS_COMPLETED",
	}
	RefundOrderStatus_value = map[string]int32{
		"REFUND_ORDER_STATUS_UNSPECIFIED":         0,
		"REFUND_ORDER_STATUS_CREATED":             100,
		"REFUND_ORDER_STATUS_TRANSACTION_CREATED": 200,
		"REFUND_ORDER_STATUS_COMPLETED":           300,
	}
)

func (x RefundOrderStatus) Enum() *RefundOrderStatus {
	p := new(RefundOrderStatus)
	*p = x
	return p
}

func (x RefundOrderStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RefundOrderStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_refund_order_enums_proto_enumTypes[1].Descriptor()
}

func (RefundOrderStatus) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_refund_order_enums_proto_enumTypes[1]
}

func (x RefundOrderStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RefundOrderStatus.Descriptor instead.
func (RefundOrderStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_refund_order_enums_proto_rawDescGZIP(), []int{1}
}

// Refund Order Item 的退款模式.
type RefundItemMode int32

const (
	// 未指定模式.
	RefundItemMode_REFUND_ITEM_MODE_UNSPECIFIED RefundItemMode = 0
	// 按照数量进行退款.
	RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY RefundItemMode = 1
	// 按照金额进行退款.
	RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT RefundItemMode = 2
)

// Enum value maps for RefundItemMode.
var (
	RefundItemMode_name = map[int32]string{
		0: "REFUND_ITEM_MODE_UNSPECIFIED",
		1: "REFUND_ITEM_MODE_BY_QUANTITY",
		2: "REFUND_ITEM_MODE_BY_AMOUNT",
	}
	RefundItemMode_value = map[string]int32{
		"REFUND_ITEM_MODE_UNSPECIFIED": 0,
		"REFUND_ITEM_MODE_BY_QUANTITY": 1,
		"REFUND_ITEM_MODE_BY_AMOUNT":   2,
	}
)

func (x RefundItemMode) Enum() *RefundItemMode {
	p := new(RefundItemMode)
	*p = x
	return p
}

func (x RefundItemMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RefundItemMode) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_refund_order_enums_proto_enumTypes[2].Descriptor()
}

func (RefundItemMode) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_refund_order_enums_proto_enumTypes[2]
}

func (x RefundItemMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RefundItemMode.Descriptor instead.
func (RefundItemMode) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_refund_order_enums_proto_rawDescGZIP(), []int{2}
}

// Refund Order Payment 的状态.
type RefundOrderPaymentStatus int32

const (
	// 未指定状态.
	RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_UNSPECIFIED RefundOrderPaymentStatus = 0
	// 已创建.
	RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED RefundOrderPaymentStatus = 100
	// 退款交易已发起.
	RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED RefundOrderPaymentStatus = 200
	// 终态 - 已退款.
	RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_REFUNDED RefundOrderPaymentStatus = 300
	// 终态 - 退款失败.
	RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_FAILED RefundOrderPaymentStatus = 400
	// 终态 - 退款取消.
	RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CANCELED RefundOrderPaymentStatus = 500
)

// Enum value maps for RefundOrderPaymentStatus.
var (
	RefundOrderPaymentStatus_name = map[int32]string{
		0:   "REFUND_ORDER_PAYMENT_STATUS_UNSPECIFIED",
		100: "REFUND_ORDER_PAYMENT_STATUS_CREATED",
		200: "REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED",
		300: "REFUND_ORDER_PAYMENT_STATUS_REFUNDED",
		400: "REFUND_ORDER_PAYMENT_STATUS_FAILED",
		500: "REFUND_ORDER_PAYMENT_STATUS_CANCELED",
	}
	RefundOrderPaymentStatus_value = map[string]int32{
		"REFUND_ORDER_PAYMENT_STATUS_UNSPECIFIED":         0,
		"REFUND_ORDER_PAYMENT_STATUS_CREATED":             100,
		"REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED": 200,
		"REFUND_ORDER_PAYMENT_STATUS_REFUNDED":            300,
		"REFUND_ORDER_PAYMENT_STATUS_FAILED":              400,
		"REFUND_ORDER_PAYMENT_STATUS_CANCELED":            500,
	}
)

func (x RefundOrderPaymentStatus) Enum() *RefundOrderPaymentStatus {
	p := new(RefundOrderPaymentStatus)
	*p = x
	return p
}

func (x RefundOrderPaymentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RefundOrderPaymentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_refund_order_enums_proto_enumTypes[3].Descriptor()
}

func (RefundOrderPaymentStatus) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_refund_order_enums_proto_enumTypes[3]
}

func (x RefundOrderPaymentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RefundOrderPaymentStatus.Descriptor instead.
func (RefundOrderPaymentStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_refund_order_enums_proto_rawDescGZIP(), []int{3}
}

var File_moego_models_order_v1_refund_order_enums_proto protoreflect.FileDescriptor

var file_moego_models_order_v1_refund_order_enums_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2a, 0x5e, 0x0a, 0x0a, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f,
	0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4d, 0x4f, 0x44,
	0x45, 0x5f, 0x42, 0x59, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x52,
	0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x59, 0x5f, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x2a, 0xab, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a,
	0x1f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x44, 0x10, 0x64, 0x12, 0x2c, 0x0a, 0x27, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0xc8,
	0x01, 0x12, 0x22, 0x0a, 0x1d, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54,
	0x45, 0x44, 0x10, 0xac, 0x02, 0x2a, 0x74, 0x0a, 0x0e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x46, 0x55, 0x4e,
	0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x46,
	0x55, 0x4e, 0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x59,
	0x5f, 0x51, 0x55, 0x41, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x52,
	0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f,
	0x42, 0x59, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x02, 0x2a, 0xa5, 0x02, 0x0a, 0x18,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x27, 0x52, 0x45, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x27, 0x0a, 0x23, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x64, 0x12, 0x34,
	0x0a, 0x2f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x44, 0x10, 0xc8, 0x01, 0x12, 0x29, 0x0a, 0x24, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x45, 0x44, 0x10, 0xac, 0x02, 0x12,
	0x27, 0x0a, 0x22, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x90, 0x03, 0x12, 0x29, 0x0a, 0x24, 0x52, 0x45, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44,
	0x10, 0xf4, 0x03, 0x42, 0x75, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_order_v1_refund_order_enums_proto_rawDescOnce sync.Once
	file_moego_models_order_v1_refund_order_enums_proto_rawDescData = file_moego_models_order_v1_refund_order_enums_proto_rawDesc
)

func file_moego_models_order_v1_refund_order_enums_proto_rawDescGZIP() []byte {
	file_moego_models_order_v1_refund_order_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_order_v1_refund_order_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_order_v1_refund_order_enums_proto_rawDescData)
	})
	return file_moego_models_order_v1_refund_order_enums_proto_rawDescData
}

var file_moego_models_order_v1_refund_order_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_moego_models_order_v1_refund_order_enums_proto_goTypes = []interface{}{
	(RefundMode)(0),               // 0: moego.models.order.v1.RefundMode
	(RefundOrderStatus)(0),        // 1: moego.models.order.v1.RefundOrderStatus
	(RefundItemMode)(0),           // 2: moego.models.order.v1.RefundItemMode
	(RefundOrderPaymentStatus)(0), // 3: moego.models.order.v1.RefundOrderPaymentStatus
}
var file_moego_models_order_v1_refund_order_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_order_v1_refund_order_enums_proto_init() }
func file_moego_models_order_v1_refund_order_enums_proto_init() {
	if File_moego_models_order_v1_refund_order_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_order_v1_refund_order_enums_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_order_v1_refund_order_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_order_v1_refund_order_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_order_v1_refund_order_enums_proto_enumTypes,
	}.Build()
	File_moego_models_order_v1_refund_order_enums_proto = out.File
	file_moego_models_order_v1_refund_order_enums_proto_rawDesc = nil
	file_moego_models_order_v1_refund_order_enums_proto_goTypes = nil
	file_moego_models_order_v1_refund_order_enums_proto_depIdxs = nil
}
