// @since 2022-06-30 19:14:21
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business/v1/business_models.proto

package businesspb

import (
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// BusinessModel
type BusinessModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// basic info
	// business id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business name
	BusinessName string `protobuf:"bytes,2,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// business avatar
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// business phone number
	PhoneNumber string `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// business owner email
	OwnerEmail string `protobuf:"bytes,5,opt,name=owner_email,json=ownerEmail,proto3" json:"owner_email,omitempty"`
	// preference info
	// currency code
	CurrencyCode string `protobuf:"bytes,20,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// currency symbol
	CurrencySymbol string `protobuf:"bytes,21,opt,name=currency_symbol,json=currencySymbol,proto3" json:"currency_symbol,omitempty"`
	// calendar format
	CalendarFormat string `protobuf:"bytes,22,opt,name=calendar_format,json=calendarFormat,proto3" json:"calendar_format,omitempty"`
	// calendar format type
	CalendarFormatType int32 `protobuf:"varint,23,opt,name=calendar_format_type,json=calendarFormatType,proto3" json:"calendar_format_type,omitempty"`
	// date format
	DateFormat string `protobuf:"bytes,24,opt,name=date_format,json=dateFormat,proto3" json:"date_format,omitempty"`
	// date format type
	DateFormatType int32 `protobuf:"varint,25,opt,name=date_format_type,json=dateFormatType,proto3" json:"date_format_type,omitempty"`
	// time format
	TimeFormat string `protobuf:"bytes,26,opt,name=time_format,json=timeFormat,proto3" json:"time_format,omitempty"`
	// time format type
	TimeFormatType int32 `protobuf:"varint,27,opt,name=time_format_type,json=timeFormatType,proto3" json:"time_format_type,omitempty"`
	// timezone name
	TimezoneName string `protobuf:"bytes,28,opt,name=timezone_name,json=timezoneName,proto3" json:"timezone_name,omitempty"`
	// number format
	NumberFormat string `protobuf:"bytes,29,opt,name=number_format,json=numberFormat,proto3" json:"number_format,omitempty"`
	// number format type
	NumberFormatType int32 `protobuf:"varint,30,opt,name=number_format_type,json=numberFormatType,proto3" json:"number_format_type,omitempty"`
	// unit of weight
	UnitOfWeight string `protobuf:"bytes,31,opt,name=unit_of_weight,json=unitOfWeight,proto3" json:"unit_of_weight,omitempty"`
	// unit of weight type
	UnitOfWeightType int32 `protobuf:"varint,32,opt,name=unit_of_weight_type,json=unitOfWeightType,proto3" json:"unit_of_weight_type,omitempty"`
	// business mode, 0-Mobile, 1-Salon
	BusinessMode int32 `protobuf:"varint,50,opt,name=business_mode,json=businessMode,proto3" json:"business_mode,omitempty"`
	// private fields starts from 51
	// business address
	Address string `protobuf:"bytes,51,opt,name=address,proto3" json:"address,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,52,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business app type
	AppType BusinessAppType `protobuf:"varint,53,opt,name=app_type,json=appType,proto3,enum=moego.models.business.v1.BusinessAppType" json:"app_type,omitempty"`
	// address1
	Address1 string `protobuf:"bytes,54,opt,name=address1,proto3" json:"address1,omitempty"`
	// address2
	Address2 string `protobuf:"bytes,55,opt,name=address2,proto3" json:"address2,omitempty"`
	// city
	AddressCity string `protobuf:"bytes,56,opt,name=address_city,json=addressCity,proto3" json:"address_city,omitempty"`
	// state
	AddressState string `protobuf:"bytes,57,opt,name=address_state,json=addressState,proto3" json:"address_state,omitempty"`
	// zipcode
	AddressZipcode string `protobuf:"bytes,58,opt,name=address_zipcode,json=addressZipcode,proto3" json:"address_zipcode,omitempty"`
	// country
	AddressCountry string `protobuf:"bytes,59,opt,name=address_country,json=addressCountry,proto3" json:"address_country,omitempty"`
	// coordinate, include latitude and longitude
	Coordinate *latlng.LatLng `protobuf:"bytes,60,opt,name=coordinate,proto3" json:"coordinate,omitempty"`
	// unit of distance type
	UnitOfDistanceType int32 `protobuf:"varint,61,opt,name=unit_of_distance_type,json=unitOfDistanceType,proto3" json:"unit_of_distance_type,omitempty"`
	// ISO 3166-1 alpha-2 country code
	CountryAlpha2Code string `protobuf:"bytes,62,opt,name=country_alpha2_code,json=countryAlpha2Code,proto3" json:"country_alpha2_code,omitempty"`
}

func (x *BusinessModel) Reset() {
	*x = BusinessModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_v1_business_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessModel) ProtoMessage() {}

func (x *BusinessModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_v1_business_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessModel.ProtoReflect.Descriptor instead.
func (*BusinessModel) Descriptor() ([]byte, []int) {
	return file_moego_models_business_v1_business_models_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessModel) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

func (x *BusinessModel) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *BusinessModel) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *BusinessModel) GetOwnerEmail() string {
	if x != nil {
		return x.OwnerEmail
	}
	return ""
}

func (x *BusinessModel) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *BusinessModel) GetCurrencySymbol() string {
	if x != nil {
		return x.CurrencySymbol
	}
	return ""
}

func (x *BusinessModel) GetCalendarFormat() string {
	if x != nil {
		return x.CalendarFormat
	}
	return ""
}

func (x *BusinessModel) GetCalendarFormatType() int32 {
	if x != nil {
		return x.CalendarFormatType
	}
	return 0
}

func (x *BusinessModel) GetDateFormat() string {
	if x != nil {
		return x.DateFormat
	}
	return ""
}

func (x *BusinessModel) GetDateFormatType() int32 {
	if x != nil {
		return x.DateFormatType
	}
	return 0
}

func (x *BusinessModel) GetTimeFormat() string {
	if x != nil {
		return x.TimeFormat
	}
	return ""
}

func (x *BusinessModel) GetTimeFormatType() int32 {
	if x != nil {
		return x.TimeFormatType
	}
	return 0
}

func (x *BusinessModel) GetTimezoneName() string {
	if x != nil {
		return x.TimezoneName
	}
	return ""
}

func (x *BusinessModel) GetNumberFormat() string {
	if x != nil {
		return x.NumberFormat
	}
	return ""
}

func (x *BusinessModel) GetNumberFormatType() int32 {
	if x != nil {
		return x.NumberFormatType
	}
	return 0
}

func (x *BusinessModel) GetUnitOfWeight() string {
	if x != nil {
		return x.UnitOfWeight
	}
	return ""
}

func (x *BusinessModel) GetUnitOfWeightType() int32 {
	if x != nil {
		return x.UnitOfWeightType
	}
	return 0
}

func (x *BusinessModel) GetBusinessMode() int32 {
	if x != nil {
		return x.BusinessMode
	}
	return 0
}

func (x *BusinessModel) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *BusinessModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BusinessModel) GetAppType() BusinessAppType {
	if x != nil {
		return x.AppType
	}
	return BusinessAppType_BUSINESS_APP_TYPE_MOBILE
}

func (x *BusinessModel) GetAddress1() string {
	if x != nil {
		return x.Address1
	}
	return ""
}

func (x *BusinessModel) GetAddress2() string {
	if x != nil {
		return x.Address2
	}
	return ""
}

func (x *BusinessModel) GetAddressCity() string {
	if x != nil {
		return x.AddressCity
	}
	return ""
}

func (x *BusinessModel) GetAddressState() string {
	if x != nil {
		return x.AddressState
	}
	return ""
}

func (x *BusinessModel) GetAddressZipcode() string {
	if x != nil {
		return x.AddressZipcode
	}
	return ""
}

func (x *BusinessModel) GetAddressCountry() string {
	if x != nil {
		return x.AddressCountry
	}
	return ""
}

func (x *BusinessModel) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

func (x *BusinessModel) GetUnitOfDistanceType() int32 {
	if x != nil {
		return x.UnitOfDistanceType
	}
	return 0
}

func (x *BusinessModel) GetCountryAlpha2Code() string {
	if x != nil {
		return x.CountryAlpha2Code
	}
	return ""
}

// business base view
type BusinessModelBasicView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// basic info
	// business id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business name
	BusinessName string `protobuf:"bytes,2,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// business avatar
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
}

func (x *BusinessModelBasicView) Reset() {
	*x = BusinessModelBasicView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_v1_business_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessModelBasicView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessModelBasicView) ProtoMessage() {}

func (x *BusinessModelBasicView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_v1_business_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessModelBasicView.ProtoReflect.Descriptor instead.
func (*BusinessModelBasicView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_v1_business_models_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessModelBasicView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessModelBasicView) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

func (x *BusinessModelBasicView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

// business base info and preference
type BusinessModelPublicView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// basic info
	// business id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business name
	BusinessName string `protobuf:"bytes,2,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// business avatar
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// preference info
	// currency code
	CurrencyCode string `protobuf:"bytes,20,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// currency symbol
	CurrencySymbol string `protobuf:"bytes,21,opt,name=currency_symbol,json=currencySymbol,proto3" json:"currency_symbol,omitempty"`
	// calendar format
	CalendarFormat string `protobuf:"bytes,22,opt,name=calendar_format,json=calendarFormat,proto3" json:"calendar_format,omitempty"`
	// calendar format type
	CalendarFormatType int32 `protobuf:"varint,23,opt,name=calendar_format_type,json=calendarFormatType,proto3" json:"calendar_format_type,omitempty"`
	// date format
	DateFormat string `protobuf:"bytes,24,opt,name=date_format,json=dateFormat,proto3" json:"date_format,omitempty"`
	// date format type
	DateFormatType int32 `protobuf:"varint,25,opt,name=date_format_type,json=dateFormatType,proto3" json:"date_format_type,omitempty"`
	// time format
	TimeFormat string `protobuf:"bytes,26,opt,name=time_format,json=timeFormat,proto3" json:"time_format,omitempty"`
	// time format type
	TimeFormatType int32 `protobuf:"varint,27,opt,name=time_format_type,json=timeFormatType,proto3" json:"time_format_type,omitempty"`
	// timezone name
	TimezoneName string `protobuf:"bytes,28,opt,name=timezone_name,json=timezoneName,proto3" json:"timezone_name,omitempty"`
	// number format
	NumberFormat string `protobuf:"bytes,29,opt,name=number_format,json=numberFormat,proto3" json:"number_format,omitempty"`
	// number format type
	NumberFormatType int32 `protobuf:"varint,30,opt,name=number_format_type,json=numberFormatType,proto3" json:"number_format_type,omitempty"`
	// unit of weight
	UnitOfWeight string `protobuf:"bytes,31,opt,name=unit_of_weight,json=unitOfWeight,proto3" json:"unit_of_weight,omitempty"`
	// unit of weight type
	UnitOfWeightType int32 `protobuf:"varint,32,opt,name=unit_of_weight_type,json=unitOfWeightType,proto3" json:"unit_of_weight_type,omitempty"`
}

func (x *BusinessModelPublicView) Reset() {
	*x = BusinessModelPublicView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_v1_business_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessModelPublicView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessModelPublicView) ProtoMessage() {}

func (x *BusinessModelPublicView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_v1_business_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessModelPublicView.ProtoReflect.Descriptor instead.
func (*BusinessModelPublicView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_v1_business_models_proto_rawDescGZIP(), []int{2}
}

func (x *BusinessModelPublicView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessModelPublicView) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

func (x *BusinessModelPublicView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *BusinessModelPublicView) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *BusinessModelPublicView) GetCurrencySymbol() string {
	if x != nil {
		return x.CurrencySymbol
	}
	return ""
}

func (x *BusinessModelPublicView) GetCalendarFormat() string {
	if x != nil {
		return x.CalendarFormat
	}
	return ""
}

func (x *BusinessModelPublicView) GetCalendarFormatType() int32 {
	if x != nil {
		return x.CalendarFormatType
	}
	return 0
}

func (x *BusinessModelPublicView) GetDateFormat() string {
	if x != nil {
		return x.DateFormat
	}
	return ""
}

func (x *BusinessModelPublicView) GetDateFormatType() int32 {
	if x != nil {
		return x.DateFormatType
	}
	return 0
}

func (x *BusinessModelPublicView) GetTimeFormat() string {
	if x != nil {
		return x.TimeFormat
	}
	return ""
}

func (x *BusinessModelPublicView) GetTimeFormatType() int32 {
	if x != nil {
		return x.TimeFormatType
	}
	return 0
}

func (x *BusinessModelPublicView) GetTimezoneName() string {
	if x != nil {
		return x.TimezoneName
	}
	return ""
}

func (x *BusinessModelPublicView) GetNumberFormat() string {
	if x != nil {
		return x.NumberFormat
	}
	return ""
}

func (x *BusinessModelPublicView) GetNumberFormatType() int32 {
	if x != nil {
		return x.NumberFormatType
	}
	return 0
}

func (x *BusinessModelPublicView) GetUnitOfWeight() string {
	if x != nil {
		return x.UnitOfWeight
	}
	return ""
}

func (x *BusinessModelPublicView) GetUnitOfWeightType() int32 {
	if x != nil {
		return x.UnitOfWeightType
	}
	return 0
}

// business mode in c app appt list view
type BusinessModelClientListView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business name
	BusinessName string `protobuf:"bytes,2,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// business avatar path
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// business app type
	AppType BusinessAppType `protobuf:"varint,4,opt,name=app_type,json=appType,proto3,enum=moego.models.business.v1.BusinessAppType" json:"app_type,omitempty"`
}

func (x *BusinessModelClientListView) Reset() {
	*x = BusinessModelClientListView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_v1_business_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessModelClientListView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessModelClientListView) ProtoMessage() {}

func (x *BusinessModelClientListView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_v1_business_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessModelClientListView.ProtoReflect.Descriptor instead.
func (*BusinessModelClientListView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_v1_business_models_proto_rawDescGZIP(), []int{3}
}

func (x *BusinessModelClientListView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessModelClientListView) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

func (x *BusinessModelClientListView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *BusinessModelClientListView) GetAppType() BusinessAppType {
	if x != nil {
		return x.AppType
	}
	return BusinessAppType_BUSINESS_APP_TYPE_MOBILE
}

// business mode in c app appt detail view
type BusinessModelClientView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business name
	BusinessName string `protobuf:"bytes,2,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// business avatar path
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// business phone number
	PhoneNumber string `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// address1
	Address1 string `protobuf:"bytes,5,opt,name=address1,proto3" json:"address1,omitempty"`
	// address2
	Address2 string `protobuf:"bytes,6,opt,name=address2,proto3" json:"address2,omitempty"`
	// city
	AddressCity string `protobuf:"bytes,7,opt,name=address_city,json=addressCity,proto3" json:"address_city,omitempty"`
	// state
	AddressState string `protobuf:"bytes,8,opt,name=address_state,json=addressState,proto3" json:"address_state,omitempty"`
	// zipcode
	AddressZipcode string `protobuf:"bytes,9,opt,name=address_zipcode,json=addressZipcode,proto3" json:"address_zipcode,omitempty"`
	// country
	AddressCountry string `protobuf:"bytes,10,opt,name=address_country,json=addressCountry,proto3" json:"address_country,omitempty"`
	// business app type
	AppType BusinessAppType `protobuf:"varint,11,opt,name=app_type,json=appType,proto3,enum=moego.models.business.v1.BusinessAppType" json:"app_type,omitempty"`
	// primary pay type
	PrimaryPayType BusinessPayType `protobuf:"varint,12,opt,name=primary_pay_type,json=primaryPayType,proto3,enum=moego.models.business.v1.BusinessPayType" json:"primary_pay_type,omitempty"`
	// coordinate, include latitude and longitude
	Coordinate *latlng.LatLng `protobuf:"bytes,13,opt,name=coordinate,proto3" json:"coordinate,omitempty"`
	// currency code
	CurrencyCode string `protobuf:"bytes,14,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// currency symbol
	CurrencySymbol string `protobuf:"bytes,15,opt,name=currency_symbol,json=currencySymbol,proto3" json:"currency_symbol,omitempty"`
	// ISO 3166-1 alpha-2 country code
	CountryAlpha2Code string `protobuf:"bytes,16,opt,name=country_alpha2_code,json=countryAlpha2Code,proto3" json:"country_alpha2_code,omitempty"`
}

func (x *BusinessModelClientView) Reset() {
	*x = BusinessModelClientView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_v1_business_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessModelClientView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessModelClientView) ProtoMessage() {}

func (x *BusinessModelClientView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_v1_business_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessModelClientView.ProtoReflect.Descriptor instead.
func (*BusinessModelClientView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_v1_business_models_proto_rawDescGZIP(), []int{4}
}

func (x *BusinessModelClientView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessModelClientView) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

func (x *BusinessModelClientView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *BusinessModelClientView) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *BusinessModelClientView) GetAddress1() string {
	if x != nil {
		return x.Address1
	}
	return ""
}

func (x *BusinessModelClientView) GetAddress2() string {
	if x != nil {
		return x.Address2
	}
	return ""
}

func (x *BusinessModelClientView) GetAddressCity() string {
	if x != nil {
		return x.AddressCity
	}
	return ""
}

func (x *BusinessModelClientView) GetAddressState() string {
	if x != nil {
		return x.AddressState
	}
	return ""
}

func (x *BusinessModelClientView) GetAddressZipcode() string {
	if x != nil {
		return x.AddressZipcode
	}
	return ""
}

func (x *BusinessModelClientView) GetAddressCountry() string {
	if x != nil {
		return x.AddressCountry
	}
	return ""
}

func (x *BusinessModelClientView) GetAppType() BusinessAppType {
	if x != nil {
		return x.AppType
	}
	return BusinessAppType_BUSINESS_APP_TYPE_MOBILE
}

func (x *BusinessModelClientView) GetPrimaryPayType() BusinessPayType {
	if x != nil {
		return x.PrimaryPayType
	}
	return BusinessPayType_BUSINESS_PAY_TYPE_NONE
}

func (x *BusinessModelClientView) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

func (x *BusinessModelClientView) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *BusinessModelClientView) GetCurrencySymbol() string {
	if x != nil {
		return x.CurrencySymbol
	}
	return ""
}

func (x *BusinessModelClientView) GetCountryAlpha2Code() string {
	if x != nil {
		return x.CountryAlpha2Code
	}
	return ""
}

var File_moego_models_business_v1_business_models_proto protoreflect.FileDescriptor

var file_moego_models_business_v1_business_models_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xcf, 0x09, 0x0a, 0x0d, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f,
	0x0a, 0x0b, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x27, 0x0a,
	0x0f, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72,
	0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64,
	0x61, 0x72, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x19, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x74, 0x69, 0x6d, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1e,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6f,
	0x66, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x75, 0x6e, 0x69, 0x74, 0x4f, 0x66, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2d, 0x0a, 0x13,
	0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x75, 0x6e, 0x69, 0x74, 0x4f,
	0x66, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x32, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x33, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x34, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x08, 0x61, 0x70, 0x70,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x35, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x41,
	0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x61, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x18, 0x36, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x12, 0x1a, 0x0a, 0x08, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x18, 0x37, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x38, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x69, 0x74, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x39, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x27, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x7a, 0x69, 0x70, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x5a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x3b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x33, 0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x18,
	0x3c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x0a, 0x63, 0x6f, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6f,
	0x66, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x3d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x75, 0x6e, 0x69, 0x74, 0x4f, 0x66, 0x44, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x5f, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x32, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x3e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x41,
	0x6c, 0x70, 0x68, 0x61, 0x32, 0x43, 0x6f, 0x64, 0x65, 0x4a, 0x04, 0x08, 0x06, 0x10, 0x14, 0x4a,
	0x04, 0x08, 0x21, 0x10, 0x32, 0x22, 0x74, 0x0a, 0x16, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42, 0x61, 0x73, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x50, 0x61, 0x74, 0x68, 0x4a, 0x04, 0x08, 0x04, 0x10, 0x14, 0x22, 0x87, 0x05, 0x0a, 0x17,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x75, 0x62,
	0x6c, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x63,
	0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72,
	0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x12, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x74,
	0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x69,
	0x6d, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x10, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6f, 0x66, 0x5f,
	0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x6e,
	0x69, 0x74, 0x4f, 0x66, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2d, 0x0a, 0x13, 0x75, 0x6e,
	0x69, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x75, 0x6e, 0x69, 0x74, 0x4f, 0x66, 0x57,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4a, 0x04, 0x08, 0x04, 0x10, 0x14, 0x4a,
	0x04, 0x08, 0x21, 0x10, 0x32, 0x22, 0xb9, 0x01, 0x0a, 0x1b, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x44, 0x0a, 0x08, 0x61,
	0x70, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x41, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x61, 0x70, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x22, 0xb2, 0x05, 0x0a, 0x17, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x31, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x31, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x69, 0x74,
	0x79, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x5f, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x27, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x44, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x70,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x61, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53,
	0x0a, 0x10, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x61, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x50, 0x61, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x0a, 0x63, 0x6f,
	0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x32, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x41, 0x6c, 0x70, 0x68,
	0x61, 0x32, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_v1_business_models_proto_rawDescOnce sync.Once
	file_moego_models_business_v1_business_models_proto_rawDescData = file_moego_models_business_v1_business_models_proto_rawDesc
)

func file_moego_models_business_v1_business_models_proto_rawDescGZIP() []byte {
	file_moego_models_business_v1_business_models_proto_rawDescOnce.Do(func() {
		file_moego_models_business_v1_business_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_v1_business_models_proto_rawDescData)
	})
	return file_moego_models_business_v1_business_models_proto_rawDescData
}

var file_moego_models_business_v1_business_models_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_models_business_v1_business_models_proto_goTypes = []interface{}{
	(*BusinessModel)(nil),               // 0: moego.models.business.v1.BusinessModel
	(*BusinessModelBasicView)(nil),      // 1: moego.models.business.v1.BusinessModelBasicView
	(*BusinessModelPublicView)(nil),     // 2: moego.models.business.v1.BusinessModelPublicView
	(*BusinessModelClientListView)(nil), // 3: moego.models.business.v1.BusinessModelClientListView
	(*BusinessModelClientView)(nil),     // 4: moego.models.business.v1.BusinessModelClientView
	(BusinessAppType)(0),                // 5: moego.models.business.v1.BusinessAppType
	(*latlng.LatLng)(nil),               // 6: google.type.LatLng
	(BusinessPayType)(0),                // 7: moego.models.business.v1.BusinessPayType
}
var file_moego_models_business_v1_business_models_proto_depIdxs = []int32{
	5, // 0: moego.models.business.v1.BusinessModel.app_type:type_name -> moego.models.business.v1.BusinessAppType
	6, // 1: moego.models.business.v1.BusinessModel.coordinate:type_name -> google.type.LatLng
	5, // 2: moego.models.business.v1.BusinessModelClientListView.app_type:type_name -> moego.models.business.v1.BusinessAppType
	5, // 3: moego.models.business.v1.BusinessModelClientView.app_type:type_name -> moego.models.business.v1.BusinessAppType
	7, // 4: moego.models.business.v1.BusinessModelClientView.primary_pay_type:type_name -> moego.models.business.v1.BusinessPayType
	6, // 5: moego.models.business.v1.BusinessModelClientView.coordinate:type_name -> google.type.LatLng
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_moego_models_business_v1_business_models_proto_init() }
func file_moego_models_business_v1_business_models_proto_init() {
	if File_moego_models_business_v1_business_models_proto != nil {
		return
	}
	file_moego_models_business_v1_business_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_v1_business_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_v1_business_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessModelBasicView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_v1_business_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessModelPublicView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_v1_business_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessModelClientListView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_v1_business_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessModelClientView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_v1_business_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_v1_business_models_proto_goTypes,
		DependencyIndexes: file_moego_models_business_v1_business_models_proto_depIdxs,
		MessageInfos:      file_moego_models_business_v1_business_models_proto_msgTypes,
	}.Build()
	File_moego_models_business_v1_business_models_proto = out.File
	file_moego_models_business_v1_business_models_proto_rawDesc = nil
	file_moego_models_business_v1_business_models_proto_goTypes = nil
	file_moego_models_business_v1_business_models_proto_depIdxs = nil
}
