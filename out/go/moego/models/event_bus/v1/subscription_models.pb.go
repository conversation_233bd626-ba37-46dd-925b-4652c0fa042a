// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/event_bus/v1/subscription_models.proto

package eventbuspb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/subscription/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Subscription update
type SubscriptionUpdated struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// plan product id
	PlanProductId int64 `protobuf:"varint,2,opt,name=plan_product_id,json=planProductId,proto3" json:"plan_product_id,omitempty"`
	// status
	SubscriptionStatus v1.Subscription_Status `protobuf:"varint,3,opt,name=subscription_status,json=subscriptionStatus,proto3,enum=moego.models.subscription.v1.Subscription_Status" json:"subscription_status,omitempty"`
	// buyer type
	BuyerType *v1.User_Type `protobuf:"varint,4,opt,name=buyer_type,json=buyerType,proto3,enum=moego.models.subscription.v1.User_Type,oneof" json:"buyer_type,omitempty"`
	// buyer id
	BuyerId *int64 `protobuf:"varint,5,opt,name=buyer_id,json=buyerId,proto3,oneof" json:"buyer_id,omitempty"`
	// validity start time
	ValidityStartTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=validity_start_time,json=validityStartTime,proto3,oneof" json:"validity_start_time,omitempty"`
	// validity end time
	ValidityEndTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=validity_end_time,json=validityEndTime,proto3,oneof" json:"validity_end_time,omitempty"`
}

func (x *SubscriptionUpdated) Reset() {
	*x = SubscriptionUpdated{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_event_bus_v1_subscription_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionUpdated) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionUpdated) ProtoMessage() {}

func (x *SubscriptionUpdated) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_event_bus_v1_subscription_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionUpdated.ProtoReflect.Descriptor instead.
func (*SubscriptionUpdated) Descriptor() ([]byte, []int) {
	return file_moego_models_event_bus_v1_subscription_models_proto_rawDescGZIP(), []int{0}
}

func (x *SubscriptionUpdated) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubscriptionUpdated) GetPlanProductId() int64 {
	if x != nil {
		return x.PlanProductId
	}
	return 0
}

func (x *SubscriptionUpdated) GetSubscriptionStatus() v1.Subscription_Status {
	if x != nil {
		return x.SubscriptionStatus
	}
	return v1.Subscription_Status(0)
}

func (x *SubscriptionUpdated) GetBuyerType() v1.User_Type {
	if x != nil && x.BuyerType != nil {
		return *x.BuyerType
	}
	return v1.User_Type(0)
}

func (x *SubscriptionUpdated) GetBuyerId() int64 {
	if x != nil && x.BuyerId != nil {
		return *x.BuyerId
	}
	return 0
}

func (x *SubscriptionUpdated) GetValidityStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidityStartTime
	}
	return nil
}

func (x *SubscriptionUpdated) GetValidityEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidityEndTime
	}
	return nil
}

// Subscription payment
type SubscriptionPaymentEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription id
	SubscriptionId int64 `protobuf:"varint,1,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// failure_message
	FailureMessage string `protobuf:"bytes,2,opt,name=failure_message,json=failureMessage,proto3" json:"failure_message,omitempty"`
}

func (x *SubscriptionPaymentEvent) Reset() {
	*x = SubscriptionPaymentEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_event_bus_v1_subscription_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionPaymentEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionPaymentEvent) ProtoMessage() {}

func (x *SubscriptionPaymentEvent) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_event_bus_v1_subscription_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionPaymentEvent.ProtoReflect.Descriptor instead.
func (*SubscriptionPaymentEvent) Descriptor() ([]byte, []int) {
	return file_moego_models_event_bus_v1_subscription_models_proto_rawDescGZIP(), []int{1}
}

func (x *SubscriptionPaymentEvent) GetSubscriptionId() int64 {
	if x != nil {
		return x.SubscriptionId
	}
	return 0
}

func (x *SubscriptionPaymentEvent) GetFailureMessage() string {
	if x != nil {
		return x.FailureMessage
	}
	return ""
}

var File_moego_models_event_bus_v1_subscription_models_proto protoreflect.FileDescriptor

var file_moego_models_event_bus_v1_subscription_models_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x86, 0x04, 0x0a, 0x13, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x62, 0x0a, 0x13, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x12, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a,
	0x0a, 0x62, 0x75, 0x79, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x09, 0x62, 0x75,
	0x79, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x62, 0x75,
	0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x07,
	0x62, 0x75, 0x79, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4f, 0x0a, 0x13, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x48, 0x02, 0x52, 0x11, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x11, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x48, 0x03, 0x52, 0x0f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x45, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x62, 0x75, 0x79,
	0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x62, 0x75, 0x79, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74,
	0x79, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x14, 0x0a, 0x12,
	0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x22, 0x6c, 0x0a, 0x18, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x27,
	0x0a, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x42, 0x80, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f,
	0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x59, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x5f, 0x62, 0x75, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x62, 0x75,
	0x73, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_event_bus_v1_subscription_models_proto_rawDescOnce sync.Once
	file_moego_models_event_bus_v1_subscription_models_proto_rawDescData = file_moego_models_event_bus_v1_subscription_models_proto_rawDesc
)

func file_moego_models_event_bus_v1_subscription_models_proto_rawDescGZIP() []byte {
	file_moego_models_event_bus_v1_subscription_models_proto_rawDescOnce.Do(func() {
		file_moego_models_event_bus_v1_subscription_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_event_bus_v1_subscription_models_proto_rawDescData)
	})
	return file_moego_models_event_bus_v1_subscription_models_proto_rawDescData
}

var file_moego_models_event_bus_v1_subscription_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_event_bus_v1_subscription_models_proto_goTypes = []interface{}{
	(*SubscriptionUpdated)(nil),      // 0: moego.models.event_bus.v1.SubscriptionUpdated
	(*SubscriptionPaymentEvent)(nil), // 1: moego.models.event_bus.v1.SubscriptionPaymentEvent
	(v1.Subscription_Status)(0),      // 2: moego.models.subscription.v1.Subscription.Status
	(v1.User_Type)(0),                // 3: moego.models.subscription.v1.User.Type
	(*timestamppb.Timestamp)(nil),    // 4: google.protobuf.Timestamp
}
var file_moego_models_event_bus_v1_subscription_models_proto_depIdxs = []int32{
	2, // 0: moego.models.event_bus.v1.SubscriptionUpdated.subscription_status:type_name -> moego.models.subscription.v1.Subscription.Status
	3, // 1: moego.models.event_bus.v1.SubscriptionUpdated.buyer_type:type_name -> moego.models.subscription.v1.User.Type
	4, // 2: moego.models.event_bus.v1.SubscriptionUpdated.validity_start_time:type_name -> google.protobuf.Timestamp
	4, // 3: moego.models.event_bus.v1.SubscriptionUpdated.validity_end_time:type_name -> google.protobuf.Timestamp
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_event_bus_v1_subscription_models_proto_init() }
func file_moego_models_event_bus_v1_subscription_models_proto_init() {
	if File_moego_models_event_bus_v1_subscription_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_event_bus_v1_subscription_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionUpdated); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_event_bus_v1_subscription_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionPaymentEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_event_bus_v1_subscription_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_event_bus_v1_subscription_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_event_bus_v1_subscription_models_proto_goTypes,
		DependencyIndexes: file_moego_models_event_bus_v1_subscription_models_proto_depIdxs,
		MessageInfos:      file_moego_models_event_bus_v1_subscription_models_proto_msgTypes,
	}.Build()
	File_moego_models_event_bus_v1_subscription_models_proto = out.File
	file_moego_models_event_bus_v1_subscription_models_proto_rawDesc = nil
	file_moego_models_event_bus_v1_subscription_models_proto_goTypes = nil
	file_moego_models_event_bus_v1_subscription_models_proto_depIdxs = nil
}
