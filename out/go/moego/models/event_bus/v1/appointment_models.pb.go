// @since 2024-11-07 14:39:17
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/event_bus/v1/appointment_models.proto

package eventbuspb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// cancel reason
type AppointmentCanceledEvent_CancelledType int32

const (
	// unspecified
	AppointmentCanceledEvent_CANCELLED_BY_UNSPECIFIED AppointmentCanceledEvent_CancelledType = 0
	// canceled by customer
	AppointmentCanceledEvent_CANCELLED_BY_CUSTOMER AppointmentCanceledEvent_CancelledType = 1
	// canceled by business
	AppointmentCanceledEvent_CANCELLED_BY_BUSINESS AppointmentCanceledEvent_CancelledType = 2
	// canceled by system
	AppointmentCanceledEvent_CANCELLED_BY_SYSTEM AppointmentCanceledEvent_CancelledType = 3
)

// Enum value maps for AppointmentCanceledEvent_CancelledType.
var (
	AppointmentCanceledEvent_CancelledType_name = map[int32]string{
		0: "CANCELLED_BY_UNSPECIFIED",
		1: "CANCELLED_BY_CUSTOMER",
		2: "CANCELLED_BY_BUSINESS",
		3: "CANCELLED_BY_SYSTEM",
	}
	AppointmentCanceledEvent_CancelledType_value = map[string]int32{
		"CANCELLED_BY_UNSPECIFIED": 0,
		"CANCELLED_BY_CUSTOMER":    1,
		"CANCELLED_BY_BUSINESS":    2,
		"CANCELLED_BY_SYSTEM":      3,
	}
)

func (x AppointmentCanceledEvent_CancelledType) Enum() *AppointmentCanceledEvent_CancelledType {
	p := new(AppointmentCanceledEvent_CancelledType)
	*p = x
	return p
}

func (x AppointmentCanceledEvent_CancelledType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentCanceledEvent_CancelledType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_event_bus_v1_appointment_models_proto_enumTypes[0].Descriptor()
}

func (AppointmentCanceledEvent_CancelledType) Type() protoreflect.EnumType {
	return &file_moego_models_event_bus_v1_appointment_models_proto_enumTypes[0]
}

func (x AppointmentCanceledEvent_CancelledType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentCanceledEvent_CancelledType.Descriptor instead.
func (AppointmentCanceledEvent_CancelledType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_event_bus_v1_appointment_models_proto_rawDescGZIP(), []int{2, 0}
}

// model for appointment creation event
type AppointmentCreatedEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// start time
	StartTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// service item types included in the appointment
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,4,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// end time
	EndTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *AppointmentCreatedEvent) Reset() {
	*x = AppointmentCreatedEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentCreatedEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentCreatedEvent) ProtoMessage() {}

func (x *AppointmentCreatedEvent) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentCreatedEvent.ProtoReflect.Descriptor instead.
func (*AppointmentCreatedEvent) Descriptor() ([]byte, []int) {
	return file_moego_models_event_bus_v1_appointment_models_proto_rawDescGZIP(), []int{0}
}

func (x *AppointmentCreatedEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AppointmentCreatedEvent) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *AppointmentCreatedEvent) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *AppointmentCreatedEvent) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *AppointmentCreatedEvent) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

// model for appointment finish event
type AppointmentFinishedEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// service item types included in the appointment
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,3,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// start time
	StartTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end time
	EndTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *AppointmentFinishedEvent) Reset() {
	*x = AppointmentFinishedEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentFinishedEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentFinishedEvent) ProtoMessage() {}

func (x *AppointmentFinishedEvent) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentFinishedEvent.ProtoReflect.Descriptor instead.
func (*AppointmentFinishedEvent) Descriptor() ([]byte, []int) {
	return file_moego_models_event_bus_v1_appointment_models_proto_rawDescGZIP(), []int{1}
}

func (x *AppointmentFinishedEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AppointmentFinishedEvent) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *AppointmentFinishedEvent) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *AppointmentFinishedEvent) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *AppointmentFinishedEvent) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

// model for appointment cancel event
type AppointmentCanceledEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// cancel type
	CancelledType AppointmentCanceledEvent_CancelledType `protobuf:"varint,2,opt,name=cancelled_type,json=cancelledType,proto3,enum=moego.models.event_bus.v1.AppointmentCanceledEvent_CancelledType" json:"cancelled_type,omitempty"`
	// cancel reason
	CancelledReason *string `protobuf:"bytes,3,opt,name=cancelled_reason,json=cancelledReason,proto3,oneof" json:"cancelled_reason,omitempty"`
	// if the appointment is canceled by customer, the operator_id is the customer id;
	// if the appointment is canceled by business, the operator_id is the business id;
	// if the appointment is canceled by system, the operator_id will be empty;
	OperatorId *int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3,oneof" json:"operator_id,omitempty"`
	// service item types included in the appointment
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,5,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,6,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// start time
	StartTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end time
	EndTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// Controls whether to automatically trigger refunds for associated orders. true required, false not required
	AutoRefundOrder bool `protobuf:"varint,9,opt,name=auto_refund_order,json=autoRefundOrder,proto3" json:"auto_refund_order,omitempty"`
	// is repeat batch cancel
	IsRepeatBatchCancel *bool `protobuf:"varint,10,opt,name=is_repeat_batch_cancel,json=isRepeatBatchCancel,proto3,oneof" json:"is_repeat_batch_cancel,omitempty"`
}

func (x *AppointmentCanceledEvent) Reset() {
	*x = AppointmentCanceledEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentCanceledEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentCanceledEvent) ProtoMessage() {}

func (x *AppointmentCanceledEvent) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentCanceledEvent.ProtoReflect.Descriptor instead.
func (*AppointmentCanceledEvent) Descriptor() ([]byte, []int) {
	return file_moego_models_event_bus_v1_appointment_models_proto_rawDescGZIP(), []int{2}
}

func (x *AppointmentCanceledEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AppointmentCanceledEvent) GetCancelledType() AppointmentCanceledEvent_CancelledType {
	if x != nil {
		return x.CancelledType
	}
	return AppointmentCanceledEvent_CANCELLED_BY_UNSPECIFIED
}

func (x *AppointmentCanceledEvent) GetCancelledReason() string {
	if x != nil && x.CancelledReason != nil {
		return *x.CancelledReason
	}
	return ""
}

func (x *AppointmentCanceledEvent) GetOperatorId() int64 {
	if x != nil && x.OperatorId != nil {
		return *x.OperatorId
	}
	return 0
}

func (x *AppointmentCanceledEvent) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *AppointmentCanceledEvent) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *AppointmentCanceledEvent) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *AppointmentCanceledEvent) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *AppointmentCanceledEvent) GetAutoRefundOrder() bool {
	if x != nil {
		return x.AutoRefundOrder
	}
	return false
}

func (x *AppointmentCanceledEvent) GetIsRepeatBatchCancel() bool {
	if x != nil && x.IsRepeatBatchCancel != nil {
		return *x.IsRepeatBatchCancel
	}
	return false
}

// model for appointment deleted event
type AppointmentDeletedEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *AppointmentDeletedEvent) Reset() {
	*x = AppointmentDeletedEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentDeletedEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentDeletedEvent) ProtoMessage() {}

func (x *AppointmentDeletedEvent) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentDeletedEvent.ProtoReflect.Descriptor instead.
func (*AppointmentDeletedEvent) Descriptor() ([]byte, []int) {
	return file_moego_models_event_bus_v1_appointment_models_proto_rawDescGZIP(), []int{3}
}

func (x *AppointmentDeletedEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// model for appointment updated event
type AppointmentUpdatedEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *AppointmentUpdatedEvent) Reset() {
	*x = AppointmentUpdatedEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentUpdatedEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentUpdatedEvent) ProtoMessage() {}

func (x *AppointmentUpdatedEvent) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentUpdatedEvent.ProtoReflect.Descriptor instead.
func (*AppointmentUpdatedEvent) Descriptor() ([]byte, []int) {
	return file_moego_models_event_bus_v1_appointment_models_proto_rawDescGZIP(), []int{4}
}

func (x *AppointmentUpdatedEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_moego_models_event_bus_v1_appointment_models_proto protoreflect.FileDescriptor

var file_moego_models_event_bus_v1_appointment_models_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x95, 0x02,
	0x0a, 0x17, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x35,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x96, 0x02, 0x0a, 0x18, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xfa,
	0x05, 0x0a, 0x18, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x68, 0x0a, 0x0e, 0x63,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c,
	0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x10, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c,
	0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x0f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0a, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x57, 0x0a, 0x12, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x16, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x5f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x13, 0x69, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x22, 0x7c, 0x0a,
	0x0d, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c,
	0x0a, 0x18, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15,
	0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x43, 0x55, 0x53,
	0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41, 0x4e, 0x43, 0x45,
	0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53,
	0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x5f,
	0x42, 0x59, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x10, 0x03, 0x42, 0x13, 0x0a, 0x11, 0x5f,
	0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x42, 0x19, 0x0a, 0x17, 0x5f, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x62,
	0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x22, 0x29, 0x0a, 0x17, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x29, 0x0a, 0x17, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x42, 0x80, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x59, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x62,
	0x75, 0x73, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_event_bus_v1_appointment_models_proto_rawDescOnce sync.Once
	file_moego_models_event_bus_v1_appointment_models_proto_rawDescData = file_moego_models_event_bus_v1_appointment_models_proto_rawDesc
)

func file_moego_models_event_bus_v1_appointment_models_proto_rawDescGZIP() []byte {
	file_moego_models_event_bus_v1_appointment_models_proto_rawDescOnce.Do(func() {
		file_moego_models_event_bus_v1_appointment_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_event_bus_v1_appointment_models_proto_rawDescData)
	})
	return file_moego_models_event_bus_v1_appointment_models_proto_rawDescData
}

var file_moego_models_event_bus_v1_appointment_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_event_bus_v1_appointment_models_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_models_event_bus_v1_appointment_models_proto_goTypes = []interface{}{
	(AppointmentCanceledEvent_CancelledType)(0), // 0: moego.models.event_bus.v1.AppointmentCanceledEvent.CancelledType
	(*AppointmentCreatedEvent)(nil),             // 1: moego.models.event_bus.v1.AppointmentCreatedEvent
	(*AppointmentFinishedEvent)(nil),            // 2: moego.models.event_bus.v1.AppointmentFinishedEvent
	(*AppointmentCanceledEvent)(nil),            // 3: moego.models.event_bus.v1.AppointmentCanceledEvent
	(*AppointmentDeletedEvent)(nil),             // 4: moego.models.event_bus.v1.AppointmentDeletedEvent
	(*AppointmentUpdatedEvent)(nil),             // 5: moego.models.event_bus.v1.AppointmentUpdatedEvent
	(*timestamppb.Timestamp)(nil),               // 6: google.protobuf.Timestamp
	(v1.ServiceItemType)(0),                     // 7: moego.models.offering.v1.ServiceItemType
}
var file_moego_models_event_bus_v1_appointment_models_proto_depIdxs = []int32{
	6,  // 0: moego.models.event_bus.v1.AppointmentCreatedEvent.start_time:type_name -> google.protobuf.Timestamp
	7,  // 1: moego.models.event_bus.v1.AppointmentCreatedEvent.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	6,  // 2: moego.models.event_bus.v1.AppointmentCreatedEvent.end_time:type_name -> google.protobuf.Timestamp
	7,  // 3: moego.models.event_bus.v1.AppointmentFinishedEvent.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	6,  // 4: moego.models.event_bus.v1.AppointmentFinishedEvent.start_time:type_name -> google.protobuf.Timestamp
	6,  // 5: moego.models.event_bus.v1.AppointmentFinishedEvent.end_time:type_name -> google.protobuf.Timestamp
	0,  // 6: moego.models.event_bus.v1.AppointmentCanceledEvent.cancelled_type:type_name -> moego.models.event_bus.v1.AppointmentCanceledEvent.CancelledType
	7,  // 7: moego.models.event_bus.v1.AppointmentCanceledEvent.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	6,  // 8: moego.models.event_bus.v1.AppointmentCanceledEvent.start_time:type_name -> google.protobuf.Timestamp
	6,  // 9: moego.models.event_bus.v1.AppointmentCanceledEvent.end_time:type_name -> google.protobuf.Timestamp
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_moego_models_event_bus_v1_appointment_models_proto_init() }
func file_moego_models_event_bus_v1_appointment_models_proto_init() {
	if File_moego_models_event_bus_v1_appointment_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentCreatedEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentFinishedEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentCanceledEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentDeletedEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentUpdatedEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_event_bus_v1_appointment_models_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_event_bus_v1_appointment_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_event_bus_v1_appointment_models_proto_goTypes,
		DependencyIndexes: file_moego_models_event_bus_v1_appointment_models_proto_depIdxs,
		EnumInfos:         file_moego_models_event_bus_v1_appointment_models_proto_enumTypes,
		MessageInfos:      file_moego_models_event_bus_v1_appointment_models_proto_msgTypes,
	}.Build()
	File_moego_models_event_bus_v1_appointment_models_proto = out.File
	file_moego_models_event_bus_v1_appointment_models_proto_rawDesc = nil
	file_moego_models_event_bus_v1_appointment_models_proto_goTypes = nil
	file_moego_models_event_bus_v1_appointment_models_proto_depIdxs = nil
}
