// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_pet_fixed_models.proto

package businesscustomerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pet fixed model
type BusinessPetFixedModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// fixed id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// fixed name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// fixed sort. The larger the sort number, the higher the priority.
	Sort int32 `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	// if the pet fixed is deleted
	Deleted bool `protobuf:"varint,4,opt,name=deleted,proto3" json:"deleted,omitempty"`
}

func (x *BusinessPetFixedModel) Reset() {
	*x = BusinessPetFixedModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_fixed_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPetFixedModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetFixedModel) ProtoMessage() {}

func (x *BusinessPetFixedModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_fixed_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetFixedModel.ProtoReflect.Descriptor instead.
func (*BusinessPetFixedModel) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_fixed_models_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessPetFixedModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessPetFixedModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BusinessPetFixedModel) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *BusinessPetFixedModel) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

// pet fixed name view
type BusinessPetFixedNameView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// fixed name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *BusinessPetFixedNameView) Reset() {
	*x = BusinessPetFixedNameView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_fixed_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPetFixedNameView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetFixedNameView) ProtoMessage() {}

func (x *BusinessPetFixedNameView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_fixed_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetFixedNameView.ProtoReflect.Descriptor instead.
func (*BusinessPetFixedNameView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_fixed_models_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessPetFixedNameView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_moego_models_business_customer_v1_business_pet_fixed_models_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_pet_fixed_models_proto_rawDesc = []byte{
	0x0a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x22, 0x69, 0x0a, 0x15, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x22, 0x2e, 0x0a, 0x18, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_pet_fixed_models_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_pet_fixed_models_proto_rawDescData = file_moego_models_business_customer_v1_business_pet_fixed_models_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_pet_fixed_models_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_pet_fixed_models_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_pet_fixed_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_pet_fixed_models_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_pet_fixed_models_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_pet_fixed_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_business_customer_v1_business_pet_fixed_models_proto_goTypes = []interface{}{
	(*BusinessPetFixedModel)(nil),    // 0: moego.models.business_customer.v1.BusinessPetFixedModel
	(*BusinessPetFixedNameView)(nil), // 1: moego.models.business_customer.v1.BusinessPetFixedNameView
}
var file_moego_models_business_customer_v1_business_pet_fixed_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_pet_fixed_models_proto_init() }
func file_moego_models_business_customer_v1_business_pet_fixed_models_proto_init() {
	if File_moego_models_business_customer_v1_business_pet_fixed_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_customer_v1_business_pet_fixed_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPetFixedModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_pet_fixed_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPetFixedNameView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_pet_fixed_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_pet_fixed_models_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_pet_fixed_models_proto_depIdxs,
		MessageInfos:      file_moego_models_business_customer_v1_business_pet_fixed_models_proto_msgTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_pet_fixed_models_proto = out.File
	file_moego_models_business_customer_v1_business_pet_fixed_models_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_pet_fixed_models_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_pet_fixed_models_proto_depIdxs = nil
}
