// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_pet_vaccine_request_defs.proto

package businesscustomerpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// BusinessPetVaccineRequestCreateDef
type BusinessPetVaccineRequestCreateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine record id (vaccine binding id).
	// if not set, a new vaccine record will be requested to add.
	// if set, an existing vaccine record will be requested to update.
	VaccineRecordId *int64 `protobuf:"varint,1,opt,name=vaccine_record_id,json=vaccineRecordId,proto3,oneof" json:"vaccine_record_id,omitempty"`
	// vaccine id
	VaccineId int64 `protobuf:"varint,2,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// expiration date, optional
	ExpirationDate *date.Date `protobuf:"bytes,3,opt,name=expiration_date,json=expirationDate,proto3,oneof" json:"expiration_date,omitempty"`
	// document url
	DocumentUrls []string `protobuf:"bytes,4,rep,name=document_urls,json=documentUrls,proto3" json:"document_urls,omitempty"`
}

func (x *BusinessPetVaccineRequestCreateDef) Reset() {
	*x = BusinessPetVaccineRequestCreateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPetVaccineRequestCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetVaccineRequestCreateDef) ProtoMessage() {}

func (x *BusinessPetVaccineRequestCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetVaccineRequestCreateDef.ProtoReflect.Descriptor instead.
func (*BusinessPetVaccineRequestCreateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessPetVaccineRequestCreateDef) GetVaccineRecordId() int64 {
	if x != nil && x.VaccineRecordId != nil {
		return *x.VaccineRecordId
	}
	return 0
}

func (x *BusinessPetVaccineRequestCreateDef) GetVaccineId() int64 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *BusinessPetVaccineRequestCreateDef) GetExpirationDate() *date.Date {
	if x != nil {
		return x.ExpirationDate
	}
	return nil
}

func (x *BusinessPetVaccineRequestCreateDef) GetDocumentUrls() []string {
	if x != nil {
		return x.DocumentUrls
	}
	return nil
}

// BusinessPetVaccineRequestUpdateDef
type BusinessPetVaccineRequestUpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine record id (vaccine binding id).
	// if set to 0, will not be related to a vaccine record
	VaccineRecordId *int64 `protobuf:"varint,1,opt,name=vaccine_record_id,json=vaccineRecordId,proto3,oneof" json:"vaccine_record_id,omitempty"`
	// vaccine id
	VaccineId *int64 `protobuf:"varint,2,opt,name=vaccine_id,json=vaccineId,proto3,oneof" json:"vaccine_id,omitempty"`
	// expiration date
	ExpirationDate *date.Date `protobuf:"bytes,3,opt,name=expiration_date,json=expirationDate,proto3,oneof" json:"expiration_date,omitempty"`
	// document url
	DocumentUrls *BusinessPetVaccineRequestUpdateDef_DocumentUrlList `protobuf:"bytes,4,opt,name=document_urls,json=documentUrls,proto3,oneof" json:"document_urls,omitempty"`
	// status
	Status *BusinessPetVaccineRequestModel_Status `protobuf:"varint,5,opt,name=status,proto3,enum=moego.models.business_customer.v1.BusinessPetVaccineRequestModel_Status,oneof" json:"status,omitempty"`
}

func (x *BusinessPetVaccineRequestUpdateDef) Reset() {
	*x = BusinessPetVaccineRequestUpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPetVaccineRequestUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetVaccineRequestUpdateDef) ProtoMessage() {}

func (x *BusinessPetVaccineRequestUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetVaccineRequestUpdateDef.ProtoReflect.Descriptor instead.
func (*BusinessPetVaccineRequestUpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessPetVaccineRequestUpdateDef) GetVaccineRecordId() int64 {
	if x != nil && x.VaccineRecordId != nil {
		return *x.VaccineRecordId
	}
	return 0
}

func (x *BusinessPetVaccineRequestUpdateDef) GetVaccineId() int64 {
	if x != nil && x.VaccineId != nil {
		return *x.VaccineId
	}
	return 0
}

func (x *BusinessPetVaccineRequestUpdateDef) GetExpirationDate() *date.Date {
	if x != nil {
		return x.ExpirationDate
	}
	return nil
}

func (x *BusinessPetVaccineRequestUpdateDef) GetDocumentUrls() *BusinessPetVaccineRequestUpdateDef_DocumentUrlList {
	if x != nil {
		return x.DocumentUrls
	}
	return nil
}

func (x *BusinessPetVaccineRequestUpdateDef) GetStatus() BusinessPetVaccineRequestModel_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return BusinessPetVaccineRequestModel_STATUS_UNSPECIFIED
}

// document url list
type BusinessPetVaccineRequestUpdateDef_DocumentUrlList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// document url
	Urls []string `protobuf:"bytes,4,rep,name=urls,proto3" json:"urls,omitempty"`
}

func (x *BusinessPetVaccineRequestUpdateDef_DocumentUrlList) Reset() {
	*x = BusinessPetVaccineRequestUpdateDef_DocumentUrlList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPetVaccineRequestUpdateDef_DocumentUrlList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetVaccineRequestUpdateDef_DocumentUrlList) ProtoMessage() {}

func (x *BusinessPetVaccineRequestUpdateDef_DocumentUrlList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetVaccineRequestUpdateDef_DocumentUrlList.ProtoReflect.Descriptor instead.
func (*BusinessPetVaccineRequestUpdateDef_DocumentUrlList) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BusinessPetVaccineRequestUpdateDef_DocumentUrlList) GetUrls() []string {
	if x != nil {
		return x.Urls
	}
	return nil
}

var File_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_rawDesc = []byte{
	0x0a, 0x49, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x16,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x4b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa9, 0x02, 0x0a,
	0x22, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x66, 0x12, 0x38, 0x0a, 0x11, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0f, 0x76, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a,
	0x0a, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x76, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x48, 0x01, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x0d, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0xfa,
	0x42, 0x0e, 0x92, 0x01, 0x0b, 0x10, 0x14, 0x18, 0x01, 0x22, 0x05, 0x72, 0x03, 0x88, 0x01, 0x01,
	0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x73, 0x42, 0x14,
	0x0a, 0x12, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x5f, 0x69, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0xce, 0x04, 0x0a, 0x22, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12,
	0x38, 0x0a, 0x11, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x28, 0x00, 0x48, 0x00, 0x52, 0x0f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x76, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x09, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x48, 0x02, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x7f, 0x0a, 0x0d, 0x64, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x55,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x56, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72,
	0x6c, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x03, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x55, 0x72, 0x6c, 0x73, 0x88, 0x01, 0x01, 0x12, 0x6f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x48, 0x04, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x1a, 0x38, 0x0a, 0x0f, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04,
	0x75, 0x72, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92,
	0x01, 0x0b, 0x10, 0x14, 0x18, 0x01, 0x22, 0x05, 0x72, 0x03, 0x88, 0x01, 0x01, 0x52, 0x04, 0x75,
	0x72, 0x6c, 0x73, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_rawDescData = file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_goTypes = []interface{}{
	(*BusinessPetVaccineRequestCreateDef)(nil),                 // 0: moego.models.business_customer.v1.BusinessPetVaccineRequestCreateDef
	(*BusinessPetVaccineRequestUpdateDef)(nil),                 // 1: moego.models.business_customer.v1.BusinessPetVaccineRequestUpdateDef
	(*BusinessPetVaccineRequestUpdateDef_DocumentUrlList)(nil), // 2: moego.models.business_customer.v1.BusinessPetVaccineRequestUpdateDef.DocumentUrlList
	(*date.Date)(nil),                          // 3: google.type.Date
	(BusinessPetVaccineRequestModel_Status)(0), // 4: moego.models.business_customer.v1.BusinessPetVaccineRequestModel.Status
}
var file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_depIdxs = []int32{
	3, // 0: moego.models.business_customer.v1.BusinessPetVaccineRequestCreateDef.expiration_date:type_name -> google.type.Date
	3, // 1: moego.models.business_customer.v1.BusinessPetVaccineRequestUpdateDef.expiration_date:type_name -> google.type.Date
	2, // 2: moego.models.business_customer.v1.BusinessPetVaccineRequestUpdateDef.document_urls:type_name -> moego.models.business_customer.v1.BusinessPetVaccineRequestUpdateDef.DocumentUrlList
	4, // 3: moego.models.business_customer.v1.BusinessPetVaccineRequestUpdateDef.status:type_name -> moego.models.business_customer.v1.BusinessPetVaccineRequestModel.Status
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_init() }
func file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_init() {
	if File_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto != nil {
		return
	}
	file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPetVaccineRequestCreateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPetVaccineRequestUpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPetVaccineRequestUpdateDef_DocumentUrlList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_msgTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto = out.File
	file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_pet_vaccine_request_defs_proto_depIdxs = nil
}
