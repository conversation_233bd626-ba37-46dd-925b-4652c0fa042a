// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_customer_merge_models.proto

package businesscustomerpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// duplicate customer group, each group should have at least 2 customers
type DuplicateCustomerGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// duplication detect rule
	Rule DuplicationDetectRule `protobuf:"varint,1,opt,name=rule,proto3,enum=moego.models.business_customer.v1.DuplicationDetectRule" json:"rule,omitempty"`
	// customers
	Customers []*CustomerDuplicationCheckView `protobuf:"bytes,2,rep,name=customers,proto3" json:"customers,omitempty"`
}

func (x *DuplicateCustomerGroup) Reset() {
	*x = DuplicateCustomerGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_merge_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DuplicateCustomerGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DuplicateCustomerGroup) ProtoMessage() {}

func (x *DuplicateCustomerGroup) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_merge_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DuplicateCustomerGroup.ProtoReflect.Descriptor instead.
func (*DuplicateCustomerGroup) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_merge_models_proto_rawDescGZIP(), []int{0}
}

func (x *DuplicateCustomerGroup) GetRule() DuplicationDetectRule {
	if x != nil {
		return x.Rule
	}
	return DuplicationDetectRule_DUPLICATION_DETECT_RULE_UNSPECIFIED
}

func (x *DuplicateCustomerGroup) GetCustomers() []*CustomerDuplicationCheckView {
	if x != nil {
		return x.Customers
	}
	return nil
}

// customer duplication check view
type CustomerDuplicationCheckView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// email
	Email string `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	// pets
	// passed away pets will not be included
	Pets []*PetDuplicationCheckView `protobuf:"bytes,6,rep,name=pets,proto3" json:"pets,omitempty"`
}

func (x *CustomerDuplicationCheckView) Reset() {
	*x = CustomerDuplicationCheckView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_merge_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerDuplicationCheckView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerDuplicationCheckView) ProtoMessage() {}

func (x *CustomerDuplicationCheckView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_merge_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerDuplicationCheckView.ProtoReflect.Descriptor instead.
func (*CustomerDuplicationCheckView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_merge_models_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerDuplicationCheckView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CustomerDuplicationCheckView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CustomerDuplicationCheckView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CustomerDuplicationCheckView) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *CustomerDuplicationCheckView) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CustomerDuplicationCheckView) GetPets() []*PetDuplicationCheckView {
	if x != nil {
		return x.Pets
	}
	return nil
}

// pet duplication check view
type PetDuplicationCheckView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// pet type
	PetType v1.PetType `protobuf:"varint,3,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// breed
	Breed string `protobuf:"bytes,4,opt,name=breed,proto3" json:"breed,omitempty"`
}

func (x *PetDuplicationCheckView) Reset() {
	*x = PetDuplicationCheckView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_merge_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetDuplicationCheckView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetDuplicationCheckView) ProtoMessage() {}

func (x *PetDuplicationCheckView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_merge_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetDuplicationCheckView.ProtoReflect.Descriptor instead.
func (*PetDuplicationCheckView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_merge_models_proto_rawDescGZIP(), []int{2}
}

func (x *PetDuplicationCheckView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetDuplicationCheckView) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *PetDuplicationCheckView) GetPetType() v1.PetType {
	if x != nil {
		return x.PetType
	}
	return v1.PetType(0)
}

func (x *PetDuplicationCheckView) GetBreed() string {
	if x != nil {
		return x.Breed
	}
	return ""
}

var File_moego_models_business_customer_v1_business_customer_merge_models_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_customer_merge_models_proto_rawDesc = []byte{
	0x0a, 0x46, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x45, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc5, 0x01, 0x0a, 0x16, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x12, 0x4c, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x12, 0x5d,
	0x0a, 0x09, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x75,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x09, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x22, 0xf3, 0x01,
	0x0a, 0x1c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x4e, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x56, 0x69, 0x65, 0x77, 0x52, 0x04, 0x70,
	0x65, 0x74, 0x73, 0x22, 0x98, 0x01, 0x0a, 0x17, 0x50, 0x65, 0x74, 0x44, 0x75, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x07, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x65, 0x65,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x42, 0x98,
	0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_business_customer_v1_business_customer_merge_models_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_customer_merge_models_proto_rawDescData = file_moego_models_business_customer_v1_business_customer_merge_models_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_customer_merge_models_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_customer_merge_models_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_customer_merge_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_customer_merge_models_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_customer_merge_models_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_customer_merge_models_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_business_customer_v1_business_customer_merge_models_proto_goTypes = []interface{}{
	(*DuplicateCustomerGroup)(nil),       // 0: moego.models.business_customer.v1.DuplicateCustomerGroup
	(*CustomerDuplicationCheckView)(nil), // 1: moego.models.business_customer.v1.CustomerDuplicationCheckView
	(*PetDuplicationCheckView)(nil),      // 2: moego.models.business_customer.v1.PetDuplicationCheckView
	(DuplicationDetectRule)(0),           // 3: moego.models.business_customer.v1.DuplicationDetectRule
	(v1.PetType)(0),                      // 4: moego.models.customer.v1.PetType
}
var file_moego_models_business_customer_v1_business_customer_merge_models_proto_depIdxs = []int32{
	3, // 0: moego.models.business_customer.v1.DuplicateCustomerGroup.rule:type_name -> moego.models.business_customer.v1.DuplicationDetectRule
	1, // 1: moego.models.business_customer.v1.DuplicateCustomerGroup.customers:type_name -> moego.models.business_customer.v1.CustomerDuplicationCheckView
	2, // 2: moego.models.business_customer.v1.CustomerDuplicationCheckView.pets:type_name -> moego.models.business_customer.v1.PetDuplicationCheckView
	4, // 3: moego.models.business_customer.v1.PetDuplicationCheckView.pet_type:type_name -> moego.models.customer.v1.PetType
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_customer_merge_models_proto_init() }
func file_moego_models_business_customer_v1_business_customer_merge_models_proto_init() {
	if File_moego_models_business_customer_v1_business_customer_merge_models_proto != nil {
		return
	}
	file_moego_models_business_customer_v1_business_customer_merge_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_customer_v1_business_customer_merge_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DuplicateCustomerGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_merge_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerDuplicationCheckView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_merge_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetDuplicationCheckView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_customer_merge_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_customer_merge_models_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_customer_merge_models_proto_depIdxs,
		MessageInfos:      file_moego_models_business_customer_v1_business_customer_merge_models_proto_msgTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_customer_merge_models_proto = out.File
	file_moego_models_business_customer_v1_business_customer_merge_models_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_customer_merge_models_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_customer_merge_models_proto_depIdxs = nil
}
