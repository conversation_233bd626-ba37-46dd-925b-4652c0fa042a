// @since 2024-08-17 10:03:02
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/appointment/v1/pricing_rule_apply_log_models.proto

package appointmentpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pricing rule apply log model
type PricingRuleApplyLogModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,4,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,5,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,6,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// original price, the price before apply the pricing rule
	OriginalPrice float64 `protobuf:"fixed64,7,opt,name=original_price,json=originalPrice,proto3" json:"original_price,omitempty"`
	// service price, the price after apply the pricing rule
	ServicePrice float64 `protobuf:"fixed64,8,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// the pricing rule
	PricingRule *v1.PricingRuleModel `protobuf:"bytes,9,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
	// the create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// the delete time
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=deleted_at,json=deletedAt,proto3,oneof" json:"deleted_at,omitempty"`
	// service date
	ServiceDate *string `protobuf:"bytes,12,opt,name=service_date,json=serviceDate,proto3,oneof" json:"service_date,omitempty"`
	// rule group type
	RuleGroupType *v1.RuleGroupType `protobuf:"varint,13,opt,name=rule_group_type,json=ruleGroupType,proto3,enum=moego.models.offering.v1.RuleGroupType,oneof" json:"rule_group_type,omitempty"`
}

func (x *PricingRuleApplyLogModel) Reset() {
	*x = PricingRuleApplyLogModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PricingRuleApplyLogModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingRuleApplyLogModel) ProtoMessage() {}

func (x *PricingRuleApplyLogModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingRuleApplyLogModel.ProtoReflect.Descriptor instead.
func (*PricingRuleApplyLogModel) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_rawDescGZIP(), []int{0}
}

func (x *PricingRuleApplyLogModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PricingRuleApplyLogModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *PricingRuleApplyLogModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *PricingRuleApplyLogModel) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *PricingRuleApplyLogModel) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PricingRuleApplyLogModel) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PricingRuleApplyLogModel) GetOriginalPrice() float64 {
	if x != nil {
		return x.OriginalPrice
	}
	return 0
}

func (x *PricingRuleApplyLogModel) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *PricingRuleApplyLogModel) GetPricingRule() *v1.PricingRuleModel {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

func (x *PricingRuleApplyLogModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PricingRuleApplyLogModel) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *PricingRuleApplyLogModel) GetServiceDate() string {
	if x != nil && x.ServiceDate != nil {
		return *x.ServiceDate
	}
	return ""
}

func (x *PricingRuleApplyLogModel) GetRuleGroupType() v1.RuleGroupType {
	if x != nil && x.RuleGroupType != nil {
		return *x.RuleGroupType
	}
	return v1.RuleGroupType(0)
}

// pricing rule apply log drawer view
type PricingRuleApplyLogDrawerView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,5,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,6,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// original price, the price before apply the pricing rule
	OriginalPrice float64 `protobuf:"fixed64,7,opt,name=original_price,json=originalPrice,proto3" json:"original_price,omitempty"`
	// service price, the price after apply the pricing rule
	ServicePrice float64 `protobuf:"fixed64,8,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// the pricing rule
	PricingRule *v1.PricingRuleModel `protobuf:"bytes,9,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *PricingRuleApplyLogDrawerView) Reset() {
	*x = PricingRuleApplyLogDrawerView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PricingRuleApplyLogDrawerView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingRuleApplyLogDrawerView) ProtoMessage() {}

func (x *PricingRuleApplyLogDrawerView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingRuleApplyLogDrawerView.ProtoReflect.Descriptor instead.
func (*PricingRuleApplyLogDrawerView) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_rawDescGZIP(), []int{1}
}

func (x *PricingRuleApplyLogDrawerView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PricingRuleApplyLogDrawerView) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PricingRuleApplyLogDrawerView) GetOriginalPrice() float64 {
	if x != nil {
		return x.OriginalPrice
	}
	return 0
}

func (x *PricingRuleApplyLogDrawerView) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *PricingRuleApplyLogDrawerView) GetPricingRule() *v1.PricingRuleModel {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

var File_moego_models_appointment_v1_pricing_rule_apply_log_models_proto protoreflect.FileDescriptor

var file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79,
	0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8f, 0x05, 0x0a, 0x18, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x4d, 0x0a, 0x0c,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x54,
	0x0a, 0x0f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x48, 0x02, 0x52, 0x0d, 0x72, 0x75, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xf0, 0x01, 0x0a, 0x1d, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67,
	0x44, 0x72, 0x61, 0x77, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x4d, 0x0a, 0x0c,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x87, 0x01, 0x0a, 0x23,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_rawDescOnce sync.Once
	file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_rawDescData = file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_rawDesc
)

func file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_rawDescGZIP() []byte {
	file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_rawDescOnce.Do(func() {
		file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_rawDescData)
	})
	return file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_rawDescData
}

var file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_goTypes = []interface{}{
	(*PricingRuleApplyLogModel)(nil),      // 0: moego.models.appointment.v1.PricingRuleApplyLogModel
	(*PricingRuleApplyLogDrawerView)(nil), // 1: moego.models.appointment.v1.PricingRuleApplyLogDrawerView
	(*v1.PricingRuleModel)(nil),           // 2: moego.models.offering.v1.PricingRuleModel
	(*timestamppb.Timestamp)(nil),         // 3: google.protobuf.Timestamp
	(v1.RuleGroupType)(0),                 // 4: moego.models.offering.v1.RuleGroupType
}
var file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_depIdxs = []int32{
	2, // 0: moego.models.appointment.v1.PricingRuleApplyLogModel.pricing_rule:type_name -> moego.models.offering.v1.PricingRuleModel
	3, // 1: moego.models.appointment.v1.PricingRuleApplyLogModel.created_at:type_name -> google.protobuf.Timestamp
	3, // 2: moego.models.appointment.v1.PricingRuleApplyLogModel.deleted_at:type_name -> google.protobuf.Timestamp
	4, // 3: moego.models.appointment.v1.PricingRuleApplyLogModel.rule_group_type:type_name -> moego.models.offering.v1.RuleGroupType
	2, // 4: moego.models.appointment.v1.PricingRuleApplyLogDrawerView.pricing_rule:type_name -> moego.models.offering.v1.PricingRuleModel
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_init() }
func file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_init() {
	if File_moego_models_appointment_v1_pricing_rule_apply_log_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PricingRuleApplyLogModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PricingRuleApplyLogDrawerView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_goTypes,
		DependencyIndexes: file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_depIdxs,
		MessageInfos:      file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_msgTypes,
	}.Build()
	File_moego_models_appointment_v1_pricing_rule_apply_log_models_proto = out.File
	file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_rawDesc = nil
	file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_goTypes = nil
	file_moego_models_appointment_v1_pricing_rule_apply_log_models_proto_depIdxs = nil
}
