// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/admin_permission/v1/role_binding_admin.proto

package adminpermissionapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// RoleBindingServiceClient is the client API for RoleBindingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RoleBindingServiceClient interface {
	// fully update account role bindings
	UpdateRoleBindings(ctx context.Context, in *UpdateRoleBindingsParams, opts ...grpc.CallOption) (*UpdateRoleBindingsResult, error)
	// describe role bindings list
	DescribeRoleBindings(ctx context.Context, in *DescribeRoleBindingsParams, opts ...grpc.CallOption) (*DescribeRoleBindingsResult, error)
	// filter specified permissions
	DescribeAccountPermissions(ctx context.Context, in *DescribeAccountPermissionsParams, opts ...grpc.CallOption) (*DescribeAccountPermissionsResult, error)
}

type roleBindingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRoleBindingServiceClient(cc grpc.ClientConnInterface) RoleBindingServiceClient {
	return &roleBindingServiceClient{cc}
}

func (c *roleBindingServiceClient) UpdateRoleBindings(ctx context.Context, in *UpdateRoleBindingsParams, opts ...grpc.CallOption) (*UpdateRoleBindingsResult, error) {
	out := new(UpdateRoleBindingsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.admin_permission.v1.RoleBindingService/UpdateRoleBindings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roleBindingServiceClient) DescribeRoleBindings(ctx context.Context, in *DescribeRoleBindingsParams, opts ...grpc.CallOption) (*DescribeRoleBindingsResult, error) {
	out := new(DescribeRoleBindingsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.admin_permission.v1.RoleBindingService/DescribeRoleBindings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roleBindingServiceClient) DescribeAccountPermissions(ctx context.Context, in *DescribeAccountPermissionsParams, opts ...grpc.CallOption) (*DescribeAccountPermissionsResult, error) {
	out := new(DescribeAccountPermissionsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.admin_permission.v1.RoleBindingService/DescribeAccountPermissions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RoleBindingServiceServer is the server API for RoleBindingService service.
// All implementations must embed UnimplementedRoleBindingServiceServer
// for forward compatibility
type RoleBindingServiceServer interface {
	// fully update account role bindings
	UpdateRoleBindings(context.Context, *UpdateRoleBindingsParams) (*UpdateRoleBindingsResult, error)
	// describe role bindings list
	DescribeRoleBindings(context.Context, *DescribeRoleBindingsParams) (*DescribeRoleBindingsResult, error)
	// filter specified permissions
	DescribeAccountPermissions(context.Context, *DescribeAccountPermissionsParams) (*DescribeAccountPermissionsResult, error)
	mustEmbedUnimplementedRoleBindingServiceServer()
}

// UnimplementedRoleBindingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRoleBindingServiceServer struct {
}

func (UnimplementedRoleBindingServiceServer) UpdateRoleBindings(context.Context, *UpdateRoleBindingsParams) (*UpdateRoleBindingsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRoleBindings not implemented")
}
func (UnimplementedRoleBindingServiceServer) DescribeRoleBindings(context.Context, *DescribeRoleBindingsParams) (*DescribeRoleBindingsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeRoleBindings not implemented")
}
func (UnimplementedRoleBindingServiceServer) DescribeAccountPermissions(context.Context, *DescribeAccountPermissionsParams) (*DescribeAccountPermissionsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeAccountPermissions not implemented")
}
func (UnimplementedRoleBindingServiceServer) mustEmbedUnimplementedRoleBindingServiceServer() {}

// UnsafeRoleBindingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RoleBindingServiceServer will
// result in compilation errors.
type UnsafeRoleBindingServiceServer interface {
	mustEmbedUnimplementedRoleBindingServiceServer()
}

func RegisterRoleBindingServiceServer(s grpc.ServiceRegistrar, srv RoleBindingServiceServer) {
	s.RegisterService(&RoleBindingService_ServiceDesc, srv)
}

func _RoleBindingService_UpdateRoleBindings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRoleBindingsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoleBindingServiceServer).UpdateRoleBindings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.admin_permission.v1.RoleBindingService/UpdateRoleBindings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoleBindingServiceServer).UpdateRoleBindings(ctx, req.(*UpdateRoleBindingsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoleBindingService_DescribeRoleBindings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeRoleBindingsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoleBindingServiceServer).DescribeRoleBindings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.admin_permission.v1.RoleBindingService/DescribeRoleBindings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoleBindingServiceServer).DescribeRoleBindings(ctx, req.(*DescribeRoleBindingsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoleBindingService_DescribeAccountPermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeAccountPermissionsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoleBindingServiceServer).DescribeAccountPermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.admin_permission.v1.RoleBindingService/DescribeAccountPermissions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoleBindingServiceServer).DescribeAccountPermissions(ctx, req.(*DescribeAccountPermissionsParams))
	}
	return interceptor(ctx, in, info, handler)
}

// RoleBindingService_ServiceDesc is the grpc.ServiceDesc for RoleBindingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RoleBindingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.admin_permission.v1.RoleBindingService",
	HandlerType: (*RoleBindingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateRoleBindings",
			Handler:    _RoleBindingService_UpdateRoleBindings_Handler,
		},
		{
			MethodName: "DescribeRoleBindings",
			Handler:    _RoleBindingService_DescribeRoleBindings_Handler,
		},
		{
			MethodName: "DescribeAccountPermissions",
			Handler:    _RoleBindingService_DescribeAccountPermissions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/admin_permission/v1/role_binding_admin.proto",
}
