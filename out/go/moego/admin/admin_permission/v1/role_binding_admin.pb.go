// @since 2023-05-27 21:29:19
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/admin_permission/v1/role_binding_admin.proto

package adminpermissionapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/admin_permission/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// update account role binding
type UpdateRoleBindingsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// role ids
	RoleIds []int64 `protobuf:"varint,2,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
}

func (x *UpdateRoleBindingsParams) Reset() {
	*x = UpdateRoleBindingsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRoleBindingsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoleBindingsParams) ProtoMessage() {}

func (x *UpdateRoleBindingsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoleBindingsParams.ProtoReflect.Descriptor instead.
func (*UpdateRoleBindingsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateRoleBindingsParams) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *UpdateRoleBindingsParams) GetRoleIds() []int64 {
	if x != nil {
		return x.RoleIds
	}
	return nil
}

// update account role binding response
type UpdateRoleBindingsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateRoleBindingsResult) Reset() {
	*x = UpdateRoleBindingsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRoleBindingsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoleBindingsResult) ProtoMessage() {}

func (x *UpdateRoleBindingsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoleBindingsResult.ProtoReflect.Descriptor instead.
func (*UpdateRoleBindingsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDescGZIP(), []int{1}
}

// describe role bindings request
type DescribeRoleBindingsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter by account id
	AccountIds *v1.StringListValue `protobuf:"bytes,1,opt,name=account_ids,json=accountIds,proto3,oneof" json:"account_ids,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,15,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *DescribeRoleBindingsParams) Reset() {
	*x = DescribeRoleBindingsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeRoleBindingsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeRoleBindingsParams) ProtoMessage() {}

func (x *DescribeRoleBindingsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeRoleBindingsParams.ProtoReflect.Descriptor instead.
func (*DescribeRoleBindingsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDescGZIP(), []int{2}
}

func (x *DescribeRoleBindingsParams) GetAccountIds() *v1.StringListValue {
	if x != nil {
		return x.AccountIds
	}
	return nil
}

func (x *DescribeRoleBindingsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// describe role bindings
type DescribeRoleBindingsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account role bindings
	RoleBindings []*v11.RoleBindingsModel `protobuf:"bytes,1,rep,name=role_bindings,json=roleBindings,proto3" json:"role_bindings,omitempty"`
	// roles map
	Roles map[int64]*v11.RoleModel `protobuf:"bytes,2,rep,name=roles,proto3" json:"roles,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeRoleBindingsResult) Reset() {
	*x = DescribeRoleBindingsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeRoleBindingsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeRoleBindingsResult) ProtoMessage() {}

func (x *DescribeRoleBindingsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeRoleBindingsResult.ProtoReflect.Descriptor instead.
func (*DescribeRoleBindingsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDescGZIP(), []int{3}
}

func (x *DescribeRoleBindingsResult) GetRoleBindings() []*v11.RoleBindingsModel {
	if x != nil {
		return x.RoleBindings
	}
	return nil
}

func (x *DescribeRoleBindingsResult) GetRoles() map[int64]*v11.RoleModel {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *DescribeRoleBindingsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// describe permissions
type DescribeAccountPermissionsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// filter by permission points
	Permissions []string `protobuf:"bytes,2,rep,name=permissions,proto3" json:"permissions,omitempty"`
}

func (x *DescribeAccountPermissionsParams) Reset() {
	*x = DescribeAccountPermissionsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAccountPermissionsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAccountPermissionsParams) ProtoMessage() {}

func (x *DescribeAccountPermissionsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAccountPermissionsParams.ProtoReflect.Descriptor instead.
func (*DescribeAccountPermissionsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDescGZIP(), []int{4}
}

func (x *DescribeAccountPermissionsParams) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *DescribeAccountPermissionsParams) GetPermissions() []string {
	if x != nil {
		return x.Permissions
	}
	return nil
}

// describe account permissions
type DescribeAccountPermissionsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// permission points
	Permissions []*v11.RolePermissionModel `protobuf:"bytes,1,rep,name=permissions,proto3" json:"permissions,omitempty"`
	// roles map
	Roles map[int64]*v11.RoleModel `protobuf:"bytes,2,rep,name=roles,proto3" json:"roles,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DescribeAccountPermissionsResult) Reset() {
	*x = DescribeAccountPermissionsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAccountPermissionsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAccountPermissionsResult) ProtoMessage() {}

func (x *DescribeAccountPermissionsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAccountPermissionsResult.ProtoReflect.Descriptor instead.
func (*DescribeAccountPermissionsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDescGZIP(), []int{5}
}

func (x *DescribeAccountPermissionsResult) GetPermissions() []*v11.RolePermissionModel {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *DescribeAccountPermissionsResult) GetRoles() map[int64]*v11.RoleModel {
	if x != nil {
		return x.Roles
	}
	return nil
}

var File_moego_admin_admin_permission_v1_role_binding_admin_proto protoreflect.FileDescriptor

var file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x3a, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f,
	0x6c, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3d, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f,
	0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x72, 0x0a, 0x18, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x2c, 0x0a, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x10, 0xe8, 0x07, 0x18, 0x01, 0x22,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x22, 0x1a,
	0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xca, 0x01, 0x0a, 0x1a, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x45, 0x0a, 0x0b, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48,
	0x00, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x88, 0x01, 0x01,
	0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xff, 0x02, 0x0a, 0x1a, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x58, 0x0a, 0x0d, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x62,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x0c, 0x72, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x5c, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x52, 0x6f, 0x6c,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x42,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x1a, 0x65, 0x0a, 0x0a, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x41, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x83, 0x01, 0x0a, 0x20, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28,
	0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x13, 0xfa,
	0x42, 0x10, 0x92, 0x01, 0x0d, 0x10, 0xe8, 0x07, 0x18, 0x01, 0x22, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x64, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0xc6, 0x02, 0x0a, 0x20, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x57, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c,
	0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x62, 0x0a,
	0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e,
	0x52, 0x6f, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65,
	0x73, 0x1a, 0x65, 0x0a, 0x0a, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x41, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xd9, 0x03, 0x0a, 0x12, 0x52, 0x6f, 0x6c,
	0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x8a, 0x01, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x90, 0x01, 0x0a,
	0x14, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0xa2, 0x01, 0x0a, 0x1a, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x41,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x42, 0x96, 0x01, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDescOnce sync.Once
	file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDescData = file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDesc
)

func file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDescData)
	})
	return file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDescData
}

var file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_admin_admin_permission_v1_role_binding_admin_proto_goTypes = []interface{}{
	(*UpdateRoleBindingsParams)(nil),         // 0: moego.admin.admin_permission.v1.UpdateRoleBindingsParams
	(*UpdateRoleBindingsResult)(nil),         // 1: moego.admin.admin_permission.v1.UpdateRoleBindingsResult
	(*DescribeRoleBindingsParams)(nil),       // 2: moego.admin.admin_permission.v1.DescribeRoleBindingsParams
	(*DescribeRoleBindingsResult)(nil),       // 3: moego.admin.admin_permission.v1.DescribeRoleBindingsResult
	(*DescribeAccountPermissionsParams)(nil), // 4: moego.admin.admin_permission.v1.DescribeAccountPermissionsParams
	(*DescribeAccountPermissionsResult)(nil), // 5: moego.admin.admin_permission.v1.DescribeAccountPermissionsResult
	nil,                                      // 6: moego.admin.admin_permission.v1.DescribeRoleBindingsResult.RolesEntry
	nil,                                      // 7: moego.admin.admin_permission.v1.DescribeAccountPermissionsResult.RolesEntry
	(*v1.StringListValue)(nil),               // 8: moego.utils.v1.StringListValue
	(*v2.PaginationRequest)(nil),             // 9: moego.utils.v2.PaginationRequest
	(*v11.RoleBindingsModel)(nil),            // 10: moego.models.admin_permission.v1.RoleBindingsModel
	(*v2.PaginationResponse)(nil),            // 11: moego.utils.v2.PaginationResponse
	(*v11.RolePermissionModel)(nil),          // 12: moego.models.admin_permission.v1.RolePermissionModel
	(*v11.RoleModel)(nil),                    // 13: moego.models.admin_permission.v1.RoleModel
}
var file_moego_admin_admin_permission_v1_role_binding_admin_proto_depIdxs = []int32{
	8,  // 0: moego.admin.admin_permission.v1.DescribeRoleBindingsParams.account_ids:type_name -> moego.utils.v1.StringListValue
	9,  // 1: moego.admin.admin_permission.v1.DescribeRoleBindingsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	10, // 2: moego.admin.admin_permission.v1.DescribeRoleBindingsResult.role_bindings:type_name -> moego.models.admin_permission.v1.RoleBindingsModel
	6,  // 3: moego.admin.admin_permission.v1.DescribeRoleBindingsResult.roles:type_name -> moego.admin.admin_permission.v1.DescribeRoleBindingsResult.RolesEntry
	11, // 4: moego.admin.admin_permission.v1.DescribeRoleBindingsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	12, // 5: moego.admin.admin_permission.v1.DescribeAccountPermissionsResult.permissions:type_name -> moego.models.admin_permission.v1.RolePermissionModel
	7,  // 6: moego.admin.admin_permission.v1.DescribeAccountPermissionsResult.roles:type_name -> moego.admin.admin_permission.v1.DescribeAccountPermissionsResult.RolesEntry
	13, // 7: moego.admin.admin_permission.v1.DescribeRoleBindingsResult.RolesEntry.value:type_name -> moego.models.admin_permission.v1.RoleModel
	13, // 8: moego.admin.admin_permission.v1.DescribeAccountPermissionsResult.RolesEntry.value:type_name -> moego.models.admin_permission.v1.RoleModel
	0,  // 9: moego.admin.admin_permission.v1.RoleBindingService.UpdateRoleBindings:input_type -> moego.admin.admin_permission.v1.UpdateRoleBindingsParams
	2,  // 10: moego.admin.admin_permission.v1.RoleBindingService.DescribeRoleBindings:input_type -> moego.admin.admin_permission.v1.DescribeRoleBindingsParams
	4,  // 11: moego.admin.admin_permission.v1.RoleBindingService.DescribeAccountPermissions:input_type -> moego.admin.admin_permission.v1.DescribeAccountPermissionsParams
	1,  // 12: moego.admin.admin_permission.v1.RoleBindingService.UpdateRoleBindings:output_type -> moego.admin.admin_permission.v1.UpdateRoleBindingsResult
	3,  // 13: moego.admin.admin_permission.v1.RoleBindingService.DescribeRoleBindings:output_type -> moego.admin.admin_permission.v1.DescribeRoleBindingsResult
	5,  // 14: moego.admin.admin_permission.v1.RoleBindingService.DescribeAccountPermissions:output_type -> moego.admin.admin_permission.v1.DescribeAccountPermissionsResult
	12, // [12:15] is the sub-list for method output_type
	9,  // [9:12] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_moego_admin_admin_permission_v1_role_binding_admin_proto_init() }
func file_moego_admin_admin_permission_v1_role_binding_admin_proto_init() {
	if File_moego_admin_admin_permission_v1_role_binding_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRoleBindingsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRoleBindingsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeRoleBindingsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeRoleBindingsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAccountPermissionsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAccountPermissionsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_admin_permission_v1_role_binding_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_admin_permission_v1_role_binding_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_admin_permission_v1_role_binding_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_admin_permission_v1_role_binding_admin_proto = out.File
	file_moego_admin_admin_permission_v1_role_binding_admin_proto_rawDesc = nil
	file_moego_admin_admin_permission_v1_role_binding_admin_proto_goTypes = nil
	file_moego_admin_admin_permission_v1_role_binding_admin_proto_depIdxs = nil
}
