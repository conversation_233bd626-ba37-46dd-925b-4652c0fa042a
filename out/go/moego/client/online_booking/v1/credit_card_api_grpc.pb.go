// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/online_booking/v1/credit_card_api.proto

package onlinebookingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CreditCardServiceClient is the client API for CreditCardService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CreditCardServiceClient interface {
	// add credit card
	AddCreditCard(ctx context.Context, in *AddCreditCardParams, opts ...grpc.CallOption) (*AddCreditCardResult, error)
	// delete credit card
	DeleteCreditCard(ctx context.Context, in *DeleteCreditCardParams, opts ...grpc.CallOption) (*DeleteCreditCardResult, error)
	// list credit cards
	ListCreditCards(ctx context.Context, in *ListCreditCardsParams, opts ...grpc.CallOption) (*ListCreditCardsResult, error)
	// get credit
	GetCredit(ctx context.Context, in *GetCreditParams, opts ...grpc.CallOption) (*GetCreditResult, error)
	// list history
	ListCreditChangeHistory(ctx context.Context, in *ListCreditChangeHistoryParams, opts ...grpc.CallOption) (*ListCreditChangeHistoryResult, error)
}

type creditCardServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCreditCardServiceClient(cc grpc.ClientConnInterface) CreditCardServiceClient {
	return &creditCardServiceClient{cc}
}

func (c *creditCardServiceClient) AddCreditCard(ctx context.Context, in *AddCreditCardParams, opts ...grpc.CallOption) (*AddCreditCardResult, error) {
	out := new(AddCreditCardResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.CreditCardService/AddCreditCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditCardServiceClient) DeleteCreditCard(ctx context.Context, in *DeleteCreditCardParams, opts ...grpc.CallOption) (*DeleteCreditCardResult, error) {
	out := new(DeleteCreditCardResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.CreditCardService/DeleteCreditCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditCardServiceClient) ListCreditCards(ctx context.Context, in *ListCreditCardsParams, opts ...grpc.CallOption) (*ListCreditCardsResult, error) {
	out := new(ListCreditCardsResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.CreditCardService/ListCreditCards", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditCardServiceClient) GetCredit(ctx context.Context, in *GetCreditParams, opts ...grpc.CallOption) (*GetCreditResult, error) {
	out := new(GetCreditResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.CreditCardService/GetCredit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditCardServiceClient) ListCreditChangeHistory(ctx context.Context, in *ListCreditChangeHistoryParams, opts ...grpc.CallOption) (*ListCreditChangeHistoryResult, error) {
	out := new(ListCreditChangeHistoryResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.CreditCardService/ListCreditChangeHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CreditCardServiceServer is the server API for CreditCardService service.
// All implementations must embed UnimplementedCreditCardServiceServer
// for forward compatibility
type CreditCardServiceServer interface {
	// add credit card
	AddCreditCard(context.Context, *AddCreditCardParams) (*AddCreditCardResult, error)
	// delete credit card
	DeleteCreditCard(context.Context, *DeleteCreditCardParams) (*DeleteCreditCardResult, error)
	// list credit cards
	ListCreditCards(context.Context, *ListCreditCardsParams) (*ListCreditCardsResult, error)
	// get credit
	GetCredit(context.Context, *GetCreditParams) (*GetCreditResult, error)
	// list history
	ListCreditChangeHistory(context.Context, *ListCreditChangeHistoryParams) (*ListCreditChangeHistoryResult, error)
	mustEmbedUnimplementedCreditCardServiceServer()
}

// UnimplementedCreditCardServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCreditCardServiceServer struct {
}

func (UnimplementedCreditCardServiceServer) AddCreditCard(context.Context, *AddCreditCardParams) (*AddCreditCardResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCreditCard not implemented")
}
func (UnimplementedCreditCardServiceServer) DeleteCreditCard(context.Context, *DeleteCreditCardParams) (*DeleteCreditCardResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCreditCard not implemented")
}
func (UnimplementedCreditCardServiceServer) ListCreditCards(context.Context, *ListCreditCardsParams) (*ListCreditCardsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCreditCards not implemented")
}
func (UnimplementedCreditCardServiceServer) GetCredit(context.Context, *GetCreditParams) (*GetCreditResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCredit not implemented")
}
func (UnimplementedCreditCardServiceServer) ListCreditChangeHistory(context.Context, *ListCreditChangeHistoryParams) (*ListCreditChangeHistoryResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCreditChangeHistory not implemented")
}
func (UnimplementedCreditCardServiceServer) mustEmbedUnimplementedCreditCardServiceServer() {}

// UnsafeCreditCardServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CreditCardServiceServer will
// result in compilation errors.
type UnsafeCreditCardServiceServer interface {
	mustEmbedUnimplementedCreditCardServiceServer()
}

func RegisterCreditCardServiceServer(s grpc.ServiceRegistrar, srv CreditCardServiceServer) {
	s.RegisterService(&CreditCardService_ServiceDesc, srv)
}

func _CreditCardService_AddCreditCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCreditCardParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditCardServiceServer).AddCreditCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.CreditCardService/AddCreditCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditCardServiceServer).AddCreditCard(ctx, req.(*AddCreditCardParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditCardService_DeleteCreditCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCreditCardParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditCardServiceServer).DeleteCreditCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.CreditCardService/DeleteCreditCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditCardServiceServer).DeleteCreditCard(ctx, req.(*DeleteCreditCardParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditCardService_ListCreditCards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCreditCardsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditCardServiceServer).ListCreditCards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.CreditCardService/ListCreditCards",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditCardServiceServer).ListCreditCards(ctx, req.(*ListCreditCardsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditCardService_GetCredit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCreditParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditCardServiceServer).GetCredit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.CreditCardService/GetCredit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditCardServiceServer).GetCredit(ctx, req.(*GetCreditParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditCardService_ListCreditChangeHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCreditChangeHistoryParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditCardServiceServer).ListCreditChangeHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.CreditCardService/ListCreditChangeHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditCardServiceServer).ListCreditChangeHistory(ctx, req.(*ListCreditChangeHistoryParams))
	}
	return interceptor(ctx, in, info, handler)
}

// CreditCardService_ServiceDesc is the grpc.ServiceDesc for CreditCardService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CreditCardService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.online_booking.v1.CreditCardService",
	HandlerType: (*CreditCardServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddCreditCard",
			Handler:    _CreditCardService_AddCreditCard_Handler,
		},
		{
			MethodName: "DeleteCreditCard",
			Handler:    _CreditCardService_DeleteCreditCard_Handler,
		},
		{
			MethodName: "ListCreditCards",
			Handler:    _CreditCardService_ListCreditCards_Handler,
		},
		{
			MethodName: "GetCredit",
			Handler:    _CreditCardService_GetCredit_Handler,
		},
		{
			MethodName: "ListCreditChangeHistory",
			Handler:    _CreditCardService_ListCreditChangeHistory_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/online_booking/v1/credit_card_api.proto",
}
