// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/online_booking/v1/group_class_service_detail_service.proto

package onlinebookingsvcpb

import (
	grpc "google.golang.org/grpc"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// GroupClassDetailServiceClient is the client API for GroupClassDetailService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GroupClassDetailServiceClient interface {
}

type groupClassDetailServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGroupClassDetailServiceClient(cc grpc.ClientConnInterface) GroupClassDetailServiceClient {
	return &groupClassDetailServiceClient{cc}
}

// GroupClassDetailServiceServer is the server API for GroupClassDetailService service.
// All implementations must embed UnimplementedGroupClassDetailServiceServer
// for forward compatibility
type GroupClassDetailServiceServer interface {
	mustEmbedUnimplementedGroupClassDetailServiceServer()
}

// UnimplementedGroupClassDetailServiceServer must be embedded to have forward compatible implementations.
type UnimplementedGroupClassDetailServiceServer struct {
}

func (UnimplementedGroupClassDetailServiceServer) mustEmbedUnimplementedGroupClassDetailServiceServer() {
}

// UnsafeGroupClassDetailServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GroupClassDetailServiceServer will
// result in compilation errors.
type UnsafeGroupClassDetailServiceServer interface {
	mustEmbedUnimplementedGroupClassDetailServiceServer()
}

func RegisterGroupClassDetailServiceServer(s grpc.ServiceRegistrar, srv GroupClassDetailServiceServer) {
	s.RegisterService(&GroupClassDetailService_ServiceDesc, srv)
}

// GroupClassDetailService_ServiceDesc is the grpc.ServiceDesc for GroupClassDetailService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GroupClassDetailService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.online_booking.v1.GroupClassDetailService",
	HandlerType: (*GroupClassDetailServiceServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams:     []grpc.StreamDesc{},
	Metadata:    "moego/service/online_booking/v1/group_class_service_detail_service.proto",
}
