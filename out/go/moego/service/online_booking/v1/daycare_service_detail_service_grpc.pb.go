// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/online_booking/v1/daycare_service_detail_service.proto

package onlinebookingsvcpb

import (
	grpc "google.golang.org/grpc"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DaycareServiceDetailServiceClient is the client API for DaycareServiceDetailService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DaycareServiceDetailServiceClient interface {
}

type daycareServiceDetailServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDaycareServiceDetailServiceClient(cc grpc.ClientConnInterface) DaycareServiceDetailServiceClient {
	return &daycareServiceDetailServiceClient{cc}
}

// DaycareServiceDetailServiceServer is the server API for DaycareServiceDetailService service.
// All implementations must embed UnimplementedDaycareServiceDetailServiceServer
// for forward compatibility
type DaycareServiceDetailServiceServer interface {
	mustEmbedUnimplementedDaycareServiceDetailServiceServer()
}

// UnimplementedDaycareServiceDetailServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDaycareServiceDetailServiceServer struct {
}

func (UnimplementedDaycareServiceDetailServiceServer) mustEmbedUnimplementedDaycareServiceDetailServiceServer() {
}

// UnsafeDaycareServiceDetailServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DaycareServiceDetailServiceServer will
// result in compilation errors.
type UnsafeDaycareServiceDetailServiceServer interface {
	mustEmbedUnimplementedDaycareServiceDetailServiceServer()
}

func RegisterDaycareServiceDetailServiceServer(s grpc.ServiceRegistrar, srv DaycareServiceDetailServiceServer) {
	s.RegisterService(&DaycareServiceDetailService_ServiceDesc, srv)
}

// DaycareServiceDetailService_ServiceDesc is the grpc.ServiceDesc for DaycareServiceDetailService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DaycareServiceDetailService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.online_booking.v1.DaycareServiceDetailService",
	HandlerType: (*DaycareServiceDetailServiceServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams:     []grpc.StreamDesc{},
	Metadata:    "moego/service/online_booking/v1/daycare_service_detail_service.proto",
}
