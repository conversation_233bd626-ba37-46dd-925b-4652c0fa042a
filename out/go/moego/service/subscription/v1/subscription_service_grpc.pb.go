// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/subscription/v1/subscription_service.proto

package subscriptionpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SubscriptionServiceClient is the client API for SubscriptionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SubscriptionServiceClient interface {
	// Create Subscription
	CreateSubscription(ctx context.Context, in *CreateSubscriptionRequest, opts ...grpc.CallOption) (*CreateSubscriptionResponse, error)
	// Update Subscription
	UpdateSubscription(ctx context.Context, in *UpdateSubscriptionRequest, opts ...grpc.CallOption) (*UpdateSubscriptionResponse, error)
	// Get Subscription
	GetSubscriptions(ctx context.Context, in *GetSubscriptionsRequest, opts ...grpc.CallOption) (*GetSubscriptionsResponse, error)
	// Update Subscriptions
	UpdateSubscriptions(ctx context.Context, in *UpdateSubscriptionsRequest, opts ...grpc.CallOption) (*UpdateSubscriptionsResponse, error)
	// ListSubscriptions
	ListSubscriptions(ctx context.Context, in *ListSubscriptionsRequest, opts ...grpc.CallOption) (*ListSubscriptionsResponse, error)
	// ListBuyerSubscriptions
	ListBuyerSubscriptions(ctx context.Context, in *ListBuyerSubscriptionsRequest, opts ...grpc.CallOption) (*ListBuyerSubscriptionsResponse, error)
	// CancelSubscription
	CancelSubscription(ctx context.Context, in *CancelSubscriptionRequest, opts ...grpc.CallOption) (*CancelSubscriptionResponse, error)
	// RenewSubscription
	RenewSubscription(ctx context.Context, in *RenewSubscriptionRequest, opts ...grpc.CallOption) (*RenewSubscriptionResponse, error)
	// PauseSubscription
	PauseSubscription(ctx context.Context, in *PauseSubscriptionRequest, opts ...grpc.CallOption) (*PauseSubscriptionResponse, error)
	// ResumeSubscription
	ResumeSubscription(ctx context.Context, in *ResumeSubscriptionRequest, opts ...grpc.CallOption) (*ResumeSubscriptionResponse, error)
	// StatSubscription
	StatSubscription(ctx context.Context, in *StatSubscriptionRequest, opts ...grpc.CallOption) (*StatSubscriptionResponse, error)
	// GetSubscriptionReport
	GetSubscriptionReport(ctx context.Context, in *GetSubscriptionReportRequest, opts ...grpc.CallOption) (*GetSubscriptionReportResponse, error)
	// PreviewPurchases
	PreviewPurchases(ctx context.Context, in *PreviewPurchasesRequest, opts ...grpc.CallOption) (*PreviewPurchasesResponse, error)
	// ListPurchaseDetails
	ListPurchaseDetails(ctx context.Context, in *ListPurchaseDetailsRequest, opts ...grpc.CallOption) (*ListPurchaseDetailsResponse, error)
	// CreateProduct
	CreateProduct(ctx context.Context, in *CreateProductRequest, opts ...grpc.CallOption) (*CreateProductResponse, error)
	// ListProducts
	ListProducts(ctx context.Context, in *ListProductsRequest, opts ...grpc.CallOption) (*ListProductsResponse, error)
	// UpdateProduct
	UpdateProduct(ctx context.Context, in *UpdateProductRequest, opts ...grpc.CallOption) (*UpdateProductResponse, error)
	// PurchaseProducts
	PurchaseProducts(ctx context.Context, in *PurchaseProductsRequest, opts ...grpc.CallOption) (*PurchaseProductsResponse, error)
	// CreatePrice
	CreatePrice(ctx context.Context, in *CreatePriceRequest, opts ...grpc.CallOption) (*CreatePriceResponse, error)
	// ListPrices
	ListPrices(ctx context.Context, in *ListPricesRequest, opts ...grpc.CallOption) (*ListPricesResponse, error)
	// CreateFeature
	CreateFeature(ctx context.Context, in *CreateFeatureRequest, opts ...grpc.CallOption) (*CreateFeatureResponse, error)
	// ListFeatures
	ListFeatures(ctx context.Context, in *ListFeaturesRequest, opts ...grpc.CallOption) (*ListFeaturesResponse, error)
	// UpdateFeature
	UpdateFeature(ctx context.Context, in *UpdateFeatureRequest, opts ...grpc.CallOption) (*UpdateFeatureResponse, error)
	// delete feature
	DeleteFeature(ctx context.Context, in *DeleteFeatureRequest, opts ...grpc.CallOption) (*DeleteFeatureResponse, error)
	// ListDiscounts
	ListDiscounts(ctx context.Context, in *ListDiscountsRequest, opts ...grpc.CallOption) (*ListDiscountsResponse, error)
	// create license and entitlements
	CreateLicenseAndEntitlements(ctx context.Context, in *CreateLicenseAndEntitlementsRequest, opts ...grpc.CallOption) (*CreateLicenseAndEntitlementsResponse, error)
	// ListLicenses
	ListLicenses(ctx context.Context, in *ListLicensesRequest, opts ...grpc.CallOption) (*ListLicensesResponse, error)
	// list revisions
	ListRevisions(ctx context.Context, in *ListRevisionsRequest, opts ...grpc.CallOption) (*ListRevisionsResponse, error)
	// ListEntitlements
	ListEntitlements(ctx context.Context, in *ListEntitlementsRequest, opts ...grpc.CallOption) (*ListEntitlementsResponse, error)
	// UpdateEntitlements
	UpdateEntitlements(ctx context.Context, in *UpdateEntitlementsRequest, opts ...grpc.CallOption) (*UpdateEntitlementsResponse, error)
	// ConsumeEntitlements
	ConsumeEntitlements(ctx context.Context, in *ConsumeEntitlementsRequest, opts ...grpc.CallOption) (*ConsumeEntitlementsResponse, error)
	// ListLastRedeemTimeForCustomers
	ListLastRedeemTimeForCustomers(ctx context.Context, in *ListLastRedeemTimeForCustomerRequest, opts ...grpc.CallOption) (*ListLastRedeemTimeForCustomersResponse, error)
	// RefreshExistedApplicationFee
	RefreshExistedApplicationFee(ctx context.Context, in *RefreshExistedApplicationFeeRequest, opts ...grpc.CallOption) (*RefreshExistedApplicationFeeResponse, error)
	// 本质是consume package，但是为了实现目前membership 消费perks场景，这一期要这么做，后续会deprecated
	// ConsumePackages
	ConsumePackages(ctx context.Context, in *ConsumePackagesRequest, opts ...grpc.CallOption) (*ConsumePackagesResponse, error)
	// 临时接口，查询所有购买过 membership 顾客的已激活订阅是否和系统内匹配
	CheckMemberships(ctx context.Context, in *CheckMembershipsRequest, opts ...grpc.CallOption) (*CheckMembershipsResponse, error)
	// 临时接口，将所有激活的 membership 账期重置为和 stripe 一模一样
	RefreshMemberships(ctx context.Context, in *RefreshMembershipsRequest, opts ...grpc.CallOption) (*RefreshMembershipsResponse, error)
}

type subscriptionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSubscriptionServiceClient(cc grpc.ClientConnInterface) SubscriptionServiceClient {
	return &subscriptionServiceClient{cc}
}

func (c *subscriptionServiceClient) CreateSubscription(ctx context.Context, in *CreateSubscriptionRequest, opts ...grpc.CallOption) (*CreateSubscriptionResponse, error) {
	out := new(CreateSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/CreateSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) UpdateSubscription(ctx context.Context, in *UpdateSubscriptionRequest, opts ...grpc.CallOption) (*UpdateSubscriptionResponse, error) {
	out := new(UpdateSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/UpdateSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) GetSubscriptions(ctx context.Context, in *GetSubscriptionsRequest, opts ...grpc.CallOption) (*GetSubscriptionsResponse, error) {
	out := new(GetSubscriptionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/GetSubscriptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) UpdateSubscriptions(ctx context.Context, in *UpdateSubscriptionsRequest, opts ...grpc.CallOption) (*UpdateSubscriptionsResponse, error) {
	out := new(UpdateSubscriptionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/UpdateSubscriptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListSubscriptions(ctx context.Context, in *ListSubscriptionsRequest, opts ...grpc.CallOption) (*ListSubscriptionsResponse, error) {
	out := new(ListSubscriptionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/ListSubscriptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListBuyerSubscriptions(ctx context.Context, in *ListBuyerSubscriptionsRequest, opts ...grpc.CallOption) (*ListBuyerSubscriptionsResponse, error) {
	out := new(ListBuyerSubscriptionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/ListBuyerSubscriptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) CancelSubscription(ctx context.Context, in *CancelSubscriptionRequest, opts ...grpc.CallOption) (*CancelSubscriptionResponse, error) {
	out := new(CancelSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/CancelSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) RenewSubscription(ctx context.Context, in *RenewSubscriptionRequest, opts ...grpc.CallOption) (*RenewSubscriptionResponse, error) {
	out := new(RenewSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/RenewSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) PauseSubscription(ctx context.Context, in *PauseSubscriptionRequest, opts ...grpc.CallOption) (*PauseSubscriptionResponse, error) {
	out := new(PauseSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/PauseSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ResumeSubscription(ctx context.Context, in *ResumeSubscriptionRequest, opts ...grpc.CallOption) (*ResumeSubscriptionResponse, error) {
	out := new(ResumeSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/ResumeSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) StatSubscription(ctx context.Context, in *StatSubscriptionRequest, opts ...grpc.CallOption) (*StatSubscriptionResponse, error) {
	out := new(StatSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/StatSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) GetSubscriptionReport(ctx context.Context, in *GetSubscriptionReportRequest, opts ...grpc.CallOption) (*GetSubscriptionReportResponse, error) {
	out := new(GetSubscriptionReportResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/GetSubscriptionReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) PreviewPurchases(ctx context.Context, in *PreviewPurchasesRequest, opts ...grpc.CallOption) (*PreviewPurchasesResponse, error) {
	out := new(PreviewPurchasesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/PreviewPurchases", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListPurchaseDetails(ctx context.Context, in *ListPurchaseDetailsRequest, opts ...grpc.CallOption) (*ListPurchaseDetailsResponse, error) {
	out := new(ListPurchaseDetailsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/ListPurchaseDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) CreateProduct(ctx context.Context, in *CreateProductRequest, opts ...grpc.CallOption) (*CreateProductResponse, error) {
	out := new(CreateProductResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/CreateProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListProducts(ctx context.Context, in *ListProductsRequest, opts ...grpc.CallOption) (*ListProductsResponse, error) {
	out := new(ListProductsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/ListProducts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) UpdateProduct(ctx context.Context, in *UpdateProductRequest, opts ...grpc.CallOption) (*UpdateProductResponse, error) {
	out := new(UpdateProductResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/UpdateProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) PurchaseProducts(ctx context.Context, in *PurchaseProductsRequest, opts ...grpc.CallOption) (*PurchaseProductsResponse, error) {
	out := new(PurchaseProductsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/PurchaseProducts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) CreatePrice(ctx context.Context, in *CreatePriceRequest, opts ...grpc.CallOption) (*CreatePriceResponse, error) {
	out := new(CreatePriceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/CreatePrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListPrices(ctx context.Context, in *ListPricesRequest, opts ...grpc.CallOption) (*ListPricesResponse, error) {
	out := new(ListPricesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/ListPrices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) CreateFeature(ctx context.Context, in *CreateFeatureRequest, opts ...grpc.CallOption) (*CreateFeatureResponse, error) {
	out := new(CreateFeatureResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/CreateFeature", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListFeatures(ctx context.Context, in *ListFeaturesRequest, opts ...grpc.CallOption) (*ListFeaturesResponse, error) {
	out := new(ListFeaturesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/ListFeatures", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) UpdateFeature(ctx context.Context, in *UpdateFeatureRequest, opts ...grpc.CallOption) (*UpdateFeatureResponse, error) {
	out := new(UpdateFeatureResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/UpdateFeature", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) DeleteFeature(ctx context.Context, in *DeleteFeatureRequest, opts ...grpc.CallOption) (*DeleteFeatureResponse, error) {
	out := new(DeleteFeatureResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/DeleteFeature", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListDiscounts(ctx context.Context, in *ListDiscountsRequest, opts ...grpc.CallOption) (*ListDiscountsResponse, error) {
	out := new(ListDiscountsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/ListDiscounts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) CreateLicenseAndEntitlements(ctx context.Context, in *CreateLicenseAndEntitlementsRequest, opts ...grpc.CallOption) (*CreateLicenseAndEntitlementsResponse, error) {
	out := new(CreateLicenseAndEntitlementsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/CreateLicenseAndEntitlements", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListLicenses(ctx context.Context, in *ListLicensesRequest, opts ...grpc.CallOption) (*ListLicensesResponse, error) {
	out := new(ListLicensesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/ListLicenses", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListRevisions(ctx context.Context, in *ListRevisionsRequest, opts ...grpc.CallOption) (*ListRevisionsResponse, error) {
	out := new(ListRevisionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/ListRevisions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListEntitlements(ctx context.Context, in *ListEntitlementsRequest, opts ...grpc.CallOption) (*ListEntitlementsResponse, error) {
	out := new(ListEntitlementsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/ListEntitlements", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) UpdateEntitlements(ctx context.Context, in *UpdateEntitlementsRequest, opts ...grpc.CallOption) (*UpdateEntitlementsResponse, error) {
	out := new(UpdateEntitlementsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/UpdateEntitlements", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ConsumeEntitlements(ctx context.Context, in *ConsumeEntitlementsRequest, opts ...grpc.CallOption) (*ConsumeEntitlementsResponse, error) {
	out := new(ConsumeEntitlementsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/ConsumeEntitlements", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListLastRedeemTimeForCustomers(ctx context.Context, in *ListLastRedeemTimeForCustomerRequest, opts ...grpc.CallOption) (*ListLastRedeemTimeForCustomersResponse, error) {
	out := new(ListLastRedeemTimeForCustomersResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/ListLastRedeemTimeForCustomers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) RefreshExistedApplicationFee(ctx context.Context, in *RefreshExistedApplicationFeeRequest, opts ...grpc.CallOption) (*RefreshExistedApplicationFeeResponse, error) {
	out := new(RefreshExistedApplicationFeeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/RefreshExistedApplicationFee", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ConsumePackages(ctx context.Context, in *ConsumePackagesRequest, opts ...grpc.CallOption) (*ConsumePackagesResponse, error) {
	out := new(ConsumePackagesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/ConsumePackages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) CheckMemberships(ctx context.Context, in *CheckMembershipsRequest, opts ...grpc.CallOption) (*CheckMembershipsResponse, error) {
	out := new(CheckMembershipsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/CheckMemberships", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) RefreshMemberships(ctx context.Context, in *RefreshMembershipsRequest, opts ...grpc.CallOption) (*RefreshMembershipsResponse, error) {
	out := new(RefreshMembershipsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.subscription.v1.SubscriptionService/RefreshMemberships", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SubscriptionServiceServer is the server API for SubscriptionService service.
// All implementations must embed UnimplementedSubscriptionServiceServer
// for forward compatibility
type SubscriptionServiceServer interface {
	// Create Subscription
	CreateSubscription(context.Context, *CreateSubscriptionRequest) (*CreateSubscriptionResponse, error)
	// Update Subscription
	UpdateSubscription(context.Context, *UpdateSubscriptionRequest) (*UpdateSubscriptionResponse, error)
	// Get Subscription
	GetSubscriptions(context.Context, *GetSubscriptionsRequest) (*GetSubscriptionsResponse, error)
	// Update Subscriptions
	UpdateSubscriptions(context.Context, *UpdateSubscriptionsRequest) (*UpdateSubscriptionsResponse, error)
	// ListSubscriptions
	ListSubscriptions(context.Context, *ListSubscriptionsRequest) (*ListSubscriptionsResponse, error)
	// ListBuyerSubscriptions
	ListBuyerSubscriptions(context.Context, *ListBuyerSubscriptionsRequest) (*ListBuyerSubscriptionsResponse, error)
	// CancelSubscription
	CancelSubscription(context.Context, *CancelSubscriptionRequest) (*CancelSubscriptionResponse, error)
	// RenewSubscription
	RenewSubscription(context.Context, *RenewSubscriptionRequest) (*RenewSubscriptionResponse, error)
	// PauseSubscription
	PauseSubscription(context.Context, *PauseSubscriptionRequest) (*PauseSubscriptionResponse, error)
	// ResumeSubscription
	ResumeSubscription(context.Context, *ResumeSubscriptionRequest) (*ResumeSubscriptionResponse, error)
	// StatSubscription
	StatSubscription(context.Context, *StatSubscriptionRequest) (*StatSubscriptionResponse, error)
	// GetSubscriptionReport
	GetSubscriptionReport(context.Context, *GetSubscriptionReportRequest) (*GetSubscriptionReportResponse, error)
	// PreviewPurchases
	PreviewPurchases(context.Context, *PreviewPurchasesRequest) (*PreviewPurchasesResponse, error)
	// ListPurchaseDetails
	ListPurchaseDetails(context.Context, *ListPurchaseDetailsRequest) (*ListPurchaseDetailsResponse, error)
	// CreateProduct
	CreateProduct(context.Context, *CreateProductRequest) (*CreateProductResponse, error)
	// ListProducts
	ListProducts(context.Context, *ListProductsRequest) (*ListProductsResponse, error)
	// UpdateProduct
	UpdateProduct(context.Context, *UpdateProductRequest) (*UpdateProductResponse, error)
	// PurchaseProducts
	PurchaseProducts(context.Context, *PurchaseProductsRequest) (*PurchaseProductsResponse, error)
	// CreatePrice
	CreatePrice(context.Context, *CreatePriceRequest) (*CreatePriceResponse, error)
	// ListPrices
	ListPrices(context.Context, *ListPricesRequest) (*ListPricesResponse, error)
	// CreateFeature
	CreateFeature(context.Context, *CreateFeatureRequest) (*CreateFeatureResponse, error)
	// ListFeatures
	ListFeatures(context.Context, *ListFeaturesRequest) (*ListFeaturesResponse, error)
	// UpdateFeature
	UpdateFeature(context.Context, *UpdateFeatureRequest) (*UpdateFeatureResponse, error)
	// delete feature
	DeleteFeature(context.Context, *DeleteFeatureRequest) (*DeleteFeatureResponse, error)
	// ListDiscounts
	ListDiscounts(context.Context, *ListDiscountsRequest) (*ListDiscountsResponse, error)
	// create license and entitlements
	CreateLicenseAndEntitlements(context.Context, *CreateLicenseAndEntitlementsRequest) (*CreateLicenseAndEntitlementsResponse, error)
	// ListLicenses
	ListLicenses(context.Context, *ListLicensesRequest) (*ListLicensesResponse, error)
	// list revisions
	ListRevisions(context.Context, *ListRevisionsRequest) (*ListRevisionsResponse, error)
	// ListEntitlements
	ListEntitlements(context.Context, *ListEntitlementsRequest) (*ListEntitlementsResponse, error)
	// UpdateEntitlements
	UpdateEntitlements(context.Context, *UpdateEntitlementsRequest) (*UpdateEntitlementsResponse, error)
	// ConsumeEntitlements
	ConsumeEntitlements(context.Context, *ConsumeEntitlementsRequest) (*ConsumeEntitlementsResponse, error)
	// ListLastRedeemTimeForCustomers
	ListLastRedeemTimeForCustomers(context.Context, *ListLastRedeemTimeForCustomerRequest) (*ListLastRedeemTimeForCustomersResponse, error)
	// RefreshExistedApplicationFee
	RefreshExistedApplicationFee(context.Context, *RefreshExistedApplicationFeeRequest) (*RefreshExistedApplicationFeeResponse, error)
	// 本质是consume package，但是为了实现目前membership 消费perks场景，这一期要这么做，后续会deprecated
	// ConsumePackages
	ConsumePackages(context.Context, *ConsumePackagesRequest) (*ConsumePackagesResponse, error)
	// 临时接口，查询所有购买过 membership 顾客的已激活订阅是否和系统内匹配
	CheckMemberships(context.Context, *CheckMembershipsRequest) (*CheckMembershipsResponse, error)
	// 临时接口，将所有激活的 membership 账期重置为和 stripe 一模一样
	RefreshMemberships(context.Context, *RefreshMembershipsRequest) (*RefreshMembershipsResponse, error)
	mustEmbedUnimplementedSubscriptionServiceServer()
}

// UnimplementedSubscriptionServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSubscriptionServiceServer struct {
}

func (UnimplementedSubscriptionServiceServer) CreateSubscription(context.Context, *CreateSubscriptionRequest) (*CreateSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSubscription not implemented")
}
func (UnimplementedSubscriptionServiceServer) UpdateSubscription(context.Context, *UpdateSubscriptionRequest) (*UpdateSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSubscription not implemented")
}
func (UnimplementedSubscriptionServiceServer) GetSubscriptions(context.Context, *GetSubscriptionsRequest) (*GetSubscriptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubscriptions not implemented")
}
func (UnimplementedSubscriptionServiceServer) UpdateSubscriptions(context.Context, *UpdateSubscriptionsRequest) (*UpdateSubscriptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSubscriptions not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListSubscriptions(context.Context, *ListSubscriptionsRequest) (*ListSubscriptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSubscriptions not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListBuyerSubscriptions(context.Context, *ListBuyerSubscriptionsRequest) (*ListBuyerSubscriptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBuyerSubscriptions not implemented")
}
func (UnimplementedSubscriptionServiceServer) CancelSubscription(context.Context, *CancelSubscriptionRequest) (*CancelSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelSubscription not implemented")
}
func (UnimplementedSubscriptionServiceServer) RenewSubscription(context.Context, *RenewSubscriptionRequest) (*RenewSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RenewSubscription not implemented")
}
func (UnimplementedSubscriptionServiceServer) PauseSubscription(context.Context, *PauseSubscriptionRequest) (*PauseSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PauseSubscription not implemented")
}
func (UnimplementedSubscriptionServiceServer) ResumeSubscription(context.Context, *ResumeSubscriptionRequest) (*ResumeSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResumeSubscription not implemented")
}
func (UnimplementedSubscriptionServiceServer) StatSubscription(context.Context, *StatSubscriptionRequest) (*StatSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StatSubscription not implemented")
}
func (UnimplementedSubscriptionServiceServer) GetSubscriptionReport(context.Context, *GetSubscriptionReportRequest) (*GetSubscriptionReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubscriptionReport not implemented")
}
func (UnimplementedSubscriptionServiceServer) PreviewPurchases(context.Context, *PreviewPurchasesRequest) (*PreviewPurchasesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewPurchases not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListPurchaseDetails(context.Context, *ListPurchaseDetailsRequest) (*ListPurchaseDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPurchaseDetails not implemented")
}
func (UnimplementedSubscriptionServiceServer) CreateProduct(context.Context, *CreateProductRequest) (*CreateProductResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateProduct not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListProducts(context.Context, *ListProductsRequest) (*ListProductsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListProducts not implemented")
}
func (UnimplementedSubscriptionServiceServer) UpdateProduct(context.Context, *UpdateProductRequest) (*UpdateProductResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateProduct not implemented")
}
func (UnimplementedSubscriptionServiceServer) PurchaseProducts(context.Context, *PurchaseProductsRequest) (*PurchaseProductsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PurchaseProducts not implemented")
}
func (UnimplementedSubscriptionServiceServer) CreatePrice(context.Context, *CreatePriceRequest) (*CreatePriceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePrice not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListPrices(context.Context, *ListPricesRequest) (*ListPricesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPrices not implemented")
}
func (UnimplementedSubscriptionServiceServer) CreateFeature(context.Context, *CreateFeatureRequest) (*CreateFeatureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFeature not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListFeatures(context.Context, *ListFeaturesRequest) (*ListFeaturesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFeatures not implemented")
}
func (UnimplementedSubscriptionServiceServer) UpdateFeature(context.Context, *UpdateFeatureRequest) (*UpdateFeatureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFeature not implemented")
}
func (UnimplementedSubscriptionServiceServer) DeleteFeature(context.Context, *DeleteFeatureRequest) (*DeleteFeatureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteFeature not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListDiscounts(context.Context, *ListDiscountsRequest) (*ListDiscountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDiscounts not implemented")
}
func (UnimplementedSubscriptionServiceServer) CreateLicenseAndEntitlements(context.Context, *CreateLicenseAndEntitlementsRequest) (*CreateLicenseAndEntitlementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLicenseAndEntitlements not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListLicenses(context.Context, *ListLicensesRequest) (*ListLicensesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLicenses not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListRevisions(context.Context, *ListRevisionsRequest) (*ListRevisionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRevisions not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListEntitlements(context.Context, *ListEntitlementsRequest) (*ListEntitlementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEntitlements not implemented")
}
func (UnimplementedSubscriptionServiceServer) UpdateEntitlements(context.Context, *UpdateEntitlementsRequest) (*UpdateEntitlementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEntitlements not implemented")
}
func (UnimplementedSubscriptionServiceServer) ConsumeEntitlements(context.Context, *ConsumeEntitlementsRequest) (*ConsumeEntitlementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeEntitlements not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListLastRedeemTimeForCustomers(context.Context, *ListLastRedeemTimeForCustomerRequest) (*ListLastRedeemTimeForCustomersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLastRedeemTimeForCustomers not implemented")
}
func (UnimplementedSubscriptionServiceServer) RefreshExistedApplicationFee(context.Context, *RefreshExistedApplicationFeeRequest) (*RefreshExistedApplicationFeeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshExistedApplicationFee not implemented")
}
func (UnimplementedSubscriptionServiceServer) ConsumePackages(context.Context, *ConsumePackagesRequest) (*ConsumePackagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumePackages not implemented")
}
func (UnimplementedSubscriptionServiceServer) CheckMemberships(context.Context, *CheckMembershipsRequest) (*CheckMembershipsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckMemberships not implemented")
}
func (UnimplementedSubscriptionServiceServer) RefreshMemberships(context.Context, *RefreshMembershipsRequest) (*RefreshMembershipsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshMemberships not implemented")
}
func (UnimplementedSubscriptionServiceServer) mustEmbedUnimplementedSubscriptionServiceServer() {}

// UnsafeSubscriptionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SubscriptionServiceServer will
// result in compilation errors.
type UnsafeSubscriptionServiceServer interface {
	mustEmbedUnimplementedSubscriptionServiceServer()
}

func RegisterSubscriptionServiceServer(s grpc.ServiceRegistrar, srv SubscriptionServiceServer) {
	s.RegisterService(&SubscriptionService_ServiceDesc, srv)
}

func _SubscriptionService_CreateSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).CreateSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/CreateSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).CreateSubscription(ctx, req.(*CreateSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_UpdateSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).UpdateSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/UpdateSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).UpdateSubscription(ctx, req.(*UpdateSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_GetSubscriptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubscriptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).GetSubscriptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/GetSubscriptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).GetSubscriptions(ctx, req.(*GetSubscriptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_UpdateSubscriptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSubscriptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).UpdateSubscriptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/UpdateSubscriptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).UpdateSubscriptions(ctx, req.(*UpdateSubscriptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListSubscriptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSubscriptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListSubscriptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/ListSubscriptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListSubscriptions(ctx, req.(*ListSubscriptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListBuyerSubscriptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBuyerSubscriptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListBuyerSubscriptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/ListBuyerSubscriptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListBuyerSubscriptions(ctx, req.(*ListBuyerSubscriptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_CancelSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).CancelSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/CancelSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).CancelSubscription(ctx, req.(*CancelSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_RenewSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RenewSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).RenewSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/RenewSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).RenewSubscription(ctx, req.(*RenewSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_PauseSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PauseSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).PauseSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/PauseSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).PauseSubscription(ctx, req.(*PauseSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ResumeSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResumeSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ResumeSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/ResumeSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ResumeSubscription(ctx, req.(*ResumeSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_StatSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StatSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).StatSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/StatSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).StatSubscription(ctx, req.(*StatSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_GetSubscriptionReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubscriptionReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).GetSubscriptionReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/GetSubscriptionReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).GetSubscriptionReport(ctx, req.(*GetSubscriptionReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_PreviewPurchases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreviewPurchasesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).PreviewPurchases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/PreviewPurchases",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).PreviewPurchases(ctx, req.(*PreviewPurchasesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListPurchaseDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPurchaseDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListPurchaseDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/ListPurchaseDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListPurchaseDetails(ctx, req.(*ListPurchaseDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_CreateProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).CreateProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/CreateProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).CreateProduct(ctx, req.(*CreateProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListProducts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListProductsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListProducts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/ListProducts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListProducts(ctx, req.(*ListProductsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_UpdateProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).UpdateProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/UpdateProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).UpdateProduct(ctx, req.(*UpdateProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_PurchaseProducts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PurchaseProductsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).PurchaseProducts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/PurchaseProducts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).PurchaseProducts(ctx, req.(*PurchaseProductsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_CreatePrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).CreatePrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/CreatePrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).CreatePrice(ctx, req.(*CreatePriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListPrices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPricesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListPrices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/ListPrices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListPrices(ctx, req.(*ListPricesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_CreateFeature_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateFeatureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).CreateFeature(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/CreateFeature",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).CreateFeature(ctx, req.(*CreateFeatureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListFeatures_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListFeaturesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListFeatures(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/ListFeatures",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListFeatures(ctx, req.(*ListFeaturesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_UpdateFeature_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFeatureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).UpdateFeature(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/UpdateFeature",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).UpdateFeature(ctx, req.(*UpdateFeatureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_DeleteFeature_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteFeatureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).DeleteFeature(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/DeleteFeature",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).DeleteFeature(ctx, req.(*DeleteFeatureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListDiscounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDiscountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListDiscounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/ListDiscounts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListDiscounts(ctx, req.(*ListDiscountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_CreateLicenseAndEntitlements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLicenseAndEntitlementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).CreateLicenseAndEntitlements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/CreateLicenseAndEntitlements",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).CreateLicenseAndEntitlements(ctx, req.(*CreateLicenseAndEntitlementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListLicenses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLicensesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListLicenses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/ListLicenses",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListLicenses(ctx, req.(*ListLicensesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListRevisions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRevisionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListRevisions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/ListRevisions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListRevisions(ctx, req.(*ListRevisionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListEntitlements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEntitlementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListEntitlements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/ListEntitlements",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListEntitlements(ctx, req.(*ListEntitlementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_UpdateEntitlements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEntitlementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).UpdateEntitlements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/UpdateEntitlements",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).UpdateEntitlements(ctx, req.(*UpdateEntitlementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ConsumeEntitlements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConsumeEntitlementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ConsumeEntitlements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/ConsumeEntitlements",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ConsumeEntitlements(ctx, req.(*ConsumeEntitlementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListLastRedeemTimeForCustomers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLastRedeemTimeForCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListLastRedeemTimeForCustomers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/ListLastRedeemTimeForCustomers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListLastRedeemTimeForCustomers(ctx, req.(*ListLastRedeemTimeForCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_RefreshExistedApplicationFee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshExistedApplicationFeeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).RefreshExistedApplicationFee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/RefreshExistedApplicationFee",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).RefreshExistedApplicationFee(ctx, req.(*RefreshExistedApplicationFeeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ConsumePackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConsumePackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ConsumePackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/ConsumePackages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ConsumePackages(ctx, req.(*ConsumePackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_CheckMemberships_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckMembershipsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).CheckMemberships(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/CheckMemberships",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).CheckMemberships(ctx, req.(*CheckMembershipsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_RefreshMemberships_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshMembershipsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).RefreshMemberships(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.subscription.v1.SubscriptionService/RefreshMemberships",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).RefreshMemberships(ctx, req.(*RefreshMembershipsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SubscriptionService_ServiceDesc is the grpc.ServiceDesc for SubscriptionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SubscriptionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.subscription.v1.SubscriptionService",
	HandlerType: (*SubscriptionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSubscription",
			Handler:    _SubscriptionService_CreateSubscription_Handler,
		},
		{
			MethodName: "UpdateSubscription",
			Handler:    _SubscriptionService_UpdateSubscription_Handler,
		},
		{
			MethodName: "GetSubscriptions",
			Handler:    _SubscriptionService_GetSubscriptions_Handler,
		},
		{
			MethodName: "UpdateSubscriptions",
			Handler:    _SubscriptionService_UpdateSubscriptions_Handler,
		},
		{
			MethodName: "ListSubscriptions",
			Handler:    _SubscriptionService_ListSubscriptions_Handler,
		},
		{
			MethodName: "ListBuyerSubscriptions",
			Handler:    _SubscriptionService_ListBuyerSubscriptions_Handler,
		},
		{
			MethodName: "CancelSubscription",
			Handler:    _SubscriptionService_CancelSubscription_Handler,
		},
		{
			MethodName: "RenewSubscription",
			Handler:    _SubscriptionService_RenewSubscription_Handler,
		},
		{
			MethodName: "PauseSubscription",
			Handler:    _SubscriptionService_PauseSubscription_Handler,
		},
		{
			MethodName: "ResumeSubscription",
			Handler:    _SubscriptionService_ResumeSubscription_Handler,
		},
		{
			MethodName: "StatSubscription",
			Handler:    _SubscriptionService_StatSubscription_Handler,
		},
		{
			MethodName: "GetSubscriptionReport",
			Handler:    _SubscriptionService_GetSubscriptionReport_Handler,
		},
		{
			MethodName: "PreviewPurchases",
			Handler:    _SubscriptionService_PreviewPurchases_Handler,
		},
		{
			MethodName: "ListPurchaseDetails",
			Handler:    _SubscriptionService_ListPurchaseDetails_Handler,
		},
		{
			MethodName: "CreateProduct",
			Handler:    _SubscriptionService_CreateProduct_Handler,
		},
		{
			MethodName: "ListProducts",
			Handler:    _SubscriptionService_ListProducts_Handler,
		},
		{
			MethodName: "UpdateProduct",
			Handler:    _SubscriptionService_UpdateProduct_Handler,
		},
		{
			MethodName: "PurchaseProducts",
			Handler:    _SubscriptionService_PurchaseProducts_Handler,
		},
		{
			MethodName: "CreatePrice",
			Handler:    _SubscriptionService_CreatePrice_Handler,
		},
		{
			MethodName: "ListPrices",
			Handler:    _SubscriptionService_ListPrices_Handler,
		},
		{
			MethodName: "CreateFeature",
			Handler:    _SubscriptionService_CreateFeature_Handler,
		},
		{
			MethodName: "ListFeatures",
			Handler:    _SubscriptionService_ListFeatures_Handler,
		},
		{
			MethodName: "UpdateFeature",
			Handler:    _SubscriptionService_UpdateFeature_Handler,
		},
		{
			MethodName: "DeleteFeature",
			Handler:    _SubscriptionService_DeleteFeature_Handler,
		},
		{
			MethodName: "ListDiscounts",
			Handler:    _SubscriptionService_ListDiscounts_Handler,
		},
		{
			MethodName: "CreateLicenseAndEntitlements",
			Handler:    _SubscriptionService_CreateLicenseAndEntitlements_Handler,
		},
		{
			MethodName: "ListLicenses",
			Handler:    _SubscriptionService_ListLicenses_Handler,
		},
		{
			MethodName: "ListRevisions",
			Handler:    _SubscriptionService_ListRevisions_Handler,
		},
		{
			MethodName: "ListEntitlements",
			Handler:    _SubscriptionService_ListEntitlements_Handler,
		},
		{
			MethodName: "UpdateEntitlements",
			Handler:    _SubscriptionService_UpdateEntitlements_Handler,
		},
		{
			MethodName: "ConsumeEntitlements",
			Handler:    _SubscriptionService_ConsumeEntitlements_Handler,
		},
		{
			MethodName: "ListLastRedeemTimeForCustomers",
			Handler:    _SubscriptionService_ListLastRedeemTimeForCustomers_Handler,
		},
		{
			MethodName: "RefreshExistedApplicationFee",
			Handler:    _SubscriptionService_RefreshExistedApplicationFee_Handler,
		},
		{
			MethodName: "ConsumePackages",
			Handler:    _SubscriptionService_ConsumePackages_Handler,
		},
		{
			MethodName: "CheckMemberships",
			Handler:    _SubscriptionService_CheckMemberships_Handler,
		},
		{
			MethodName: "RefreshMemberships",
			Handler:    _SubscriptionService_RefreshMemberships_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/subscription/v1/subscription_service.proto",
}
