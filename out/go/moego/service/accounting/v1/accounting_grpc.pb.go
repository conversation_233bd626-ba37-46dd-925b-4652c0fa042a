// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/accounting/v1/accounting.proto

package accountingpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AccountingServiceClient is the client API for AccountingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountingServiceClient interface {
	// GetVisibility
	GetVisibility(ctx context.Context, in *GetVisibilityRequest, opts ...grpc.CallOption) (*GetVisibilityResponse, error)
	// AddToEnrollList
	AddToEnrollList(ctx context.Context, in *AddToEnrollListRequest, opts ...grpc.CallOption) (*AddToEnrollListResponse, error)
	// GetEnrollList
	GetEnrollList(ctx context.Context, in *GetEnrollListRequest, opts ...grpc.CallOption) (*GetEnrollListResponse, error)
	// get onboarding status
	GetOnboardingStatus(ctx context.Context, in *GetOnboardingStatusRequest, opts ...grpc.CallOption) (*GetOnboardingStatusResponse, error)
	// get unselected businesses
	GetUnselectedBusinesses(ctx context.Context, in *GetUnselectedBusinessesRequest, opts ...grpc.CallOption) (*GetUnselectedBusinessesResponse, error)
	// set unselected businesses
	SetUnselectedBusinesses(ctx context.Context, in *SetUnselectedBusinessesRequest, opts ...grpc.CallOption) (*SetUnselectedBusinessesResponse, error)
	// add unselected businesses
	AddUnselectedBusinesses(ctx context.Context, in *AddUnselectedBusinessesRequest, opts ...grpc.CallOption) (*AddUnselectedBusinessesResponse, error)
	// remove unselected businesses
	RemoveUnselectedBusinesses(ctx context.Context, in *RemoveUnselectedBusinessesRequest, opts ...grpc.CallOption) (*RemoveUnselectedBusinessesResponse, error)
	// list settings
	ListSettings(ctx context.Context, in *ListSettingsRequest, opts ...grpc.CallOption) (*ListSettingsResponse, error)
	// get auth token
	GetAuthToken(ctx context.Context, in *GetAuthTokenRequest, opts ...grpc.CallOption) (*GetAuthTokenResponse, error)
	// retry sync data
	RetrySync(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type accountingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountingServiceClient(cc grpc.ClientConnInterface) AccountingServiceClient {
	return &accountingServiceClient{cc}
}

func (c *accountingServiceClient) GetVisibility(ctx context.Context, in *GetVisibilityRequest, opts ...grpc.CallOption) (*GetVisibilityResponse, error) {
	out := new(GetVisibilityResponse)
	err := c.cc.Invoke(ctx, "/moego.service.accounting.v1.AccountingService/GetVisibility", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) AddToEnrollList(ctx context.Context, in *AddToEnrollListRequest, opts ...grpc.CallOption) (*AddToEnrollListResponse, error) {
	out := new(AddToEnrollListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.accounting.v1.AccountingService/AddToEnrollList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) GetEnrollList(ctx context.Context, in *GetEnrollListRequest, opts ...grpc.CallOption) (*GetEnrollListResponse, error) {
	out := new(GetEnrollListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.accounting.v1.AccountingService/GetEnrollList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) GetOnboardingStatus(ctx context.Context, in *GetOnboardingStatusRequest, opts ...grpc.CallOption) (*GetOnboardingStatusResponse, error) {
	out := new(GetOnboardingStatusResponse)
	err := c.cc.Invoke(ctx, "/moego.service.accounting.v1.AccountingService/GetOnboardingStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) GetUnselectedBusinesses(ctx context.Context, in *GetUnselectedBusinessesRequest, opts ...grpc.CallOption) (*GetUnselectedBusinessesResponse, error) {
	out := new(GetUnselectedBusinessesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.accounting.v1.AccountingService/GetUnselectedBusinesses", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) SetUnselectedBusinesses(ctx context.Context, in *SetUnselectedBusinessesRequest, opts ...grpc.CallOption) (*SetUnselectedBusinessesResponse, error) {
	out := new(SetUnselectedBusinessesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.accounting.v1.AccountingService/SetUnselectedBusinesses", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) AddUnselectedBusinesses(ctx context.Context, in *AddUnselectedBusinessesRequest, opts ...grpc.CallOption) (*AddUnselectedBusinessesResponse, error) {
	out := new(AddUnselectedBusinessesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.accounting.v1.AccountingService/AddUnselectedBusinesses", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) RemoveUnselectedBusinesses(ctx context.Context, in *RemoveUnselectedBusinessesRequest, opts ...grpc.CallOption) (*RemoveUnselectedBusinessesResponse, error) {
	out := new(RemoveUnselectedBusinessesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.accounting.v1.AccountingService/RemoveUnselectedBusinesses", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) ListSettings(ctx context.Context, in *ListSettingsRequest, opts ...grpc.CallOption) (*ListSettingsResponse, error) {
	out := new(ListSettingsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.accounting.v1.AccountingService/ListSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) GetAuthToken(ctx context.Context, in *GetAuthTokenRequest, opts ...grpc.CallOption) (*GetAuthTokenResponse, error) {
	out := new(GetAuthTokenResponse)
	err := c.cc.Invoke(ctx, "/moego.service.accounting.v1.AccountingService/GetAuthToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) RetrySync(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.accounting.v1.AccountingService/RetrySync", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountingServiceServer is the server API for AccountingService service.
// All implementations must embed UnimplementedAccountingServiceServer
// for forward compatibility
type AccountingServiceServer interface {
	// GetVisibility
	GetVisibility(context.Context, *GetVisibilityRequest) (*GetVisibilityResponse, error)
	// AddToEnrollList
	AddToEnrollList(context.Context, *AddToEnrollListRequest) (*AddToEnrollListResponse, error)
	// GetEnrollList
	GetEnrollList(context.Context, *GetEnrollListRequest) (*GetEnrollListResponse, error)
	// get onboarding status
	GetOnboardingStatus(context.Context, *GetOnboardingStatusRequest) (*GetOnboardingStatusResponse, error)
	// get unselected businesses
	GetUnselectedBusinesses(context.Context, *GetUnselectedBusinessesRequest) (*GetUnselectedBusinessesResponse, error)
	// set unselected businesses
	SetUnselectedBusinesses(context.Context, *SetUnselectedBusinessesRequest) (*SetUnselectedBusinessesResponse, error)
	// add unselected businesses
	AddUnselectedBusinesses(context.Context, *AddUnselectedBusinessesRequest) (*AddUnselectedBusinessesResponse, error)
	// remove unselected businesses
	RemoveUnselectedBusinesses(context.Context, *RemoveUnselectedBusinessesRequest) (*RemoveUnselectedBusinessesResponse, error)
	// list settings
	ListSettings(context.Context, *ListSettingsRequest) (*ListSettingsResponse, error)
	// get auth token
	GetAuthToken(context.Context, *GetAuthTokenRequest) (*GetAuthTokenResponse, error)
	// retry sync data
	RetrySync(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedAccountingServiceServer()
}

// UnimplementedAccountingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAccountingServiceServer struct {
}

func (UnimplementedAccountingServiceServer) GetVisibility(context.Context, *GetVisibilityRequest) (*GetVisibilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVisibility not implemented")
}
func (UnimplementedAccountingServiceServer) AddToEnrollList(context.Context, *AddToEnrollListRequest) (*AddToEnrollListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddToEnrollList not implemented")
}
func (UnimplementedAccountingServiceServer) GetEnrollList(context.Context, *GetEnrollListRequest) (*GetEnrollListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnrollList not implemented")
}
func (UnimplementedAccountingServiceServer) GetOnboardingStatus(context.Context, *GetOnboardingStatusRequest) (*GetOnboardingStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnboardingStatus not implemented")
}
func (UnimplementedAccountingServiceServer) GetUnselectedBusinesses(context.Context, *GetUnselectedBusinessesRequest) (*GetUnselectedBusinessesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUnselectedBusinesses not implemented")
}
func (UnimplementedAccountingServiceServer) SetUnselectedBusinesses(context.Context, *SetUnselectedBusinessesRequest) (*SetUnselectedBusinessesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUnselectedBusinesses not implemented")
}
func (UnimplementedAccountingServiceServer) AddUnselectedBusinesses(context.Context, *AddUnselectedBusinessesRequest) (*AddUnselectedBusinessesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddUnselectedBusinesses not implemented")
}
func (UnimplementedAccountingServiceServer) RemoveUnselectedBusinesses(context.Context, *RemoveUnselectedBusinessesRequest) (*RemoveUnselectedBusinessesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveUnselectedBusinesses not implemented")
}
func (UnimplementedAccountingServiceServer) ListSettings(context.Context, *ListSettingsRequest) (*ListSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSettings not implemented")
}
func (UnimplementedAccountingServiceServer) GetAuthToken(context.Context, *GetAuthTokenRequest) (*GetAuthTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthToken not implemented")
}
func (UnimplementedAccountingServiceServer) RetrySync(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetrySync not implemented")
}
func (UnimplementedAccountingServiceServer) mustEmbedUnimplementedAccountingServiceServer() {}

// UnsafeAccountingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountingServiceServer will
// result in compilation errors.
type UnsafeAccountingServiceServer interface {
	mustEmbedUnimplementedAccountingServiceServer()
}

func RegisterAccountingServiceServer(s grpc.ServiceRegistrar, srv AccountingServiceServer) {
	s.RegisterService(&AccountingService_ServiceDesc, srv)
}

func _AccountingService_GetVisibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVisibilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).GetVisibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.accounting.v1.AccountingService/GetVisibility",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).GetVisibility(ctx, req.(*GetVisibilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_AddToEnrollList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddToEnrollListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).AddToEnrollList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.accounting.v1.AccountingService/AddToEnrollList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).AddToEnrollList(ctx, req.(*AddToEnrollListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_GetEnrollList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnrollListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).GetEnrollList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.accounting.v1.AccountingService/GetEnrollList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).GetEnrollList(ctx, req.(*GetEnrollListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_GetOnboardingStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnboardingStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).GetOnboardingStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.accounting.v1.AccountingService/GetOnboardingStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).GetOnboardingStatus(ctx, req.(*GetOnboardingStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_GetUnselectedBusinesses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUnselectedBusinessesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).GetUnselectedBusinesses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.accounting.v1.AccountingService/GetUnselectedBusinesses",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).GetUnselectedBusinesses(ctx, req.(*GetUnselectedBusinessesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_SetUnselectedBusinesses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUnselectedBusinessesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).SetUnselectedBusinesses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.accounting.v1.AccountingService/SetUnselectedBusinesses",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).SetUnselectedBusinesses(ctx, req.(*SetUnselectedBusinessesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_AddUnselectedBusinesses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUnselectedBusinessesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).AddUnselectedBusinesses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.accounting.v1.AccountingService/AddUnselectedBusinesses",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).AddUnselectedBusinesses(ctx, req.(*AddUnselectedBusinessesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_RemoveUnselectedBusinesses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveUnselectedBusinessesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).RemoveUnselectedBusinesses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.accounting.v1.AccountingService/RemoveUnselectedBusinesses",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).RemoveUnselectedBusinesses(ctx, req.(*RemoveUnselectedBusinessesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_ListSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).ListSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.accounting.v1.AccountingService/ListSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).ListSettings(ctx, req.(*ListSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_GetAuthToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).GetAuthToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.accounting.v1.AccountingService/GetAuthToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).GetAuthToken(ctx, req.(*GetAuthTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_RetrySync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).RetrySync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.accounting.v1.AccountingService/RetrySync",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).RetrySync(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// AccountingService_ServiceDesc is the grpc.ServiceDesc for AccountingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccountingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.accounting.v1.AccountingService",
	HandlerType: (*AccountingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetVisibility",
			Handler:    _AccountingService_GetVisibility_Handler,
		},
		{
			MethodName: "AddToEnrollList",
			Handler:    _AccountingService_AddToEnrollList_Handler,
		},
		{
			MethodName: "GetEnrollList",
			Handler:    _AccountingService_GetEnrollList_Handler,
		},
		{
			MethodName: "GetOnboardingStatus",
			Handler:    _AccountingService_GetOnboardingStatus_Handler,
		},
		{
			MethodName: "GetUnselectedBusinesses",
			Handler:    _AccountingService_GetUnselectedBusinesses_Handler,
		},
		{
			MethodName: "SetUnselectedBusinesses",
			Handler:    _AccountingService_SetUnselectedBusinesses_Handler,
		},
		{
			MethodName: "AddUnselectedBusinesses",
			Handler:    _AccountingService_AddUnselectedBusinesses_Handler,
		},
		{
			MethodName: "RemoveUnselectedBusinesses",
			Handler:    _AccountingService_RemoveUnselectedBusinesses_Handler,
		},
		{
			MethodName: "ListSettings",
			Handler:    _AccountingService_ListSettings_Handler,
		},
		{
			MethodName: "GetAuthToken",
			Handler:    _AccountingService_GetAuthToken_Handler,
		},
		{
			MethodName: "RetrySync",
			Handler:    _AccountingService_RetrySync_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/accounting/v1/accounting.proto",
}
