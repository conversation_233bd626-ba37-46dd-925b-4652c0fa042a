// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/accounting/v1/compensation_service.proto

package accountingpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CompensationServiceClient is the client API for CompensationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CompensationServiceClient interface {
	// compensate by time range
	CompensateByTimeRange(ctx context.Context, in *CompensateByTimeRangeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// compensate by id
	CompensateByID(ctx context.Context, in *CompensateByIDRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type compensationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCompensationServiceClient(cc grpc.ClientConnInterface) CompensationServiceClient {
	return &compensationServiceClient{cc}
}

func (c *compensationServiceClient) CompensateByTimeRange(ctx context.Context, in *CompensateByTimeRangeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.accounting.v1.CompensationService/CompensateByTimeRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *compensationServiceClient) CompensateByID(ctx context.Context, in *CompensateByIDRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.accounting.v1.CompensationService/CompensateByID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CompensationServiceServer is the server API for CompensationService service.
// All implementations must embed UnimplementedCompensationServiceServer
// for forward compatibility
type CompensationServiceServer interface {
	// compensate by time range
	CompensateByTimeRange(context.Context, *CompensateByTimeRangeRequest) (*emptypb.Empty, error)
	// compensate by id
	CompensateByID(context.Context, *CompensateByIDRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedCompensationServiceServer()
}

// UnimplementedCompensationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCompensationServiceServer struct {
}

func (UnimplementedCompensationServiceServer) CompensateByTimeRange(context.Context, *CompensateByTimeRangeRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompensateByTimeRange not implemented")
}
func (UnimplementedCompensationServiceServer) CompensateByID(context.Context, *CompensateByIDRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompensateByID not implemented")
}
func (UnimplementedCompensationServiceServer) mustEmbedUnimplementedCompensationServiceServer() {}

// UnsafeCompensationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CompensationServiceServer will
// result in compilation errors.
type UnsafeCompensationServiceServer interface {
	mustEmbedUnimplementedCompensationServiceServer()
}

func RegisterCompensationServiceServer(s grpc.ServiceRegistrar, srv CompensationServiceServer) {
	s.RegisterService(&CompensationService_ServiceDesc, srv)
}

func _CompensationService_CompensateByTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompensateByTimeRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompensationServiceServer).CompensateByTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.accounting.v1.CompensationService/CompensateByTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompensationServiceServer).CompensateByTimeRange(ctx, req.(*CompensateByTimeRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompensationService_CompensateByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompensateByIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompensationServiceServer).CompensateByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.accounting.v1.CompensationService/CompensateByID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompensationServiceServer).CompensateByID(ctx, req.(*CompensateByIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CompensationService_ServiceDesc is the grpc.ServiceDesc for CompensationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CompensationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.accounting.v1.CompensationService",
	HandlerType: (*CompensationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CompensateByTimeRange",
			Handler:    _CompensationService_CompensateByTimeRange_Handler,
		},
		{
			MethodName: "CompensateByID",
			Handler:    _CompensationService_CompensateByID_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/accounting/v1/compensation_service.proto",
}
