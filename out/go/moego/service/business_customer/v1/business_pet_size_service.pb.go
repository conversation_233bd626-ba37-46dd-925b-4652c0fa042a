// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/business_customer/v1/business_pet_size_service.proto

package businesscustomersvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list pet size request
type ListPetSizeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *ListPetSizeRequest) Reset() {
	*x = ListPetSizeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetSizeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetSizeRequest) ProtoMessage() {}

func (x *ListPetSizeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetSizeRequest.ProtoReflect.Descriptor instead.
func (*ListPetSizeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListPetSizeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListPetSizeRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// list pet size response
type ListPetSizeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet size list
	Sizes []*v1.BusinessPetSizeModel `protobuf:"bytes,1,rep,name=sizes,proto3" json:"sizes,omitempty"`
}

func (x *ListPetSizeResponse) Reset() {
	*x = ListPetSizeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetSizeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetSizeResponse) ProtoMessage() {}

func (x *ListPetSizeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetSizeResponse.ProtoReflect.Descriptor instead.
func (*ListPetSizeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListPetSizeResponse) GetSizes() []*v1.BusinessPetSizeModel {
	if x != nil {
		return x.Sizes
	}
	return nil
}

// batch upsert pet size request
type BatchUpsertPetSizeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// pet sizes to create, no pet sizes will be created if it is empty
	SizesToCreate []*v1.BusinessPetSizeUpsertDef `protobuf:"bytes,3,rep,name=sizes_to_create,json=sizesToCreate,proto3" json:"sizes_to_create,omitempty"`
	// pet sizes to update, no pet sizes will be updated if it is empty, and the pet sizes in database will be deleted
	SizesToUpdate map[int64]*v1.BusinessPetSizeUpsertDef `protobuf:"bytes,4,rep,name=sizes_to_update,json=sizesToUpdate,proto3" json:"sizes_to_update,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BatchUpsertPetSizeRequest) Reset() {
	*x = BatchUpsertPetSizeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpsertPetSizeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpsertPetSizeRequest) ProtoMessage() {}

func (x *BatchUpsertPetSizeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpsertPetSizeRequest.ProtoReflect.Descriptor instead.
func (*BatchUpsertPetSizeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDescGZIP(), []int{2}
}

func (x *BatchUpsertPetSizeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BatchUpsertPetSizeRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *BatchUpsertPetSizeRequest) GetSizesToCreate() []*v1.BusinessPetSizeUpsertDef {
	if x != nil {
		return x.SizesToCreate
	}
	return nil
}

func (x *BatchUpsertPetSizeRequest) GetSizesToUpdate() map[int64]*v1.BusinessPetSizeUpsertDef {
	if x != nil {
		return x.SizesToUpdate
	}
	return nil
}

// batch upsert pet size response
type BatchUpsertPetSizeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet sizes
	Sizes []*v1.BusinessPetSizeModel `protobuf:"bytes,1,rep,name=sizes,proto3" json:"sizes,omitempty"`
	// deleted pet size ids
	DeletedSizeIds []int64 `protobuf:"varint,2,rep,packed,name=deleted_size_ids,json=deletedSizeIds,proto3" json:"deleted_size_ids,omitempty"`
}

func (x *BatchUpsertPetSizeResponse) Reset() {
	*x = BatchUpsertPetSizeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpsertPetSizeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpsertPetSizeResponse) ProtoMessage() {}

func (x *BatchUpsertPetSizeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpsertPetSizeResponse.ProtoReflect.Descriptor instead.
func (*BatchUpsertPetSizeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDescGZIP(), []int{3}
}

func (x *BatchUpsertPetSizeResponse) GetSizes() []*v1.BusinessPetSizeModel {
	if x != nil {
		return x.Sizes
	}
	return nil
}

func (x *BatchUpsertPetSizeResponse) GetDeletedSizeIds() []int64 {
	if x != nil {
		return x.DeletedSizeIds
	}
	return nil
}

var File_moego_service_business_customer_v1_business_pet_size_service_proto protoreflect.FileDescriptor

var file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDesc = []byte{
	0x0a, 0x42, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x3e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x7b, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x53, 0x69,
	0x7a, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x22, 0x64, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x05, 0x73, 0x69, 0x7a, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x05, 0x73, 0x69, 0x7a, 0x65, 0x73, 0x22, 0xe0, 0x03, 0x0a, 0x19, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x63, 0x0a, 0x0f, 0x73,
	0x69, 0x7a, 0x65, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x65,
	0x66, 0x52, 0x0d, 0x73, 0x69, 0x7a, 0x65, 0x73, 0x54, 0x6f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x12, 0x78, 0x0a, 0x0f, 0x73, 0x69, 0x7a, 0x65, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x69, 0x7a, 0x65, 0x73, 0x54, 0x6f,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x73, 0x69, 0x7a,
	0x65, 0x73, 0x54, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x1a, 0x7d, 0x0a, 0x12, 0x53, 0x69,
	0x7a, 0x65, 0x73, 0x54, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x51, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65,
	0x74, 0x53, 0x69, 0x7a, 0x65, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x65, 0x66, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x95, 0x01, 0x0a, 0x1a, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x05, 0x73, 0x69, 0x7a, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x05, 0x73, 0x69, 0x7a, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x49, 0x64,
	0x73, 0x32, 0xae, 0x02, 0x0a, 0x16, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65,
	0x74, 0x53, 0x69, 0x7a, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7e, 0x0a, 0x0b,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74,
	0x53, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01, 0x0a,
	0x12, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x65, 0x74, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70,
	0x73, 0x65, 0x72, 0x74, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x9d, 0x01, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x6d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x76, 0x63,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDescOnce sync.Once
	file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDescData = file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDesc
)

func file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDescGZIP() []byte {
	file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDescOnce.Do(func() {
		file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDescData)
	})
	return file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDescData
}

var file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_service_business_customer_v1_business_pet_size_service_proto_goTypes = []interface{}{
	(*ListPetSizeRequest)(nil),          // 0: moego.service.business_customer.v1.ListPetSizeRequest
	(*ListPetSizeResponse)(nil),         // 1: moego.service.business_customer.v1.ListPetSizeResponse
	(*BatchUpsertPetSizeRequest)(nil),   // 2: moego.service.business_customer.v1.BatchUpsertPetSizeRequest
	(*BatchUpsertPetSizeResponse)(nil),  // 3: moego.service.business_customer.v1.BatchUpsertPetSizeResponse
	nil,                                 // 4: moego.service.business_customer.v1.BatchUpsertPetSizeRequest.SizesToUpdateEntry
	(*v1.BusinessPetSizeModel)(nil),     // 5: moego.models.business_customer.v1.BusinessPetSizeModel
	(*v1.BusinessPetSizeUpsertDef)(nil), // 6: moego.models.business_customer.v1.BusinessPetSizeUpsertDef
}
var file_moego_service_business_customer_v1_business_pet_size_service_proto_depIdxs = []int32{
	5, // 0: moego.service.business_customer.v1.ListPetSizeResponse.sizes:type_name -> moego.models.business_customer.v1.BusinessPetSizeModel
	6, // 1: moego.service.business_customer.v1.BatchUpsertPetSizeRequest.sizes_to_create:type_name -> moego.models.business_customer.v1.BusinessPetSizeUpsertDef
	4, // 2: moego.service.business_customer.v1.BatchUpsertPetSizeRequest.sizes_to_update:type_name -> moego.service.business_customer.v1.BatchUpsertPetSizeRequest.SizesToUpdateEntry
	5, // 3: moego.service.business_customer.v1.BatchUpsertPetSizeResponse.sizes:type_name -> moego.models.business_customer.v1.BusinessPetSizeModel
	6, // 4: moego.service.business_customer.v1.BatchUpsertPetSizeRequest.SizesToUpdateEntry.value:type_name -> moego.models.business_customer.v1.BusinessPetSizeUpsertDef
	0, // 5: moego.service.business_customer.v1.BusinessPetSizeService.ListPetSize:input_type -> moego.service.business_customer.v1.ListPetSizeRequest
	2, // 6: moego.service.business_customer.v1.BusinessPetSizeService.BatchUpsertPetSize:input_type -> moego.service.business_customer.v1.BatchUpsertPetSizeRequest
	1, // 7: moego.service.business_customer.v1.BusinessPetSizeService.ListPetSize:output_type -> moego.service.business_customer.v1.ListPetSizeResponse
	3, // 8: moego.service.business_customer.v1.BusinessPetSizeService.BatchUpsertPetSize:output_type -> moego.service.business_customer.v1.BatchUpsertPetSizeResponse
	7, // [7:9] is the sub-list for method output_type
	5, // [5:7] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_service_business_customer_v1_business_pet_size_service_proto_init() }
func file_moego_service_business_customer_v1_business_pet_size_service_proto_init() {
	if File_moego_service_business_customer_v1_business_pet_size_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetSizeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetSizeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpsertPetSizeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpsertPetSizeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_business_customer_v1_business_pet_size_service_proto_goTypes,
		DependencyIndexes: file_moego_service_business_customer_v1_business_pet_size_service_proto_depIdxs,
		MessageInfos:      file_moego_service_business_customer_v1_business_pet_size_service_proto_msgTypes,
	}.Build()
	File_moego_service_business_customer_v1_business_pet_size_service_proto = out.File
	file_moego_service_business_customer_v1_business_pet_size_service_proto_rawDesc = nil
	file_moego_service_business_customer_v1_business_pet_size_service_proto_goTypes = nil
	file_moego_service_business_customer_v1_business_pet_size_service_proto_depIdxs = nil
}
