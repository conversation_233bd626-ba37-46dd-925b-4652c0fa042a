// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/automation/v1/workflow_service.proto

package automationsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/automation/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetWorkflowConfigRequest
type GetWorkflowConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetWorkflowConfigRequest) Reset() {
	*x = GetWorkflowConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkflowConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowConfigRequest) ProtoMessage() {}

func (x *GetWorkflowConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowConfigRequest.ProtoReflect.Descriptor instead.
func (*GetWorkflowConfigRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{0}
}

// GetWorkflowConfigResponse
type GetWorkflowConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// WorkflowConfig List
	WorkflowConfigs []*v1.WorkflowConfig `protobuf:"bytes,1,rep,name=workflow_configs,json=workflowConfigs,proto3" json:"workflow_configs,omitempty"`
	// Common Filters
	FilterGroups []*v2.FilterGroup `protobuf:"bytes,2,rep,name=filter_groups,json=filterGroups,proto3" json:"filter_groups,omitempty"`
	// Event Filters
	EventFilterGroups []*v1.EventFilterGroups `protobuf:"bytes,3,rep,name=event_filter_groups,json=eventFilterGroups,proto3" json:"event_filter_groups,omitempty"`
}

func (x *GetWorkflowConfigResponse) Reset() {
	*x = GetWorkflowConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkflowConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowConfigResponse) ProtoMessage() {}

func (x *GetWorkflowConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowConfigResponse.ProtoReflect.Descriptor instead.
func (*GetWorkflowConfigResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetWorkflowConfigResponse) GetWorkflowConfigs() []*v1.WorkflowConfig {
	if x != nil {
		return x.WorkflowConfigs
	}
	return nil
}

func (x *GetWorkflowConfigResponse) GetFilterGroups() []*v2.FilterGroup {
	if x != nil {
		return x.FilterGroups
	}
	return nil
}

func (x *GetWorkflowConfigResponse) GetEventFilterGroups() []*v1.EventFilterGroups {
	if x != nil {
		return x.EventFilterGroups
	}
	return nil
}

// CreateWorkflowRequest
type CreateWorkflowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow to be created
	Workflow *v1.CreateWorkflowDef `protobuf:"bytes,1,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *CreateWorkflowRequest) Reset() {
	*x = CreateWorkflowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorkflowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkflowRequest) ProtoMessage() {}

func (x *CreateWorkflowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkflowRequest.ProtoReflect.Descriptor instead.
func (*CreateWorkflowRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateWorkflowRequest) GetWorkflow() *v1.CreateWorkflowDef {
	if x != nil {
		return x.Workflow
	}
	return nil
}

// CreateWorkflowResponse
type CreateWorkflowResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Created workflow
	Workflow *v1.Workflow `protobuf:"bytes,1,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *CreateWorkflowResponse) Reset() {
	*x = CreateWorkflowResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorkflowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkflowResponse) ProtoMessage() {}

func (x *CreateWorkflowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkflowResponse.ProtoReflect.Descriptor instead.
func (*CreateWorkflowResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreateWorkflowResponse) GetWorkflow() *v1.Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

// UpdateWorkflowContentRequest
type UpdateWorkflowContentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow ID
	WorkflowId int64 `protobuf:"varint,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	// Steps to update
	Steps []*v1.CreateStepDef `protobuf:"bytes,2,rep,name=steps,proto3" json:"steps,omitempty"`
	// Workflow Consumer Data
	ConsumerData *v1.Workflow_ConsumerData `protobuf:"bytes,3,opt,name=consumer_data,json=consumerData,proto3" json:"consumer_data,omitempty"`
}

func (x *UpdateWorkflowContentRequest) Reset() {
	*x = UpdateWorkflowContentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkflowContentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkflowContentRequest) ProtoMessage() {}

func (x *UpdateWorkflowContentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkflowContentRequest.ProtoReflect.Descriptor instead.
func (*UpdateWorkflowContentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateWorkflowContentRequest) GetWorkflowId() int64 {
	if x != nil {
		return x.WorkflowId
	}
	return 0
}

func (x *UpdateWorkflowContentRequest) GetSteps() []*v1.CreateStepDef {
	if x != nil {
		return x.Steps
	}
	return nil
}

func (x *UpdateWorkflowContentRequest) GetConsumerData() *v1.Workflow_ConsumerData {
	if x != nil {
		return x.ConsumerData
	}
	return nil
}

// UpdateWorkflowContentResponse
type UpdateWorkflowContentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Updated workflow
	Workflow *v1.Workflow `protobuf:"bytes,1,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *UpdateWorkflowContentResponse) Reset() {
	*x = UpdateWorkflowContentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkflowContentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkflowContentResponse) ProtoMessage() {}

func (x *UpdateWorkflowContentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkflowContentResponse.ProtoReflect.Descriptor instead.
func (*UpdateWorkflowContentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateWorkflowContentResponse) GetWorkflow() *v1.Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

// UpdateWorkflowInfoRequest
type UpdateWorkflowInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow ID
	WorkflowId int64 `protobuf:"varint,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	// Workflow name
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// Workflow description
	Desc *string `protobuf:"bytes,3,opt,name=desc,proto3,oneof" json:"desc,omitempty"`
	// Workflow status
	Status *v1.Workflow_Status `protobuf:"varint,4,opt,name=status,proto3,enum=moego.models.automation.v1.Workflow_Status,oneof" json:"status,omitempty"`
	// Workflow setting
	Setting *v1.WorkflowSetting `protobuf:"bytes,5,opt,name=setting,proto3,oneof" json:"setting,omitempty"`
	// enterprise apply to
	WorkflowEnterpriseApply *v1.WorkflowEnterpriseApply `protobuf:"bytes,6,opt,name=workflow_enterprise_apply,json=workflowEnterpriseApply,proto3,oneof" json:"workflow_enterprise_apply,omitempty"`
	// shut down steps option for set workflow to INACTIVE
	ShutDownPendingSteps *bool `protobuf:"varint,7,opt,name=shut_down_pending_steps,json=shutDownPendingSteps,proto3,oneof" json:"shut_down_pending_steps,omitempty"`
}

func (x *UpdateWorkflowInfoRequest) Reset() {
	*x = UpdateWorkflowInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkflowInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkflowInfoRequest) ProtoMessage() {}

func (x *UpdateWorkflowInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkflowInfoRequest.ProtoReflect.Descriptor instead.
func (*UpdateWorkflowInfoRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateWorkflowInfoRequest) GetWorkflowId() int64 {
	if x != nil {
		return x.WorkflowId
	}
	return 0
}

func (x *UpdateWorkflowInfoRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateWorkflowInfoRequest) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

func (x *UpdateWorkflowInfoRequest) GetStatus() v1.Workflow_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.Workflow_Status(0)
}

func (x *UpdateWorkflowInfoRequest) GetSetting() *v1.WorkflowSetting {
	if x != nil {
		return x.Setting
	}
	return nil
}

func (x *UpdateWorkflowInfoRequest) GetWorkflowEnterpriseApply() *v1.WorkflowEnterpriseApply {
	if x != nil {
		return x.WorkflowEnterpriseApply
	}
	return nil
}

func (x *UpdateWorkflowInfoRequest) GetShutDownPendingSteps() bool {
	if x != nil && x.ShutDownPendingSteps != nil {
		return *x.ShutDownPendingSteps
	}
	return false
}

// UpdateWorkflowInfoResponse
type UpdateWorkflowInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Updated workflow
	Workflow *v1.Workflow `protobuf:"bytes,1,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *UpdateWorkflowInfoResponse) Reset() {
	*x = UpdateWorkflowInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkflowInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkflowInfoResponse) ProtoMessage() {}

func (x *UpdateWorkflowInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkflowInfoResponse.ProtoReflect.Descriptor instead.
func (*UpdateWorkflowInfoResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateWorkflowInfoResponse) GetWorkflow() *v1.Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

// ListWorkflowCategoriesRequest
type ListWorkflowCategoriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListWorkflowCategoriesRequest) Reset() {
	*x = ListWorkflowCategoriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkflowCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowCategoriesRequest) ProtoMessage() {}

func (x *ListWorkflowCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowCategoriesRequest.ProtoReflect.Descriptor instead.
func (*ListWorkflowCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{8}
}

// ListWorkflowCategoriesResponse
type ListWorkflowCategoriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow categories
	Categories []*v1.WorkflowCategory `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
}

func (x *ListWorkflowCategoriesResponse) Reset() {
	*x = ListWorkflowCategoriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkflowCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowCategoriesResponse) ProtoMessage() {}

func (x *ListWorkflowCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowCategoriesResponse.ProtoReflect.Descriptor instead.
func (*ListWorkflowCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListWorkflowCategoriesResponse) GetCategories() []*v1.WorkflowCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

// ListWorkflowsRequest
type ListWorkflowsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pagination request
	Pagination *v21.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// Filter criteria
	Filter *ListWorkflowsRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *ListWorkflowsRequest) Reset() {
	*x = ListWorkflowsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkflowsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowsRequest) ProtoMessage() {}

func (x *ListWorkflowsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowsRequest.ProtoReflect.Descriptor instead.
func (*ListWorkflowsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{10}
}

func (x *ListWorkflowsRequest) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListWorkflowsRequest) GetFilter() *ListWorkflowsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListWorkflowsResponse
type ListWorkflowsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of workflows
	Workflows []*v1.Workflow `protobuf:"bytes,1,rep,name=workflows,proto3" json:"workflows,omitempty"`
	// Pagination response
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListWorkflowsResponse) Reset() {
	*x = ListWorkflowsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkflowsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowsResponse) ProtoMessage() {}

func (x *ListWorkflowsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowsResponse.ProtoReflect.Descriptor instead.
func (*ListWorkflowsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{11}
}

func (x *ListWorkflowsResponse) GetWorkflows() []*v1.Workflow {
	if x != nil {
		return x.Workflows
	}
	return nil
}

func (x *ListWorkflowsResponse) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// ListEnterpriseWorkflowsRequest
type ListEnterpriseWorkflowsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pagination request
	Pagination *v21.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// Filter criteria
	Filter *ListEnterpriseWorkflowsRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *ListEnterpriseWorkflowsRequest) Reset() {
	*x = ListEnterpriseWorkflowsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEnterpriseWorkflowsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEnterpriseWorkflowsRequest) ProtoMessage() {}

func (x *ListEnterpriseWorkflowsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEnterpriseWorkflowsRequest.ProtoReflect.Descriptor instead.
func (*ListEnterpriseWorkflowsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{12}
}

func (x *ListEnterpriseWorkflowsRequest) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListEnterpriseWorkflowsRequest) GetFilter() *ListEnterpriseWorkflowsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListEnterpriseWorkflowsResponse
type ListEnterpriseWorkflowsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of workflows
	Workflows []*v1.Workflow `protobuf:"bytes,1,rep,name=workflows,proto3" json:"workflows,omitempty"`
	// Pagination response
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListEnterpriseWorkflowsResponse) Reset() {
	*x = ListEnterpriseWorkflowsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEnterpriseWorkflowsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEnterpriseWorkflowsResponse) ProtoMessage() {}

func (x *ListEnterpriseWorkflowsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEnterpriseWorkflowsResponse.ProtoReflect.Descriptor instead.
func (*ListEnterpriseWorkflowsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{13}
}

func (x *ListEnterpriseWorkflowsResponse) GetWorkflows() []*v1.Workflow {
	if x != nil {
		return x.Workflows
	}
	return nil
}

func (x *ListEnterpriseWorkflowsResponse) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// ListWorkflowRecordsRequest
type ListWorkflowRecordsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow ID
	WorkflowId int64 `protobuf:"varint,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	// Pagination request
	Pagination *v21.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// Filter criteria
	Filter *ListWorkflowRecordsRequest_Filter `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *ListWorkflowRecordsRequest) Reset() {
	*x = ListWorkflowRecordsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkflowRecordsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowRecordsRequest) ProtoMessage() {}

func (x *ListWorkflowRecordsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowRecordsRequest.ProtoReflect.Descriptor instead.
func (*ListWorkflowRecordsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{14}
}

func (x *ListWorkflowRecordsRequest) GetWorkflowId() int64 {
	if x != nil {
		return x.WorkflowId
	}
	return 0
}

func (x *ListWorkflowRecordsRequest) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListWorkflowRecordsRequest) GetFilter() *ListWorkflowRecordsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListWorkflowRecordsResponse
type ListWorkflowRecordsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of workflow records
	WorkflowRecords []*v1.WorkflowRecord `protobuf:"bytes,1,rep,name=workflow_records,json=workflowRecords,proto3" json:"workflow_records,omitempty"`
	// Pagination response
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListWorkflowRecordsResponse) Reset() {
	*x = ListWorkflowRecordsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkflowRecordsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowRecordsResponse) ProtoMessage() {}

func (x *ListWorkflowRecordsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowRecordsResponse.ProtoReflect.Descriptor instead.
func (*ListWorkflowRecordsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{15}
}

func (x *ListWorkflowRecordsResponse) GetWorkflowRecords() []*v1.WorkflowRecord {
	if x != nil {
		return x.WorkflowRecords
	}
	return nil
}

func (x *ListWorkflowRecordsResponse) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// ListWorkflowTemplatesRequest
type ListWorkflowTemplatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pagination request
	Pagination *v21.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// Filter criteria
	Filter *ListWorkflowTemplatesRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *ListWorkflowTemplatesRequest) Reset() {
	*x = ListWorkflowTemplatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkflowTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowTemplatesRequest) ProtoMessage() {}

func (x *ListWorkflowTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowTemplatesRequest.ProtoReflect.Descriptor instead.
func (*ListWorkflowTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{16}
}

func (x *ListWorkflowTemplatesRequest) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListWorkflowTemplatesRequest) GetFilter() *ListWorkflowTemplatesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListWorkflowTemplatesResponse
type ListWorkflowTemplatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of workflow templates
	Workflows []*v1.Workflow `protobuf:"bytes,1,rep,name=workflows,proto3" json:"workflows,omitempty"`
	// Pagination response
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListWorkflowTemplatesResponse) Reset() {
	*x = ListWorkflowTemplatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkflowTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowTemplatesResponse) ProtoMessage() {}

func (x *ListWorkflowTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowTemplatesResponse.ProtoReflect.Descriptor instead.
func (*ListWorkflowTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{17}
}

func (x *ListWorkflowTemplatesResponse) GetWorkflows() []*v1.Workflow {
	if x != nil {
		return x.Workflows
	}
	return nil
}

func (x *ListWorkflowTemplatesResponse) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// GetWorkflowInfoRequest
type GetWorkflowInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow ID
	WorkflowId int64 `protobuf:"varint,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
}

func (x *GetWorkflowInfoRequest) Reset() {
	*x = GetWorkflowInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkflowInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowInfoRequest) ProtoMessage() {}

func (x *GetWorkflowInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowInfoRequest.ProtoReflect.Descriptor instead.
func (*GetWorkflowInfoRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetWorkflowInfoRequest) GetWorkflowId() int64 {
	if x != nil {
		return x.WorkflowId
	}
	return 0
}

// GetWorkflowInfoResponse
type GetWorkflowInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow information
	Workflow *v1.Workflow `protobuf:"bytes,1,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *GetWorkflowInfoResponse) Reset() {
	*x = GetWorkflowInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkflowInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowInfoResponse) ProtoMessage() {}

func (x *GetWorkflowInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowInfoResponse.ProtoReflect.Descriptor instead.
func (*GetWorkflowInfoResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetWorkflowInfoResponse) GetWorkflow() *v1.Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

// GetWorkflowTemplateInfoRequest
type GetWorkflowTemplateInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow template ID
	WorkflowId int64 `protobuf:"varint,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
}

func (x *GetWorkflowTemplateInfoRequest) Reset() {
	*x = GetWorkflowTemplateInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkflowTemplateInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowTemplateInfoRequest) ProtoMessage() {}

func (x *GetWorkflowTemplateInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowTemplateInfoRequest.ProtoReflect.Descriptor instead.
func (*GetWorkflowTemplateInfoRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetWorkflowTemplateInfoRequest) GetWorkflowId() int64 {
	if x != nil {
		return x.WorkflowId
	}
	return 0
}

// GetWorkflowTemplateInfoResponse
type GetWorkflowTemplateInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow template information
	Workflow *v1.Workflow `protobuf:"bytes,1,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *GetWorkflowTemplateInfoResponse) Reset() {
	*x = GetWorkflowTemplateInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkflowTemplateInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowTemplateInfoResponse) ProtoMessage() {}

func (x *GetWorkflowTemplateInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowTemplateInfoResponse.ProtoReflect.Descriptor instead.
func (*GetWorkflowTemplateInfoResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetWorkflowTemplateInfoResponse) GetWorkflow() *v1.Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

// CreateWorkflowTemplateRequest
type CreateWorkflowTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow template to be created
	Workflow *v1.CreateWorkflowDef `protobuf:"bytes,1,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *CreateWorkflowTemplateRequest) Reset() {
	*x = CreateWorkflowTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorkflowTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkflowTemplateRequest) ProtoMessage() {}

func (x *CreateWorkflowTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkflowTemplateRequest.ProtoReflect.Descriptor instead.
func (*CreateWorkflowTemplateRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{22}
}

func (x *CreateWorkflowTemplateRequest) GetWorkflow() *v1.CreateWorkflowDef {
	if x != nil {
		return x.Workflow
	}
	return nil
}

// CreateWorkflowTemplateResponse
type CreateWorkflowTemplateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Created workflow template
	Workflow *v1.Workflow `protobuf:"bytes,1,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *CreateWorkflowTemplateResponse) Reset() {
	*x = CreateWorkflowTemplateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorkflowTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkflowTemplateResponse) ProtoMessage() {}

func (x *CreateWorkflowTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkflowTemplateResponse.ProtoReflect.Descriptor instead.
func (*CreateWorkflowTemplateResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{23}
}

func (x *CreateWorkflowTemplateResponse) GetWorkflow() *v1.Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

// UpdateWorkflowSettingRequest
type UpdateWorkflowSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Workflow setting
	Setting *v1.WorkflowSetting `protobuf:"bytes,2,opt,name=setting,proto3" json:"setting,omitempty"`
}

func (x *UpdateWorkflowSettingRequest) Reset() {
	*x = UpdateWorkflowSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkflowSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkflowSettingRequest) ProtoMessage() {}

func (x *UpdateWorkflowSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkflowSettingRequest.ProtoReflect.Descriptor instead.
func (*UpdateWorkflowSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{24}
}

func (x *UpdateWorkflowSettingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateWorkflowSettingRequest) GetSetting() *v1.WorkflowSetting {
	if x != nil {
		return x.Setting
	}
	return nil
}

// UpdateWorkflowSettingResponse
type UpdateWorkflowSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Updated workflow setting
	Setting *v1.WorkflowSetting `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
}

func (x *UpdateWorkflowSettingResponse) Reset() {
	*x = UpdateWorkflowSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkflowSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkflowSettingResponse) ProtoMessage() {}

func (x *UpdateWorkflowSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkflowSettingResponse.ProtoReflect.Descriptor instead.
func (*UpdateWorkflowSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateWorkflowSettingResponse) GetSetting() *v1.WorkflowSetting {
	if x != nil {
		return x.Setting
	}
	return nil
}

// GetWorkflowSettingRequest
type GetWorkflowSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetWorkflowSettingRequest) Reset() {
	*x = GetWorkflowSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkflowSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowSettingRequest) ProtoMessage() {}

func (x *GetWorkflowSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowSettingRequest.ProtoReflect.Descriptor instead.
func (*GetWorkflowSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetWorkflowSettingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// GetWorkflowSettingResponse
type GetWorkflowSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow setting
	Setting *v1.WorkflowSetting `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
}

func (x *GetWorkflowSettingResponse) Reset() {
	*x = GetWorkflowSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkflowSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowSettingResponse) ProtoMessage() {}

func (x *GetWorkflowSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowSettingResponse.ProtoReflect.Descriptor instead.
func (*GetWorkflowSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetWorkflowSettingResponse) GetSetting() *v1.WorkflowSetting {
	if x != nil {
		return x.Setting
	}
	return nil
}

// FilterCustomerRequest
type FilterCustomerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Filter requests
	Filters []*v2.FilterRequest `protobuf:"bytes,1,rep,name=filters,proto3" json:"filters,omitempty"`
	// Pagination request
	Pagination *v21.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// Company ID
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Tenants IDs, empty is all tenants
	TenantsIds []int64 `protobuf:"varint,4,rep,packed,name=tenants_ids,json=tenantsIds,proto3" json:"tenants_ids,omitempty"`
	// Customer ID filter
	CustomerId *int64 `protobuf:"varint,10,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
}

func (x *FilterCustomerRequest) Reset() {
	*x = FilterCustomerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCustomerRequest) ProtoMessage() {}

func (x *FilterCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCustomerRequest.ProtoReflect.Descriptor instead.
func (*FilterCustomerRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{28}
}

func (x *FilterCustomerRequest) GetFilters() []*v2.FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *FilterCustomerRequest) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *FilterCustomerRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *FilterCustomerRequest) GetTenantsIds() []int64 {
	if x != nil {
		return x.TenantsIds
	}
	return nil
}

func (x *FilterCustomerRequest) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

// FilterCustomerResponse
type FilterCustomerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of customers
	Customer []*v11.BusinessCustomerInfoModel `protobuf:"bytes,1,rep,name=customer,proto3" json:"customer,omitempty"`
	// Pagination response
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *FilterCustomerResponse) Reset() {
	*x = FilterCustomerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCustomerResponse) ProtoMessage() {}

func (x *FilterCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCustomerResponse.ProtoReflect.Descriptor instead.
func (*FilterCustomerResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{29}
}

func (x *FilterCustomerResponse) GetCustomer() []*v11.BusinessCustomerInfoModel {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *FilterCustomerResponse) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// Filter
type ListWorkflowsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow name filter
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// Workflow status filter
	Status []v1.Workflow_Status `protobuf:"varint,2,rep,packed,name=status,proto3,enum=moego.models.automation.v1.Workflow_Status" json:"status,omitempty"`
	// Category ID filter
	CategoryId *int64 `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
}

func (x *ListWorkflowsRequest_Filter) Reset() {
	*x = ListWorkflowsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkflowsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowsRequest_Filter) ProtoMessage() {}

func (x *ListWorkflowsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListWorkflowsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{10, 0}
}

func (x *ListWorkflowsRequest_Filter) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ListWorkflowsRequest_Filter) GetStatus() []v1.Workflow_Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ListWorkflowsRequest_Filter) GetCategoryId() int64 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

// Filter
type ListEnterpriseWorkflowsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow name filter
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// Workflow status filter
	Status []v1.Workflow_Status `protobuf:"varint,2,rep,packed,name=status,proto3,enum=moego.models.automation.v1.Workflow_Status" json:"status,omitempty"`
	// Tenant Group IDs filter
	TenantsGroupIds []int64 `protobuf:"varint,3,rep,packed,name=tenants_group_ids,json=tenantsGroupIds,proto3" json:"tenants_group_ids,omitempty"`
	// Tenant IDs filter
	TenantsIds []int64 `protobuf:"varint,4,rep,packed,name=tenants_ids,json=tenantsIds,proto3" json:"tenants_ids,omitempty"`
}

func (x *ListEnterpriseWorkflowsRequest_Filter) Reset() {
	*x = ListEnterpriseWorkflowsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEnterpriseWorkflowsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEnterpriseWorkflowsRequest_Filter) ProtoMessage() {}

func (x *ListEnterpriseWorkflowsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEnterpriseWorkflowsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListEnterpriseWorkflowsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{12, 0}
}

func (x *ListEnterpriseWorkflowsRequest_Filter) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ListEnterpriseWorkflowsRequest_Filter) GetStatus() []v1.Workflow_Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ListEnterpriseWorkflowsRequest_Filter) GetTenantsGroupIds() []int64 {
	if x != nil {
		return x.TenantsGroupIds
	}
	return nil
}

func (x *ListEnterpriseWorkflowsRequest_Filter) GetTenantsIds() []int64 {
	if x != nil {
		return x.TenantsIds
	}
	return nil
}

// Filter
type ListWorkflowRecordsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Customer name filter
	CustomerName *string `protobuf:"bytes,1,opt,name=customer_name,json=customerName,proto3,oneof" json:"customer_name,omitempty"`
}

func (x *ListWorkflowRecordsRequest_Filter) Reset() {
	*x = ListWorkflowRecordsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkflowRecordsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowRecordsRequest_Filter) ProtoMessage() {}

func (x *ListWorkflowRecordsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowRecordsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListWorkflowRecordsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{14, 0}
}

func (x *ListWorkflowRecordsRequest_Filter) GetCustomerName() string {
	if x != nil && x.CustomerName != nil {
		return *x.CustomerName
	}
	return ""
}

// Filter
type ListWorkflowTemplatesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Template name filter
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// Category ID filter
	CategoryId *int64 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	// Recommendation type filter
	RecommendType *v1.Workflow_RecommendType `protobuf:"varint,3,opt,name=recommend_type,json=recommendType,proto3,enum=moego.models.automation.v1.Workflow_RecommendType,oneof" json:"recommend_type,omitempty"`
}

func (x *ListWorkflowTemplatesRequest_Filter) Reset() {
	*x = ListWorkflowTemplatesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkflowTemplatesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowTemplatesRequest_Filter) ProtoMessage() {}

func (x *ListWorkflowTemplatesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_automation_v1_workflow_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowTemplatesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListWorkflowTemplatesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP(), []int{16, 0}
}

func (x *ListWorkflowTemplatesRequest_Filter) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ListWorkflowTemplatesRequest_Filter) GetCategoryId() int64 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

func (x *ListWorkflowTemplatesRequest_Filter) GetRecommendType() v1.Workflow_RecommendType {
	if x != nil && x.RecommendType != nil {
		return *x.RecommendType
	}
	return v1.Workflow_RecommendType(0)
}

var File_moego_service_automation_v1_workflow_service_proto protoreflect.FileDescriptor

var file_moego_service_automation_v1_workflow_service_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32,
	0x2f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1a, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x9e, 0x02, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x55, 0x0a, 0x10, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x4b,
	0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0c, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x5d, 0x0a, 0x13, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x11, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x62, 0x0a, 0x15, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x44, 0x65, 0x66, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x5a,
	0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0xd8, 0x01, 0x0a, 0x1c, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x05,
	0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x65, 0x70, 0x44, 0x65, 0x66, 0x52, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x12, 0x56, 0x0a,
	0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x22, 0x61, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x08,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x99, 0x04, 0x0a, 0x19, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x17, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01,
	0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x02, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x4a, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x48, 0x03, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12,
	0x74, 0x0a, 0x19, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x48, 0x04, 0x52, 0x17, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x17, 0x73, 0x68, 0x75, 0x74, 0x5f, 0x64, 0x6f,
	0x77, 0x6e, 0x5f, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x14, 0x73, 0x68, 0x75, 0x74, 0x44, 0x6f,
	0x77, 0x6e, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x65, 0x70, 0x73, 0x88, 0x01,
	0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a,
	0x0a, 0x08, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x1c, 0x0a, 0x1a, 0x5f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x73, 0x68, 0x75,
	0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73,
	0x74, 0x65, 0x70, 0x73, 0x22, 0x5e, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x40, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x22, 0x1f, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x6e, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0xf7, 0x02, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x55, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x48, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x1a, 0xa5, 0x01,
	0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0a, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22,
	0xb3, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x09, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x12, 0x47, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa2, 0x03, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01,
	0x12, 0x5f, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x48, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01,
	0x01, 0x1a, 0xbc, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x73, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x73, 0x49, 0x64, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xbd, 0x01, 0x0a, 0x1f, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42,
	0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x73, 0x12, 0x47, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xc2, 0x02, 0x0a, 0x1a, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x5b, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x48, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x1a,
	0x44, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x0d, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22,
	0xcc, 0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x55, 0x0a, 0x10, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x0f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x47, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb5,
	0x03, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x5d, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x1a, 0xd3, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x01, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x5e, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x48, 0x02, 0x52, 0x0d,
	0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01,
	0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x72, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xbb, 0x01, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x12, 0x47, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x39, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x22,
	0x5b, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x08, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x41, 0x0a, 0x1e,
	0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x22,
	0x63, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x40, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x22, 0x6a, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x44, 0x65, 0x66, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x22, 0x62, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x40, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x22, 0x84, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x66, 0x0a, 0x1d, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x07,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74,
	0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x22, 0x3a, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22,
	0x63, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a,
	0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x22, 0xa8, 0x02, 0x0a, 0x15, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x42,
	0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x49, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x01, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22,
	0xca, 0x01, 0x0a, 0x16, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x08, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x12, 0x47, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0xa6, 0x10, 0x0a,
	0x0f, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x82, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x8e, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x85, 0x01, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x16, 0x4c, 0x69,
	0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a,
	0x0d, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x12, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x94, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x73, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x88, 0x01, 0x0a,
	0x13, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8e, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74,
	0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x94, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8e, 0x01,
	0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85,
	0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x0e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x75, 0x74,
	0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x89, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x3b, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x76, 0x63, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_automation_v1_workflow_service_proto_rawDescOnce sync.Once
	file_moego_service_automation_v1_workflow_service_proto_rawDescData = file_moego_service_automation_v1_workflow_service_proto_rawDesc
)

func file_moego_service_automation_v1_workflow_service_proto_rawDescGZIP() []byte {
	file_moego_service_automation_v1_workflow_service_proto_rawDescOnce.Do(func() {
		file_moego_service_automation_v1_workflow_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_automation_v1_workflow_service_proto_rawDescData)
	})
	return file_moego_service_automation_v1_workflow_service_proto_rawDescData
}

var file_moego_service_automation_v1_workflow_service_proto_msgTypes = make([]protoimpl.MessageInfo, 34)
var file_moego_service_automation_v1_workflow_service_proto_goTypes = []interface{}{
	(*GetWorkflowConfigRequest)(nil),              // 0: moego.service.automation.v1.GetWorkflowConfigRequest
	(*GetWorkflowConfigResponse)(nil),             // 1: moego.service.automation.v1.GetWorkflowConfigResponse
	(*CreateWorkflowRequest)(nil),                 // 2: moego.service.automation.v1.CreateWorkflowRequest
	(*CreateWorkflowResponse)(nil),                // 3: moego.service.automation.v1.CreateWorkflowResponse
	(*UpdateWorkflowContentRequest)(nil),          // 4: moego.service.automation.v1.UpdateWorkflowContentRequest
	(*UpdateWorkflowContentResponse)(nil),         // 5: moego.service.automation.v1.UpdateWorkflowContentResponse
	(*UpdateWorkflowInfoRequest)(nil),             // 6: moego.service.automation.v1.UpdateWorkflowInfoRequest
	(*UpdateWorkflowInfoResponse)(nil),            // 7: moego.service.automation.v1.UpdateWorkflowInfoResponse
	(*ListWorkflowCategoriesRequest)(nil),         // 8: moego.service.automation.v1.ListWorkflowCategoriesRequest
	(*ListWorkflowCategoriesResponse)(nil),        // 9: moego.service.automation.v1.ListWorkflowCategoriesResponse
	(*ListWorkflowsRequest)(nil),                  // 10: moego.service.automation.v1.ListWorkflowsRequest
	(*ListWorkflowsResponse)(nil),                 // 11: moego.service.automation.v1.ListWorkflowsResponse
	(*ListEnterpriseWorkflowsRequest)(nil),        // 12: moego.service.automation.v1.ListEnterpriseWorkflowsRequest
	(*ListEnterpriseWorkflowsResponse)(nil),       // 13: moego.service.automation.v1.ListEnterpriseWorkflowsResponse
	(*ListWorkflowRecordsRequest)(nil),            // 14: moego.service.automation.v1.ListWorkflowRecordsRequest
	(*ListWorkflowRecordsResponse)(nil),           // 15: moego.service.automation.v1.ListWorkflowRecordsResponse
	(*ListWorkflowTemplatesRequest)(nil),          // 16: moego.service.automation.v1.ListWorkflowTemplatesRequest
	(*ListWorkflowTemplatesResponse)(nil),         // 17: moego.service.automation.v1.ListWorkflowTemplatesResponse
	(*GetWorkflowInfoRequest)(nil),                // 18: moego.service.automation.v1.GetWorkflowInfoRequest
	(*GetWorkflowInfoResponse)(nil),               // 19: moego.service.automation.v1.GetWorkflowInfoResponse
	(*GetWorkflowTemplateInfoRequest)(nil),        // 20: moego.service.automation.v1.GetWorkflowTemplateInfoRequest
	(*GetWorkflowTemplateInfoResponse)(nil),       // 21: moego.service.automation.v1.GetWorkflowTemplateInfoResponse
	(*CreateWorkflowTemplateRequest)(nil),         // 22: moego.service.automation.v1.CreateWorkflowTemplateRequest
	(*CreateWorkflowTemplateResponse)(nil),        // 23: moego.service.automation.v1.CreateWorkflowTemplateResponse
	(*UpdateWorkflowSettingRequest)(nil),          // 24: moego.service.automation.v1.UpdateWorkflowSettingRequest
	(*UpdateWorkflowSettingResponse)(nil),         // 25: moego.service.automation.v1.UpdateWorkflowSettingResponse
	(*GetWorkflowSettingRequest)(nil),             // 26: moego.service.automation.v1.GetWorkflowSettingRequest
	(*GetWorkflowSettingResponse)(nil),            // 27: moego.service.automation.v1.GetWorkflowSettingResponse
	(*FilterCustomerRequest)(nil),                 // 28: moego.service.automation.v1.FilterCustomerRequest
	(*FilterCustomerResponse)(nil),                // 29: moego.service.automation.v1.FilterCustomerResponse
	(*ListWorkflowsRequest_Filter)(nil),           // 30: moego.service.automation.v1.ListWorkflowsRequest.Filter
	(*ListEnterpriseWorkflowsRequest_Filter)(nil), // 31: moego.service.automation.v1.ListEnterpriseWorkflowsRequest.Filter
	(*ListWorkflowRecordsRequest_Filter)(nil),     // 32: moego.service.automation.v1.ListWorkflowRecordsRequest.Filter
	(*ListWorkflowTemplatesRequest_Filter)(nil),   // 33: moego.service.automation.v1.ListWorkflowTemplatesRequest.Filter
	(*v1.WorkflowConfig)(nil),                     // 34: moego.models.automation.v1.WorkflowConfig
	(*v2.FilterGroup)(nil),                        // 35: moego.models.reporting.v2.FilterGroup
	(*v1.EventFilterGroups)(nil),                  // 36: moego.models.automation.v1.EventFilterGroups
	(*v1.CreateWorkflowDef)(nil),                  // 37: moego.models.automation.v1.CreateWorkflowDef
	(*v1.Workflow)(nil),                           // 38: moego.models.automation.v1.Workflow
	(*v1.CreateStepDef)(nil),                      // 39: moego.models.automation.v1.CreateStepDef
	(*v1.Workflow_ConsumerData)(nil),              // 40: moego.models.automation.v1.Workflow.ConsumerData
	(v1.Workflow_Status)(0),                       // 41: moego.models.automation.v1.Workflow.Status
	(*v1.WorkflowSetting)(nil),                    // 42: moego.models.automation.v1.WorkflowSetting
	(*v1.WorkflowEnterpriseApply)(nil),            // 43: moego.models.automation.v1.WorkflowEnterpriseApply
	(*v1.WorkflowCategory)(nil),                   // 44: moego.models.automation.v1.WorkflowCategory
	(*v21.PaginationRequest)(nil),                 // 45: moego.utils.v2.PaginationRequest
	(*v21.PaginationResponse)(nil),                // 46: moego.utils.v2.PaginationResponse
	(*v1.WorkflowRecord)(nil),                     // 47: moego.models.automation.v1.WorkflowRecord
	(*v2.FilterRequest)(nil),                      // 48: moego.models.reporting.v2.FilterRequest
	(*v11.BusinessCustomerInfoModel)(nil),         // 49: moego.models.business_customer.v1.BusinessCustomerInfoModel
	(v1.Workflow_RecommendType)(0),                // 50: moego.models.automation.v1.Workflow.RecommendType
}
var file_moego_service_automation_v1_workflow_service_proto_depIdxs = []int32{
	34, // 0: moego.service.automation.v1.GetWorkflowConfigResponse.workflow_configs:type_name -> moego.models.automation.v1.WorkflowConfig
	35, // 1: moego.service.automation.v1.GetWorkflowConfigResponse.filter_groups:type_name -> moego.models.reporting.v2.FilterGroup
	36, // 2: moego.service.automation.v1.GetWorkflowConfigResponse.event_filter_groups:type_name -> moego.models.automation.v1.EventFilterGroups
	37, // 3: moego.service.automation.v1.CreateWorkflowRequest.workflow:type_name -> moego.models.automation.v1.CreateWorkflowDef
	38, // 4: moego.service.automation.v1.CreateWorkflowResponse.workflow:type_name -> moego.models.automation.v1.Workflow
	39, // 5: moego.service.automation.v1.UpdateWorkflowContentRequest.steps:type_name -> moego.models.automation.v1.CreateStepDef
	40, // 6: moego.service.automation.v1.UpdateWorkflowContentRequest.consumer_data:type_name -> moego.models.automation.v1.Workflow.ConsumerData
	38, // 7: moego.service.automation.v1.UpdateWorkflowContentResponse.workflow:type_name -> moego.models.automation.v1.Workflow
	41, // 8: moego.service.automation.v1.UpdateWorkflowInfoRequest.status:type_name -> moego.models.automation.v1.Workflow.Status
	42, // 9: moego.service.automation.v1.UpdateWorkflowInfoRequest.setting:type_name -> moego.models.automation.v1.WorkflowSetting
	43, // 10: moego.service.automation.v1.UpdateWorkflowInfoRequest.workflow_enterprise_apply:type_name -> moego.models.automation.v1.WorkflowEnterpriseApply
	38, // 11: moego.service.automation.v1.UpdateWorkflowInfoResponse.workflow:type_name -> moego.models.automation.v1.Workflow
	44, // 12: moego.service.automation.v1.ListWorkflowCategoriesResponse.categories:type_name -> moego.models.automation.v1.WorkflowCategory
	45, // 13: moego.service.automation.v1.ListWorkflowsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	30, // 14: moego.service.automation.v1.ListWorkflowsRequest.filter:type_name -> moego.service.automation.v1.ListWorkflowsRequest.Filter
	38, // 15: moego.service.automation.v1.ListWorkflowsResponse.workflows:type_name -> moego.models.automation.v1.Workflow
	46, // 16: moego.service.automation.v1.ListWorkflowsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	45, // 17: moego.service.automation.v1.ListEnterpriseWorkflowsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	31, // 18: moego.service.automation.v1.ListEnterpriseWorkflowsRequest.filter:type_name -> moego.service.automation.v1.ListEnterpriseWorkflowsRequest.Filter
	38, // 19: moego.service.automation.v1.ListEnterpriseWorkflowsResponse.workflows:type_name -> moego.models.automation.v1.Workflow
	46, // 20: moego.service.automation.v1.ListEnterpriseWorkflowsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	45, // 21: moego.service.automation.v1.ListWorkflowRecordsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	32, // 22: moego.service.automation.v1.ListWorkflowRecordsRequest.filter:type_name -> moego.service.automation.v1.ListWorkflowRecordsRequest.Filter
	47, // 23: moego.service.automation.v1.ListWorkflowRecordsResponse.workflow_records:type_name -> moego.models.automation.v1.WorkflowRecord
	46, // 24: moego.service.automation.v1.ListWorkflowRecordsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	45, // 25: moego.service.automation.v1.ListWorkflowTemplatesRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	33, // 26: moego.service.automation.v1.ListWorkflowTemplatesRequest.filter:type_name -> moego.service.automation.v1.ListWorkflowTemplatesRequest.Filter
	38, // 27: moego.service.automation.v1.ListWorkflowTemplatesResponse.workflows:type_name -> moego.models.automation.v1.Workflow
	46, // 28: moego.service.automation.v1.ListWorkflowTemplatesResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	38, // 29: moego.service.automation.v1.GetWorkflowInfoResponse.workflow:type_name -> moego.models.automation.v1.Workflow
	38, // 30: moego.service.automation.v1.GetWorkflowTemplateInfoResponse.workflow:type_name -> moego.models.automation.v1.Workflow
	37, // 31: moego.service.automation.v1.CreateWorkflowTemplateRequest.workflow:type_name -> moego.models.automation.v1.CreateWorkflowDef
	38, // 32: moego.service.automation.v1.CreateWorkflowTemplateResponse.workflow:type_name -> moego.models.automation.v1.Workflow
	42, // 33: moego.service.automation.v1.UpdateWorkflowSettingRequest.setting:type_name -> moego.models.automation.v1.WorkflowSetting
	42, // 34: moego.service.automation.v1.UpdateWorkflowSettingResponse.setting:type_name -> moego.models.automation.v1.WorkflowSetting
	42, // 35: moego.service.automation.v1.GetWorkflowSettingResponse.setting:type_name -> moego.models.automation.v1.WorkflowSetting
	48, // 36: moego.service.automation.v1.FilterCustomerRequest.filters:type_name -> moego.models.reporting.v2.FilterRequest
	45, // 37: moego.service.automation.v1.FilterCustomerRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	49, // 38: moego.service.automation.v1.FilterCustomerResponse.customer:type_name -> moego.models.business_customer.v1.BusinessCustomerInfoModel
	46, // 39: moego.service.automation.v1.FilterCustomerResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	41, // 40: moego.service.automation.v1.ListWorkflowsRequest.Filter.status:type_name -> moego.models.automation.v1.Workflow.Status
	41, // 41: moego.service.automation.v1.ListEnterpriseWorkflowsRequest.Filter.status:type_name -> moego.models.automation.v1.Workflow.Status
	50, // 42: moego.service.automation.v1.ListWorkflowTemplatesRequest.Filter.recommend_type:type_name -> moego.models.automation.v1.Workflow.RecommendType
	0,  // 43: moego.service.automation.v1.WorkflowService.GetWorkflowConfig:input_type -> moego.service.automation.v1.GetWorkflowConfigRequest
	2,  // 44: moego.service.automation.v1.WorkflowService.CreateWorkflow:input_type -> moego.service.automation.v1.CreateWorkflowRequest
	4,  // 45: moego.service.automation.v1.WorkflowService.UpdateWorkflowContent:input_type -> moego.service.automation.v1.UpdateWorkflowContentRequest
	6,  // 46: moego.service.automation.v1.WorkflowService.UpdateWorkflowInfo:input_type -> moego.service.automation.v1.UpdateWorkflowInfoRequest
	8,  // 47: moego.service.automation.v1.WorkflowService.ListWorkflowCategories:input_type -> moego.service.automation.v1.ListWorkflowCategoriesRequest
	10, // 48: moego.service.automation.v1.WorkflowService.ListWorkflows:input_type -> moego.service.automation.v1.ListWorkflowsRequest
	12, // 49: moego.service.automation.v1.WorkflowService.ListEnterpriseWorkflows:input_type -> moego.service.automation.v1.ListEnterpriseWorkflowsRequest
	14, // 50: moego.service.automation.v1.WorkflowService.ListWorkflowRecords:input_type -> moego.service.automation.v1.ListWorkflowRecordsRequest
	16, // 51: moego.service.automation.v1.WorkflowService.ListWorkflowTemplates:input_type -> moego.service.automation.v1.ListWorkflowTemplatesRequest
	18, // 52: moego.service.automation.v1.WorkflowService.GetWorkflowInfo:input_type -> moego.service.automation.v1.GetWorkflowInfoRequest
	20, // 53: moego.service.automation.v1.WorkflowService.GetWorkflowTemplateInfo:input_type -> moego.service.automation.v1.GetWorkflowTemplateInfoRequest
	24, // 54: moego.service.automation.v1.WorkflowService.UpdateWorkflowSetting:input_type -> moego.service.automation.v1.UpdateWorkflowSettingRequest
	26, // 55: moego.service.automation.v1.WorkflowService.GetWorkflowSetting:input_type -> moego.service.automation.v1.GetWorkflowSettingRequest
	22, // 56: moego.service.automation.v1.WorkflowService.CreateWorkflowTemplate:input_type -> moego.service.automation.v1.CreateWorkflowTemplateRequest
	28, // 57: moego.service.automation.v1.WorkflowService.FilterCustomer:input_type -> moego.service.automation.v1.FilterCustomerRequest
	1,  // 58: moego.service.automation.v1.WorkflowService.GetWorkflowConfig:output_type -> moego.service.automation.v1.GetWorkflowConfigResponse
	3,  // 59: moego.service.automation.v1.WorkflowService.CreateWorkflow:output_type -> moego.service.automation.v1.CreateWorkflowResponse
	5,  // 60: moego.service.automation.v1.WorkflowService.UpdateWorkflowContent:output_type -> moego.service.automation.v1.UpdateWorkflowContentResponse
	7,  // 61: moego.service.automation.v1.WorkflowService.UpdateWorkflowInfo:output_type -> moego.service.automation.v1.UpdateWorkflowInfoResponse
	9,  // 62: moego.service.automation.v1.WorkflowService.ListWorkflowCategories:output_type -> moego.service.automation.v1.ListWorkflowCategoriesResponse
	11, // 63: moego.service.automation.v1.WorkflowService.ListWorkflows:output_type -> moego.service.automation.v1.ListWorkflowsResponse
	13, // 64: moego.service.automation.v1.WorkflowService.ListEnterpriseWorkflows:output_type -> moego.service.automation.v1.ListEnterpriseWorkflowsResponse
	15, // 65: moego.service.automation.v1.WorkflowService.ListWorkflowRecords:output_type -> moego.service.automation.v1.ListWorkflowRecordsResponse
	17, // 66: moego.service.automation.v1.WorkflowService.ListWorkflowTemplates:output_type -> moego.service.automation.v1.ListWorkflowTemplatesResponse
	19, // 67: moego.service.automation.v1.WorkflowService.GetWorkflowInfo:output_type -> moego.service.automation.v1.GetWorkflowInfoResponse
	21, // 68: moego.service.automation.v1.WorkflowService.GetWorkflowTemplateInfo:output_type -> moego.service.automation.v1.GetWorkflowTemplateInfoResponse
	25, // 69: moego.service.automation.v1.WorkflowService.UpdateWorkflowSetting:output_type -> moego.service.automation.v1.UpdateWorkflowSettingResponse
	27, // 70: moego.service.automation.v1.WorkflowService.GetWorkflowSetting:output_type -> moego.service.automation.v1.GetWorkflowSettingResponse
	23, // 71: moego.service.automation.v1.WorkflowService.CreateWorkflowTemplate:output_type -> moego.service.automation.v1.CreateWorkflowTemplateResponse
	29, // 72: moego.service.automation.v1.WorkflowService.FilterCustomer:output_type -> moego.service.automation.v1.FilterCustomerResponse
	58, // [58:73] is the sub-list for method output_type
	43, // [43:58] is the sub-list for method input_type
	43, // [43:43] is the sub-list for extension type_name
	43, // [43:43] is the sub-list for extension extendee
	0,  // [0:43] is the sub-list for field type_name
}

func init() { file_moego_service_automation_v1_workflow_service_proto_init() }
func file_moego_service_automation_v1_workflow_service_proto_init() {
	if File_moego_service_automation_v1_workflow_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkflowConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkflowConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWorkflowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWorkflowResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorkflowContentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorkflowContentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorkflowInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorkflowInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkflowCategoriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkflowCategoriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkflowsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkflowsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEnterpriseWorkflowsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEnterpriseWorkflowsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkflowRecordsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkflowRecordsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkflowTemplatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkflowTemplatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkflowInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkflowInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkflowTemplateInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkflowTemplateInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWorkflowTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWorkflowTemplateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorkflowSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorkflowSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkflowSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkflowSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterCustomerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterCustomerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkflowsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEnterpriseWorkflowsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkflowRecordsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_automation_v1_workflow_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkflowTemplatesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[15].OneofWrappers = []interface{}{}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[16].OneofWrappers = []interface{}{}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[17].OneofWrappers = []interface{}{}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[28].OneofWrappers = []interface{}{}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[29].OneofWrappers = []interface{}{}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[30].OneofWrappers = []interface{}{}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[31].OneofWrappers = []interface{}{}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[32].OneofWrappers = []interface{}{}
	file_moego_service_automation_v1_workflow_service_proto_msgTypes[33].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_automation_v1_workflow_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   34,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_automation_v1_workflow_service_proto_goTypes,
		DependencyIndexes: file_moego_service_automation_v1_workflow_service_proto_depIdxs,
		MessageInfos:      file_moego_service_automation_v1_workflow_service_proto_msgTypes,
	}.Build()
	File_moego_service_automation_v1_workflow_service_proto = out.File
	file_moego_service_automation_v1_workflow_service_proto_rawDesc = nil
	file_moego_service_automation_v1_workflow_service_proto_goTypes = nil
	file_moego_service_automation_v1_workflow_service_proto_depIdxs = nil
}
