// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/message/v2/message_service.proto

package messagesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MessageServiceClient is the client API for MessageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MessageServiceClient interface {
	// 通过 ID 获取对话
	GetChat(ctx context.Context, in *GetChatRequest, opts ...grpc.CallOption) (*GetChatResponse, error)
	// 获取对话列表
	ListChat(ctx context.Context, in *ListChatRequest, opts ...grpc.CallOption) (*ListChatResponse, error)
	// 创建对话
	CreateChat(ctx context.Context, in *CreateChatRequest, opts ...grpc.CallOption) (*CreateChatResponse, error)
	// 获取对话的消息
	ListChatMessage(ctx context.Context, in *ListChatMessageRequest, opts ...grpc.CallOption) (*ListChatMessageResponse, error)
	// 发送消息
	SendMessage(ctx context.Context, in *SendMessageRequest, opts ...grpc.CallOption) (*SendMessageResponse, error)
	// 获取全部的未读消息数量
	GetAllChatUnreadMessageCount(ctx context.Context, in *GetAllChatUnreadMessageCountRequest, opts ...grpc.CallOption) (*GetAllChatUnreadMessageCountResponse, error)
	// 获取指定 company 和 business 内的未读消息数量
	GetCustomerUnreadMessageCount(ctx context.Context, in *GetCustomerUnreadMessageCountRequest, opts ...grpc.CallOption) (*GetCustomerUnreadMessageCountResponse, error)
	// 批量获取消息
	BatchGetMessage(ctx context.Context, in *BatchGetMessageRequest, opts ...grpc.CallOption) (*BatchGetMessageResponse, error)
	// 合并消息记录
	MergeMessages(ctx context.Context, in *MergeMessagesRequest, opts ...grpc.CallOption) (*MergeMessagesResponse, error)
}

type messageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMessageServiceClient(cc grpc.ClientConnInterface) MessageServiceClient {
	return &messageServiceClient{cc}
}

func (c *messageServiceClient) GetChat(ctx context.Context, in *GetChatRequest, opts ...grpc.CallOption) (*GetChatResponse, error) {
	out := new(GetChatResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v2.MessageService/GetChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) ListChat(ctx context.Context, in *ListChatRequest, opts ...grpc.CallOption) (*ListChatResponse, error) {
	out := new(ListChatResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v2.MessageService/ListChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) CreateChat(ctx context.Context, in *CreateChatRequest, opts ...grpc.CallOption) (*CreateChatResponse, error) {
	out := new(CreateChatResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v2.MessageService/CreateChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) ListChatMessage(ctx context.Context, in *ListChatMessageRequest, opts ...grpc.CallOption) (*ListChatMessageResponse, error) {
	out := new(ListChatMessageResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v2.MessageService/ListChatMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) SendMessage(ctx context.Context, in *SendMessageRequest, opts ...grpc.CallOption) (*SendMessageResponse, error) {
	out := new(SendMessageResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v2.MessageService/SendMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) GetAllChatUnreadMessageCount(ctx context.Context, in *GetAllChatUnreadMessageCountRequest, opts ...grpc.CallOption) (*GetAllChatUnreadMessageCountResponse, error) {
	out := new(GetAllChatUnreadMessageCountResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v2.MessageService/GetAllChatUnreadMessageCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) GetCustomerUnreadMessageCount(ctx context.Context, in *GetCustomerUnreadMessageCountRequest, opts ...grpc.CallOption) (*GetCustomerUnreadMessageCountResponse, error) {
	out := new(GetCustomerUnreadMessageCountResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v2.MessageService/GetCustomerUnreadMessageCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) BatchGetMessage(ctx context.Context, in *BatchGetMessageRequest, opts ...grpc.CallOption) (*BatchGetMessageResponse, error) {
	out := new(BatchGetMessageResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v2.MessageService/BatchGetMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) MergeMessages(ctx context.Context, in *MergeMessagesRequest, opts ...grpc.CallOption) (*MergeMessagesResponse, error) {
	out := new(MergeMessagesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v2.MessageService/MergeMessages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageServiceServer is the server API for MessageService service.
// All implementations must embed UnimplementedMessageServiceServer
// for forward compatibility
type MessageServiceServer interface {
	// 通过 ID 获取对话
	GetChat(context.Context, *GetChatRequest) (*GetChatResponse, error)
	// 获取对话列表
	ListChat(context.Context, *ListChatRequest) (*ListChatResponse, error)
	// 创建对话
	CreateChat(context.Context, *CreateChatRequest) (*CreateChatResponse, error)
	// 获取对话的消息
	ListChatMessage(context.Context, *ListChatMessageRequest) (*ListChatMessageResponse, error)
	// 发送消息
	SendMessage(context.Context, *SendMessageRequest) (*SendMessageResponse, error)
	// 获取全部的未读消息数量
	GetAllChatUnreadMessageCount(context.Context, *GetAllChatUnreadMessageCountRequest) (*GetAllChatUnreadMessageCountResponse, error)
	// 获取指定 company 和 business 内的未读消息数量
	GetCustomerUnreadMessageCount(context.Context, *GetCustomerUnreadMessageCountRequest) (*GetCustomerUnreadMessageCountResponse, error)
	// 批量获取消息
	BatchGetMessage(context.Context, *BatchGetMessageRequest) (*BatchGetMessageResponse, error)
	// 合并消息记录
	MergeMessages(context.Context, *MergeMessagesRequest) (*MergeMessagesResponse, error)
	mustEmbedUnimplementedMessageServiceServer()
}

// UnimplementedMessageServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMessageServiceServer struct {
}

func (UnimplementedMessageServiceServer) GetChat(context.Context, *GetChatRequest) (*GetChatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChat not implemented")
}
func (UnimplementedMessageServiceServer) ListChat(context.Context, *ListChatRequest) (*ListChatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListChat not implemented")
}
func (UnimplementedMessageServiceServer) CreateChat(context.Context, *CreateChatRequest) (*CreateChatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateChat not implemented")
}
func (UnimplementedMessageServiceServer) ListChatMessage(context.Context, *ListChatMessageRequest) (*ListChatMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListChatMessage not implemented")
}
func (UnimplementedMessageServiceServer) SendMessage(context.Context, *SendMessageRequest) (*SendMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMessage not implemented")
}
func (UnimplementedMessageServiceServer) GetAllChatUnreadMessageCount(context.Context, *GetAllChatUnreadMessageCountRequest) (*GetAllChatUnreadMessageCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllChatUnreadMessageCount not implemented")
}
func (UnimplementedMessageServiceServer) GetCustomerUnreadMessageCount(context.Context, *GetCustomerUnreadMessageCountRequest) (*GetCustomerUnreadMessageCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerUnreadMessageCount not implemented")
}
func (UnimplementedMessageServiceServer) BatchGetMessage(context.Context, *BatchGetMessageRequest) (*BatchGetMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetMessage not implemented")
}
func (UnimplementedMessageServiceServer) MergeMessages(context.Context, *MergeMessagesRequest) (*MergeMessagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MergeMessages not implemented")
}
func (UnimplementedMessageServiceServer) mustEmbedUnimplementedMessageServiceServer() {}

// UnsafeMessageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessageServiceServer will
// result in compilation errors.
type UnsafeMessageServiceServer interface {
	mustEmbedUnimplementedMessageServiceServer()
}

func RegisterMessageServiceServer(s grpc.ServiceRegistrar, srv MessageServiceServer) {
	s.RegisterService(&MessageService_ServiceDesc, srv)
}

func _MessageService_GetChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).GetChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v2.MessageService/GetChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).GetChat(ctx, req.(*GetChatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_ListChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListChatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).ListChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v2.MessageService/ListChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).ListChat(ctx, req.(*ListChatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_CreateChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).CreateChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v2.MessageService/CreateChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).CreateChat(ctx, req.(*CreateChatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_ListChatMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListChatMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).ListChatMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v2.MessageService/ListChatMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).ListChatMessage(ctx, req.(*ListChatMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_SendMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).SendMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v2.MessageService/SendMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).SendMessage(ctx, req.(*SendMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_GetAllChatUnreadMessageCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllChatUnreadMessageCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).GetAllChatUnreadMessageCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v2.MessageService/GetAllChatUnreadMessageCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).GetAllChatUnreadMessageCount(ctx, req.(*GetAllChatUnreadMessageCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_GetCustomerUnreadMessageCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerUnreadMessageCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).GetCustomerUnreadMessageCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v2.MessageService/GetCustomerUnreadMessageCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).GetCustomerUnreadMessageCount(ctx, req.(*GetCustomerUnreadMessageCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_BatchGetMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).BatchGetMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v2.MessageService/BatchGetMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).BatchGetMessage(ctx, req.(*BatchGetMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_MergeMessages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MergeMessagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).MergeMessages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v2.MessageService/MergeMessages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).MergeMessages(ctx, req.(*MergeMessagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MessageService_ServiceDesc is the grpc.ServiceDesc for MessageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.message.v2.MessageService",
	HandlerType: (*MessageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChat",
			Handler:    _MessageService_GetChat_Handler,
		},
		{
			MethodName: "ListChat",
			Handler:    _MessageService_ListChat_Handler,
		},
		{
			MethodName: "CreateChat",
			Handler:    _MessageService_CreateChat_Handler,
		},
		{
			MethodName: "ListChatMessage",
			Handler:    _MessageService_ListChatMessage_Handler,
		},
		{
			MethodName: "SendMessage",
			Handler:    _MessageService_SendMessage_Handler,
		},
		{
			MethodName: "GetAllChatUnreadMessageCount",
			Handler:    _MessageService_GetAllChatUnreadMessageCount_Handler,
		},
		{
			MethodName: "GetCustomerUnreadMessageCount",
			Handler:    _MessageService_GetCustomerUnreadMessageCount_Handler,
		},
		{
			MethodName: "BatchGetMessage",
			Handler:    _MessageService_BatchGetMessage_Handler,
		},
		{
			MethodName: "MergeMessages",
			Handler:    _MessageService_MergeMessages_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/message/v2/message_service.proto",
}
