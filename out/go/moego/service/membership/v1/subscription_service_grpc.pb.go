// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/membership/v1/subscription_service.proto

package membershipsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SubscriptionServiceClient is the client API for SubscriptionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SubscriptionServiceClient interface {
	// create sell link
	CreateSellLink(ctx context.Context, in *CreateSellLinkRequest, opts ...grpc.CallOption) (*CreateSellLinkResponse, error)
	// get sell link
	GetSellLink(ctx context.Context, in *GetSellLinkRequest, opts ...grpc.CallOption) (*GetSellLinkResponse, error)
	// delete sell link
	DeleteSellLink(ctx context.Context, in *DeleteSellLinkRequest, opts ...grpc.CallOption) (*DeleteSellLinkResponse, error)
	// create subscription
	CreateSubscription(ctx context.Context, in *CreateSubscriptionRequest, opts ...grpc.CallOption) (*CreateSubscriptionResponse, error)
	// update subscription
	UpdateSubscription(ctx context.Context, in *UpdateSubscriptionRequest, opts ...grpc.CallOption) (*UpdateSubscriptionResponse, error)
	// get subscription
	GetSubscription(ctx context.Context, in *GetSubscriptionRequest, opts ...grpc.CallOption) (*GetSubscriptionResponse, error)
	// list subscription
	ListSubscriptions(ctx context.Context, in *ListSubscriptionsRequest, opts ...grpc.CallOption) (*ListSubscriptionsResponse, error)
	// list customer subscription
	ListCustomerSubscriptions(ctx context.Context, in *ListCustomerSubscriptionsRequest, opts ...grpc.CallOption) (*ListCustomerSubscriptionsResponse, error)
	// update subscription
	CancelSubscription(ctx context.Context, in *CancelSubscriptionRequest, opts ...grpc.CallOption) (*CancelSubscriptionResponse, error)
	// delete subscription
	RenewSubscription(ctx context.Context, in *RenewSubscriptionRequest, opts ...grpc.CallOption) (*RenewSubscriptionResponse, error)
	// PauseSubscription
	PauseSubscription(ctx context.Context, in *PauseSubscriptionRequest, opts ...grpc.CallOption) (*PauseSubscriptionResponse, error)
	// ResumeSubscription
	ResumeSubscription(ctx context.Context, in *ResumeSubscriptionRequest, opts ...grpc.CallOption) (*ResumeSubscriptionResponse, error)
	// merge subscriptions
	MergeSubscriptions(ctx context.Context, in *MergeSubscriptionsRequest, opts ...grpc.CallOption) (*MergeSubscriptionsResponse, error)
	// list membership's buyers
	ListBuyers(ctx context.Context, in *ListBuyersRequest, opts ...grpc.CallOption) (*ListBuyersResponse, error)
	// list multiple membership's buyers
	ListMembershipsBuyers(ctx context.Context, in *ListMembershipsBuyersRequest, opts ...grpc.CallOption) (*ListMembershipsBuyersResponse, error)
	// get membership buyer report
	GetBuyerReport(ctx context.Context, in *GetBuyerReportRequest, opts ...grpc.CallOption) (*GetBuyerReportResponse, error)
	// list payment history
	ListPaymentHistory(ctx context.Context, in *ListPaymentHistoryRequest, opts ...grpc.CallOption) (*ListPaymentHistoryResponse, error)
	// Create OB Request
	CreateOBRequestSetting(ctx context.Context, in *CreateOBRequestSettingRequest, opts ...grpc.CallOption) (*CreateOBRequestSettingResponse, error)
	// Get OB Request
	GetOBRequestSetting(ctx context.Context, in *GetOBRequestSettingRequest, opts ...grpc.CallOption) (*GetOBRequestSettingResponse, error)
}

type subscriptionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSubscriptionServiceClient(cc grpc.ClientConnInterface) SubscriptionServiceClient {
	return &subscriptionServiceClient{cc}
}

func (c *subscriptionServiceClient) CreateSellLink(ctx context.Context, in *CreateSellLinkRequest, opts ...grpc.CallOption) (*CreateSellLinkResponse, error) {
	out := new(CreateSellLinkResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/CreateSellLink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) GetSellLink(ctx context.Context, in *GetSellLinkRequest, opts ...grpc.CallOption) (*GetSellLinkResponse, error) {
	out := new(GetSellLinkResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/GetSellLink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) DeleteSellLink(ctx context.Context, in *DeleteSellLinkRequest, opts ...grpc.CallOption) (*DeleteSellLinkResponse, error) {
	out := new(DeleteSellLinkResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/DeleteSellLink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) CreateSubscription(ctx context.Context, in *CreateSubscriptionRequest, opts ...grpc.CallOption) (*CreateSubscriptionResponse, error) {
	out := new(CreateSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/CreateSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) UpdateSubscription(ctx context.Context, in *UpdateSubscriptionRequest, opts ...grpc.CallOption) (*UpdateSubscriptionResponse, error) {
	out := new(UpdateSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/UpdateSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) GetSubscription(ctx context.Context, in *GetSubscriptionRequest, opts ...grpc.CallOption) (*GetSubscriptionResponse, error) {
	out := new(GetSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/GetSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListSubscriptions(ctx context.Context, in *ListSubscriptionsRequest, opts ...grpc.CallOption) (*ListSubscriptionsResponse, error) {
	out := new(ListSubscriptionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/ListSubscriptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListCustomerSubscriptions(ctx context.Context, in *ListCustomerSubscriptionsRequest, opts ...grpc.CallOption) (*ListCustomerSubscriptionsResponse, error) {
	out := new(ListCustomerSubscriptionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/ListCustomerSubscriptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) CancelSubscription(ctx context.Context, in *CancelSubscriptionRequest, opts ...grpc.CallOption) (*CancelSubscriptionResponse, error) {
	out := new(CancelSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/CancelSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) RenewSubscription(ctx context.Context, in *RenewSubscriptionRequest, opts ...grpc.CallOption) (*RenewSubscriptionResponse, error) {
	out := new(RenewSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/RenewSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) PauseSubscription(ctx context.Context, in *PauseSubscriptionRequest, opts ...grpc.CallOption) (*PauseSubscriptionResponse, error) {
	out := new(PauseSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/PauseSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ResumeSubscription(ctx context.Context, in *ResumeSubscriptionRequest, opts ...grpc.CallOption) (*ResumeSubscriptionResponse, error) {
	out := new(ResumeSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/ResumeSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) MergeSubscriptions(ctx context.Context, in *MergeSubscriptionsRequest, opts ...grpc.CallOption) (*MergeSubscriptionsResponse, error) {
	out := new(MergeSubscriptionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/MergeSubscriptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListBuyers(ctx context.Context, in *ListBuyersRequest, opts ...grpc.CallOption) (*ListBuyersResponse, error) {
	out := new(ListBuyersResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/ListBuyers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListMembershipsBuyers(ctx context.Context, in *ListMembershipsBuyersRequest, opts ...grpc.CallOption) (*ListMembershipsBuyersResponse, error) {
	out := new(ListMembershipsBuyersResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/ListMembershipsBuyers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) GetBuyerReport(ctx context.Context, in *GetBuyerReportRequest, opts ...grpc.CallOption) (*GetBuyerReportResponse, error) {
	out := new(GetBuyerReportResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/GetBuyerReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListPaymentHistory(ctx context.Context, in *ListPaymentHistoryRequest, opts ...grpc.CallOption) (*ListPaymentHistoryResponse, error) {
	out := new(ListPaymentHistoryResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/ListPaymentHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) CreateOBRequestSetting(ctx context.Context, in *CreateOBRequestSettingRequest, opts ...grpc.CallOption) (*CreateOBRequestSettingResponse, error) {
	out := new(CreateOBRequestSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/CreateOBRequestSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) GetOBRequestSetting(ctx context.Context, in *GetOBRequestSettingRequest, opts ...grpc.CallOption) (*GetOBRequestSettingResponse, error) {
	out := new(GetOBRequestSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.SubscriptionService/GetOBRequestSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SubscriptionServiceServer is the server API for SubscriptionService service.
// All implementations must embed UnimplementedSubscriptionServiceServer
// for forward compatibility
type SubscriptionServiceServer interface {
	// create sell link
	CreateSellLink(context.Context, *CreateSellLinkRequest) (*CreateSellLinkResponse, error)
	// get sell link
	GetSellLink(context.Context, *GetSellLinkRequest) (*GetSellLinkResponse, error)
	// delete sell link
	DeleteSellLink(context.Context, *DeleteSellLinkRequest) (*DeleteSellLinkResponse, error)
	// create subscription
	CreateSubscription(context.Context, *CreateSubscriptionRequest) (*CreateSubscriptionResponse, error)
	// update subscription
	UpdateSubscription(context.Context, *UpdateSubscriptionRequest) (*UpdateSubscriptionResponse, error)
	// get subscription
	GetSubscription(context.Context, *GetSubscriptionRequest) (*GetSubscriptionResponse, error)
	// list subscription
	ListSubscriptions(context.Context, *ListSubscriptionsRequest) (*ListSubscriptionsResponse, error)
	// list customer subscription
	ListCustomerSubscriptions(context.Context, *ListCustomerSubscriptionsRequest) (*ListCustomerSubscriptionsResponse, error)
	// update subscription
	CancelSubscription(context.Context, *CancelSubscriptionRequest) (*CancelSubscriptionResponse, error)
	// delete subscription
	RenewSubscription(context.Context, *RenewSubscriptionRequest) (*RenewSubscriptionResponse, error)
	// PauseSubscription
	PauseSubscription(context.Context, *PauseSubscriptionRequest) (*PauseSubscriptionResponse, error)
	// ResumeSubscription
	ResumeSubscription(context.Context, *ResumeSubscriptionRequest) (*ResumeSubscriptionResponse, error)
	// merge subscriptions
	MergeSubscriptions(context.Context, *MergeSubscriptionsRequest) (*MergeSubscriptionsResponse, error)
	// list membership's buyers
	ListBuyers(context.Context, *ListBuyersRequest) (*ListBuyersResponse, error)
	// list multiple membership's buyers
	ListMembershipsBuyers(context.Context, *ListMembershipsBuyersRequest) (*ListMembershipsBuyersResponse, error)
	// get membership buyer report
	GetBuyerReport(context.Context, *GetBuyerReportRequest) (*GetBuyerReportResponse, error)
	// list payment history
	ListPaymentHistory(context.Context, *ListPaymentHistoryRequest) (*ListPaymentHistoryResponse, error)
	// Create OB Request
	CreateOBRequestSetting(context.Context, *CreateOBRequestSettingRequest) (*CreateOBRequestSettingResponse, error)
	// Get OB Request
	GetOBRequestSetting(context.Context, *GetOBRequestSettingRequest) (*GetOBRequestSettingResponse, error)
	mustEmbedUnimplementedSubscriptionServiceServer()
}

// UnimplementedSubscriptionServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSubscriptionServiceServer struct {
}

func (UnimplementedSubscriptionServiceServer) CreateSellLink(context.Context, *CreateSellLinkRequest) (*CreateSellLinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSellLink not implemented")
}
func (UnimplementedSubscriptionServiceServer) GetSellLink(context.Context, *GetSellLinkRequest) (*GetSellLinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSellLink not implemented")
}
func (UnimplementedSubscriptionServiceServer) DeleteSellLink(context.Context, *DeleteSellLinkRequest) (*DeleteSellLinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSellLink not implemented")
}
func (UnimplementedSubscriptionServiceServer) CreateSubscription(context.Context, *CreateSubscriptionRequest) (*CreateSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSubscription not implemented")
}
func (UnimplementedSubscriptionServiceServer) UpdateSubscription(context.Context, *UpdateSubscriptionRequest) (*UpdateSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSubscription not implemented")
}
func (UnimplementedSubscriptionServiceServer) GetSubscription(context.Context, *GetSubscriptionRequest) (*GetSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubscription not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListSubscriptions(context.Context, *ListSubscriptionsRequest) (*ListSubscriptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSubscriptions not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListCustomerSubscriptions(context.Context, *ListCustomerSubscriptionsRequest) (*ListCustomerSubscriptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomerSubscriptions not implemented")
}
func (UnimplementedSubscriptionServiceServer) CancelSubscription(context.Context, *CancelSubscriptionRequest) (*CancelSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelSubscription not implemented")
}
func (UnimplementedSubscriptionServiceServer) RenewSubscription(context.Context, *RenewSubscriptionRequest) (*RenewSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RenewSubscription not implemented")
}
func (UnimplementedSubscriptionServiceServer) PauseSubscription(context.Context, *PauseSubscriptionRequest) (*PauseSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PauseSubscription not implemented")
}
func (UnimplementedSubscriptionServiceServer) ResumeSubscription(context.Context, *ResumeSubscriptionRequest) (*ResumeSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResumeSubscription not implemented")
}
func (UnimplementedSubscriptionServiceServer) MergeSubscriptions(context.Context, *MergeSubscriptionsRequest) (*MergeSubscriptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MergeSubscriptions not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListBuyers(context.Context, *ListBuyersRequest) (*ListBuyersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBuyers not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListMembershipsBuyers(context.Context, *ListMembershipsBuyersRequest) (*ListMembershipsBuyersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMembershipsBuyers not implemented")
}
func (UnimplementedSubscriptionServiceServer) GetBuyerReport(context.Context, *GetBuyerReportRequest) (*GetBuyerReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBuyerReport not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListPaymentHistory(context.Context, *ListPaymentHistoryRequest) (*ListPaymentHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPaymentHistory not implemented")
}
func (UnimplementedSubscriptionServiceServer) CreateOBRequestSetting(context.Context, *CreateOBRequestSettingRequest) (*CreateOBRequestSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOBRequestSetting not implemented")
}
func (UnimplementedSubscriptionServiceServer) GetOBRequestSetting(context.Context, *GetOBRequestSettingRequest) (*GetOBRequestSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOBRequestSetting not implemented")
}
func (UnimplementedSubscriptionServiceServer) mustEmbedUnimplementedSubscriptionServiceServer() {}

// UnsafeSubscriptionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SubscriptionServiceServer will
// result in compilation errors.
type UnsafeSubscriptionServiceServer interface {
	mustEmbedUnimplementedSubscriptionServiceServer()
}

func RegisterSubscriptionServiceServer(s grpc.ServiceRegistrar, srv SubscriptionServiceServer) {
	s.RegisterService(&SubscriptionService_ServiceDesc, srv)
}

func _SubscriptionService_CreateSellLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSellLinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).CreateSellLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/CreateSellLink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).CreateSellLink(ctx, req.(*CreateSellLinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_GetSellLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSellLinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).GetSellLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/GetSellLink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).GetSellLink(ctx, req.(*GetSellLinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_DeleteSellLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSellLinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).DeleteSellLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/DeleteSellLink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).DeleteSellLink(ctx, req.(*DeleteSellLinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_CreateSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).CreateSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/CreateSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).CreateSubscription(ctx, req.(*CreateSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_UpdateSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).UpdateSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/UpdateSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).UpdateSubscription(ctx, req.(*UpdateSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_GetSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).GetSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/GetSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).GetSubscription(ctx, req.(*GetSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListSubscriptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSubscriptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListSubscriptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/ListSubscriptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListSubscriptions(ctx, req.(*ListSubscriptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListCustomerSubscriptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomerSubscriptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListCustomerSubscriptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/ListCustomerSubscriptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListCustomerSubscriptions(ctx, req.(*ListCustomerSubscriptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_CancelSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).CancelSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/CancelSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).CancelSubscription(ctx, req.(*CancelSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_RenewSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RenewSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).RenewSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/RenewSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).RenewSubscription(ctx, req.(*RenewSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_PauseSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PauseSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).PauseSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/PauseSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).PauseSubscription(ctx, req.(*PauseSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ResumeSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResumeSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ResumeSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/ResumeSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ResumeSubscription(ctx, req.(*ResumeSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_MergeSubscriptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MergeSubscriptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).MergeSubscriptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/MergeSubscriptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).MergeSubscriptions(ctx, req.(*MergeSubscriptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListBuyers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBuyersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListBuyers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/ListBuyers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListBuyers(ctx, req.(*ListBuyersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListMembershipsBuyers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMembershipsBuyersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListMembershipsBuyers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/ListMembershipsBuyers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListMembershipsBuyers(ctx, req.(*ListMembershipsBuyersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_GetBuyerReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBuyerReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).GetBuyerReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/GetBuyerReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).GetBuyerReport(ctx, req.(*GetBuyerReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListPaymentHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPaymentHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListPaymentHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/ListPaymentHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListPaymentHistory(ctx, req.(*ListPaymentHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_CreateOBRequestSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOBRequestSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).CreateOBRequestSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/CreateOBRequestSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).CreateOBRequestSetting(ctx, req.(*CreateOBRequestSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_GetOBRequestSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOBRequestSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).GetOBRequestSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.SubscriptionService/GetOBRequestSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).GetOBRequestSetting(ctx, req.(*GetOBRequestSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SubscriptionService_ServiceDesc is the grpc.ServiceDesc for SubscriptionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SubscriptionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.membership.v1.SubscriptionService",
	HandlerType: (*SubscriptionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSellLink",
			Handler:    _SubscriptionService_CreateSellLink_Handler,
		},
		{
			MethodName: "GetSellLink",
			Handler:    _SubscriptionService_GetSellLink_Handler,
		},
		{
			MethodName: "DeleteSellLink",
			Handler:    _SubscriptionService_DeleteSellLink_Handler,
		},
		{
			MethodName: "CreateSubscription",
			Handler:    _SubscriptionService_CreateSubscription_Handler,
		},
		{
			MethodName: "UpdateSubscription",
			Handler:    _SubscriptionService_UpdateSubscription_Handler,
		},
		{
			MethodName: "GetSubscription",
			Handler:    _SubscriptionService_GetSubscription_Handler,
		},
		{
			MethodName: "ListSubscriptions",
			Handler:    _SubscriptionService_ListSubscriptions_Handler,
		},
		{
			MethodName: "ListCustomerSubscriptions",
			Handler:    _SubscriptionService_ListCustomerSubscriptions_Handler,
		},
		{
			MethodName: "CancelSubscription",
			Handler:    _SubscriptionService_CancelSubscription_Handler,
		},
		{
			MethodName: "RenewSubscription",
			Handler:    _SubscriptionService_RenewSubscription_Handler,
		},
		{
			MethodName: "PauseSubscription",
			Handler:    _SubscriptionService_PauseSubscription_Handler,
		},
		{
			MethodName: "ResumeSubscription",
			Handler:    _SubscriptionService_ResumeSubscription_Handler,
		},
		{
			MethodName: "MergeSubscriptions",
			Handler:    _SubscriptionService_MergeSubscriptions_Handler,
		},
		{
			MethodName: "ListBuyers",
			Handler:    _SubscriptionService_ListBuyers_Handler,
		},
		{
			MethodName: "ListMembershipsBuyers",
			Handler:    _SubscriptionService_ListMembershipsBuyers_Handler,
		},
		{
			MethodName: "GetBuyerReport",
			Handler:    _SubscriptionService_GetBuyerReport_Handler,
		},
		{
			MethodName: "ListPaymentHistory",
			Handler:    _SubscriptionService_ListPaymentHistory_Handler,
		},
		{
			MethodName: "CreateOBRequestSetting",
			Handler:    _SubscriptionService_CreateOBRequestSetting_Handler,
		},
		{
			MethodName: "GetOBRequestSetting",
			Handler:    _SubscriptionService_GetOBRequestSetting_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/membership/v1/subscription_service.proto",
}
