// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/staff/v1/staff_api.proto

package staffapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// StaffServiceClient is the client API for StaffService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StaffServiceClient interface {
	// create staff
	CreateStaff(ctx context.Context, in *CreateStaffParams, opts ...grpc.CallOption) (*CreateStaffResult, error)
	// update staff
	UpdateStaff(ctx context.Context, in *UpdateStaffParams, opts ...grpc.CallOption) (*UpdateStaffResult, error)
	// get staff
	GetStaff(ctx context.Context, in *GetStaffParams, opts ...grpc.CallOption) (*GetStaffResult, error)
	// delete staff
	DeleteStaff(ctx context.Context, in *DeleteStaffParams, opts ...grpc.CallOption) (*DeleteStaffResult, error)
	// list staff
	ListStaffs(ctx context.Context, in *ListStaffsParams, opts ...grpc.CallOption) (*ListStaffsResult, error)
	// send invite link
	SendStaffInviteLink(ctx context.Context, in *SendStaffInviteLinkParams, opts ...grpc.CallOption) (*SendStaffInviteLinkResult, error)
	// unlink staff
	UnlinkStaff(ctx context.Context, in *UnlinkStaffParams, opts ...grpc.CallOption) (*UnlinkStaffResult, error)
}

type staffServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStaffServiceClient(cc grpc.ClientConnInterface) StaffServiceClient {
	return &staffServiceClient{cc}
}

func (c *staffServiceClient) CreateStaff(ctx context.Context, in *CreateStaffParams, opts ...grpc.CallOption) (*CreateStaffResult, error) {
	out := new(CreateStaffResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.staff.v1.StaffService/CreateStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateStaff(ctx context.Context, in *UpdateStaffParams, opts ...grpc.CallOption) (*UpdateStaffResult, error) {
	out := new(UpdateStaffResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.staff.v1.StaffService/UpdateStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaff(ctx context.Context, in *GetStaffParams, opts ...grpc.CallOption) (*GetStaffResult, error) {
	out := new(GetStaffResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.staff.v1.StaffService/GetStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) DeleteStaff(ctx context.Context, in *DeleteStaffParams, opts ...grpc.CallOption) (*DeleteStaffResult, error) {
	out := new(DeleteStaffResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.staff.v1.StaffService/DeleteStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) ListStaffs(ctx context.Context, in *ListStaffsParams, opts ...grpc.CallOption) (*ListStaffsResult, error) {
	out := new(ListStaffsResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.staff.v1.StaffService/ListStaffs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) SendStaffInviteLink(ctx context.Context, in *SendStaffInviteLinkParams, opts ...grpc.CallOption) (*SendStaffInviteLinkResult, error) {
	out := new(SendStaffInviteLinkResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.staff.v1.StaffService/SendStaffInviteLink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UnlinkStaff(ctx context.Context, in *UnlinkStaffParams, opts ...grpc.CallOption) (*UnlinkStaffResult, error) {
	out := new(UnlinkStaffResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.staff.v1.StaffService/UnlinkStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StaffServiceServer is the server API for StaffService service.
// All implementations must embed UnimplementedStaffServiceServer
// for forward compatibility
type StaffServiceServer interface {
	// create staff
	CreateStaff(context.Context, *CreateStaffParams) (*CreateStaffResult, error)
	// update staff
	UpdateStaff(context.Context, *UpdateStaffParams) (*UpdateStaffResult, error)
	// get staff
	GetStaff(context.Context, *GetStaffParams) (*GetStaffResult, error)
	// delete staff
	DeleteStaff(context.Context, *DeleteStaffParams) (*DeleteStaffResult, error)
	// list staff
	ListStaffs(context.Context, *ListStaffsParams) (*ListStaffsResult, error)
	// send invite link
	SendStaffInviteLink(context.Context, *SendStaffInviteLinkParams) (*SendStaffInviteLinkResult, error)
	// unlink staff
	UnlinkStaff(context.Context, *UnlinkStaffParams) (*UnlinkStaffResult, error)
	mustEmbedUnimplementedStaffServiceServer()
}

// UnimplementedStaffServiceServer must be embedded to have forward compatible implementations.
type UnimplementedStaffServiceServer struct {
}

func (UnimplementedStaffServiceServer) CreateStaff(context.Context, *CreateStaffParams) (*CreateStaffResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStaff not implemented")
}
func (UnimplementedStaffServiceServer) UpdateStaff(context.Context, *UpdateStaffParams) (*UpdateStaffResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaff not implemented")
}
func (UnimplementedStaffServiceServer) GetStaff(context.Context, *GetStaffParams) (*GetStaffResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaff not implemented")
}
func (UnimplementedStaffServiceServer) DeleteStaff(context.Context, *DeleteStaffParams) (*DeleteStaffResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteStaff not implemented")
}
func (UnimplementedStaffServiceServer) ListStaffs(context.Context, *ListStaffsParams) (*ListStaffsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStaffs not implemented")
}
func (UnimplementedStaffServiceServer) SendStaffInviteLink(context.Context, *SendStaffInviteLinkParams) (*SendStaffInviteLinkResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendStaffInviteLink not implemented")
}
func (UnimplementedStaffServiceServer) UnlinkStaff(context.Context, *UnlinkStaffParams) (*UnlinkStaffResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkStaff not implemented")
}
func (UnimplementedStaffServiceServer) mustEmbedUnimplementedStaffServiceServer() {}

// UnsafeStaffServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StaffServiceServer will
// result in compilation errors.
type UnsafeStaffServiceServer interface {
	mustEmbedUnimplementedStaffServiceServer()
}

func RegisterStaffServiceServer(s grpc.ServiceRegistrar, srv StaffServiceServer) {
	s.RegisterService(&StaffService_ServiceDesc, srv)
}

func _StaffService_CreateStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStaffParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).CreateStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.staff.v1.StaffService/CreateStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).CreateStaff(ctx, req.(*CreateStaffParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStaffParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.staff.v1.StaffService/UpdateStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateStaff(ctx, req.(*UpdateStaffParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.staff.v1.StaffService/GetStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaff(ctx, req.(*GetStaffParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_DeleteStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteStaffParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).DeleteStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.staff.v1.StaffService/DeleteStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).DeleteStaff(ctx, req.(*DeleteStaffParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_ListStaffs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStaffsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).ListStaffs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.staff.v1.StaffService/ListStaffs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).ListStaffs(ctx, req.(*ListStaffsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_SendStaffInviteLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendStaffInviteLinkParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).SendStaffInviteLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.staff.v1.StaffService/SendStaffInviteLink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).SendStaffInviteLink(ctx, req.(*SendStaffInviteLinkParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UnlinkStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnlinkStaffParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UnlinkStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.staff.v1.StaffService/UnlinkStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UnlinkStaff(ctx, req.(*UnlinkStaffParams))
	}
	return interceptor(ctx, in, info, handler)
}

// StaffService_ServiceDesc is the grpc.ServiceDesc for StaffService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StaffService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.staff.v1.StaffService",
	HandlerType: (*StaffServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateStaff",
			Handler:    _StaffService_CreateStaff_Handler,
		},
		{
			MethodName: "UpdateStaff",
			Handler:    _StaffService_UpdateStaff_Handler,
		},
		{
			MethodName: "GetStaff",
			Handler:    _StaffService_GetStaff_Handler,
		},
		{
			MethodName: "DeleteStaff",
			Handler:    _StaffService_DeleteStaff_Handler,
		},
		{
			MethodName: "ListStaffs",
			Handler:    _StaffService_ListStaffs_Handler,
		},
		{
			MethodName: "SendStaffInviteLink",
			Handler:    _StaffService_SendStaffInviteLink_Handler,
		},
		{
			MethodName: "UnlinkStaff",
			Handler:    _StaffService_UnlinkStaff_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/staff/v1/staff_api.proto",
}
