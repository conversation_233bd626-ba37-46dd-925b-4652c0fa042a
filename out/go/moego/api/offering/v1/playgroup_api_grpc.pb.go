// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/offering/v1/playgroup_api.proto

package offeringapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PlaygroupServiceClient is the client API for PlaygroupService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PlaygroupServiceClient interface {
	// list playgroup
	ListPlaygroup(ctx context.Context, in *ListPlaygroupParams, opts ...grpc.CallOption) (*ListPlaygroupResult, error)
	// create playgroup
	CreatePlaygroup(ctx context.Context, in *CreatePlaygroupParams, opts ...grpc.CallOption) (*CreatePlaygroupResult, error)
	// update playgroup
	UpdatePlaygroup(ctx context.Context, in *UpdatePlaygroupParams, opts ...grpc.CallOption) (*UpdatePlaygroupResult, error)
	// delete playgroup
	DeletePlaygroup(ctx context.Context, in *DeletePlaygroupParams, opts ...grpc.CallOption) (*DeletePlaygroupResult, error)
	// sort playgroup
	SortPlaygroup(ctx context.Context, in *SortPlaygroupParams, opts ...grpc.CallOption) (*SortPlaygroupResult, error)
}

type playgroupServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPlaygroupServiceClient(cc grpc.ClientConnInterface) PlaygroupServiceClient {
	return &playgroupServiceClient{cc}
}

func (c *playgroupServiceClient) ListPlaygroup(ctx context.Context, in *ListPlaygroupParams, opts ...grpc.CallOption) (*ListPlaygroupResult, error) {
	out := new(ListPlaygroupResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.PlaygroupService/ListPlaygroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *playgroupServiceClient) CreatePlaygroup(ctx context.Context, in *CreatePlaygroupParams, opts ...grpc.CallOption) (*CreatePlaygroupResult, error) {
	out := new(CreatePlaygroupResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.PlaygroupService/CreatePlaygroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *playgroupServiceClient) UpdatePlaygroup(ctx context.Context, in *UpdatePlaygroupParams, opts ...grpc.CallOption) (*UpdatePlaygroupResult, error) {
	out := new(UpdatePlaygroupResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.PlaygroupService/UpdatePlaygroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *playgroupServiceClient) DeletePlaygroup(ctx context.Context, in *DeletePlaygroupParams, opts ...grpc.CallOption) (*DeletePlaygroupResult, error) {
	out := new(DeletePlaygroupResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.PlaygroupService/DeletePlaygroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *playgroupServiceClient) SortPlaygroup(ctx context.Context, in *SortPlaygroupParams, opts ...grpc.CallOption) (*SortPlaygroupResult, error) {
	out := new(SortPlaygroupResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.PlaygroupService/SortPlaygroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PlaygroupServiceServer is the server API for PlaygroupService service.
// All implementations must embed UnimplementedPlaygroupServiceServer
// for forward compatibility
type PlaygroupServiceServer interface {
	// list playgroup
	ListPlaygroup(context.Context, *ListPlaygroupParams) (*ListPlaygroupResult, error)
	// create playgroup
	CreatePlaygroup(context.Context, *CreatePlaygroupParams) (*CreatePlaygroupResult, error)
	// update playgroup
	UpdatePlaygroup(context.Context, *UpdatePlaygroupParams) (*UpdatePlaygroupResult, error)
	// delete playgroup
	DeletePlaygroup(context.Context, *DeletePlaygroupParams) (*DeletePlaygroupResult, error)
	// sort playgroup
	SortPlaygroup(context.Context, *SortPlaygroupParams) (*SortPlaygroupResult, error)
	mustEmbedUnimplementedPlaygroupServiceServer()
}

// UnimplementedPlaygroupServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPlaygroupServiceServer struct {
}

func (UnimplementedPlaygroupServiceServer) ListPlaygroup(context.Context, *ListPlaygroupParams) (*ListPlaygroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPlaygroup not implemented")
}
func (UnimplementedPlaygroupServiceServer) CreatePlaygroup(context.Context, *CreatePlaygroupParams) (*CreatePlaygroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePlaygroup not implemented")
}
func (UnimplementedPlaygroupServiceServer) UpdatePlaygroup(context.Context, *UpdatePlaygroupParams) (*UpdatePlaygroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePlaygroup not implemented")
}
func (UnimplementedPlaygroupServiceServer) DeletePlaygroup(context.Context, *DeletePlaygroupParams) (*DeletePlaygroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePlaygroup not implemented")
}
func (UnimplementedPlaygroupServiceServer) SortPlaygroup(context.Context, *SortPlaygroupParams) (*SortPlaygroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortPlaygroup not implemented")
}
func (UnimplementedPlaygroupServiceServer) mustEmbedUnimplementedPlaygroupServiceServer() {}

// UnsafePlaygroupServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PlaygroupServiceServer will
// result in compilation errors.
type UnsafePlaygroupServiceServer interface {
	mustEmbedUnimplementedPlaygroupServiceServer()
}

func RegisterPlaygroupServiceServer(s grpc.ServiceRegistrar, srv PlaygroupServiceServer) {
	s.RegisterService(&PlaygroupService_ServiceDesc, srv)
}

func _PlaygroupService_ListPlaygroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPlaygroupParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlaygroupServiceServer).ListPlaygroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.PlaygroupService/ListPlaygroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlaygroupServiceServer).ListPlaygroup(ctx, req.(*ListPlaygroupParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlaygroupService_CreatePlaygroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePlaygroupParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlaygroupServiceServer).CreatePlaygroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.PlaygroupService/CreatePlaygroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlaygroupServiceServer).CreatePlaygroup(ctx, req.(*CreatePlaygroupParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlaygroupService_UpdatePlaygroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePlaygroupParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlaygroupServiceServer).UpdatePlaygroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.PlaygroupService/UpdatePlaygroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlaygroupServiceServer).UpdatePlaygroup(ctx, req.(*UpdatePlaygroupParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlaygroupService_DeletePlaygroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePlaygroupParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlaygroupServiceServer).DeletePlaygroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.PlaygroupService/DeletePlaygroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlaygroupServiceServer).DeletePlaygroup(ctx, req.(*DeletePlaygroupParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlaygroupService_SortPlaygroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPlaygroupParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlaygroupServiceServer).SortPlaygroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.PlaygroupService/SortPlaygroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlaygroupServiceServer).SortPlaygroup(ctx, req.(*SortPlaygroupParams))
	}
	return interceptor(ctx, in, info, handler)
}

// PlaygroupService_ServiceDesc is the grpc.ServiceDesc for PlaygroupService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PlaygroupService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.offering.v1.PlaygroupService",
	HandlerType: (*PlaygroupServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListPlaygroup",
			Handler:    _PlaygroupService_ListPlaygroup_Handler,
		},
		{
			MethodName: "CreatePlaygroup",
			Handler:    _PlaygroupService_CreatePlaygroup_Handler,
		},
		{
			MethodName: "UpdatePlaygroup",
			Handler:    _PlaygroupService_UpdatePlaygroup_Handler,
		},
		{
			MethodName: "DeletePlaygroup",
			Handler:    _PlaygroupService_DeletePlaygroup_Handler,
		},
		{
			MethodName: "SortPlaygroup",
			Handler:    _PlaygroupService_SortPlaygroup_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/offering/v1/playgroup_api.proto",
}
