// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/offering/v1/service_staff_api.proto

package offeringapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ServiceStaffServiceClient is the client API for ServiceStaffService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServiceStaffServiceClient interface {
	// list service_staff
	ListServiceStaffs(ctx context.Context, in *ListServiceStaffsParams, opts ...grpc.CallOption) (*ListServiceStaffsResult, error)
	// list evaluation_staff
	ListEvaluationStaffs(ctx context.Context, in *ListEvaluationStaffsParams, opts ...grpc.CallOption) (*ListEvaluationStaffsResult, error)
}

type serviceStaffServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceStaffServiceClient(cc grpc.ClientConnInterface) ServiceStaffServiceClient {
	return &serviceStaffServiceClient{cc}
}

func (c *serviceStaffServiceClient) ListServiceStaffs(ctx context.Context, in *ListServiceStaffsParams, opts ...grpc.CallOption) (*ListServiceStaffsResult, error) {
	out := new(ListServiceStaffsResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.ServiceStaffService/ListServiceStaffs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceStaffServiceClient) ListEvaluationStaffs(ctx context.Context, in *ListEvaluationStaffsParams, opts ...grpc.CallOption) (*ListEvaluationStaffsResult, error) {
	out := new(ListEvaluationStaffsResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.ServiceStaffService/ListEvaluationStaffs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceStaffServiceServer is the server API for ServiceStaffService service.
// All implementations must embed UnimplementedServiceStaffServiceServer
// for forward compatibility
type ServiceStaffServiceServer interface {
	// list service_staff
	ListServiceStaffs(context.Context, *ListServiceStaffsParams) (*ListServiceStaffsResult, error)
	// list evaluation_staff
	ListEvaluationStaffs(context.Context, *ListEvaluationStaffsParams) (*ListEvaluationStaffsResult, error)
	mustEmbedUnimplementedServiceStaffServiceServer()
}

// UnimplementedServiceStaffServiceServer must be embedded to have forward compatible implementations.
type UnimplementedServiceStaffServiceServer struct {
}

func (UnimplementedServiceStaffServiceServer) ListServiceStaffs(context.Context, *ListServiceStaffsParams) (*ListServiceStaffsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceStaffs not implemented")
}
func (UnimplementedServiceStaffServiceServer) ListEvaluationStaffs(context.Context, *ListEvaluationStaffsParams) (*ListEvaluationStaffsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEvaluationStaffs not implemented")
}
func (UnimplementedServiceStaffServiceServer) mustEmbedUnimplementedServiceStaffServiceServer() {}

// UnsafeServiceStaffServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceStaffServiceServer will
// result in compilation errors.
type UnsafeServiceStaffServiceServer interface {
	mustEmbedUnimplementedServiceStaffServiceServer()
}

func RegisterServiceStaffServiceServer(s grpc.ServiceRegistrar, srv ServiceStaffServiceServer) {
	s.RegisterService(&ServiceStaffService_ServiceDesc, srv)
}

func _ServiceStaffService_ListServiceStaffs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceStaffsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceStaffServiceServer).ListServiceStaffs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.ServiceStaffService/ListServiceStaffs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceStaffServiceServer).ListServiceStaffs(ctx, req.(*ListServiceStaffsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceStaffService_ListEvaluationStaffs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEvaluationStaffsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceStaffServiceServer).ListEvaluationStaffs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.ServiceStaffService/ListEvaluationStaffs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceStaffServiceServer).ListEvaluationStaffs(ctx, req.(*ListEvaluationStaffsParams))
	}
	return interceptor(ctx, in, info, handler)
}

// ServiceStaffService_ServiceDesc is the grpc.ServiceDesc for ServiceStaffService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServiceStaffService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.offering.v1.ServiceStaffService",
	HandlerType: (*ServiceStaffServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListServiceStaffs",
			Handler:    _ServiceStaffService_ListServiceStaffs_Handler,
		},
		{
			MethodName: "ListEvaluationStaffs",
			Handler:    _ServiceStaffService_ListEvaluationStaffs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/offering/v1/service_staff_api.proto",
}
