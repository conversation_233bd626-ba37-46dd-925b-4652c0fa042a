// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/organization/v1/staff_api.proto

package organizationapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// StaffServiceClient is the client API for StaffService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StaffServiceClient interface {
	// create a new staff
	CreateStaff(ctx context.Context, in *CreateStaffParams, opts ...grpc.CallOption) (*CreateStaffResult, error)
	// get staff detail
	GetStaffFullDetail(ctx context.Context, in *GetStaffFullDetailParams, opts ...grpc.CallOption) (*GetStaffFullDetailResult, error)
	// update staff
	UpdateStaff(ctx context.Context, in *UpdateStaffParams, opts ...grpc.CallOption) (*UpdateStaffResult, error)
	// delete staff
	DeleteStaff(ctx context.Context, in *DeleteStaffParams, opts ...grpc.CallOption) (*DeleteStaffResult, error)
	// query staff list by pagination
	QueryStaffListByPagination(ctx context.Context, in *QueryStaffListByPaginationParams, opts ...grpc.CallOption) (*QueryStaffListByPaginationResult, error)
	// get all working location staffs
	GetAllWorkingLocationStaffs(ctx context.Context, in *GetAllWorkingLocationStaffsParams, opts ...grpc.CallOption) (*GetAllWorkingLocationStaffsResult, error)
	// get staffs by working locations
	GetStaffsByWorkingLocationIds(ctx context.Context, in *GetStaffsByWorkingLocationIdsParams, opts ...grpc.CallOption) (*GetStaffsByWorkingLocationIdsResult, error)
	// get clock in out staffs of current staff
	GetClockInOutStaffs(ctx context.Context, in *GetClockInOutStaffsParams, opts ...grpc.CallOption) (*GetClockInOutStaffsResult, error)
	// get enterprise staffs by working locations
	GetEnterpriseStaffsByWorkingLocationIds(ctx context.Context, in *GetEnterpriseStaffsByWorkingLocationIdsParams, opts ...grpc.CallOption) (*GetEnterpriseStaffsByWorkingLocationIdsResult, error)
	// get staff login time
	GetStaffLoginTime(ctx context.Context, in *GetStaffLoginTimeParams, opts ...grpc.CallOption) (*GetStaffLoginTimeResult, error)
	// update staff login time
	UpdateStaffLoginTime(ctx context.Context, in *UpdateStaffLoginTimeParams, opts ...grpc.CallOption) (*UpdateStaffLoginTimeResult, error)
	// get recommended staff login time
	GetRecommendedStaffLoginTime(ctx context.Context, in *GetRecommendedStaffLoginTimeParams, opts ...grpc.CallOption) (*GetRecommendedStaffLoginTimeResult, error)
	// List staff group by role
	ListStaffGroupByRole(ctx context.Context, in *ListStaffGroupByRoleParams, opts ...grpc.CallOption) (*ListStaffGroupByRoleResult, error)
}

type staffServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStaffServiceClient(cc grpc.ClientConnInterface) StaffServiceClient {
	return &staffServiceClient{cc}
}

func (c *staffServiceClient) CreateStaff(ctx context.Context, in *CreateStaffParams, opts ...grpc.CallOption) (*CreateStaffResult, error) {
	out := new(CreateStaffResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/CreateStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffFullDetail(ctx context.Context, in *GetStaffFullDetailParams, opts ...grpc.CallOption) (*GetStaffFullDetailResult, error) {
	out := new(GetStaffFullDetailResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetStaffFullDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateStaff(ctx context.Context, in *UpdateStaffParams, opts ...grpc.CallOption) (*UpdateStaffResult, error) {
	out := new(UpdateStaffResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/UpdateStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) DeleteStaff(ctx context.Context, in *DeleteStaffParams, opts ...grpc.CallOption) (*DeleteStaffResult, error) {
	out := new(DeleteStaffResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/DeleteStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) QueryStaffListByPagination(ctx context.Context, in *QueryStaffListByPaginationParams, opts ...grpc.CallOption) (*QueryStaffListByPaginationResult, error) {
	out := new(QueryStaffListByPaginationResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/QueryStaffListByPagination", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetAllWorkingLocationStaffs(ctx context.Context, in *GetAllWorkingLocationStaffsParams, opts ...grpc.CallOption) (*GetAllWorkingLocationStaffsResult, error) {
	out := new(GetAllWorkingLocationStaffsResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetAllWorkingLocationStaffs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffsByWorkingLocationIds(ctx context.Context, in *GetStaffsByWorkingLocationIdsParams, opts ...grpc.CallOption) (*GetStaffsByWorkingLocationIdsResult, error) {
	out := new(GetStaffsByWorkingLocationIdsResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetStaffsByWorkingLocationIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetClockInOutStaffs(ctx context.Context, in *GetClockInOutStaffsParams, opts ...grpc.CallOption) (*GetClockInOutStaffsResult, error) {
	out := new(GetClockInOutStaffsResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetClockInOutStaffs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetEnterpriseStaffsByWorkingLocationIds(ctx context.Context, in *GetEnterpriseStaffsByWorkingLocationIdsParams, opts ...grpc.CallOption) (*GetEnterpriseStaffsByWorkingLocationIdsResult, error) {
	out := new(GetEnterpriseStaffsByWorkingLocationIdsResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetEnterpriseStaffsByWorkingLocationIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffLoginTime(ctx context.Context, in *GetStaffLoginTimeParams, opts ...grpc.CallOption) (*GetStaffLoginTimeResult, error) {
	out := new(GetStaffLoginTimeResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetStaffLoginTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateStaffLoginTime(ctx context.Context, in *UpdateStaffLoginTimeParams, opts ...grpc.CallOption) (*UpdateStaffLoginTimeResult, error) {
	out := new(UpdateStaffLoginTimeResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/UpdateStaffLoginTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetRecommendedStaffLoginTime(ctx context.Context, in *GetRecommendedStaffLoginTimeParams, opts ...grpc.CallOption) (*GetRecommendedStaffLoginTimeResult, error) {
	out := new(GetRecommendedStaffLoginTimeResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetRecommendedStaffLoginTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) ListStaffGroupByRole(ctx context.Context, in *ListStaffGroupByRoleParams, opts ...grpc.CallOption) (*ListStaffGroupByRoleResult, error) {
	out := new(ListStaffGroupByRoleResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/ListStaffGroupByRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StaffServiceServer is the server API for StaffService service.
// All implementations must embed UnimplementedStaffServiceServer
// for forward compatibility
type StaffServiceServer interface {
	// create a new staff
	CreateStaff(context.Context, *CreateStaffParams) (*CreateStaffResult, error)
	// get staff detail
	GetStaffFullDetail(context.Context, *GetStaffFullDetailParams) (*GetStaffFullDetailResult, error)
	// update staff
	UpdateStaff(context.Context, *UpdateStaffParams) (*UpdateStaffResult, error)
	// delete staff
	DeleteStaff(context.Context, *DeleteStaffParams) (*DeleteStaffResult, error)
	// query staff list by pagination
	QueryStaffListByPagination(context.Context, *QueryStaffListByPaginationParams) (*QueryStaffListByPaginationResult, error)
	// get all working location staffs
	GetAllWorkingLocationStaffs(context.Context, *GetAllWorkingLocationStaffsParams) (*GetAllWorkingLocationStaffsResult, error)
	// get staffs by working locations
	GetStaffsByWorkingLocationIds(context.Context, *GetStaffsByWorkingLocationIdsParams) (*GetStaffsByWorkingLocationIdsResult, error)
	// get clock in out staffs of current staff
	GetClockInOutStaffs(context.Context, *GetClockInOutStaffsParams) (*GetClockInOutStaffsResult, error)
	// get enterprise staffs by working locations
	GetEnterpriseStaffsByWorkingLocationIds(context.Context, *GetEnterpriseStaffsByWorkingLocationIdsParams) (*GetEnterpriseStaffsByWorkingLocationIdsResult, error)
	// get staff login time
	GetStaffLoginTime(context.Context, *GetStaffLoginTimeParams) (*GetStaffLoginTimeResult, error)
	// update staff login time
	UpdateStaffLoginTime(context.Context, *UpdateStaffLoginTimeParams) (*UpdateStaffLoginTimeResult, error)
	// get recommended staff login time
	GetRecommendedStaffLoginTime(context.Context, *GetRecommendedStaffLoginTimeParams) (*GetRecommendedStaffLoginTimeResult, error)
	// List staff group by role
	ListStaffGroupByRole(context.Context, *ListStaffGroupByRoleParams) (*ListStaffGroupByRoleResult, error)
	mustEmbedUnimplementedStaffServiceServer()
}

// UnimplementedStaffServiceServer must be embedded to have forward compatible implementations.
type UnimplementedStaffServiceServer struct {
}

func (UnimplementedStaffServiceServer) CreateStaff(context.Context, *CreateStaffParams) (*CreateStaffResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStaff not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffFullDetail(context.Context, *GetStaffFullDetailParams) (*GetStaffFullDetailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffFullDetail not implemented")
}
func (UnimplementedStaffServiceServer) UpdateStaff(context.Context, *UpdateStaffParams) (*UpdateStaffResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaff not implemented")
}
func (UnimplementedStaffServiceServer) DeleteStaff(context.Context, *DeleteStaffParams) (*DeleteStaffResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteStaff not implemented")
}
func (UnimplementedStaffServiceServer) QueryStaffListByPagination(context.Context, *QueryStaffListByPaginationParams) (*QueryStaffListByPaginationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryStaffListByPagination not implemented")
}
func (UnimplementedStaffServiceServer) GetAllWorkingLocationStaffs(context.Context, *GetAllWorkingLocationStaffsParams) (*GetAllWorkingLocationStaffsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllWorkingLocationStaffs not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffsByWorkingLocationIds(context.Context, *GetStaffsByWorkingLocationIdsParams) (*GetStaffsByWorkingLocationIdsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffsByWorkingLocationIds not implemented")
}
func (UnimplementedStaffServiceServer) GetClockInOutStaffs(context.Context, *GetClockInOutStaffsParams) (*GetClockInOutStaffsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClockInOutStaffs not implemented")
}
func (UnimplementedStaffServiceServer) GetEnterpriseStaffsByWorkingLocationIds(context.Context, *GetEnterpriseStaffsByWorkingLocationIdsParams) (*GetEnterpriseStaffsByWorkingLocationIdsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnterpriseStaffsByWorkingLocationIds not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffLoginTime(context.Context, *GetStaffLoginTimeParams) (*GetStaffLoginTimeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffLoginTime not implemented")
}
func (UnimplementedStaffServiceServer) UpdateStaffLoginTime(context.Context, *UpdateStaffLoginTimeParams) (*UpdateStaffLoginTimeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaffLoginTime not implemented")
}
func (UnimplementedStaffServiceServer) GetRecommendedStaffLoginTime(context.Context, *GetRecommendedStaffLoginTimeParams) (*GetRecommendedStaffLoginTimeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecommendedStaffLoginTime not implemented")
}
func (UnimplementedStaffServiceServer) ListStaffGroupByRole(context.Context, *ListStaffGroupByRoleParams) (*ListStaffGroupByRoleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStaffGroupByRole not implemented")
}
func (UnimplementedStaffServiceServer) mustEmbedUnimplementedStaffServiceServer() {}

// UnsafeStaffServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StaffServiceServer will
// result in compilation errors.
type UnsafeStaffServiceServer interface {
	mustEmbedUnimplementedStaffServiceServer()
}

func RegisterStaffServiceServer(s grpc.ServiceRegistrar, srv StaffServiceServer) {
	s.RegisterService(&StaffService_ServiceDesc, srv)
}

func _StaffService_CreateStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStaffParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).CreateStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/CreateStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).CreateStaff(ctx, req.(*CreateStaffParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffFullDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffFullDetailParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffFullDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetStaffFullDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffFullDetail(ctx, req.(*GetStaffFullDetailParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStaffParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/UpdateStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateStaff(ctx, req.(*UpdateStaffParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_DeleteStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteStaffParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).DeleteStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/DeleteStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).DeleteStaff(ctx, req.(*DeleteStaffParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_QueryStaffListByPagination_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryStaffListByPaginationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).QueryStaffListByPagination(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/QueryStaffListByPagination",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).QueryStaffListByPagination(ctx, req.(*QueryStaffListByPaginationParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetAllWorkingLocationStaffs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllWorkingLocationStaffsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetAllWorkingLocationStaffs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetAllWorkingLocationStaffs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetAllWorkingLocationStaffs(ctx, req.(*GetAllWorkingLocationStaffsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffsByWorkingLocationIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffsByWorkingLocationIdsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffsByWorkingLocationIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetStaffsByWorkingLocationIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffsByWorkingLocationIds(ctx, req.(*GetStaffsByWorkingLocationIdsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetClockInOutStaffs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClockInOutStaffsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetClockInOutStaffs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetClockInOutStaffs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetClockInOutStaffs(ctx, req.(*GetClockInOutStaffsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetEnterpriseStaffsByWorkingLocationIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnterpriseStaffsByWorkingLocationIdsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetEnterpriseStaffsByWorkingLocationIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetEnterpriseStaffsByWorkingLocationIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetEnterpriseStaffsByWorkingLocationIds(ctx, req.(*GetEnterpriseStaffsByWorkingLocationIdsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffLoginTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffLoginTimeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffLoginTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetStaffLoginTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffLoginTime(ctx, req.(*GetStaffLoginTimeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateStaffLoginTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStaffLoginTimeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateStaffLoginTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/UpdateStaffLoginTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateStaffLoginTime(ctx, req.(*UpdateStaffLoginTimeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetRecommendedStaffLoginTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendedStaffLoginTimeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetRecommendedStaffLoginTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetRecommendedStaffLoginTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetRecommendedStaffLoginTime(ctx, req.(*GetRecommendedStaffLoginTimeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_ListStaffGroupByRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStaffGroupByRoleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).ListStaffGroupByRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/ListStaffGroupByRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).ListStaffGroupByRole(ctx, req.(*ListStaffGroupByRoleParams))
	}
	return interceptor(ctx, in, info, handler)
}

// StaffService_ServiceDesc is the grpc.ServiceDesc for StaffService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StaffService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.organization.v1.StaffService",
	HandlerType: (*StaffServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateStaff",
			Handler:    _StaffService_CreateStaff_Handler,
		},
		{
			MethodName: "GetStaffFullDetail",
			Handler:    _StaffService_GetStaffFullDetail_Handler,
		},
		{
			MethodName: "UpdateStaff",
			Handler:    _StaffService_UpdateStaff_Handler,
		},
		{
			MethodName: "DeleteStaff",
			Handler:    _StaffService_DeleteStaff_Handler,
		},
		{
			MethodName: "QueryStaffListByPagination",
			Handler:    _StaffService_QueryStaffListByPagination_Handler,
		},
		{
			MethodName: "GetAllWorkingLocationStaffs",
			Handler:    _StaffService_GetAllWorkingLocationStaffs_Handler,
		},
		{
			MethodName: "GetStaffsByWorkingLocationIds",
			Handler:    _StaffService_GetStaffsByWorkingLocationIds_Handler,
		},
		{
			MethodName: "GetClockInOutStaffs",
			Handler:    _StaffService_GetClockInOutStaffs_Handler,
		},
		{
			MethodName: "GetEnterpriseStaffsByWorkingLocationIds",
			Handler:    _StaffService_GetEnterpriseStaffsByWorkingLocationIds_Handler,
		},
		{
			MethodName: "GetStaffLoginTime",
			Handler:    _StaffService_GetStaffLoginTime_Handler,
		},
		{
			MethodName: "UpdateStaffLoginTime",
			Handler:    _StaffService_UpdateStaffLoginTime_Handler,
		},
		{
			MethodName: "GetRecommendedStaffLoginTime",
			Handler:    _StaffService_GetRecommendedStaffLoginTime_Handler,
		},
		{
			MethodName: "ListStaffGroupByRole",
			Handler:    _StaffService_ListStaffGroupByRole_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/organization/v1/staff_api.proto",
}
