// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/organization/v1/company_api.proto

package organizationapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CompanyServiceClient is the client API for CompanyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CompanyServiceClient interface {
	// create company
	CreateCompany(ctx context.Context, in *CreateCompanyParams, opts ...grpc.CallOption) (*CreateCompanyResult, error)
	// query company staff by account
	QueryCompanyStaffByAccount(ctx context.Context, in *QueryCompanyStaffByAccountParams, opts ...grpc.CallOption) (*QueryCompanyStaffByAccountResult, error)
	// update company preference setting
	UpdateCompanyPreferenceSetting(ctx context.Context, in *UpdateCompanyPreferenceSettingParams, opts ...grpc.CallOption) (*UpdateCompanyPreferenceSettingResult, error)
	// get company preference setting
	GetCompanyPreferenceSetting(ctx context.Context, in *GetCompanyPreferenceSettingParams, opts ...grpc.CallOption) (*GetCompanyPreferenceSettingResult, error)
	// switch company
	SwitchCompany(ctx context.Context, in *SwitchCompanyParams, opts ...grpc.CallOption) (*SwitchCompanyResult, error)
	// get companies extra info
	GetCompaniesExtraInfo(ctx context.Context, in *GetCompaniesExtraInfoParams, opts ...grpc.CallOption) (*GetCompaniesExtraInfoResponse, error)
	// query company staff by account with locations, only working locations
	QueryCompanyStaffByAccountWithLocations(ctx context.Context, in *QueryCompanyStaffByAccountWithLocationsParams, opts ...grpc.CallOption) (*QueryCompanyStaffByAccountWithLocationsResult, error)
	// add tax rule
	AddTaxRule(ctx context.Context, in *AddTaxRuleParams, opts ...grpc.CallOption) (*AddTaxRuleResult, error)
	// update tax rule
	UpdateTaxRule(ctx context.Context, in *UpdateTaxRuleParams, opts ...grpc.CallOption) (*UpdateTaxRuleResult, error)
	// delete tax rule
	DeleteTaxRule(ctx context.Context, in *DeleteTaxRuleParams, opts ...grpc.CallOption) (*DeleteTaxRuleResult, error)
	// get tax rule list
	GetTaxRuleList(ctx context.Context, in *GetTaxRuleListParams, opts ...grpc.CallOption) (*GetTaxRuleListResult, error)
	// get clock in/out setting
	GetClockInOutSetting(ctx context.Context, in *GetClockInOutSettingParams, opts ...grpc.CallOption) (*GetClockInOutSettingResult, error)
	// update clock in/out setting
	UpdateClockInOutSetting(ctx context.Context, in *UpdateClockInOutSettingParams, opts ...grpc.CallOption) (*UpdateClockInOutSettingResult, error)
	// query company question status
	GetCompanyQuestionRecord(ctx context.Context, in *CompanyQuestionRecordQueryParams, opts ...grpc.CallOption) (*CompanyQuestionRecordQueryResult, error)
	// save company question
	CompanyQuestionRecordSave(ctx context.Context, in *CompanyQuestionRecordParams, opts ...grpc.CallOption) (*CompanyQuestionRecordResult, error)
	// sort company
	SortCompany(ctx context.Context, in *SortCompanyParams, opts ...grpc.CallOption) (*SortCompanyResult, error)
}

type companyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCompanyServiceClient(cc grpc.ClientConnInterface) CompanyServiceClient {
	return &companyServiceClient{cc}
}

func (c *companyServiceClient) CreateCompany(ctx context.Context, in *CreateCompanyParams, opts ...grpc.CallOption) (*CreateCompanyResult, error) {
	out := new(CreateCompanyResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/CreateCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) QueryCompanyStaffByAccount(ctx context.Context, in *QueryCompanyStaffByAccountParams, opts ...grpc.CallOption) (*QueryCompanyStaffByAccountResult, error) {
	out := new(QueryCompanyStaffByAccountResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/QueryCompanyStaffByAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) UpdateCompanyPreferenceSetting(ctx context.Context, in *UpdateCompanyPreferenceSettingParams, opts ...grpc.CallOption) (*UpdateCompanyPreferenceSettingResult, error) {
	out := new(UpdateCompanyPreferenceSettingResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/UpdateCompanyPreferenceSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) GetCompanyPreferenceSetting(ctx context.Context, in *GetCompanyPreferenceSettingParams, opts ...grpc.CallOption) (*GetCompanyPreferenceSettingResult, error) {
	out := new(GetCompanyPreferenceSettingResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/GetCompanyPreferenceSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) SwitchCompany(ctx context.Context, in *SwitchCompanyParams, opts ...grpc.CallOption) (*SwitchCompanyResult, error) {
	out := new(SwitchCompanyResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/SwitchCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) GetCompaniesExtraInfo(ctx context.Context, in *GetCompaniesExtraInfoParams, opts ...grpc.CallOption) (*GetCompaniesExtraInfoResponse, error) {
	out := new(GetCompaniesExtraInfoResponse)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/GetCompaniesExtraInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) QueryCompanyStaffByAccountWithLocations(ctx context.Context, in *QueryCompanyStaffByAccountWithLocationsParams, opts ...grpc.CallOption) (*QueryCompanyStaffByAccountWithLocationsResult, error) {
	out := new(QueryCompanyStaffByAccountWithLocationsResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/QueryCompanyStaffByAccountWithLocations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) AddTaxRule(ctx context.Context, in *AddTaxRuleParams, opts ...grpc.CallOption) (*AddTaxRuleResult, error) {
	out := new(AddTaxRuleResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/AddTaxRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) UpdateTaxRule(ctx context.Context, in *UpdateTaxRuleParams, opts ...grpc.CallOption) (*UpdateTaxRuleResult, error) {
	out := new(UpdateTaxRuleResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/UpdateTaxRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) DeleteTaxRule(ctx context.Context, in *DeleteTaxRuleParams, opts ...grpc.CallOption) (*DeleteTaxRuleResult, error) {
	out := new(DeleteTaxRuleResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/DeleteTaxRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) GetTaxRuleList(ctx context.Context, in *GetTaxRuleListParams, opts ...grpc.CallOption) (*GetTaxRuleListResult, error) {
	out := new(GetTaxRuleListResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/GetTaxRuleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) GetClockInOutSetting(ctx context.Context, in *GetClockInOutSettingParams, opts ...grpc.CallOption) (*GetClockInOutSettingResult, error) {
	out := new(GetClockInOutSettingResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/GetClockInOutSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) UpdateClockInOutSetting(ctx context.Context, in *UpdateClockInOutSettingParams, opts ...grpc.CallOption) (*UpdateClockInOutSettingResult, error) {
	out := new(UpdateClockInOutSettingResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/UpdateClockInOutSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) GetCompanyQuestionRecord(ctx context.Context, in *CompanyQuestionRecordQueryParams, opts ...grpc.CallOption) (*CompanyQuestionRecordQueryResult, error) {
	out := new(CompanyQuestionRecordQueryResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/GetCompanyQuestionRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) CompanyQuestionRecordSave(ctx context.Context, in *CompanyQuestionRecordParams, opts ...grpc.CallOption) (*CompanyQuestionRecordResult, error) {
	out := new(CompanyQuestionRecordResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/CompanyQuestionRecordSave", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) SortCompany(ctx context.Context, in *SortCompanyParams, opts ...grpc.CallOption) (*SortCompanyResult, error) {
	out := new(SortCompanyResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CompanyService/SortCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CompanyServiceServer is the server API for CompanyService service.
// All implementations must embed UnimplementedCompanyServiceServer
// for forward compatibility
type CompanyServiceServer interface {
	// create company
	CreateCompany(context.Context, *CreateCompanyParams) (*CreateCompanyResult, error)
	// query company staff by account
	QueryCompanyStaffByAccount(context.Context, *QueryCompanyStaffByAccountParams) (*QueryCompanyStaffByAccountResult, error)
	// update company preference setting
	UpdateCompanyPreferenceSetting(context.Context, *UpdateCompanyPreferenceSettingParams) (*UpdateCompanyPreferenceSettingResult, error)
	// get company preference setting
	GetCompanyPreferenceSetting(context.Context, *GetCompanyPreferenceSettingParams) (*GetCompanyPreferenceSettingResult, error)
	// switch company
	SwitchCompany(context.Context, *SwitchCompanyParams) (*SwitchCompanyResult, error)
	// get companies extra info
	GetCompaniesExtraInfo(context.Context, *GetCompaniesExtraInfoParams) (*GetCompaniesExtraInfoResponse, error)
	// query company staff by account with locations, only working locations
	QueryCompanyStaffByAccountWithLocations(context.Context, *QueryCompanyStaffByAccountWithLocationsParams) (*QueryCompanyStaffByAccountWithLocationsResult, error)
	// add tax rule
	AddTaxRule(context.Context, *AddTaxRuleParams) (*AddTaxRuleResult, error)
	// update tax rule
	UpdateTaxRule(context.Context, *UpdateTaxRuleParams) (*UpdateTaxRuleResult, error)
	// delete tax rule
	DeleteTaxRule(context.Context, *DeleteTaxRuleParams) (*DeleteTaxRuleResult, error)
	// get tax rule list
	GetTaxRuleList(context.Context, *GetTaxRuleListParams) (*GetTaxRuleListResult, error)
	// get clock in/out setting
	GetClockInOutSetting(context.Context, *GetClockInOutSettingParams) (*GetClockInOutSettingResult, error)
	// update clock in/out setting
	UpdateClockInOutSetting(context.Context, *UpdateClockInOutSettingParams) (*UpdateClockInOutSettingResult, error)
	// query company question status
	GetCompanyQuestionRecord(context.Context, *CompanyQuestionRecordQueryParams) (*CompanyQuestionRecordQueryResult, error)
	// save company question
	CompanyQuestionRecordSave(context.Context, *CompanyQuestionRecordParams) (*CompanyQuestionRecordResult, error)
	// sort company
	SortCompany(context.Context, *SortCompanyParams) (*SortCompanyResult, error)
	mustEmbedUnimplementedCompanyServiceServer()
}

// UnimplementedCompanyServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCompanyServiceServer struct {
}

func (UnimplementedCompanyServiceServer) CreateCompany(context.Context, *CreateCompanyParams) (*CreateCompanyResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCompany not implemented")
}
func (UnimplementedCompanyServiceServer) QueryCompanyStaffByAccount(context.Context, *QueryCompanyStaffByAccountParams) (*QueryCompanyStaffByAccountResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryCompanyStaffByAccount not implemented")
}
func (UnimplementedCompanyServiceServer) UpdateCompanyPreferenceSetting(context.Context, *UpdateCompanyPreferenceSettingParams) (*UpdateCompanyPreferenceSettingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCompanyPreferenceSetting not implemented")
}
func (UnimplementedCompanyServiceServer) GetCompanyPreferenceSetting(context.Context, *GetCompanyPreferenceSettingParams) (*GetCompanyPreferenceSettingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanyPreferenceSetting not implemented")
}
func (UnimplementedCompanyServiceServer) SwitchCompany(context.Context, *SwitchCompanyParams) (*SwitchCompanyResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SwitchCompany not implemented")
}
func (UnimplementedCompanyServiceServer) GetCompaniesExtraInfo(context.Context, *GetCompaniesExtraInfoParams) (*GetCompaniesExtraInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompaniesExtraInfo not implemented")
}
func (UnimplementedCompanyServiceServer) QueryCompanyStaffByAccountWithLocations(context.Context, *QueryCompanyStaffByAccountWithLocationsParams) (*QueryCompanyStaffByAccountWithLocationsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryCompanyStaffByAccountWithLocations not implemented")
}
func (UnimplementedCompanyServiceServer) AddTaxRule(context.Context, *AddTaxRuleParams) (*AddTaxRuleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTaxRule not implemented")
}
func (UnimplementedCompanyServiceServer) UpdateTaxRule(context.Context, *UpdateTaxRuleParams) (*UpdateTaxRuleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTaxRule not implemented")
}
func (UnimplementedCompanyServiceServer) DeleteTaxRule(context.Context, *DeleteTaxRuleParams) (*DeleteTaxRuleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTaxRule not implemented")
}
func (UnimplementedCompanyServiceServer) GetTaxRuleList(context.Context, *GetTaxRuleListParams) (*GetTaxRuleListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaxRuleList not implemented")
}
func (UnimplementedCompanyServiceServer) GetClockInOutSetting(context.Context, *GetClockInOutSettingParams) (*GetClockInOutSettingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClockInOutSetting not implemented")
}
func (UnimplementedCompanyServiceServer) UpdateClockInOutSetting(context.Context, *UpdateClockInOutSettingParams) (*UpdateClockInOutSettingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateClockInOutSetting not implemented")
}
func (UnimplementedCompanyServiceServer) GetCompanyQuestionRecord(context.Context, *CompanyQuestionRecordQueryParams) (*CompanyQuestionRecordQueryResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanyQuestionRecord not implemented")
}
func (UnimplementedCompanyServiceServer) CompanyQuestionRecordSave(context.Context, *CompanyQuestionRecordParams) (*CompanyQuestionRecordResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompanyQuestionRecordSave not implemented")
}
func (UnimplementedCompanyServiceServer) SortCompany(context.Context, *SortCompanyParams) (*SortCompanyResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortCompany not implemented")
}
func (UnimplementedCompanyServiceServer) mustEmbedUnimplementedCompanyServiceServer() {}

// UnsafeCompanyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CompanyServiceServer will
// result in compilation errors.
type UnsafeCompanyServiceServer interface {
	mustEmbedUnimplementedCompanyServiceServer()
}

func RegisterCompanyServiceServer(s grpc.ServiceRegistrar, srv CompanyServiceServer) {
	s.RegisterService(&CompanyService_ServiceDesc, srv)
}

func _CompanyService_CreateCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCompanyParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).CreateCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/CreateCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).CreateCompany(ctx, req.(*CreateCompanyParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_QueryCompanyStaffByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCompanyStaffByAccountParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).QueryCompanyStaffByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/QueryCompanyStaffByAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).QueryCompanyStaffByAccount(ctx, req.(*QueryCompanyStaffByAccountParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_UpdateCompanyPreferenceSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCompanyPreferenceSettingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).UpdateCompanyPreferenceSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/UpdateCompanyPreferenceSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).UpdateCompanyPreferenceSetting(ctx, req.(*UpdateCompanyPreferenceSettingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_GetCompanyPreferenceSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompanyPreferenceSettingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).GetCompanyPreferenceSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/GetCompanyPreferenceSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).GetCompanyPreferenceSetting(ctx, req.(*GetCompanyPreferenceSettingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_SwitchCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchCompanyParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).SwitchCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/SwitchCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).SwitchCompany(ctx, req.(*SwitchCompanyParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_GetCompaniesExtraInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompaniesExtraInfoParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).GetCompaniesExtraInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/GetCompaniesExtraInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).GetCompaniesExtraInfo(ctx, req.(*GetCompaniesExtraInfoParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_QueryCompanyStaffByAccountWithLocations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCompanyStaffByAccountWithLocationsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).QueryCompanyStaffByAccountWithLocations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/QueryCompanyStaffByAccountWithLocations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).QueryCompanyStaffByAccountWithLocations(ctx, req.(*QueryCompanyStaffByAccountWithLocationsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_AddTaxRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTaxRuleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).AddTaxRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/AddTaxRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).AddTaxRule(ctx, req.(*AddTaxRuleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_UpdateTaxRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTaxRuleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).UpdateTaxRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/UpdateTaxRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).UpdateTaxRule(ctx, req.(*UpdateTaxRuleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_DeleteTaxRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTaxRuleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).DeleteTaxRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/DeleteTaxRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).DeleteTaxRule(ctx, req.(*DeleteTaxRuleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_GetTaxRuleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaxRuleListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).GetTaxRuleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/GetTaxRuleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).GetTaxRuleList(ctx, req.(*GetTaxRuleListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_GetClockInOutSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClockInOutSettingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).GetClockInOutSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/GetClockInOutSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).GetClockInOutSetting(ctx, req.(*GetClockInOutSettingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_UpdateClockInOutSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateClockInOutSettingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).UpdateClockInOutSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/UpdateClockInOutSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).UpdateClockInOutSetting(ctx, req.(*UpdateClockInOutSettingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_GetCompanyQuestionRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompanyQuestionRecordQueryParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).GetCompanyQuestionRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/GetCompanyQuestionRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).GetCompanyQuestionRecord(ctx, req.(*CompanyQuestionRecordQueryParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_CompanyQuestionRecordSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompanyQuestionRecordParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).CompanyQuestionRecordSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/CompanyQuestionRecordSave",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).CompanyQuestionRecordSave(ctx, req.(*CompanyQuestionRecordParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_SortCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortCompanyParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).SortCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CompanyService/SortCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).SortCompany(ctx, req.(*SortCompanyParams))
	}
	return interceptor(ctx, in, info, handler)
}

// CompanyService_ServiceDesc is the grpc.ServiceDesc for CompanyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CompanyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.organization.v1.CompanyService",
	HandlerType: (*CompanyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCompany",
			Handler:    _CompanyService_CreateCompany_Handler,
		},
		{
			MethodName: "QueryCompanyStaffByAccount",
			Handler:    _CompanyService_QueryCompanyStaffByAccount_Handler,
		},
		{
			MethodName: "UpdateCompanyPreferenceSetting",
			Handler:    _CompanyService_UpdateCompanyPreferenceSetting_Handler,
		},
		{
			MethodName: "GetCompanyPreferenceSetting",
			Handler:    _CompanyService_GetCompanyPreferenceSetting_Handler,
		},
		{
			MethodName: "SwitchCompany",
			Handler:    _CompanyService_SwitchCompany_Handler,
		},
		{
			MethodName: "GetCompaniesExtraInfo",
			Handler:    _CompanyService_GetCompaniesExtraInfo_Handler,
		},
		{
			MethodName: "QueryCompanyStaffByAccountWithLocations",
			Handler:    _CompanyService_QueryCompanyStaffByAccountWithLocations_Handler,
		},
		{
			MethodName: "AddTaxRule",
			Handler:    _CompanyService_AddTaxRule_Handler,
		},
		{
			MethodName: "UpdateTaxRule",
			Handler:    _CompanyService_UpdateTaxRule_Handler,
		},
		{
			MethodName: "DeleteTaxRule",
			Handler:    _CompanyService_DeleteTaxRule_Handler,
		},
		{
			MethodName: "GetTaxRuleList",
			Handler:    _CompanyService_GetTaxRuleList_Handler,
		},
		{
			MethodName: "GetClockInOutSetting",
			Handler:    _CompanyService_GetClockInOutSetting_Handler,
		},
		{
			MethodName: "UpdateClockInOutSetting",
			Handler:    _CompanyService_UpdateClockInOutSetting_Handler,
		},
		{
			MethodName: "GetCompanyQuestionRecord",
			Handler:    _CompanyService_GetCompanyQuestionRecord_Handler,
		},
		{
			MethodName: "CompanyQuestionRecordSave",
			Handler:    _CompanyService_CompanyQuestionRecordSave_Handler,
		},
		{
			MethodName: "SortCompany",
			Handler:    _CompanyService_SortCompany_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/organization/v1/company_api.proto",
}
