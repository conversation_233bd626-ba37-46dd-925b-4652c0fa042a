// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/business_customer/v1/business_pet_vaccine_api.proto

package businesscustomerapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list pet vaccine template params
type ListPetVaccineTemplateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListPetVaccineTemplateParams) Reset() {
	*x = ListPetVaccineTemplateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetVaccineTemplateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetVaccineTemplateParams) ProtoMessage() {}

func (x *ListPetVaccineTemplateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetVaccineTemplateParams.ProtoReflect.Descriptor instead.
func (*ListPetVaccineTemplateParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP(), []int{0}
}

// list pet vaccine template result
type ListPetVaccineTemplateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet vaccines
	Vaccines []*v1.BusinessPetVaccineNameView `protobuf:"bytes,1,rep,name=vaccines,proto3" json:"vaccines,omitempty"`
}

func (x *ListPetVaccineTemplateResult) Reset() {
	*x = ListPetVaccineTemplateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetVaccineTemplateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetVaccineTemplateResult) ProtoMessage() {}

func (x *ListPetVaccineTemplateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetVaccineTemplateResult.ProtoReflect.Descriptor instead.
func (*ListPetVaccineTemplateResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListPetVaccineTemplateResult) GetVaccines() []*v1.BusinessPetVaccineNameView {
	if x != nil {
		return x.Vaccines
	}
	return nil
}

// list pet vaccine params
type ListPetVaccineParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListPetVaccineParams) Reset() {
	*x = ListPetVaccineParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetVaccineParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetVaccineParams) ProtoMessage() {}

func (x *ListPetVaccineParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetVaccineParams.ProtoReflect.Descriptor instead.
func (*ListPetVaccineParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP(), []int{2}
}

// list pet vaccine result
type ListPetVaccineResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet vaccine list
	Vaccines []*v1.BusinessPetVaccineModel `protobuf:"bytes,1,rep,name=vaccines,proto3" json:"vaccines,omitempty"`
	// requirements of service
	VaccineRequirementByService []*ListPetVaccineResult_VaccineRequirementByService `protobuf:"bytes,2,rep,name=vaccine_requirement_by_service,json=vaccineRequirementByService,proto3" json:"vaccine_requirement_by_service,omitempty"`
}

func (x *ListPetVaccineResult) Reset() {
	*x = ListPetVaccineResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetVaccineResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetVaccineResult) ProtoMessage() {}

func (x *ListPetVaccineResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetVaccineResult.ProtoReflect.Descriptor instead.
func (*ListPetVaccineResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListPetVaccineResult) GetVaccines() []*v1.BusinessPetVaccineModel {
	if x != nil {
		return x.Vaccines
	}
	return nil
}

func (x *ListPetVaccineResult) GetVaccineRequirementByService() []*ListPetVaccineResult_VaccineRequirementByService {
	if x != nil {
		return x.VaccineRequirementByService
	}
	return nil
}

// create pet vaccine params
type CreatePetVaccineParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet vaccine
	Vaccine *v1.BusinessPetVaccineCreateDef `protobuf:"bytes,1,opt,name=vaccine,proto3" json:"vaccine,omitempty"`
	// vaccine requirements
	// requirements of service item types
	RequiredByServiceItemTypes []v11.ServiceItemType `protobuf:"varint,2,rep,packed,name=required_by_service_item_types,json=requiredByServiceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"required_by_service_item_types,omitempty"`
}

func (x *CreatePetVaccineParams) Reset() {
	*x = CreatePetVaccineParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetVaccineParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetVaccineParams) ProtoMessage() {}

func (x *CreatePetVaccineParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetVaccineParams.ProtoReflect.Descriptor instead.
func (*CreatePetVaccineParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP(), []int{4}
}

func (x *CreatePetVaccineParams) GetVaccine() *v1.BusinessPetVaccineCreateDef {
	if x != nil {
		return x.Vaccine
	}
	return nil
}

func (x *CreatePetVaccineParams) GetRequiredByServiceItemTypes() []v11.ServiceItemType {
	if x != nil {
		return x.RequiredByServiceItemTypes
	}
	return nil
}

// create pet vaccine result
type CreatePetVaccineResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine
	Vaccine *v1.BusinessPetVaccineModel `protobuf:"bytes,1,opt,name=vaccine,proto3" json:"vaccine,omitempty"`
}

func (x *CreatePetVaccineResult) Reset() {
	*x = CreatePetVaccineResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetVaccineResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetVaccineResult) ProtoMessage() {}

func (x *CreatePetVaccineResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetVaccineResult.ProtoReflect.Descriptor instead.
func (*CreatePetVaccineResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP(), []int{5}
}

func (x *CreatePetVaccineResult) GetVaccine() *v1.BusinessPetVaccineModel {
	if x != nil {
		return x.Vaccine
	}
	return nil
}

// update pet vaccine params
type UpdatePetVaccineParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet vaccine id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet vaccine
	Vaccine *v1.BusinessPetVaccineUpdateDef `protobuf:"bytes,2,opt,name=vaccine,proto3" json:"vaccine,omitempty"`
	// requirements of service
	RequiredByServiceItemTypes *UpdatePetVaccineParams_ServiceItemTypeList `protobuf:"bytes,3,opt,name=required_by_service_item_types,json=requiredByServiceItemTypes,proto3,oneof" json:"required_by_service_item_types,omitempty"`
}

func (x *UpdatePetVaccineParams) Reset() {
	*x = UpdatePetVaccineParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetVaccineParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetVaccineParams) ProtoMessage() {}

func (x *UpdatePetVaccineParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetVaccineParams.ProtoReflect.Descriptor instead.
func (*UpdatePetVaccineParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP(), []int{6}
}

func (x *UpdatePetVaccineParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetVaccineParams) GetVaccine() *v1.BusinessPetVaccineUpdateDef {
	if x != nil {
		return x.Vaccine
	}
	return nil
}

func (x *UpdatePetVaccineParams) GetRequiredByServiceItemTypes() *UpdatePetVaccineParams_ServiceItemTypeList {
	if x != nil {
		return x.RequiredByServiceItemTypes
	}
	return nil
}

// sort pet vaccine params
type SortPetVaccineParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet vaccine id list, should contain all pet vaccine ids for the company / business
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortPetVaccineParams) Reset() {
	*x = SortPetVaccineParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetVaccineParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetVaccineParams) ProtoMessage() {}

func (x *SortPetVaccineParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetVaccineParams.ProtoReflect.Descriptor instead.
func (*SortPetVaccineParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP(), []int{7}
}

func (x *SortPetVaccineParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// sort pet vaccine result
type SortPetVaccineResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortPetVaccineResult) Reset() {
	*x = SortPetVaccineResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetVaccineResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetVaccineResult) ProtoMessage() {}

func (x *SortPetVaccineResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetVaccineResult.ProtoReflect.Descriptor instead.
func (*SortPetVaccineResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP(), []int{8}
}

// update pet vaccine result
type UpdatePetVaccineResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdatePetVaccineResult) Reset() {
	*x = UpdatePetVaccineResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetVaccineResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetVaccineResult) ProtoMessage() {}

func (x *UpdatePetVaccineResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetVaccineResult.ProtoReflect.Descriptor instead.
func (*UpdatePetVaccineResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP(), []int{9}
}

// delete pet vaccine params
type DeletePetVaccineParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet vaccine id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePetVaccineParams) Reset() {
	*x = DeletePetVaccineParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetVaccineParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetVaccineParams) ProtoMessage() {}

func (x *DeletePetVaccineParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetVaccineParams.ProtoReflect.Descriptor instead.
func (*DeletePetVaccineParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP(), []int{10}
}

func (x *DeletePetVaccineParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete pet vaccine result
type DeletePetVaccineResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePetVaccineResult) Reset() {
	*x = DeletePetVaccineResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetVaccineResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetVaccineResult) ProtoMessage() {}

func (x *DeletePetVaccineResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetVaccineResult.ProtoReflect.Descriptor instead.
func (*DeletePetVaccineResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP(), []int{11}
}

// vaccine id to requirements setting
// requirements of service
type ListPetVaccineResult_VaccineRequirementByService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine id
	VaccineId int64 `protobuf:"varint,1,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// requirements of service item types
	RequiredByServiceItemTypes []v11.ServiceItemType `protobuf:"varint,2,rep,packed,name=required_by_service_item_types,json=requiredByServiceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"required_by_service_item_types,omitempty"`
}

func (x *ListPetVaccineResult_VaccineRequirementByService) Reset() {
	*x = ListPetVaccineResult_VaccineRequirementByService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetVaccineResult_VaccineRequirementByService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetVaccineResult_VaccineRequirementByService) ProtoMessage() {}

func (x *ListPetVaccineResult_VaccineRequirementByService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetVaccineResult_VaccineRequirementByService.ProtoReflect.Descriptor instead.
func (*ListPetVaccineResult_VaccineRequirementByService) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ListPetVaccineResult_VaccineRequirementByService) GetVaccineId() int64 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *ListPetVaccineResult_VaccineRequirementByService) GetRequiredByServiceItemTypes() []v11.ServiceItemType {
	if x != nil {
		return x.RequiredByServiceItemTypes
	}
	return nil
}

// vaccine requirements
// requirements of service item types
type UpdatePetVaccineParams_ServiceItemTypeList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// requirements of service item types
	Types []v11.ServiceItemType `protobuf:"varint,1,rep,packed,name=types,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"types,omitempty"`
}

func (x *UpdatePetVaccineParams_ServiceItemTypeList) Reset() {
	*x = UpdatePetVaccineParams_ServiceItemTypeList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetVaccineParams_ServiceItemTypeList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetVaccineParams_ServiceItemTypeList) ProtoMessage() {}

func (x *UpdatePetVaccineParams_ServiceItemTypeList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetVaccineParams_ServiceItemTypeList.ProtoReflect.Descriptor instead.
func (*UpdatePetVaccineParams_ServiceItemTypeList) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP(), []int{6, 0}
}

func (x *UpdatePetVaccineParams_ServiceItemTypeList) GetTypes() []v11.ServiceItemType {
	if x != nil {
		return x.Types
	}
	return nil
}

var File_moego_api_business_customer_v1_business_pet_vaccine_api_proto protoreflect.FileDescriptor

var file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDesc = []byte{
	0x0a, 0x3d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a,
	0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70,
	0x65, 0x74, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1e, 0x0a,
	0x1c, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x79, 0x0a,
	0x1c, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x59, 0x0a,
	0x08, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x56,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x22, 0x16, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x22, 0xc8, 0x03, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x56, 0x0a, 0x08, 0x76, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x73, 0x12, 0x95, 0x01, 0x0a, 0x1e, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x79, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x1b, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x1a, 0xbf, 0x01, 0x0a, 0x1b, 0x56, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x76,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x80, 0x01, 0x0a, 0x1e, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x11, 0xfa, 0x42,
	0x0e, 0x92, 0x01, 0x0b, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52,
	0x1a, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0xff, 0x01, 0x0a, 0x16,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x62, 0x0a, 0x07, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x1e, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x11,
	0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x52, 0x1a, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x42, 0x79, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x6e, 0x0a,
	0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x54, 0x0a, 0x07, 0x76, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x22, 0xb9, 0x03,
	0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x62, 0x0a, 0x07, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50,
	0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x1e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x1a, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x88, 0x01, 0x01, 0x1a, 0x69, 0x0a, 0x13, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x52, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x11, 0xfa, 0x42,
	0x0e, 0x92, 0x01, 0x0b, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52,
	0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x42, 0x21, 0x0a, 0x1f, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x38, 0x0a, 0x14, 0x53, 0x6f, 0x72,
	0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x20, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e,
	0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03,
	0x69, 0x64, 0x73, 0x22, 0x16, 0x0a, 0x14, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x56, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x18, 0x0a, 0x16, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x31, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x18, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x32, 0xbd, 0x06, 0x0a, 0x19, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50,
	0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x94, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x3c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x82, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x82, 0x01, 0x0a, 0x10, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x12,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x7c, 0x0a, 0x0e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74,
	0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x82, 0x01,
	0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x42, 0x95, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescOnce sync.Once
	file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescData = file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDesc
)

func file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescGZIP() []byte {
	file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescOnce.Do(func() {
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescData)
	})
	return file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDescData
}

var file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_goTypes = []interface{}{
	(*ListPetVaccineTemplateParams)(nil),                     // 0: moego.api.business_customer.v1.ListPetVaccineTemplateParams
	(*ListPetVaccineTemplateResult)(nil),                     // 1: moego.api.business_customer.v1.ListPetVaccineTemplateResult
	(*ListPetVaccineParams)(nil),                             // 2: moego.api.business_customer.v1.ListPetVaccineParams
	(*ListPetVaccineResult)(nil),                             // 3: moego.api.business_customer.v1.ListPetVaccineResult
	(*CreatePetVaccineParams)(nil),                           // 4: moego.api.business_customer.v1.CreatePetVaccineParams
	(*CreatePetVaccineResult)(nil),                           // 5: moego.api.business_customer.v1.CreatePetVaccineResult
	(*UpdatePetVaccineParams)(nil),                           // 6: moego.api.business_customer.v1.UpdatePetVaccineParams
	(*SortPetVaccineParams)(nil),                             // 7: moego.api.business_customer.v1.SortPetVaccineParams
	(*SortPetVaccineResult)(nil),                             // 8: moego.api.business_customer.v1.SortPetVaccineResult
	(*UpdatePetVaccineResult)(nil),                           // 9: moego.api.business_customer.v1.UpdatePetVaccineResult
	(*DeletePetVaccineParams)(nil),                           // 10: moego.api.business_customer.v1.DeletePetVaccineParams
	(*DeletePetVaccineResult)(nil),                           // 11: moego.api.business_customer.v1.DeletePetVaccineResult
	(*ListPetVaccineResult_VaccineRequirementByService)(nil), // 12: moego.api.business_customer.v1.ListPetVaccineResult.VaccineRequirementByService
	(*UpdatePetVaccineParams_ServiceItemTypeList)(nil),       // 13: moego.api.business_customer.v1.UpdatePetVaccineParams.ServiceItemTypeList
	(*v1.BusinessPetVaccineNameView)(nil),                    // 14: moego.models.business_customer.v1.BusinessPetVaccineNameView
	(*v1.BusinessPetVaccineModel)(nil),                       // 15: moego.models.business_customer.v1.BusinessPetVaccineModel
	(*v1.BusinessPetVaccineCreateDef)(nil),                   // 16: moego.models.business_customer.v1.BusinessPetVaccineCreateDef
	(v11.ServiceItemType)(0),                                 // 17: moego.models.offering.v1.ServiceItemType
	(*v1.BusinessPetVaccineUpdateDef)(nil),                   // 18: moego.models.business_customer.v1.BusinessPetVaccineUpdateDef
}
var file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_depIdxs = []int32{
	14, // 0: moego.api.business_customer.v1.ListPetVaccineTemplateResult.vaccines:type_name -> moego.models.business_customer.v1.BusinessPetVaccineNameView
	15, // 1: moego.api.business_customer.v1.ListPetVaccineResult.vaccines:type_name -> moego.models.business_customer.v1.BusinessPetVaccineModel
	12, // 2: moego.api.business_customer.v1.ListPetVaccineResult.vaccine_requirement_by_service:type_name -> moego.api.business_customer.v1.ListPetVaccineResult.VaccineRequirementByService
	16, // 3: moego.api.business_customer.v1.CreatePetVaccineParams.vaccine:type_name -> moego.models.business_customer.v1.BusinessPetVaccineCreateDef
	17, // 4: moego.api.business_customer.v1.CreatePetVaccineParams.required_by_service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	15, // 5: moego.api.business_customer.v1.CreatePetVaccineResult.vaccine:type_name -> moego.models.business_customer.v1.BusinessPetVaccineModel
	18, // 6: moego.api.business_customer.v1.UpdatePetVaccineParams.vaccine:type_name -> moego.models.business_customer.v1.BusinessPetVaccineUpdateDef
	13, // 7: moego.api.business_customer.v1.UpdatePetVaccineParams.required_by_service_item_types:type_name -> moego.api.business_customer.v1.UpdatePetVaccineParams.ServiceItemTypeList
	17, // 8: moego.api.business_customer.v1.ListPetVaccineResult.VaccineRequirementByService.required_by_service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	17, // 9: moego.api.business_customer.v1.UpdatePetVaccineParams.ServiceItemTypeList.types:type_name -> moego.models.offering.v1.ServiceItemType
	0,  // 10: moego.api.business_customer.v1.BusinessPetVaccineService.ListPetVaccineTemplate:input_type -> moego.api.business_customer.v1.ListPetVaccineTemplateParams
	2,  // 11: moego.api.business_customer.v1.BusinessPetVaccineService.ListPetVaccine:input_type -> moego.api.business_customer.v1.ListPetVaccineParams
	4,  // 12: moego.api.business_customer.v1.BusinessPetVaccineService.CreatePetVaccine:input_type -> moego.api.business_customer.v1.CreatePetVaccineParams
	6,  // 13: moego.api.business_customer.v1.BusinessPetVaccineService.UpdatePetVaccine:input_type -> moego.api.business_customer.v1.UpdatePetVaccineParams
	7,  // 14: moego.api.business_customer.v1.BusinessPetVaccineService.SortPetVaccine:input_type -> moego.api.business_customer.v1.SortPetVaccineParams
	10, // 15: moego.api.business_customer.v1.BusinessPetVaccineService.DeletePetVaccine:input_type -> moego.api.business_customer.v1.DeletePetVaccineParams
	1,  // 16: moego.api.business_customer.v1.BusinessPetVaccineService.ListPetVaccineTemplate:output_type -> moego.api.business_customer.v1.ListPetVaccineTemplateResult
	3,  // 17: moego.api.business_customer.v1.BusinessPetVaccineService.ListPetVaccine:output_type -> moego.api.business_customer.v1.ListPetVaccineResult
	5,  // 18: moego.api.business_customer.v1.BusinessPetVaccineService.CreatePetVaccine:output_type -> moego.api.business_customer.v1.CreatePetVaccineResult
	9,  // 19: moego.api.business_customer.v1.BusinessPetVaccineService.UpdatePetVaccine:output_type -> moego.api.business_customer.v1.UpdatePetVaccineResult
	8,  // 20: moego.api.business_customer.v1.BusinessPetVaccineService.SortPetVaccine:output_type -> moego.api.business_customer.v1.SortPetVaccineResult
	11, // 21: moego.api.business_customer.v1.BusinessPetVaccineService.DeletePetVaccine:output_type -> moego.api.business_customer.v1.DeletePetVaccineResult
	16, // [16:22] is the sub-list for method output_type
	10, // [10:16] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_init() }
func file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_init() {
	if File_moego_api_business_customer_v1_business_pet_vaccine_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetVaccineTemplateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetVaccineTemplateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetVaccineParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetVaccineResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetVaccineParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetVaccineResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetVaccineParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetVaccineParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetVaccineResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetVaccineResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetVaccineParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetVaccineResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetVaccineResult_VaccineRequirementByService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetVaccineParams_ServiceItemTypeList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_goTypes,
		DependencyIndexes: file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_depIdxs,
		MessageInfos:      file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_msgTypes,
	}.Build()
	File_moego_api_business_customer_v1_business_pet_vaccine_api_proto = out.File
	file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_rawDesc = nil
	file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_goTypes = nil
	file_moego_api_business_customer_v1_business_pet_vaccine_api_proto_depIdxs = nil
}
