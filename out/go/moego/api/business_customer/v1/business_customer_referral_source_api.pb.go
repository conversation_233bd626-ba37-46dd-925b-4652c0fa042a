// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/business_customer/v1/business_customer_referral_source_api.proto

package businesscustomerapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list customer referral source template params
type ListCustomerReferralSourceTemplateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListCustomerReferralSourceTemplateParams) Reset() {
	*x = ListCustomerReferralSourceTemplateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomerReferralSourceTemplateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerReferralSourceTemplateParams) ProtoMessage() {}

func (x *ListCustomerReferralSourceTemplateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerReferralSourceTemplateParams.ProtoReflect.Descriptor instead.
func (*ListCustomerReferralSourceTemplateParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescGZIP(), []int{0}
}

// list customer referral source template result
type ListCustomerReferralSourceTemplateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// referral source template list
	ReferralSources []*v1.BusinessCustomerReferralSourceNameView `protobuf:"bytes,1,rep,name=referral_sources,json=referralSources,proto3" json:"referral_sources,omitempty"`
}

func (x *ListCustomerReferralSourceTemplateResult) Reset() {
	*x = ListCustomerReferralSourceTemplateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomerReferralSourceTemplateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerReferralSourceTemplateResult) ProtoMessage() {}

func (x *ListCustomerReferralSourceTemplateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerReferralSourceTemplateResult.ProtoReflect.Descriptor instead.
func (*ListCustomerReferralSourceTemplateResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListCustomerReferralSourceTemplateResult) GetReferralSources() []*v1.BusinessCustomerReferralSourceNameView {
	if x != nil {
		return x.ReferralSources
	}
	return nil
}

// list customer referral source params
type ListCustomerReferralSourceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListCustomerReferralSourceParams) Reset() {
	*x = ListCustomerReferralSourceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomerReferralSourceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerReferralSourceParams) ProtoMessage() {}

func (x *ListCustomerReferralSourceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerReferralSourceParams.ProtoReflect.Descriptor instead.
func (*ListCustomerReferralSourceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescGZIP(), []int{2}
}

// list customer referral source result
type ListCustomerReferralSourceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// referral source list
	ReferralSources []*v1.BusinessCustomerReferralSourceModel `protobuf:"bytes,1,rep,name=referral_sources,json=referralSources,proto3" json:"referral_sources,omitempty"`
}

func (x *ListCustomerReferralSourceResult) Reset() {
	*x = ListCustomerReferralSourceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomerReferralSourceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerReferralSourceResult) ProtoMessage() {}

func (x *ListCustomerReferralSourceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerReferralSourceResult.ProtoReflect.Descriptor instead.
func (*ListCustomerReferralSourceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListCustomerReferralSourceResult) GetReferralSources() []*v1.BusinessCustomerReferralSourceModel {
	if x != nil {
		return x.ReferralSources
	}
	return nil
}

// create customer referral source params
type CreateCustomerReferralSourceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// referral source
	ReferralSource *v1.BusinessCustomerReferralSourceCreateDef `protobuf:"bytes,1,opt,name=referral_source,json=referralSource,proto3" json:"referral_source,omitempty"`
}

func (x *CreateCustomerReferralSourceParams) Reset() {
	*x = CreateCustomerReferralSourceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerReferralSourceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerReferralSourceParams) ProtoMessage() {}

func (x *CreateCustomerReferralSourceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerReferralSourceParams.ProtoReflect.Descriptor instead.
func (*CreateCustomerReferralSourceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescGZIP(), []int{4}
}

func (x *CreateCustomerReferralSourceParams) GetReferralSource() *v1.BusinessCustomerReferralSourceCreateDef {
	if x != nil {
		return x.ReferralSource
	}
	return nil
}

// create customer referral source result
type CreateCustomerReferralSourceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// referral source
	ReferralSource *v1.BusinessCustomerReferralSourceModel `protobuf:"bytes,1,opt,name=referral_source,json=referralSource,proto3" json:"referral_source,omitempty"`
}

func (x *CreateCustomerReferralSourceResult) Reset() {
	*x = CreateCustomerReferralSourceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerReferralSourceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerReferralSourceResult) ProtoMessage() {}

func (x *CreateCustomerReferralSourceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerReferralSourceResult.ProtoReflect.Descriptor instead.
func (*CreateCustomerReferralSourceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescGZIP(), []int{5}
}

func (x *CreateCustomerReferralSourceResult) GetReferralSource() *v1.BusinessCustomerReferralSourceModel {
	if x != nil {
		return x.ReferralSource
	}
	return nil
}

// update customer referral source params
type UpdateCustomerReferralSourceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// referral source id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer referral source
	ReferralSource *v1.BusinessCustomerReferralSourceUpdateDef `protobuf:"bytes,2,opt,name=referral_source,json=referralSource,proto3" json:"referral_source,omitempty"`
}

func (x *UpdateCustomerReferralSourceParams) Reset() {
	*x = UpdateCustomerReferralSourceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCustomerReferralSourceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerReferralSourceParams) ProtoMessage() {}

func (x *UpdateCustomerReferralSourceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerReferralSourceParams.ProtoReflect.Descriptor instead.
func (*UpdateCustomerReferralSourceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateCustomerReferralSourceParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCustomerReferralSourceParams) GetReferralSource() *v1.BusinessCustomerReferralSourceUpdateDef {
	if x != nil {
		return x.ReferralSource
	}
	return nil
}

// update customer referral source result
type UpdateCustomerReferralSourceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateCustomerReferralSourceResult) Reset() {
	*x = UpdateCustomerReferralSourceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCustomerReferralSourceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerReferralSourceResult) ProtoMessage() {}

func (x *UpdateCustomerReferralSourceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerReferralSourceResult.ProtoReflect.Descriptor instead.
func (*UpdateCustomerReferralSourceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescGZIP(), []int{7}
}

// sort customer referral source params
type SortCustomerReferralSourceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer referral source id list, should contain all customer referral source ids for the company / business
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortCustomerReferralSourceParams) Reset() {
	*x = SortCustomerReferralSourceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortCustomerReferralSourceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortCustomerReferralSourceParams) ProtoMessage() {}

func (x *SortCustomerReferralSourceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortCustomerReferralSourceParams.ProtoReflect.Descriptor instead.
func (*SortCustomerReferralSourceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescGZIP(), []int{8}
}

func (x *SortCustomerReferralSourceParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// sort customer referral source result
type SortCustomerReferralSourceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortCustomerReferralSourceResult) Reset() {
	*x = SortCustomerReferralSourceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortCustomerReferralSourceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortCustomerReferralSourceResult) ProtoMessage() {}

func (x *SortCustomerReferralSourceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortCustomerReferralSourceResult.ProtoReflect.Descriptor instead.
func (*SortCustomerReferralSourceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescGZIP(), []int{9}
}

// delete customer referral source params
type DeleteCustomerReferralSourceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// referral source id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteCustomerReferralSourceParams) Reset() {
	*x = DeleteCustomerReferralSourceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCustomerReferralSourceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomerReferralSourceParams) ProtoMessage() {}

func (x *DeleteCustomerReferralSourceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomerReferralSourceParams.ProtoReflect.Descriptor instead.
func (*DeleteCustomerReferralSourceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteCustomerReferralSourceParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete customer referral source result
type DeleteCustomerReferralSourceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteCustomerReferralSourceResult) Reset() {
	*x = DeleteCustomerReferralSourceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCustomerReferralSourceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomerReferralSourceResult) ProtoMessage() {}

func (x *DeleteCustomerReferralSourceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomerReferralSourceResult.ProtoReflect.Descriptor instead.
func (*DeleteCustomerReferralSourceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescGZIP(), []int{11}
}

var File_moego_api_business_customer_v1_business_customer_referral_source_api_proto protoreflect.FileDescriptor

var file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDesc = []byte{
	0x0a, 0x4a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x4e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x50, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2a, 0x0a, 0x28, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x28, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x74, 0x0a, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x22, 0x22, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x95, 0x01, 0x0a, 0x20, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x71, 0x0a, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x73, 0x22, 0xa3, 0x01, 0x0a, 0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x7d, 0x0a, 0x0f, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x95, 0x01, 0x0a, 0x22, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x6f, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x22, 0xbc, 0x01, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x7d, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22,
	0x24, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x44, 0x0a, 0x20, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x03, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x22, 0x0a, 0x20, 0x53,
	0x6f, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x3d, 0x0a, 0x22, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x24,
	0x0a, 0x22, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x32, 0xa3, 0x08, 0x0a, 0x25, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61,
	0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xb8,
	0x01, 0x0a, 0x22, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xa0, 0x01, 0x0a, 0x1a, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x40, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xa6, 0x01, 0x0a,
	0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x42, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xa6, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x42, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xa0,
	0x01, 0x0a, 0x1a, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x40, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x6f, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0xa6, 0x01, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x95, 0x01, 0x0a, 0x26, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x61, 0x70, 0x69,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescOnce sync.Once
	file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescData = file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDesc
)

func file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescGZIP() []byte {
	file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescOnce.Do(func() {
		file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescData)
	})
	return file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDescData
}

var file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_goTypes = []interface{}{
	(*ListCustomerReferralSourceTemplateParams)(nil),   // 0: moego.api.business_customer.v1.ListCustomerReferralSourceTemplateParams
	(*ListCustomerReferralSourceTemplateResult)(nil),   // 1: moego.api.business_customer.v1.ListCustomerReferralSourceTemplateResult
	(*ListCustomerReferralSourceParams)(nil),           // 2: moego.api.business_customer.v1.ListCustomerReferralSourceParams
	(*ListCustomerReferralSourceResult)(nil),           // 3: moego.api.business_customer.v1.ListCustomerReferralSourceResult
	(*CreateCustomerReferralSourceParams)(nil),         // 4: moego.api.business_customer.v1.CreateCustomerReferralSourceParams
	(*CreateCustomerReferralSourceResult)(nil),         // 5: moego.api.business_customer.v1.CreateCustomerReferralSourceResult
	(*UpdateCustomerReferralSourceParams)(nil),         // 6: moego.api.business_customer.v1.UpdateCustomerReferralSourceParams
	(*UpdateCustomerReferralSourceResult)(nil),         // 7: moego.api.business_customer.v1.UpdateCustomerReferralSourceResult
	(*SortCustomerReferralSourceParams)(nil),           // 8: moego.api.business_customer.v1.SortCustomerReferralSourceParams
	(*SortCustomerReferralSourceResult)(nil),           // 9: moego.api.business_customer.v1.SortCustomerReferralSourceResult
	(*DeleteCustomerReferralSourceParams)(nil),         // 10: moego.api.business_customer.v1.DeleteCustomerReferralSourceParams
	(*DeleteCustomerReferralSourceResult)(nil),         // 11: moego.api.business_customer.v1.DeleteCustomerReferralSourceResult
	(*v1.BusinessCustomerReferralSourceNameView)(nil),  // 12: moego.models.business_customer.v1.BusinessCustomerReferralSourceNameView
	(*v1.BusinessCustomerReferralSourceModel)(nil),     // 13: moego.models.business_customer.v1.BusinessCustomerReferralSourceModel
	(*v1.BusinessCustomerReferralSourceCreateDef)(nil), // 14: moego.models.business_customer.v1.BusinessCustomerReferralSourceCreateDef
	(*v1.BusinessCustomerReferralSourceUpdateDef)(nil), // 15: moego.models.business_customer.v1.BusinessCustomerReferralSourceUpdateDef
}
var file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_depIdxs = []int32{
	12, // 0: moego.api.business_customer.v1.ListCustomerReferralSourceTemplateResult.referral_sources:type_name -> moego.models.business_customer.v1.BusinessCustomerReferralSourceNameView
	13, // 1: moego.api.business_customer.v1.ListCustomerReferralSourceResult.referral_sources:type_name -> moego.models.business_customer.v1.BusinessCustomerReferralSourceModel
	14, // 2: moego.api.business_customer.v1.CreateCustomerReferralSourceParams.referral_source:type_name -> moego.models.business_customer.v1.BusinessCustomerReferralSourceCreateDef
	13, // 3: moego.api.business_customer.v1.CreateCustomerReferralSourceResult.referral_source:type_name -> moego.models.business_customer.v1.BusinessCustomerReferralSourceModel
	15, // 4: moego.api.business_customer.v1.UpdateCustomerReferralSourceParams.referral_source:type_name -> moego.models.business_customer.v1.BusinessCustomerReferralSourceUpdateDef
	0,  // 5: moego.api.business_customer.v1.BusinessCustomerReferralSourceService.ListCustomerReferralSourceTemplate:input_type -> moego.api.business_customer.v1.ListCustomerReferralSourceTemplateParams
	2,  // 6: moego.api.business_customer.v1.BusinessCustomerReferralSourceService.ListCustomerReferralSource:input_type -> moego.api.business_customer.v1.ListCustomerReferralSourceParams
	4,  // 7: moego.api.business_customer.v1.BusinessCustomerReferralSourceService.CreateCustomerReferralSource:input_type -> moego.api.business_customer.v1.CreateCustomerReferralSourceParams
	6,  // 8: moego.api.business_customer.v1.BusinessCustomerReferralSourceService.UpdateCustomerReferralSource:input_type -> moego.api.business_customer.v1.UpdateCustomerReferralSourceParams
	8,  // 9: moego.api.business_customer.v1.BusinessCustomerReferralSourceService.SortCustomerReferralSource:input_type -> moego.api.business_customer.v1.SortCustomerReferralSourceParams
	10, // 10: moego.api.business_customer.v1.BusinessCustomerReferralSourceService.DeleteCustomerReferralSource:input_type -> moego.api.business_customer.v1.DeleteCustomerReferralSourceParams
	1,  // 11: moego.api.business_customer.v1.BusinessCustomerReferralSourceService.ListCustomerReferralSourceTemplate:output_type -> moego.api.business_customer.v1.ListCustomerReferralSourceTemplateResult
	3,  // 12: moego.api.business_customer.v1.BusinessCustomerReferralSourceService.ListCustomerReferralSource:output_type -> moego.api.business_customer.v1.ListCustomerReferralSourceResult
	5,  // 13: moego.api.business_customer.v1.BusinessCustomerReferralSourceService.CreateCustomerReferralSource:output_type -> moego.api.business_customer.v1.CreateCustomerReferralSourceResult
	7,  // 14: moego.api.business_customer.v1.BusinessCustomerReferralSourceService.UpdateCustomerReferralSource:output_type -> moego.api.business_customer.v1.UpdateCustomerReferralSourceResult
	9,  // 15: moego.api.business_customer.v1.BusinessCustomerReferralSourceService.SortCustomerReferralSource:output_type -> moego.api.business_customer.v1.SortCustomerReferralSourceResult
	11, // 16: moego.api.business_customer.v1.BusinessCustomerReferralSourceService.DeleteCustomerReferralSource:output_type -> moego.api.business_customer.v1.DeleteCustomerReferralSourceResult
	11, // [11:17] is the sub-list for method output_type
	5,  // [5:11] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_init() }
func file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_init() {
	if File_moego_api_business_customer_v1_business_customer_referral_source_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomerReferralSourceTemplateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomerReferralSourceTemplateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomerReferralSourceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomerReferralSourceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerReferralSourceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerReferralSourceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCustomerReferralSourceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCustomerReferralSourceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortCustomerReferralSourceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortCustomerReferralSourceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCustomerReferralSourceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCustomerReferralSourceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_goTypes,
		DependencyIndexes: file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_depIdxs,
		MessageInfos:      file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_msgTypes,
	}.Build()
	File_moego_api_business_customer_v1_business_customer_referral_source_api_proto = out.File
	file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_rawDesc = nil
	file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_goTypes = nil
	file_moego_api_business_customer_v1_business_customer_referral_source_api_proto_depIdxs = nil
}
