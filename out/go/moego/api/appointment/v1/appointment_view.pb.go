// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/appointment/v1/appointment_view.proto

package appointmentapipb

import (
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// customer package view
type CustomerPackageView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// package id
	PackageId int64 `protobuf:"varint,2,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	// package name
	PackageName string `protobuf:"bytes,3,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	// package detail
	PackageDetails []*CustomerPackageView_PackageDetail `protobuf:"bytes,4,rep,name=package_details,json=packageDetails,proto3" json:"package_details,omitempty"`
	// valid start date
	StartDate *date.Date `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// expiration date
	ExpirationDate *date.Date `protobuf:"bytes,6,opt,name=expiration_date,json=expirationDate,proto3,oneof" json:"expiration_date,omitempty"`
}

func (x *CustomerPackageView) Reset() {
	*x = CustomerPackageView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_view_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerPackageView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPackageView) ProtoMessage() {}

func (x *CustomerPackageView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_view_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPackageView.ProtoReflect.Descriptor instead.
func (*CustomerPackageView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_view_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerPackageView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CustomerPackageView) GetPackageId() int64 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

func (x *CustomerPackageView) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *CustomerPackageView) GetPackageDetails() []*CustomerPackageView_PackageDetail {
	if x != nil {
		return x.PackageDetails
	}
	return nil
}

func (x *CustomerPackageView) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *CustomerPackageView) GetExpirationDate() *date.Date {
	if x != nil {
		return x.ExpirationDate
	}
	return nil
}

// package detail
type CustomerPackageView_PackageDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	// Deprecated by Freeman, use Service instead
	//
	// Deprecated: Do not use.
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service name
	// Deprecated by Freeman, use Service instead
	//
	// Deprecated: Do not use.
	ServiceName string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// package remaining quantity
	RemainingQuantity int32 `protobuf:"varint,3,opt,name=remaining_quantity,json=remainingQuantity,proto3" json:"remaining_quantity,omitempty"`
	// package total quantity
	TotalQuantity int32 `protobuf:"varint,4,opt,name=total_quantity,json=totalQuantity,proto3" json:"total_quantity,omitempty"`
	// services in package
	Services []*CustomerPackageView_PackageDetail_Service `protobuf:"bytes,5,rep,name=services,proto3" json:"services,omitempty"`
}

func (x *CustomerPackageView_PackageDetail) Reset() {
	*x = CustomerPackageView_PackageDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_view_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerPackageView_PackageDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPackageView_PackageDetail) ProtoMessage() {}

func (x *CustomerPackageView_PackageDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_view_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPackageView_PackageDetail.ProtoReflect.Descriptor instead.
func (*CustomerPackageView_PackageDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_view_proto_rawDescGZIP(), []int{0, 0}
}

// Deprecated: Do not use.
func (x *CustomerPackageView_PackageDetail) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// Deprecated: Do not use.
func (x *CustomerPackageView_PackageDetail) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *CustomerPackageView_PackageDetail) GetRemainingQuantity() int32 {
	if x != nil {
		return x.RemainingQuantity
	}
	return 0
}

func (x *CustomerPackageView_PackageDetail) GetTotalQuantity() int32 {
	if x != nil {
		return x.TotalQuantity
	}
	return 0
}

func (x *CustomerPackageView_PackageDetail) GetServices() []*CustomerPackageView_PackageDetail_Service {
	if x != nil {
		return x.Services
	}
	return nil
}

// service
type CustomerPackageView_PackageDetail_Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// service name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// unit price
	UnitPrice float64 `protobuf:"fixed64,3,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
}

func (x *CustomerPackageView_PackageDetail_Service) Reset() {
	*x = CustomerPackageView_PackageDetail_Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_view_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerPackageView_PackageDetail_Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPackageView_PackageDetail_Service) ProtoMessage() {}

func (x *CustomerPackageView_PackageDetail_Service) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_view_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPackageView_PackageDetail_Service.ProtoReflect.Descriptor instead.
func (*CustomerPackageView_PackageDetail_Service) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_view_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *CustomerPackageView_PackageDetail_Service) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CustomerPackageView_PackageDetail_Service) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerPackageView_PackageDetail_Service) GetUnitPrice() float64 {
	if x != nil {
		return x.UnitPrice
	}
	return 0
}

var File_moego_api_appointment_v1_appointment_view_proto protoreflect.FileDescriptor

var file_moego_api_appointment_v1_appointment_view_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xc6, 0x05, 0x0a, 0x13, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x64,
	0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x1a, 0xde, 0x02, 0x0a, 0x0d, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x21, 0x0a, 0x0a, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x11, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x71, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x5f, 0x0a, 0x08, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0x4c, 0x0a, 0x07, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x6e, 0x69,
	0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x75,
	0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x84, 0x01, 0x0a,
	0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70,
	0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_appointment_v1_appointment_view_proto_rawDescOnce sync.Once
	file_moego_api_appointment_v1_appointment_view_proto_rawDescData = file_moego_api_appointment_v1_appointment_view_proto_rawDesc
)

func file_moego_api_appointment_v1_appointment_view_proto_rawDescGZIP() []byte {
	file_moego_api_appointment_v1_appointment_view_proto_rawDescOnce.Do(func() {
		file_moego_api_appointment_v1_appointment_view_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_appointment_v1_appointment_view_proto_rawDescData)
	})
	return file_moego_api_appointment_v1_appointment_view_proto_rawDescData
}

var file_moego_api_appointment_v1_appointment_view_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_api_appointment_v1_appointment_view_proto_goTypes = []interface{}{
	(*CustomerPackageView)(nil),                       // 0: moego.api.appointment.v1.CustomerPackageView
	(*CustomerPackageView_PackageDetail)(nil),         // 1: moego.api.appointment.v1.CustomerPackageView.PackageDetail
	(*CustomerPackageView_PackageDetail_Service)(nil), // 2: moego.api.appointment.v1.CustomerPackageView.PackageDetail.Service
	(*date.Date)(nil),                                 // 3: google.type.Date
}
var file_moego_api_appointment_v1_appointment_view_proto_depIdxs = []int32{
	1, // 0: moego.api.appointment.v1.CustomerPackageView.package_details:type_name -> moego.api.appointment.v1.CustomerPackageView.PackageDetail
	3, // 1: moego.api.appointment.v1.CustomerPackageView.start_date:type_name -> google.type.Date
	3, // 2: moego.api.appointment.v1.CustomerPackageView.expiration_date:type_name -> google.type.Date
	2, // 3: moego.api.appointment.v1.CustomerPackageView.PackageDetail.services:type_name -> moego.api.appointment.v1.CustomerPackageView.PackageDetail.Service
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_api_appointment_v1_appointment_view_proto_init() }
func file_moego_api_appointment_v1_appointment_view_proto_init() {
	if File_moego_api_appointment_v1_appointment_view_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_appointment_v1_appointment_view_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerPackageView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_view_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerPackageView_PackageDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_view_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerPackageView_PackageDetail_Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_appointment_v1_appointment_view_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_appointment_v1_appointment_view_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_api_appointment_v1_appointment_view_proto_goTypes,
		DependencyIndexes: file_moego_api_appointment_v1_appointment_view_proto_depIdxs,
		MessageInfos:      file_moego_api_appointment_v1_appointment_view_proto_msgTypes,
	}.Build()
	File_moego_api_appointment_v1_appointment_view_proto = out.File
	file_moego_api_appointment_v1_appointment_view_proto_rawDesc = nil
	file_moego_api_appointment_v1_appointment_view_proto_goTypes = nil
	file_moego_api_appointment_v1_appointment_view_proto_depIdxs = nil
}
