// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/appointment/v1/appointment_schedule_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AppointmentScheduleServiceClient is the client API for AppointmentScheduleService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppointmentScheduleServiceClient interface {
	// Deprecated: Do not use.
	// Reschedule All-in-One (boarding&daycare&grooming) service, used to appointment card edit schedule
	// Replace to @see [ReschedulePetDetails](#moego.api.appointment.v1.AppointmentScheduleService.ReschedulePetDetails)
	RescheduleAllInOneService(ctx context.Context, in *RescheduleAllInOneServiceParams, opts ...grpc.CallOption) (*RescheduleAllInOneServiceResult, error)
	// Reschedule boarding service, used to lodging calendar
	// If there are multiple pets, the date and time of other pets will be changed in conjunction.
	RescheduleBoardingService(ctx context.Context, in *RescheduleBoardingServiceParams, opts ...grpc.CallOption) (*RescheduleBoardingServiceResult, error)
	// Reschedule daycare service.
	RescheduleDaycareService(ctx context.Context, in *RescheduleDaycareServiceParams, opts ...grpc.CallOption) (*RescheduleDaycareServiceResult, error)
	// Deprecated: Do not use.
	// Reschedule grooming service, used to grooming calendar
	// Replace to @see [RescheduleCalendarCard](#moego.api.appointment.v1.CalendarService.RescheduleCalendarCard)
	RescheduleGroomingService(ctx context.Context, in *RescheduleGroomingServiceParams, opts ...grpc.CallOption) (*RescheduleGroomingServiceResult, error)
	// Reschedule evaluation service
	RescheduleEvaluationService(ctx context.Context, in *RescheduleEvaluationServiceParams, opts ...grpc.CallOption) (*RescheduleEvaluationServiceResult, error)
	// lodging assign (boarding&daycare&evaluation) service
	LodgingAssign(ctx context.Context, in *LodgingAssignParams, opts ...grpc.CallOption) (*LodgingAssignResult, error)
	// Get pet's feeding and medication schedules
	GetPetFeedingMedicationSchedules(ctx context.Context, in *GetPetFeedingMedicationSchedulesParams, opts ...grpc.CallOption) (*GetPetFeedingMedicationSchedulesResult, error)
	// Update pet feeding and medication, used to appointment's Feeding & Medication tasks
	// Fully update the schedules of a single pet, including feeding and medication
	ReschedulePetFeedingMedication(ctx context.Context, in *ReschedulePetFeedingMedicationParams, opts ...grpc.CallOption) (*ReschedulePetFeedingMedicationResult, error)
	// Deprecated: Do not use.
	// Calculate appointment and pet's service schedules
	// Appointment: Start date, end date, start time, end time
	// Pet Service: Start date, end date, start time, end time
	CalculateAppointmentSchedule(ctx context.Context, in *CalculateAppointmentScheduleParams, opts ...grpc.CallOption) (*CalculateAppointmentScheduleResult, error)
	// Batch reschedule appointment by staff and date
	BatchRescheduleAppointment(ctx context.Context, in *BatchRescheduleAppointmentParams, opts ...grpc.CallOption) (*BatchRescheduleAppointmentResult, error)
	// Reschedule calendar card
	// For use with @see [ListDayCardsWithMixType](#moego.api.appointment.v1.CalendarService.ListDayCardsWithMixType)
	// reference: https://moego.atlassian.net/wiki/spaces/ET/pages/665387113/Calendar+card+split#%E4%BF%AE%E6%94%B9-RescheduleGroomingService-API
	// Reschedule object: card_type + appointment_id + pet_detail_ids
	// Target position: staff_id + start_date + start_time
	// Scope: repeat_modify_type
	// Stretch position: end_time
	RescheduleCalendarCard(ctx context.Context, in *RescheduleCalendarCardParams, opts ...grpc.CallOption) (*RescheduleCalendarCardResult, error)
	// Reschedule appointment
	RescheduleAppointment(ctx context.Context, in *RescheduleAppointmentParams, opts ...grpc.CallOption) (*RescheduleAppointmentResult, error)
	// Switch all pets start at the same time
	SwitchAllPetsStartAtSameTime(ctx context.Context, in *SwitchAllPetsStartAtSameTimeParams, opts ...grpc.CallOption) (*SwitchAllPetsStartAtSameTimeResult, error)
	// Reschedule pet details, Each modification is independent and does not affect other pet details
	// Allow scheduling can be done for any of the pet details of the appointment
	// Not allow to grooming only Modification
	ReschedulePetDetails(ctx context.Context, in *ReschedulePetDetailsParams, opts ...grpc.CallOption) (*ReschedulePetDetailsResult, error)
}

type appointmentScheduleServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppointmentScheduleServiceClient(cc grpc.ClientConnInterface) AppointmentScheduleServiceClient {
	return &appointmentScheduleServiceClient{cc}
}

// Deprecated: Do not use.
func (c *appointmentScheduleServiceClient) RescheduleAllInOneService(ctx context.Context, in *RescheduleAllInOneServiceParams, opts ...grpc.CallOption) (*RescheduleAllInOneServiceResult, error) {
	out := new(RescheduleAllInOneServiceResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentScheduleService/RescheduleAllInOneService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentScheduleServiceClient) RescheduleBoardingService(ctx context.Context, in *RescheduleBoardingServiceParams, opts ...grpc.CallOption) (*RescheduleBoardingServiceResult, error) {
	out := new(RescheduleBoardingServiceResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentScheduleService/RescheduleBoardingService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentScheduleServiceClient) RescheduleDaycareService(ctx context.Context, in *RescheduleDaycareServiceParams, opts ...grpc.CallOption) (*RescheduleDaycareServiceResult, error) {
	out := new(RescheduleDaycareServiceResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentScheduleService/RescheduleDaycareService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *appointmentScheduleServiceClient) RescheduleGroomingService(ctx context.Context, in *RescheduleGroomingServiceParams, opts ...grpc.CallOption) (*RescheduleGroomingServiceResult, error) {
	out := new(RescheduleGroomingServiceResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentScheduleService/RescheduleGroomingService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentScheduleServiceClient) RescheduleEvaluationService(ctx context.Context, in *RescheduleEvaluationServiceParams, opts ...grpc.CallOption) (*RescheduleEvaluationServiceResult, error) {
	out := new(RescheduleEvaluationServiceResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentScheduleService/RescheduleEvaluationService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentScheduleServiceClient) LodgingAssign(ctx context.Context, in *LodgingAssignParams, opts ...grpc.CallOption) (*LodgingAssignResult, error) {
	out := new(LodgingAssignResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentScheduleService/LodgingAssign", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentScheduleServiceClient) GetPetFeedingMedicationSchedules(ctx context.Context, in *GetPetFeedingMedicationSchedulesParams, opts ...grpc.CallOption) (*GetPetFeedingMedicationSchedulesResult, error) {
	out := new(GetPetFeedingMedicationSchedulesResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentScheduleService/GetPetFeedingMedicationSchedules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentScheduleServiceClient) ReschedulePetFeedingMedication(ctx context.Context, in *ReschedulePetFeedingMedicationParams, opts ...grpc.CallOption) (*ReschedulePetFeedingMedicationResult, error) {
	out := new(ReschedulePetFeedingMedicationResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentScheduleService/ReschedulePetFeedingMedication", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *appointmentScheduleServiceClient) CalculateAppointmentSchedule(ctx context.Context, in *CalculateAppointmentScheduleParams, opts ...grpc.CallOption) (*CalculateAppointmentScheduleResult, error) {
	out := new(CalculateAppointmentScheduleResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentScheduleService/CalculateAppointmentSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentScheduleServiceClient) BatchRescheduleAppointment(ctx context.Context, in *BatchRescheduleAppointmentParams, opts ...grpc.CallOption) (*BatchRescheduleAppointmentResult, error) {
	out := new(BatchRescheduleAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentScheduleService/BatchRescheduleAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentScheduleServiceClient) RescheduleCalendarCard(ctx context.Context, in *RescheduleCalendarCardParams, opts ...grpc.CallOption) (*RescheduleCalendarCardResult, error) {
	out := new(RescheduleCalendarCardResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentScheduleService/RescheduleCalendarCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentScheduleServiceClient) RescheduleAppointment(ctx context.Context, in *RescheduleAppointmentParams, opts ...grpc.CallOption) (*RescheduleAppointmentResult, error) {
	out := new(RescheduleAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentScheduleService/RescheduleAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentScheduleServiceClient) SwitchAllPetsStartAtSameTime(ctx context.Context, in *SwitchAllPetsStartAtSameTimeParams, opts ...grpc.CallOption) (*SwitchAllPetsStartAtSameTimeResult, error) {
	out := new(SwitchAllPetsStartAtSameTimeResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentScheduleService/SwitchAllPetsStartAtSameTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentScheduleServiceClient) ReschedulePetDetails(ctx context.Context, in *ReschedulePetDetailsParams, opts ...grpc.CallOption) (*ReschedulePetDetailsResult, error) {
	out := new(ReschedulePetDetailsResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentScheduleService/ReschedulePetDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppointmentScheduleServiceServer is the server API for AppointmentScheduleService service.
// All implementations must embed UnimplementedAppointmentScheduleServiceServer
// for forward compatibility
type AppointmentScheduleServiceServer interface {
	// Deprecated: Do not use.
	// Reschedule All-in-One (boarding&daycare&grooming) service, used to appointment card edit schedule
	// Replace to @see [ReschedulePetDetails](#moego.api.appointment.v1.AppointmentScheduleService.ReschedulePetDetails)
	RescheduleAllInOneService(context.Context, *RescheduleAllInOneServiceParams) (*RescheduleAllInOneServiceResult, error)
	// Reschedule boarding service, used to lodging calendar
	// If there are multiple pets, the date and time of other pets will be changed in conjunction.
	RescheduleBoardingService(context.Context, *RescheduleBoardingServiceParams) (*RescheduleBoardingServiceResult, error)
	// Reschedule daycare service.
	RescheduleDaycareService(context.Context, *RescheduleDaycareServiceParams) (*RescheduleDaycareServiceResult, error)
	// Deprecated: Do not use.
	// Reschedule grooming service, used to grooming calendar
	// Replace to @see [RescheduleCalendarCard](#moego.api.appointment.v1.CalendarService.RescheduleCalendarCard)
	RescheduleGroomingService(context.Context, *RescheduleGroomingServiceParams) (*RescheduleGroomingServiceResult, error)
	// Reschedule evaluation service
	RescheduleEvaluationService(context.Context, *RescheduleEvaluationServiceParams) (*RescheduleEvaluationServiceResult, error)
	// lodging assign (boarding&daycare&evaluation) service
	LodgingAssign(context.Context, *LodgingAssignParams) (*LodgingAssignResult, error)
	// Get pet's feeding and medication schedules
	GetPetFeedingMedicationSchedules(context.Context, *GetPetFeedingMedicationSchedulesParams) (*GetPetFeedingMedicationSchedulesResult, error)
	// Update pet feeding and medication, used to appointment's Feeding & Medication tasks
	// Fully update the schedules of a single pet, including feeding and medication
	ReschedulePetFeedingMedication(context.Context, *ReschedulePetFeedingMedicationParams) (*ReschedulePetFeedingMedicationResult, error)
	// Deprecated: Do not use.
	// Calculate appointment and pet's service schedules
	// Appointment: Start date, end date, start time, end time
	// Pet Service: Start date, end date, start time, end time
	CalculateAppointmentSchedule(context.Context, *CalculateAppointmentScheduleParams) (*CalculateAppointmentScheduleResult, error)
	// Batch reschedule appointment by staff and date
	BatchRescheduleAppointment(context.Context, *BatchRescheduleAppointmentParams) (*BatchRescheduleAppointmentResult, error)
	// Reschedule calendar card
	// For use with @see [ListDayCardsWithMixType](#moego.api.appointment.v1.CalendarService.ListDayCardsWithMixType)
	// reference: https://moego.atlassian.net/wiki/spaces/ET/pages/665387113/Calendar+card+split#%E4%BF%AE%E6%94%B9-RescheduleGroomingService-API
	// Reschedule object: card_type + appointment_id + pet_detail_ids
	// Target position: staff_id + start_date + start_time
	// Scope: repeat_modify_type
	// Stretch position: end_time
	RescheduleCalendarCard(context.Context, *RescheduleCalendarCardParams) (*RescheduleCalendarCardResult, error)
	// Reschedule appointment
	RescheduleAppointment(context.Context, *RescheduleAppointmentParams) (*RescheduleAppointmentResult, error)
	// Switch all pets start at the same time
	SwitchAllPetsStartAtSameTime(context.Context, *SwitchAllPetsStartAtSameTimeParams) (*SwitchAllPetsStartAtSameTimeResult, error)
	// Reschedule pet details, Each modification is independent and does not affect other pet details
	// Allow scheduling can be done for any of the pet details of the appointment
	// Not allow to grooming only Modification
	ReschedulePetDetails(context.Context, *ReschedulePetDetailsParams) (*ReschedulePetDetailsResult, error)
	mustEmbedUnimplementedAppointmentScheduleServiceServer()
}

// UnimplementedAppointmentScheduleServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAppointmentScheduleServiceServer struct {
}

func (UnimplementedAppointmentScheduleServiceServer) RescheduleAllInOneService(context.Context, *RescheduleAllInOneServiceParams) (*RescheduleAllInOneServiceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RescheduleAllInOneService not implemented")
}
func (UnimplementedAppointmentScheduleServiceServer) RescheduleBoardingService(context.Context, *RescheduleBoardingServiceParams) (*RescheduleBoardingServiceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RescheduleBoardingService not implemented")
}
func (UnimplementedAppointmentScheduleServiceServer) RescheduleDaycareService(context.Context, *RescheduleDaycareServiceParams) (*RescheduleDaycareServiceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RescheduleDaycareService not implemented")
}
func (UnimplementedAppointmentScheduleServiceServer) RescheduleGroomingService(context.Context, *RescheduleGroomingServiceParams) (*RescheduleGroomingServiceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RescheduleGroomingService not implemented")
}
func (UnimplementedAppointmentScheduleServiceServer) RescheduleEvaluationService(context.Context, *RescheduleEvaluationServiceParams) (*RescheduleEvaluationServiceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RescheduleEvaluationService not implemented")
}
func (UnimplementedAppointmentScheduleServiceServer) LodgingAssign(context.Context, *LodgingAssignParams) (*LodgingAssignResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LodgingAssign not implemented")
}
func (UnimplementedAppointmentScheduleServiceServer) GetPetFeedingMedicationSchedules(context.Context, *GetPetFeedingMedicationSchedulesParams) (*GetPetFeedingMedicationSchedulesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetFeedingMedicationSchedules not implemented")
}
func (UnimplementedAppointmentScheduleServiceServer) ReschedulePetFeedingMedication(context.Context, *ReschedulePetFeedingMedicationParams) (*ReschedulePetFeedingMedicationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReschedulePetFeedingMedication not implemented")
}
func (UnimplementedAppointmentScheduleServiceServer) CalculateAppointmentSchedule(context.Context, *CalculateAppointmentScheduleParams) (*CalculateAppointmentScheduleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateAppointmentSchedule not implemented")
}
func (UnimplementedAppointmentScheduleServiceServer) BatchRescheduleAppointment(context.Context, *BatchRescheduleAppointmentParams) (*BatchRescheduleAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchRescheduleAppointment not implemented")
}
func (UnimplementedAppointmentScheduleServiceServer) RescheduleCalendarCard(context.Context, *RescheduleCalendarCardParams) (*RescheduleCalendarCardResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RescheduleCalendarCard not implemented")
}
func (UnimplementedAppointmentScheduleServiceServer) RescheduleAppointment(context.Context, *RescheduleAppointmentParams) (*RescheduleAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RescheduleAppointment not implemented")
}
func (UnimplementedAppointmentScheduleServiceServer) SwitchAllPetsStartAtSameTime(context.Context, *SwitchAllPetsStartAtSameTimeParams) (*SwitchAllPetsStartAtSameTimeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SwitchAllPetsStartAtSameTime not implemented")
}
func (UnimplementedAppointmentScheduleServiceServer) ReschedulePetDetails(context.Context, *ReschedulePetDetailsParams) (*ReschedulePetDetailsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReschedulePetDetails not implemented")
}
func (UnimplementedAppointmentScheduleServiceServer) mustEmbedUnimplementedAppointmentScheduleServiceServer() {
}

// UnsafeAppointmentScheduleServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppointmentScheduleServiceServer will
// result in compilation errors.
type UnsafeAppointmentScheduleServiceServer interface {
	mustEmbedUnimplementedAppointmentScheduleServiceServer()
}

func RegisterAppointmentScheduleServiceServer(s grpc.ServiceRegistrar, srv AppointmentScheduleServiceServer) {
	s.RegisterService(&AppointmentScheduleService_ServiceDesc, srv)
}

func _AppointmentScheduleService_RescheduleAllInOneService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RescheduleAllInOneServiceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentScheduleServiceServer).RescheduleAllInOneService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentScheduleService/RescheduleAllInOneService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentScheduleServiceServer).RescheduleAllInOneService(ctx, req.(*RescheduleAllInOneServiceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentScheduleService_RescheduleBoardingService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RescheduleBoardingServiceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentScheduleServiceServer).RescheduleBoardingService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentScheduleService/RescheduleBoardingService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentScheduleServiceServer).RescheduleBoardingService(ctx, req.(*RescheduleBoardingServiceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentScheduleService_RescheduleDaycareService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RescheduleDaycareServiceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentScheduleServiceServer).RescheduleDaycareService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentScheduleService/RescheduleDaycareService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentScheduleServiceServer).RescheduleDaycareService(ctx, req.(*RescheduleDaycareServiceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentScheduleService_RescheduleGroomingService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RescheduleGroomingServiceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentScheduleServiceServer).RescheduleGroomingService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentScheduleService/RescheduleGroomingService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentScheduleServiceServer).RescheduleGroomingService(ctx, req.(*RescheduleGroomingServiceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentScheduleService_RescheduleEvaluationService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RescheduleEvaluationServiceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentScheduleServiceServer).RescheduleEvaluationService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentScheduleService/RescheduleEvaluationService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentScheduleServiceServer).RescheduleEvaluationService(ctx, req.(*RescheduleEvaluationServiceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentScheduleService_LodgingAssign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LodgingAssignParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentScheduleServiceServer).LodgingAssign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentScheduleService/LodgingAssign",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentScheduleServiceServer).LodgingAssign(ctx, req.(*LodgingAssignParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentScheduleService_GetPetFeedingMedicationSchedules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetFeedingMedicationSchedulesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentScheduleServiceServer).GetPetFeedingMedicationSchedules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentScheduleService/GetPetFeedingMedicationSchedules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentScheduleServiceServer).GetPetFeedingMedicationSchedules(ctx, req.(*GetPetFeedingMedicationSchedulesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentScheduleService_ReschedulePetFeedingMedication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReschedulePetFeedingMedicationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentScheduleServiceServer).ReschedulePetFeedingMedication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentScheduleService/ReschedulePetFeedingMedication",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentScheduleServiceServer).ReschedulePetFeedingMedication(ctx, req.(*ReschedulePetFeedingMedicationParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentScheduleService_CalculateAppointmentSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculateAppointmentScheduleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentScheduleServiceServer).CalculateAppointmentSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentScheduleService/CalculateAppointmentSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentScheduleServiceServer).CalculateAppointmentSchedule(ctx, req.(*CalculateAppointmentScheduleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentScheduleService_BatchRescheduleAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRescheduleAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentScheduleServiceServer).BatchRescheduleAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentScheduleService/BatchRescheduleAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentScheduleServiceServer).BatchRescheduleAppointment(ctx, req.(*BatchRescheduleAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentScheduleService_RescheduleCalendarCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RescheduleCalendarCardParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentScheduleServiceServer).RescheduleCalendarCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentScheduleService/RescheduleCalendarCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentScheduleServiceServer).RescheduleCalendarCard(ctx, req.(*RescheduleCalendarCardParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentScheduleService_RescheduleAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RescheduleAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentScheduleServiceServer).RescheduleAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentScheduleService/RescheduleAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentScheduleServiceServer).RescheduleAppointment(ctx, req.(*RescheduleAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentScheduleService_SwitchAllPetsStartAtSameTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchAllPetsStartAtSameTimeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentScheduleServiceServer).SwitchAllPetsStartAtSameTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentScheduleService/SwitchAllPetsStartAtSameTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentScheduleServiceServer).SwitchAllPetsStartAtSameTime(ctx, req.(*SwitchAllPetsStartAtSameTimeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentScheduleService_ReschedulePetDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReschedulePetDetailsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentScheduleServiceServer).ReschedulePetDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentScheduleService/ReschedulePetDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentScheduleServiceServer).ReschedulePetDetails(ctx, req.(*ReschedulePetDetailsParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AppointmentScheduleService_ServiceDesc is the grpc.ServiceDesc for AppointmentScheduleService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppointmentScheduleService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.appointment.v1.AppointmentScheduleService",
	HandlerType: (*AppointmentScheduleServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RescheduleAllInOneService",
			Handler:    _AppointmentScheduleService_RescheduleAllInOneService_Handler,
		},
		{
			MethodName: "RescheduleBoardingService",
			Handler:    _AppointmentScheduleService_RescheduleBoardingService_Handler,
		},
		{
			MethodName: "RescheduleDaycareService",
			Handler:    _AppointmentScheduleService_RescheduleDaycareService_Handler,
		},
		{
			MethodName: "RescheduleGroomingService",
			Handler:    _AppointmentScheduleService_RescheduleGroomingService_Handler,
		},
		{
			MethodName: "RescheduleEvaluationService",
			Handler:    _AppointmentScheduleService_RescheduleEvaluationService_Handler,
		},
		{
			MethodName: "LodgingAssign",
			Handler:    _AppointmentScheduleService_LodgingAssign_Handler,
		},
		{
			MethodName: "GetPetFeedingMedicationSchedules",
			Handler:    _AppointmentScheduleService_GetPetFeedingMedicationSchedules_Handler,
		},
		{
			MethodName: "ReschedulePetFeedingMedication",
			Handler:    _AppointmentScheduleService_ReschedulePetFeedingMedication_Handler,
		},
		{
			MethodName: "CalculateAppointmentSchedule",
			Handler:    _AppointmentScheduleService_CalculateAppointmentSchedule_Handler,
		},
		{
			MethodName: "BatchRescheduleAppointment",
			Handler:    _AppointmentScheduleService_BatchRescheduleAppointment_Handler,
		},
		{
			MethodName: "RescheduleCalendarCard",
			Handler:    _AppointmentScheduleService_RescheduleCalendarCard_Handler,
		},
		{
			MethodName: "RescheduleAppointment",
			Handler:    _AppointmentScheduleService_RescheduleAppointment_Handler,
		},
		{
			MethodName: "SwitchAllPetsStartAtSameTime",
			Handler:    _AppointmentScheduleService_SwitchAllPetsStartAtSameTime_Handler,
		},
		{
			MethodName: "ReschedulePetDetails",
			Handler:    _AppointmentScheduleService_ReschedulePetDetails_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/appointment/v1/appointment_schedule_api.proto",
}
