// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/payment/v2/payment_onboard_api.proto

package paymentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PaymentOnboardServiceClient is the client API for PaymentOnboardService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentOnboardServiceClient interface {
	// Get onboard information for specific channel.
	GetOnboard(ctx context.Context, in *GetOnboardParams, opts ...grpc.CallOption) (*GetOnboardResult, error)
	// Proceed onboard for specific channel.
	ProceedOnboard(ctx context.Context, in *ProceedOnboardParams, opts ...grpc.CallOption) (*ProceedOnboardResult, error)
	// User confirms onboard is finished. Should be only called in ONBOARDED status.
	ConfirmOnboard(ctx context.Context, in *ConfirmOnboardParams, opts ...grpc.CallOption) (*ConfirmOnboardResult, error)
	// Get channel account detail.
	GetChannelAccountDetail(ctx context.Context, in *GetChannelAccountDetailParams, opts ...grpc.CallOption) (*GetChannelAccountDetailResult, error)
}

type paymentOnboardServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentOnboardServiceClient(cc grpc.ClientConnInterface) PaymentOnboardServiceClient {
	return &paymentOnboardServiceClient{cc}
}

func (c *paymentOnboardServiceClient) GetOnboard(ctx context.Context, in *GetOnboardParams, opts ...grpc.CallOption) (*GetOnboardResult, error) {
	out := new(GetOnboardResult)
	err := c.cc.Invoke(ctx, "/moego.api.payment.v2.PaymentOnboardService/GetOnboard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentOnboardServiceClient) ProceedOnboard(ctx context.Context, in *ProceedOnboardParams, opts ...grpc.CallOption) (*ProceedOnboardResult, error) {
	out := new(ProceedOnboardResult)
	err := c.cc.Invoke(ctx, "/moego.api.payment.v2.PaymentOnboardService/ProceedOnboard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentOnboardServiceClient) ConfirmOnboard(ctx context.Context, in *ConfirmOnboardParams, opts ...grpc.CallOption) (*ConfirmOnboardResult, error) {
	out := new(ConfirmOnboardResult)
	err := c.cc.Invoke(ctx, "/moego.api.payment.v2.PaymentOnboardService/ConfirmOnboard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentOnboardServiceClient) GetChannelAccountDetail(ctx context.Context, in *GetChannelAccountDetailParams, opts ...grpc.CallOption) (*GetChannelAccountDetailResult, error) {
	out := new(GetChannelAccountDetailResult)
	err := c.cc.Invoke(ctx, "/moego.api.payment.v2.PaymentOnboardService/GetChannelAccountDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentOnboardServiceServer is the server API for PaymentOnboardService service.
// All implementations must embed UnimplementedPaymentOnboardServiceServer
// for forward compatibility
type PaymentOnboardServiceServer interface {
	// Get onboard information for specific channel.
	GetOnboard(context.Context, *GetOnboardParams) (*GetOnboardResult, error)
	// Proceed onboard for specific channel.
	ProceedOnboard(context.Context, *ProceedOnboardParams) (*ProceedOnboardResult, error)
	// User confirms onboard is finished. Should be only called in ONBOARDED status.
	ConfirmOnboard(context.Context, *ConfirmOnboardParams) (*ConfirmOnboardResult, error)
	// Get channel account detail.
	GetChannelAccountDetail(context.Context, *GetChannelAccountDetailParams) (*GetChannelAccountDetailResult, error)
	mustEmbedUnimplementedPaymentOnboardServiceServer()
}

// UnimplementedPaymentOnboardServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPaymentOnboardServiceServer struct {
}

func (UnimplementedPaymentOnboardServiceServer) GetOnboard(context.Context, *GetOnboardParams) (*GetOnboardResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnboard not implemented")
}
func (UnimplementedPaymentOnboardServiceServer) ProceedOnboard(context.Context, *ProceedOnboardParams) (*ProceedOnboardResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProceedOnboard not implemented")
}
func (UnimplementedPaymentOnboardServiceServer) ConfirmOnboard(context.Context, *ConfirmOnboardParams) (*ConfirmOnboardResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmOnboard not implemented")
}
func (UnimplementedPaymentOnboardServiceServer) GetChannelAccountDetail(context.Context, *GetChannelAccountDetailParams) (*GetChannelAccountDetailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChannelAccountDetail not implemented")
}
func (UnimplementedPaymentOnboardServiceServer) mustEmbedUnimplementedPaymentOnboardServiceServer() {}

// UnsafePaymentOnboardServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentOnboardServiceServer will
// result in compilation errors.
type UnsafePaymentOnboardServiceServer interface {
	mustEmbedUnimplementedPaymentOnboardServiceServer()
}

func RegisterPaymentOnboardServiceServer(s grpc.ServiceRegistrar, srv PaymentOnboardServiceServer) {
	s.RegisterService(&PaymentOnboardService_ServiceDesc, srv)
}

func _PaymentOnboardService_GetOnboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnboardParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentOnboardServiceServer).GetOnboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.payment.v2.PaymentOnboardService/GetOnboard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentOnboardServiceServer).GetOnboard(ctx, req.(*GetOnboardParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentOnboardService_ProceedOnboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProceedOnboardParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentOnboardServiceServer).ProceedOnboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.payment.v2.PaymentOnboardService/ProceedOnboard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentOnboardServiceServer).ProceedOnboard(ctx, req.(*ProceedOnboardParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentOnboardService_ConfirmOnboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmOnboardParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentOnboardServiceServer).ConfirmOnboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.payment.v2.PaymentOnboardService/ConfirmOnboard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentOnboardServiceServer).ConfirmOnboard(ctx, req.(*ConfirmOnboardParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentOnboardService_GetChannelAccountDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelAccountDetailParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentOnboardServiceServer).GetChannelAccountDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.payment.v2.PaymentOnboardService/GetChannelAccountDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentOnboardServiceServer).GetChannelAccountDetail(ctx, req.(*GetChannelAccountDetailParams))
	}
	return interceptor(ctx, in, info, handler)
}

// PaymentOnboardService_ServiceDesc is the grpc.ServiceDesc for PaymentOnboardService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PaymentOnboardService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.payment.v2.PaymentOnboardService",
	HandlerType: (*PaymentOnboardServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOnboard",
			Handler:    _PaymentOnboardService_GetOnboard_Handler,
		},
		{
			MethodName: "ProceedOnboard",
			Handler:    _PaymentOnboardService_ProceedOnboard_Handler,
		},
		{
			MethodName: "ConfirmOnboard",
			Handler:    _PaymentOnboardService_ConfirmOnboard_Handler,
		},
		{
			MethodName: "GetChannelAccountDetail",
			Handler:    _PaymentOnboardService_GetChannelAccountDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/payment/v2/payment_onboard_api.proto",
}
