# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/utils/v1/time_of_day_interval.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/utils/v1/time_of_day_interval.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import timeofday_pb2 as google_dot_type_dot_timeofday__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)moego/utils/v1/time_of_day_interval.proto\x12\x0emoego.utils.v1\x1a\x1bgoogle/type/timeofday.proto\"k\n\x11TimeOfDayInterval\x12,\n\x05start\x18\x01 \x01(\x0b\x32\x16.google.type.TimeOfDayR\x05start\x12(\n\x03\x65nd\x18\x02 \x01(\x0b\x32\x16.google.type.TimeOfDayR\x03\x65ndBg\n\x16\x63om.moego.idl.utils.v1P\x01ZKgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1;utilsV1b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.utils.v1.time_of_day_interval_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\026com.moego.idl.utils.v1P\001ZKgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1;utilsV1'
  _globals['_TIMEOFDAYINTERVAL']._serialized_start=90
  _globals['_TIMEOFDAYINTERVAL']._serialized_end=197
# @@protoc_insertion_point(module_scope)
