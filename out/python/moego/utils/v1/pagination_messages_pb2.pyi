from validate import validate_pb2 as _validate_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class PaginationRequest(_message.Message):
    __slots__ = ("page_size", "page_no")
    PAGE_SIZE_FIELD_NUMBER: _ClassVar[int]
    PAGE_NO_FIELD_NUMBER: _ClassVar[int]
    page_size: int
    page_no: int
    def __init__(self, page_size: _Optional[int] = ..., page_no: _Optional[int] = ...) -> None: ...

class PaginationResponse(_message.Message):
    __slots__ = ("total", "page_size", "page_no")
    TOTAL_FIELD_NUMBER: _ClassVar[int]
    PAGE_SIZE_FIELD_NUMBER: _ClassVar[int]
    PAGE_NO_FIELD_NUMBER: _ClassVar[int]
    total: int
    page_size: int
    page_no: int
    def __init__(self, total: _Optional[int] = ..., page_size: _Optional[int] = ..., page_no: _Optional[int] = ...) -> None: ...
