# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/offering/v1/service_enum.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/offering/v1/service_enum.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+moego/models/offering/v1/service_enum.proto\x12\x18moego.models.offering.v1*\x8f\x01\n\x0fServiceItemType\x12!\n\x1dSERVICE_ITEM_TYPE_UNSPECIFIED\x10\x00\x12\x0c\n\x08GROOMING\x10\x01\x12\x0c\n\x08\x42OARDING\x10\x02\x12\x0b\n\x07\x44\x41YCARE\x10\x03\x12\x0e\n\nEVALUATION\x10\x04\x12\x0f\n\x0b\x44OG_WALKING\x10\x05\x12\x0f\n\x0bGROUP_CLASS\x10\x06*q\n\x10ServicePriceUnit\x12\"\n\x1eSERVICE_PRICE_UNIT_UNSPECIFIED\x10\x00\x12\x0f\n\x0bPER_SESSION\x10\x01\x12\r\n\tPER_NIGHT\x10\x02\x12\x0c\n\x08PER_HOUR\x10\x03\x12\x0b\n\x07PER_DAY\x10\x04*C\n\x0bServiceType\x12\x1c\n\x18SERVICE_TYPE_UNSPECIFIED\x10\x00\x12\x0b\n\x07SERVICE\x10\x01\x12\t\n\x05\x41\x44\x44ON\x10\x02*}\n\x10ServiceScopeType\x12\"\n\x1eSERVICE_SCOPE_TYPE_UNSPECIFIED\x10\x00\x12\r\n\tONLY_THIS\x10\x01\x12\x0f\n\x0b\x44O_NOT_SAVE\x10\x02\x12\x13\n\x0fTHIS_AND_FUTURE\x10\x03\x12\x10\n\x0c\x41LL_UPCOMING\x10\x04*a\n\x13ServiceOverrideType\x12%\n!SERVICE_OVERRIDE_TYPE_UNSPECIFIED\x10\x00\x12\x0c\n\x08LOCATION\x10\x01\x12\n\n\x06\x43LIENT\x10\x02\x12\t\n\x05STAFF\x10\x03*^\n\x12ServiceOrderByType\x12%\n!SERVICE_ORDER_BY_TYPE_UNSPECIFIED\x10\x00\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x01\x12\x14\n\x10MAX_DURATION_ASC\x10\x02*\xce\x01\n\x08\x44\x61teType\x12\x19\n\x15\x44\x41TE_TYPE_UNSPECIFIED\x10\x00\x12!\n\x1d\x45VERY_DAY_EXCEPT_CHECKOUT_DAY\x10\x01\x12\x11\n\rSPECIFIC_DATE\x10\x02\x12\x0e\n\nDATE_POINT\x10\x03\x12\"\n\x1e\x45VERY_DAY_INCLUDE_CHECKOUT_DAY\x10\x04\x12 \n\x1c\x45VERY_DAY_EXCEPT_CHECKIN_DAY\x10\x05\x12\x0c\n\x08LAST_DAY\x10\x06\x12\r\n\tFIRST_DAY\x10\x07\x42~\n com.moego.idl.models.offering.v1P\x01ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.offering.v1.service_enum_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.models.offering.v1P\001ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb'
  _globals['_SERVICEITEMTYPE']._serialized_start=74
  _globals['_SERVICEITEMTYPE']._serialized_end=217
  _globals['_SERVICEPRICEUNIT']._serialized_start=219
  _globals['_SERVICEPRICEUNIT']._serialized_end=332
  _globals['_SERVICETYPE']._serialized_start=334
  _globals['_SERVICETYPE']._serialized_end=401
  _globals['_SERVICESCOPETYPE']._serialized_start=403
  _globals['_SERVICESCOPETYPE']._serialized_end=528
  _globals['_SERVICEOVERRIDETYPE']._serialized_start=530
  _globals['_SERVICEOVERRIDETYPE']._serialized_end=627
  _globals['_SERVICEORDERBYTYPE']._serialized_start=629
  _globals['_SERVICEORDERBYTYPE']._serialized_end=723
  _globals['_DATETYPE']._serialized_start=726
  _globals['_DATETYPE']._serialized_end=932
# @@protoc_insertion_point(module_scope)
