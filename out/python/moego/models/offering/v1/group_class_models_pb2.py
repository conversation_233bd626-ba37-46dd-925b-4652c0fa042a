# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/offering/v1/group_class_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/offering/v1/group_class_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.type import datetime_pb2 as google_dot_type_dot_datetime__pb2
from google.type import dayofweek_pb2 as google_dot_type_dot_dayofweek__pb2
from google.type import interval_pb2 as google_dot_type_dot_interval__pb2
from google.type import money_pb2 as google_dot_type_dot_money__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1moego/models/offering/v1/group_class_models.proto\x12\x18moego.models.offering.v1\x1a\x1egoogle/protobuf/duration.proto\x1a\x1agoogle/type/datetime.proto\x1a\x1bgoogle/type/dayofweek.proto\x1a\x1agoogle/type/interval.proto\x1a\x17google/type/money.proto\"\xa0\n\n\x12GroupClassInstance\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n\ncompany_id\x18\x02 \x01(\x03R\tcompanyId\x12\x1f\n\x0b\x62usiness_id\x18\x03 \x01(\x03R\nbusinessId\x12$\n\x0egroup_class_id\x18\x04 \x01(\x03R\x0cgroupClassId\x12\x12\n\x04name\x18\x05 \x01(\tR\x04name\x12\x19\n\x08staff_id\x18\x06 \x01(\x03R\x07staffId\x12\x34\n\nstart_time\x18\x07 \x01(\x0b\x32\x15.google.type.DateTimeR\tstartTime\x12\x1a\n\x08\x63\x61pacity\x18\x08 \x01(\x05R\x08\x63\x61pacity\x12W\n\noccurrence\x18\t \x01(\x0b\x32\x37.moego.models.offering.v1.GroupClassInstance.OccurrenceR\noccurrence\x12K\n\x06status\x18\n \x01(\x0e\x32\x33.moego.models.offering.v1.GroupClassInstance.StatusR\x06status\x12(\n\x05price\x18\x0b \x01(\x0b\x32\x12.google.type.MoneyR\x05price\x1a\xf7\x05\n\nOccurrence\x12X\n\x04type\x18\x01 \x01(\x0e\x32\x44.moego.models.offering.v1.GroupClassInstance.Occurrence.IntervalTypeR\x04type\x12\x1a\n\x08interval\x18\x02 \x01(\x05R\x08interval\x12O\n\x03\x64\x61y\x18\x03 \x01(\x0b\x32;.moego.models.offering.v1.GroupClassInstance.Occurrence.DayH\x00R\x03\x64\x61y\x12R\n\x04week\x18\x04 \x01(\x0b\x32<.moego.models.offering.v1.GroupClassInstance.Occurrence.WeekH\x00R\x04week\x12U\n\x05month\x18\x05 \x01(\x0b\x32=.moego.models.offering.v1.GroupClassInstance.Occurrence.MonthH\x00R\x05month\x1a\x05\n\x03\x44\x61y\x1a@\n\x04Week\x12\x38\n\x0c\x64\x61y_of_weeks\x18\x01 \x03(\x0e\x32\x16.google.type.DayOfWeekR\ndayOfWeeks\x1a\xd1\x01\n\x05Month\x12r\n\x0eselection_type\x18\x01 \x01(\x0e\x32K.moego.models.offering.v1.GroupClassInstance.Occurrence.Month.SelectionTypeR\rselectionType\"T\n\rSelectionType\x12\x1e\n\x1aSELECTION_TYPE_UNSPECIFIED\x10\x00\x12\x0b\n\x07ON_25TH\x10\x01\x12\x16\n\x12ON_THIRD_WEDNESDAY\x10\x02\"I\n\x0cIntervalType\x12\x1b\n\x17REPEAT_TYPE_UNSPECIFIED\x10\x00\x12\x07\n\x03\x44\x41Y\x10\x01\x12\x08\n\x04WEEK\x10\x02\x12\t\n\x05MONTH\x10\x03\x42\x0f\n\rinterval_unit\"I\n\x06Status\x12\x16\n\x12STATUS_UNSPECIFIED\x10\x00\x12\x0c\n\x08UPCOMING\x10\x01\x12\x0f\n\x0bIN_PROGRESS\x10\x02\x12\x08\n\x04PAST\x10\x03\"\xbc\x03\n\x11GroupClassSession\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n\ncompany_id\x18\x02 \x01(\x03R\tcompanyId\x12\x1f\n\x0b\x62usiness_id\x18\x03 \x01(\x03R\nbusinessId\x12\x35\n\x17group_class_instance_id\x18\x04 \x01(\x03R\x14groupClassInstanceId\x12\x31\n\x08interval\x18\x05 \x01(\x0b\x32\x15.google.type.IntervalR\x08interval\x12\x35\n\x08\x64uration\x18\x06 \x01(\x0b\x32\x19.google.protobuf.DurationR\x08\x64uration\x12\x1f\n\x0bis_modified\x18\x07 \x01(\x08R\nisModified\x12J\n\x06status\x18\x08 \x01(\x0e\x32\x32.moego.models.offering.v1.GroupClassSession.StatusR\x06status\"I\n\x06Status\x12\x16\n\x12STATUS_UNSPECIFIED\x10\x00\x12\x0c\n\x08UPCOMING\x10\x01\x12\x0f\n\x0bIN_PROGRESS\x10\x02\x12\x08\n\x04PAST\x10\x03\"\xc2\x01\n\x0fGroupClassModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12 \n\x0b\x64\x65scription\x18\x03 \x01(\tR\x0b\x64\x65scription\x12\x14\n\x05price\x18\x04 \x01(\x01R\x05price\x12!\n\x0cnum_sessions\x18\x05 \x01(\x05R\x0bnumSessions\x12\x30\n\x14\x64uration_session_min\x18\x06 \x01(\x05R\x12\x64urationSessionMinB~\n com.moego.idl.models.offering.v1P\x01ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.offering.v1.group_class_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.models.offering.v1P\001ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb'
  _globals['_GROUPCLASSINSTANCE']._serialized_start=222
  _globals['_GROUPCLASSINSTANCE']._serialized_end=1534
  _globals['_GROUPCLASSINSTANCE_OCCURRENCE']._serialized_start=700
  _globals['_GROUPCLASSINSTANCE_OCCURRENCE']._serialized_end=1459
  _globals['_GROUPCLASSINSTANCE_OCCURRENCE_DAY']._serialized_start=1084
  _globals['_GROUPCLASSINSTANCE_OCCURRENCE_DAY']._serialized_end=1089
  _globals['_GROUPCLASSINSTANCE_OCCURRENCE_WEEK']._serialized_start=1091
  _globals['_GROUPCLASSINSTANCE_OCCURRENCE_WEEK']._serialized_end=1155
  _globals['_GROUPCLASSINSTANCE_OCCURRENCE_MONTH']._serialized_start=1158
  _globals['_GROUPCLASSINSTANCE_OCCURRENCE_MONTH']._serialized_end=1367
  _globals['_GROUPCLASSINSTANCE_OCCURRENCE_MONTH_SELECTIONTYPE']._serialized_start=1283
  _globals['_GROUPCLASSINSTANCE_OCCURRENCE_MONTH_SELECTIONTYPE']._serialized_end=1367
  _globals['_GROUPCLASSINSTANCE_OCCURRENCE_INTERVALTYPE']._serialized_start=1369
  _globals['_GROUPCLASSINSTANCE_OCCURRENCE_INTERVALTYPE']._serialized_end=1442
  _globals['_GROUPCLASSINSTANCE_STATUS']._serialized_start=1461
  _globals['_GROUPCLASSINSTANCE_STATUS']._serialized_end=1534
  _globals['_GROUPCLASSSESSION']._serialized_start=1537
  _globals['_GROUPCLASSSESSION']._serialized_end=1981
  _globals['_GROUPCLASSSESSION_STATUS']._serialized_start=1461
  _globals['_GROUPCLASSSESSION_STATUS']._serialized_end=1534
  _globals['_GROUPCLASSMODEL']._serialized_start=1984
  _globals['_GROUPCLASSMODEL']._serialized_end=2178
# @@protoc_insertion_point(module_scope)
