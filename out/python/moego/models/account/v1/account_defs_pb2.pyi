from moego.models.account.v1 import account_enums_pb2 as _account_enums_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class NamespaceDef(_message.Message):
    __slots__ = ("type", "id")
    TYPE_FIELD_NUMBER: _ClassVar[int]
    ID_FIELD_NUMBER: _ClassVar[int]
    type: _account_enums_pb2.AccountNamespaceType
    id: int
    def __init__(self, type: _Optional[_Union[_account_enums_pb2.AccountNamespaceType, str]] = ..., id: _Optional[int] = ...) -> None: ...
