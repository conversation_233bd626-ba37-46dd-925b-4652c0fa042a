# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/fulfillment/v1/staff_time_slot_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/fulfillment/v1/staff_time_slot_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8moego/models/fulfillment/v1/staff_time_slot_models.proto\x12\x1bmoego.models.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a+moego/models/offering/v1/service_enum.proto\"\x9b\x05\n\x12StaffTimeSlotModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n\ncompany_id\x18\x02 \x01(\x03R\tcompanyId\x12\x1f\n\x0b\x62usiness_id\x18\x03 \x01(\x03R\nbusinessId\x12%\n\x0e\x66ulfillment_id\x18\x04 \x01(\x03R\rfulfillmentId\x12\x46\n\tcare_type\x18\x05 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x08\x63\x61reType\x12\x1b\n\tdetail_id\x18\x06 \x01(\x03R\x08\x64\x65tailId\x12+\n\x12order_line_item_id\x18\x07 \x01(\x03R\x0forderLineItemId\x12\x19\n\x08staff_id\x18\x08 \x01(\x03R\x07staffId\x12\x15\n\x06pet_id\x18\t \x01(\x03R\x05petId\x12\x1f\n\x0b\x63ustomer_id\x18\n \x01(\x03R\ncustomerId\x12\x41\n\x0estart_datetime\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.TimestampR\rstartDatetime\x12!\n\x0c\x65nd_datetime\x18\x0c \x01(\x03R\x0b\x65ndDatetime\x12\x39\n\ncreated_at\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x39\n\nupdated_at\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tupdatedAt\x12>\n\ndeleted_at\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x00R\tdeletedAt\x88\x01\x01\x42\r\n\x0b_deleted_atB\x87\x01\n#com.moego.idl.models.fulfillment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1;fulfillmentpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.fulfillment.v1.staff_time_slot_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.models.fulfillment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1;fulfillmentpb'
  _globals['_STAFFTIMESLOTMODEL']._serialized_start=168
  _globals['_STAFFTIMESLOTMODEL']._serialized_end=835
# @@protoc_insertion_point(module_scope)
