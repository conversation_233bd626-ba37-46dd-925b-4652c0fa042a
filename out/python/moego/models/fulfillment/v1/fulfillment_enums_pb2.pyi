from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from typing import ClassVar as _ClassVar

DESCRIPTOR: _descriptor.FileDescriptor

class Status(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    STATUS_UNSPECIFIED: _ClassVar[Status]
    UNCONFIRMED: _ClassVar[Status]
    CONFIRMED: _ClassVar[Status]
    FINISHED: _ClassVar[Status]
    CANCELED: _ClassVar[Status]
    READY: _ClassVar[Status]
    CHECK_IN: _ClassVar[Status]
    PENDING_PAYMENT: _ClassVar[Status]
    COMPLETED: _ClassVar[Status]

class Source(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    SOURCE_UNSPECIFIED: _ClassVar[Source]
    WEB: _ClassVar[Source]
    OB: _ClassVar[Source]
    ANDROID: _ClassVar[Source]
    IOS: _ClassVar[Source]
    AUTO_DM: _ClassVar[Source]
    GOOGLE_CALENDAR: _ClassVar[Source]
    OPEN_API: _ClassVar[Source]

class DateType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    DATE_TYPE_UNSPECIFIED: _ClassVar[DateType]
    EVERYDAY: _ClassVar[DateType]
    SPECIFIC_DATES: _ClassVar[DateType]
    DATE_POINT: _ClassVar[DateType]
    EVERYDAY_INCLUDE_CHECKOUT_DAY: _ClassVar[DateType]

class ActionType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    ACTION_TYPE_UNSPECIFIED: _ClassVar[ActionType]
    STATUS_CHANGE: _ClassVar[ActionType]
    TIME_CHANGE: _ClassVar[ActionType]
    COLOR_CODE_CHANGE: _ClassVar[ActionType]
    EDIT_PET_AND_SERVICES: _ClassVar[ActionType]
    DELETE_PET: _ClassVar[ActionType]
STATUS_UNSPECIFIED: Status
UNCONFIRMED: Status
CONFIRMED: Status
FINISHED: Status
CANCELED: Status
READY: Status
CHECK_IN: Status
PENDING_PAYMENT: Status
COMPLETED: Status
SOURCE_UNSPECIFIED: Source
WEB: Source
OB: Source
ANDROID: Source
IOS: Source
AUTO_DM: Source
GOOGLE_CALENDAR: Source
OPEN_API: Source
DATE_TYPE_UNSPECIFIED: DateType
EVERYDAY: DateType
SPECIFIC_DATES: DateType
DATE_POINT: DateType
EVERYDAY_INCLUDE_CHECKOUT_DAY: DateType
ACTION_TYPE_UNSPECIFIED: ActionType
STATUS_CHANGE: ActionType
TIME_CHANGE: ActionType
COLOR_CODE_CHANGE: ActionType
EDIT_PET_AND_SERVICES: ActionType
DELETE_PET: ActionType
