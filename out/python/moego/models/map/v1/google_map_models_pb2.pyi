from google.geo.type import viewport_pb2 as _viewport_pb2
from google.type import latlng_pb2 as _latlng_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GooglePlace(_message.Message):
    __slots__ = ("id", "name", "types", "formatted_address", "short_formatted_address", "address_components", "plus_code", "location", "viewport", "photos", "adr_format_address", "attributions")
    class AddressComponent(_message.Message):
        __slots__ = ("long_text", "short_text", "types", "language_code")
        LONG_TEXT_FIELD_NUMBER: _ClassVar[int]
        SHORT_TEXT_FIELD_NUMBER: _ClassVar[int]
        TYPES_FIELD_NUMBER: _ClassVar[int]
        LANGUAGE_CODE_FIELD_NUMBER: _ClassVar[int]
        long_text: str
        short_text: str
        types: _containers.RepeatedScalarFieldContainer[str]
        language_code: str
        def __init__(self, long_text: _Optional[str] = ..., short_text: _Optional[str] = ..., types: _Optional[_Iterable[str]] = ..., language_code: _Optional[str] = ...) -> None: ...
    class PlusCode(_message.Message):
        __slots__ = ("global_code", "compound_code")
        GLOBAL_CODE_FIELD_NUMBER: _ClassVar[int]
        COMPOUND_CODE_FIELD_NUMBER: _ClassVar[int]
        global_code: str
        compound_code: str
        def __init__(self, global_code: _Optional[str] = ..., compound_code: _Optional[str] = ...) -> None: ...
    class Attribution(_message.Message):
        __slots__ = ("provider", "provider_uri")
        PROVIDER_FIELD_NUMBER: _ClassVar[int]
        PROVIDER_URI_FIELD_NUMBER: _ClassVar[int]
        provider: str
        provider_uri: str
        def __init__(self, provider: _Optional[str] = ..., provider_uri: _Optional[str] = ...) -> None: ...
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    TYPES_FIELD_NUMBER: _ClassVar[int]
    FORMATTED_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    SHORT_FORMATTED_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    ADDRESS_COMPONENTS_FIELD_NUMBER: _ClassVar[int]
    PLUS_CODE_FIELD_NUMBER: _ClassVar[int]
    LOCATION_FIELD_NUMBER: _ClassVar[int]
    VIEWPORT_FIELD_NUMBER: _ClassVar[int]
    PHOTOS_FIELD_NUMBER: _ClassVar[int]
    ADR_FORMAT_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    ATTRIBUTIONS_FIELD_NUMBER: _ClassVar[int]
    id: str
    name: str
    types: _containers.RepeatedScalarFieldContainer[str]
    formatted_address: str
    short_formatted_address: str
    address_components: _containers.RepeatedCompositeFieldContainer[GooglePlace.AddressComponent]
    plus_code: GooglePlace.PlusCode
    location: _latlng_pb2.LatLng
    viewport: _viewport_pb2.Viewport
    photos: _containers.RepeatedCompositeFieldContainer[Photo]
    adr_format_address: str
    attributions: _containers.RepeatedCompositeFieldContainer[GooglePlace.Attribution]
    def __init__(self, id: _Optional[str] = ..., name: _Optional[str] = ..., types: _Optional[_Iterable[str]] = ..., formatted_address: _Optional[str] = ..., short_formatted_address: _Optional[str] = ..., address_components: _Optional[_Iterable[_Union[GooglePlace.AddressComponent, _Mapping]]] = ..., plus_code: _Optional[_Union[GooglePlace.PlusCode, _Mapping]] = ..., location: _Optional[_Union[_latlng_pb2.LatLng, _Mapping]] = ..., viewport: _Optional[_Union[_viewport_pb2.Viewport, _Mapping]] = ..., photos: _Optional[_Iterable[_Union[Photo, _Mapping]]] = ..., adr_format_address: _Optional[str] = ..., attributions: _Optional[_Iterable[_Union[GooglePlace.Attribution, _Mapping]]] = ...) -> None: ...

class Photo(_message.Message):
    __slots__ = ("name", "width_px", "height_px", "author_attributions")
    NAME_FIELD_NUMBER: _ClassVar[int]
    WIDTH_PX_FIELD_NUMBER: _ClassVar[int]
    HEIGHT_PX_FIELD_NUMBER: _ClassVar[int]
    AUTHOR_ATTRIBUTIONS_FIELD_NUMBER: _ClassVar[int]
    name: str
    width_px: int
    height_px: int
    author_attributions: _containers.RepeatedCompositeFieldContainer[AuthorAttribution]
    def __init__(self, name: _Optional[str] = ..., width_px: _Optional[int] = ..., height_px: _Optional[int] = ..., author_attributions: _Optional[_Iterable[_Union[AuthorAttribution, _Mapping]]] = ...) -> None: ...

class AuthorAttribution(_message.Message):
    __slots__ = ("display_name", "uri", "photo_uri")
    DISPLAY_NAME_FIELD_NUMBER: _ClassVar[int]
    URI_FIELD_NUMBER: _ClassVar[int]
    PHOTO_URI_FIELD_NUMBER: _ClassVar[int]
    display_name: str
    uri: str
    photo_uri: str
    def __init__(self, display_name: _Optional[str] = ..., uri: _Optional[str] = ..., photo_uri: _Optional[str] = ...) -> None: ...
