# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/appointment/v1/appointment_tracking.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/appointment/v1/appointment_tracking.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import latlng_pb2 as google_dot_type_dot_latlng__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n6moego/models/appointment/v1/appointment_tracking.proto\x12\x1bmoego.models.appointment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x18google/type/latlng.proto\"\xea\t\n\x13\x41ppointmentTracking\x12%\n\x0e\x61ppointment_id\x18\x01 \x01(\x03R\rappointmentId\x12O\n\x10\x63ustomer_address\x18\x02 \x01(\x0b\x32$.moego.models.appointment.v1.AddressR\x0f\x63ustomerAddress\x12\x64\n\x15staff_location_status\x18\x03 \x01(\x0e\x32\x30.moego.models.appointment.v1.StaffLocationStatusR\x13staffLocationStatus\x12\x39\n\x19location_sharing_staff_id\x18\x04 \x01(\x03R\x16locationSharingStaffId\x12;\n\x1alocation_sharing_device_id\x18\x05 \x01(\tR\x17locationSharingDeviceId\x12G\n\x12last_in_transit_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0flastInTransitAt\x12\x38\n\x18\x65stimated_travel_seconds\x18\x07 \x01(\x03R\x16\x65stimatedTravelSeconds\x12\x44\n\x10last_estimate_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0elastEstimateAt\x12\'\n\x0ftravel_distance\x18\t \x01(\x03R\x0etravelDistance\x12Q\n\x0e\x64\x65layed_status\x18\n \x01(\x0e\x32*.moego.models.appointment.v1.DelayedStatusR\rdelayedStatus\x12^\n-estimated_travel_seconds_from_last_in_transit\x18\x0b \x01(\x03R\'estimatedTravelSecondsFromLastInTransit\x12j\n%from_last_in_transit_last_estimate_at\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x1f\x66romLastInTransitLastEstimateAt\x12\x42\n\x1elast_in_transit_appointment_id\x18\r \x01(\x03R\x1alastInTransitAppointmentId\x12M\n$travel_distance_from_last_in_transit\x18\x0e \x01(\x03R\x1ftravelDistanceFromLastInTransit\x12w\n#delayed_status_from_last_in_transit\x18\x0f \x01(\x0e\x32*.moego.models.appointment.v1.DelayedStatusR\x1e\x64\x65layedStatusFromLastInTransit\x12N\n\rstaff_address\x18\x10 \x01(\x0b\x32$.moego.models.appointment.v1.AddressH\x00R\x0cstaffAddress\x88\x01\x01\x42\x10\n\x0e_staff_address\"\xee\t\n\x17\x41ppointmentTrackingView\x12%\n\x0e\x61ppointment_id\x18\x01 \x01(\x03R\rappointmentId\x12O\n\x10\x63ustomer_address\x18\x02 \x01(\x0b\x32$.moego.models.appointment.v1.AddressR\x0f\x63ustomerAddress\x12\x64\n\x15staff_location_status\x18\x03 \x01(\x0e\x32\x30.moego.models.appointment.v1.StaffLocationStatusR\x13staffLocationStatus\x12\x39\n\x19location_sharing_staff_id\x18\x04 \x01(\x03R\x16locationSharingStaffId\x12;\n\x1alocation_sharing_device_id\x18\x05 \x01(\tR\x17locationSharingDeviceId\x12G\n\x12last_in_transit_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0flastInTransitAt\x12\x38\n\x18\x65stimated_travel_seconds\x18\x07 \x01(\x03R\x16\x65stimatedTravelSeconds\x12\x44\n\x10last_estimate_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0elastEstimateAt\x12\'\n\x0ftravel_distance\x18\t \x01(\x03R\x0etravelDistance\x12Q\n\x0e\x64\x65layed_status\x18\n \x01(\x0e\x32*.moego.models.appointment.v1.DelayedStatusR\rdelayedStatus\x12^\n-estimated_travel_seconds_from_last_in_transit\x18\x0b \x01(\x03R\'estimatedTravelSecondsFromLastInTransit\x12j\n%from_last_in_transit_last_estimate_at\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x1f\x66romLastInTransitLastEstimateAt\x12\x42\n\x1elast_in_transit_appointment_id\x18\r \x01(\x03R\x1alastInTransitAppointmentId\x12M\n$travel_distance_from_last_in_transit\x18\x0e \x01(\x03R\x1ftravelDistanceFromLastInTransit\x12w\n#delayed_status_from_last_in_transit\x18\x0f \x01(\x0e\x32*.moego.models.appointment.v1.DelayedStatusR\x1e\x64\x65layedStatusFromLastInTransit\x12N\n\rstaff_address\x18\x10 \x01(\x0b\x32$.moego.models.appointment.v1.AddressH\x00R\x0cstaffAddress\x88\x01\x01\x42\x10\n\x0e_staff_address\"\xe8\x01\n\x07\x41\x64\x64ress\x12\x1a\n\x08\x61\x64\x64ress1\x18\x01 \x01(\tR\x08\x61\x64\x64ress1\x12\x1a\n\x08\x61\x64\x64ress2\x18\x02 \x01(\tR\x08\x61\x64\x64ress2\x12\x18\n\x07\x63ountry\x18\x03 \x01(\tR\x07\x63ountry\x12\x12\n\x04\x63ity\x18\x04 \x01(\tR\x04\x63ity\x12\x14\n\x05state\x18\x05 \x01(\tR\x05state\x12\x18\n\x07zipcode\x18\x06 \x01(\tR\x07zipcode\x12\x38\n\ncoordinate\x18\x07 \x01(\x0b\x32\x13.google.type.LatLngH\x00R\ncoordinate\x88\x01\x01\x42\r\n\x0b_coordinate\"\xc4\x03\n\x1cUpdateAppointmentTrackingDef\x12>\n\x19location_sharing_staff_id\x18\x01 \x01(\x03H\x00R\x16locationSharingStaffId\x88\x01\x01\x12i\n\x15staff_location_status\x18\x02 \x01(\x0e\x32\x30.moego.models.appointment.v1.StaffLocationStatusH\x01R\x13staffLocationStatus\x88\x01\x01\x12N\n\rstaff_address\x18\x03 \x01(\x0b\x32$.moego.models.appointment.v1.AddressH\x02R\x0cstaffAddress\x88\x01\x01\x12@\n\x1alocation_sharing_device_id\x18\x04 \x01(\tH\x03R\x17locationSharingDeviceId\x88\x01\x01\x42\x1c\n\x1a_location_sharing_staff_idB\x18\n\x16_staff_location_statusB\x10\n\x0e_staff_addressB\x1d\n\x1b_location_sharing_device_id*\x7f\n\x13StaffLocationStatus\x12%\n!STAFF_LOCATION_STATUS_UNSPECIFIED\x10\x00\x12\x0f\n\x0bNOT_STARTED\x10\x01\x12\x0e\n\nIN_TRANSIT\x10\x02\x12\x13\n\x0fSHARING_STOPPED\x10\x03\x12\x0b\n\x07\x41RRIVED\x10\x04*i\n\rDelayedStatus\x12\x1e\n\x1a\x44\x45LAYED_STATUS_UNSPECIFIED\x10\x00\x12\x0b\n\x07ON_TIME\x10\x01\x12\x14\n\x10\x44\x45LAYED_SLIGHTLY\x10\x02\x12\x15\n\x11\x44\x45LAYED_SERIOUSLY\x10\x03\x42\x87\x01\n#com.moego.idl.models.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.appointment.v1.appointment_tracking_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.models.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb'
  _globals['_STAFFLOCATIONSTATUS']._serialized_start=3362
  _globals['_STAFFLOCATIONSTATUS']._serialized_end=3489
  _globals['_DELAYEDSTATUS']._serialized_start=3491
  _globals['_DELAYEDSTATUS']._serialized_end=3596
  _globals['_APPOINTMENTTRACKING']._serialized_start=147
  _globals['_APPOINTMENTTRACKING']._serialized_end=1405
  _globals['_APPOINTMENTTRACKINGVIEW']._serialized_start=1408
  _globals['_APPOINTMENTTRACKINGVIEW']._serialized_end=2670
  _globals['_ADDRESS']._serialized_start=2673
  _globals['_ADDRESS']._serialized_end=2905
  _globals['_UPDATEAPPOINTMENTTRACKINGDEF']._serialized_start=2908
  _globals['_UPDATEAPPOINTMENTTRACKINGDEF']._serialized_end=3360
# @@protoc_insertion_point(module_scope)
