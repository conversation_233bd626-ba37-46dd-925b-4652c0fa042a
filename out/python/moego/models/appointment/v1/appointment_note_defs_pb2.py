# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/appointment/v1/appointment_note_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/appointment/v1/appointment_note_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.appointment.v1 import appointment_note_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__note__enums__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n7moego/models/appointment/v1/appointment_note_defs.proto\x12\x1bmoego.models.appointment.v1\x1a\x38moego/models/appointment/v1/appointment_note_enums.proto\x1a\x17validate/validate.proto\"\x8b\x01\n\x18\x41ppointmentNoteCreateDef\x12\x1d\n\x04note\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x18\x80\x80@R\x04note\x12P\n\x04type\x18\x02 \x01(\x0e\x32\x30.moego.models.appointment.v1.AppointmentNoteTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x04type\"R\n\x18\x41ppointmentNoteUpdateDef\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12\x1d\n\x04note\x18\x02 \x01(\tB\t\xfa\x42\x06r\x04\x18\x80\x80@R\x04noteB\x87\x01\n#com.moego.idl.models.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.appointment.v1.appointment_note_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.models.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb'
  _globals['_APPOINTMENTNOTECREATEDEF'].fields_by_name['note']._loaded_options = None
  _globals['_APPOINTMENTNOTECREATEDEF'].fields_by_name['note']._serialized_options = b'\372B\006r\004\030\200\200@'
  _globals['_APPOINTMENTNOTECREATEDEF'].fields_by_name['type']._loaded_options = None
  _globals['_APPOINTMENTNOTECREATEDEF'].fields_by_name['type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_APPOINTMENTNOTEUPDATEDEF'].fields_by_name['id']._loaded_options = None
  _globals['_APPOINTMENTNOTEUPDATEDEF'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_APPOINTMENTNOTEUPDATEDEF'].fields_by_name['note']._loaded_options = None
  _globals['_APPOINTMENTNOTEUPDATEDEF'].fields_by_name['note']._serialized_options = b'\372B\006r\004\030\200\200@'
  _globals['_APPOINTMENTNOTECREATEDEF']._serialized_start=172
  _globals['_APPOINTMENTNOTECREATEDEF']._serialized_end=311
  _globals['_APPOINTMENTNOTEUPDATEDEF']._serialized_start=313
  _globals['_APPOINTMENTNOTEUPDATEDEF']._serialized_end=395
# @@protoc_insertion_point(module_scope)
