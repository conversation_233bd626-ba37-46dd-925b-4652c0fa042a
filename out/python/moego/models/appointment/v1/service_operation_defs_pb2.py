# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/appointment/v1/service_operation_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/appointment/v1/service_operation_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8moego/models/appointment/v1/service_operation_defs.proto\x12\x1bmoego.models.appointment.v1\x1a\x17validate/validate.proto\"\x91\x02\n\x13ServiceOperationDef\x12\"\n\x08staff_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12/\n\x0eoperation_name\x18\x02 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x96\x01R\roperationName\x12\x1d\n\nstart_time\x18\x03 \x01(\x05R\tstartTime\x12&\n\x08\x64uration\x18\x04 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\x08\x64uration\x12\x38\n\x0bprice_ratio\x18\x05 \x01(\x01\x42\x17\xfa\x42\x14\x12\x12\x19\x00\x00\x00\x00\x00\x00\xf0?)\x00\x00\x00\x00\x00\x00\x00\x00R\npriceRatio\x12$\n\x05price\x18\x06 \x01(\x01\x42\x0e\xfa\x42\x0b\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00R\x05price\"i\n\x1bServiceOperationCalendarDef\x12\"\n\x08staff_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12&\n\x08\x64uration\x18\x02 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\x08\x64uration\"\xb5\x02\n#ServiceOperationCalendarScheduleDef\x12\"\n\x08staff_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12&\n\x08\x64uration\x18\x02 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\x08\x64uration\x12\x39\n\nstart_date\x18\x0b \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\tstartDate\x12\x35\n\x08\x65nd_date\x18\x0c \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\x07\x65ndDate\x12)\n\nstart_time\x18\r \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\tstartTime\x12%\n\x08\x65nd_time\x18\x0e \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\x07\x65ndTimeB\x87\x01\n#com.moego.idl.models.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.appointment.v1.service_operation_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.models.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb'
  _globals['_SERVICEOPERATIONDEF'].fields_by_name['staff_id']._loaded_options = None
  _globals['_SERVICEOPERATIONDEF'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SERVICEOPERATIONDEF'].fields_by_name['operation_name']._loaded_options = None
  _globals['_SERVICEOPERATIONDEF'].fields_by_name['operation_name']._serialized_options = b'\372B\005r\003\030\226\001'
  _globals['_SERVICEOPERATIONDEF'].fields_by_name['duration']._loaded_options = None
  _globals['_SERVICEOPERATIONDEF'].fields_by_name['duration']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SERVICEOPERATIONDEF'].fields_by_name['price_ratio']._loaded_options = None
  _globals['_SERVICEOPERATIONDEF'].fields_by_name['price_ratio']._serialized_options = b'\372B\024\022\022\031\000\000\000\000\000\000\360?)\000\000\000\000\000\000\000\000'
  _globals['_SERVICEOPERATIONDEF'].fields_by_name['price']._loaded_options = None
  _globals['_SERVICEOPERATIONDEF'].fields_by_name['price']._serialized_options = b'\372B\013\022\t)\000\000\000\000\000\000\000\000'
  _globals['_SERVICEOPERATIONCALENDARDEF'].fields_by_name['staff_id']._loaded_options = None
  _globals['_SERVICEOPERATIONCALENDARDEF'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SERVICEOPERATIONCALENDARDEF'].fields_by_name['duration']._loaded_options = None
  _globals['_SERVICEOPERATIONCALENDARDEF'].fields_by_name['duration']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SERVICEOPERATIONCALENDARSCHEDULEDEF'].fields_by_name['staff_id']._loaded_options = None
  _globals['_SERVICEOPERATIONCALENDARSCHEDULEDEF'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SERVICEOPERATIONCALENDARSCHEDULEDEF'].fields_by_name['duration']._loaded_options = None
  _globals['_SERVICEOPERATIONCALENDARSCHEDULEDEF'].fields_by_name['duration']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SERVICEOPERATIONCALENDARSCHEDULEDEF'].fields_by_name['start_date']._loaded_options = None
  _globals['_SERVICEOPERATIONCALENDARSCHEDULEDEF'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_SERVICEOPERATIONCALENDARSCHEDULEDEF'].fields_by_name['end_date']._loaded_options = None
  _globals['_SERVICEOPERATIONCALENDARSCHEDULEDEF'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_SERVICEOPERATIONCALENDARSCHEDULEDEF'].fields_by_name['start_time']._loaded_options = None
  _globals['_SERVICEOPERATIONCALENDARSCHEDULEDEF'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SERVICEOPERATIONCALENDARSCHEDULEDEF'].fields_by_name['end_time']._loaded_options = None
  _globals['_SERVICEOPERATIONCALENDARSCHEDULEDEF'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SERVICEOPERATIONDEF']._serialized_start=115
  _globals['_SERVICEOPERATIONDEF']._serialized_end=388
  _globals['_SERVICEOPERATIONCALENDARDEF']._serialized_start=390
  _globals['_SERVICEOPERATIONCALENDARDEF']._serialized_end=495
  _globals['_SERVICEOPERATIONCALENDARSCHEDULEDEF']._serialized_start=498
  _globals['_SERVICEOPERATIONCALENDARSCHEDULEDEF']._serialized_end=807
# @@protoc_insertion_point(module_scope)
