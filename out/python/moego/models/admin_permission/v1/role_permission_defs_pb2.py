# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/admin_permission/v1/role_permission_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/admin_permission/v1/role_permission_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n;moego/models/admin_permission/v1/role_permission_defs.proto\x12 moego.models.admin_permission.v1\x1a\x17validate/validate.proto\"/\n\x0e\x45qConditionDef\x12\x1d\n\x05value\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32R\x05value\"8\n\x0eInConditionDef\x12&\n\x06values\x18\x01 \x03(\tB\x0e\xfa\x42\x0b\x92\x01\x08\x10\x64\"\x04r\x02\x18\x32R\x06values\"-\n\x11MatchConditionDef\x12\x18\n\x07pattern\x18\x01 \x01(\tR\x07pattern\"\xb5\x02\n\tFilterDef\x12\'\n\tattribute\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x32R\tattribute\x12\x18\n\x07reverse\x18\x02 \x01(\x08R\x07reverse\x12\x42\n\x02\x65q\x18\x03 \x01(\x0b\x32\x30.moego.models.admin_permission.v1.EqConditionDefH\x00R\x02\x65q\x12\x42\n\x02in\x18\x04 \x01(\x0b\x32\x30.moego.models.admin_permission.v1.InConditionDefH\x00R\x02in\x12K\n\x05match\x18\x05 \x01(\x0b\x32\x33.moego.models.admin_permission.v1.MatchConditionDefH\x00R\x05matchB\x10\n\tcondition\x12\x03\xf8\x42\x01\x42\x95\x01\n(com.moego.idl.models.admin_permission.v1P\x01Zggithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/admin_permission/v1;adminpermissionpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.admin_permission.v1.role_permission_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n(com.moego.idl.models.admin_permission.v1P\001Zggithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/admin_permission/v1;adminpermissionpb'
  _globals['_EQCONDITIONDEF'].fields_by_name['value']._loaded_options = None
  _globals['_EQCONDITIONDEF'].fields_by_name['value']._serialized_options = b'\372B\004r\002\0302'
  _globals['_INCONDITIONDEF'].fields_by_name['values']._loaded_options = None
  _globals['_INCONDITIONDEF'].fields_by_name['values']._serialized_options = b'\372B\013\222\001\010\020d\"\004r\002\0302'
  _globals['_FILTERDEF'].oneofs_by_name['condition']._loaded_options = None
  _globals['_FILTERDEF'].oneofs_by_name['condition']._serialized_options = b'\370B\001'
  _globals['_FILTERDEF'].fields_by_name['attribute']._loaded_options = None
  _globals['_FILTERDEF'].fields_by_name['attribute']._serialized_options = b'\372B\006r\004\020\001\0302'
  _globals['_EQCONDITIONDEF']._serialized_start=122
  _globals['_EQCONDITIONDEF']._serialized_end=169
  _globals['_INCONDITIONDEF']._serialized_start=171
  _globals['_INCONDITIONDEF']._serialized_end=227
  _globals['_MATCHCONDITIONDEF']._serialized_start=229
  _globals['_MATCHCONDITIONDEF']._serialized_end=274
  _globals['_FILTERDEF']._serialized_start=277
  _globals['_FILTERDEF']._serialized_end=586
# @@protoc_insertion_point(module_scope)
