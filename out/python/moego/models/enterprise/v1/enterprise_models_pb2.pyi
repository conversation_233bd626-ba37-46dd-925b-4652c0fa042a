from google.type import latlng_pb2 as _latlng_pb2
from moego.models.enterprise.v1 import country_defs_pb2 as _country_defs_pb2
from moego.models.enterprise.v1 import enterprise_enums_pb2 as _enterprise_enums_pb2
from moego.models.enterprise.v1 import time_zone_pb2 as _time_zone_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class EnterpriseModel(_message.Message):
    __slots__ = ("id", "name", "account_id", "email", "theme_color", "currency_code", "currency_symbol", "logo_path", "date_format_type", "time_format_type", "unit_of_weight_type", "unit_of_distance_type", "notification_sound_enable", "country", "time_zone", "address", "source")
    class Source(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        NORMALLY_ADD: _ClassVar[EnterpriseModel.Source]
        MANUALLY_ADD: _ClassVar[EnterpriseModel.Source]
        SPLIT_COMPANY: _ClassVar[EnterpriseModel.Source]
        DEMO_ENTERPRISE: _ClassVar[EnterpriseModel.Source]
    NORMALLY_ADD: EnterpriseModel.Source
    MANUALLY_ADD: EnterpriseModel.Source
    SPLIT_COMPANY: EnterpriseModel.Source
    DEMO_ENTERPRISE: EnterpriseModel.Source
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    ACCOUNT_ID_FIELD_NUMBER: _ClassVar[int]
    EMAIL_FIELD_NUMBER: _ClassVar[int]
    THEME_COLOR_FIELD_NUMBER: _ClassVar[int]
    CURRENCY_CODE_FIELD_NUMBER: _ClassVar[int]
    CURRENCY_SYMBOL_FIELD_NUMBER: _ClassVar[int]
    LOGO_PATH_FIELD_NUMBER: _ClassVar[int]
    DATE_FORMAT_TYPE_FIELD_NUMBER: _ClassVar[int]
    TIME_FORMAT_TYPE_FIELD_NUMBER: _ClassVar[int]
    UNIT_OF_WEIGHT_TYPE_FIELD_NUMBER: _ClassVar[int]
    UNIT_OF_DISTANCE_TYPE_FIELD_NUMBER: _ClassVar[int]
    NOTIFICATION_SOUND_ENABLE_FIELD_NUMBER: _ClassVar[int]
    COUNTRY_FIELD_NUMBER: _ClassVar[int]
    TIME_ZONE_FIELD_NUMBER: _ClassVar[int]
    ADDRESS_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    account_id: int
    email: str
    theme_color: str
    currency_code: str
    currency_symbol: str
    logo_path: str
    date_format_type: _enterprise_enums_pb2.DateFormat
    time_format_type: _enterprise_enums_pb2.TimeFormat
    unit_of_weight_type: _enterprise_enums_pb2.WeightUnit
    unit_of_distance_type: _enterprise_enums_pb2.DistanceUnit
    notification_sound_enable: bool
    country: _country_defs_pb2.CountryDef
    time_zone: _time_zone_pb2.TimeZone
    address: AddressDef
    source: EnterpriseModel.Source
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ..., account_id: _Optional[int] = ..., email: _Optional[str] = ..., theme_color: _Optional[str] = ..., currency_code: _Optional[str] = ..., currency_symbol: _Optional[str] = ..., logo_path: _Optional[str] = ..., date_format_type: _Optional[_Union[_enterprise_enums_pb2.DateFormat, str]] = ..., time_format_type: _Optional[_Union[_enterprise_enums_pb2.TimeFormat, str]] = ..., unit_of_weight_type: _Optional[_Union[_enterprise_enums_pb2.WeightUnit, str]] = ..., unit_of_distance_type: _Optional[_Union[_enterprise_enums_pb2.DistanceUnit, str]] = ..., notification_sound_enable: bool = ..., country: _Optional[_Union[_country_defs_pb2.CountryDef, _Mapping]] = ..., time_zone: _Optional[_Union[_time_zone_pb2.TimeZone, _Mapping]] = ..., address: _Optional[_Union[AddressDef, _Mapping]] = ..., source: _Optional[_Union[EnterpriseModel.Source, str]] = ...) -> None: ...

class AddressDef(_message.Message):
    __slots__ = ("address1", "address2", "city", "state", "zipcode", "country", "coordinate")
    ADDRESS1_FIELD_NUMBER: _ClassVar[int]
    ADDRESS2_FIELD_NUMBER: _ClassVar[int]
    CITY_FIELD_NUMBER: _ClassVar[int]
    STATE_FIELD_NUMBER: _ClassVar[int]
    ZIPCODE_FIELD_NUMBER: _ClassVar[int]
    COUNTRY_FIELD_NUMBER: _ClassVar[int]
    COORDINATE_FIELD_NUMBER: _ClassVar[int]
    address1: str
    address2: str
    city: str
    state: str
    zipcode: str
    country: str
    coordinate: _latlng_pb2.LatLng
    def __init__(self, address1: _Optional[str] = ..., address2: _Optional[str] = ..., city: _Optional[str] = ..., state: _Optional[str] = ..., zipcode: _Optional[str] = ..., country: _Optional[str] = ..., coordinate: _Optional[_Union[_latlng_pb2.LatLng, _Mapping]] = ...) -> None: ...
