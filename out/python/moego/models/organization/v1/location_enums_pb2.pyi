from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from typing import ClassVar as _ClassVar

DESCRIPTOR: _descriptor.FileDescriptor

class BusinessType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    MOBILE: _ClassVar[BusinessType]
    SALON: _ClassVar[BusinessType]
    HYBRID: _ClassVar[BusinessType]

class BusinessSourceFromType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    BUSINESS_SOURCE_FROM_UNSPECIFIED: _ClassVar[BusinessSourceFromType]
    APP_ANDROID: _ClassVar[BusinessSourceFromType]
    APP_IOS: _ClassVar[BusinessSourceFromType]
    WEB_ANDROID: _ClassVar[BusinessSourceFromType]
    WEB_IOS: _ClassVar[BusinessSourceFromType]
    WEB_DESKTOP: _ClassVar[BusinessSourceFromType]
    ENTERPRISE_TENANT: _ClassVar[BusinessSourceFromType]

class SourceType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    SOURCE_TYPE_UNSPECIFIED: _ClassVar[SourceType]
    OTHER: _ClassVar[SourceType]
    FACEBOOK: _ClassVar[SourceType]
    INTERNET_SEARCH: _ClassVar[SourceType]
    CAPTERRA: _ClassVar[SourceType]
    SQUARE: _ClassVar[SourceType]
    EXPO: _ClassVar[SourceType]
MOBILE: BusinessType
SALON: BusinessType
HYBRID: BusinessType
BUSINESS_SOURCE_FROM_UNSPECIFIED: BusinessSourceFromType
APP_ANDROID: BusinessSourceFromType
APP_IOS: BusinessSourceFromType
WEB_ANDROID: BusinessSourceFromType
WEB_IOS: BusinessSourceFromType
WEB_DESKTOP: BusinessSourceFromType
ENTERPRISE_TENANT: BusinessSourceFromType
SOURCE_TYPE_UNSPECIFIED: SourceType
OTHER: SourceType
FACEBOOK: SourceType
INTERNET_SEARCH: SourceType
CAPTERRA: SourceType
SQUARE: SourceType
EXPO: SourceType
