from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class EnterpriseModel(_message.Message):
    __slots__ = ("id", "name", "account_id", "email", "source", "created_at", "updated_at")
    class Source(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        NORMALLY_ADD: _ClassVar[EnterpriseModel.Source]
        MANUALLY_ADD: _ClassVar[EnterpriseModel.Source]
        SPLIT_COMPANY: _ClassVar[EnterpriseModel.Source]
    NORMALLY_ADD: EnterpriseModel.Source
    MANUALLY_ADD: EnterpriseModel.Source
    SPLIT_COMPANY: EnterpriseModel.Source
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    ACCOUNT_ID_FIELD_NUMBER: _ClassVar[int]
    EMAIL_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    UPDATED_AT_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    account_id: int
    email: str
    source: EnterpriseModel.Source
    created_at: _timestamp_pb2.Timestamp
    updated_at: _timestamp_pb2.Timestamp
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ..., account_id: _Optional[int] = ..., email: _Optional[str] = ..., source: _Optional[_Union[EnterpriseModel.Source, str]] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., updated_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...
