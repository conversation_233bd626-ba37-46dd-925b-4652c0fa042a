# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/organization/v1/close_date_enums.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/organization/v1/close_date_enums.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n3moego/models/organization/v1/close_date_enums.proto\x12\x1cmoego.models.organization.v1*I\n\rCloseDateType\x12\x1f\n\x1b\x43LOSE_DATE_TYPE_UNSPECIFIED\x10\x00\x12\n\n\x06NORMAL\x10\x01\x12\x0b\n\x07HOLIDAY\x10\x02\x42\x8a\x01\n$com.moego.idl.models.organization.v1P\x01Z`github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.organization.v1.close_date_enums_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.models.organization.v1P\001Z`github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb'
  _globals['_CLOSEDATETYPE']._serialized_start=85
  _globals['_CLOSEDATETYPE']._serialized_end=158
# @@protoc_insertion_point(module_scope)
