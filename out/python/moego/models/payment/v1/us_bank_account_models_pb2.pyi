from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class UsBankAccountModel(_message.Message):
    __slots__ = ("id", "account_holder_type", "account_type", "bank_name", "financial_connections_account", "fingerprint", "last4", "routing_number", "status_details")
    ID_FIELD_NUMBER: _ClassVar[int]
    ACCOUNT_HOLDER_TYPE_FIELD_NUMBER: _ClassVar[int]
    ACCOUNT_TYPE_FIELD_NUMBER: _ClassVar[int]
    BANK_NAME_FIELD_NUMBER: _ClassVar[int]
    FINANCIAL_CONNECTIONS_ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    FINGERPRINT_FIELD_NUMBER: _ClassVar[int]
    LAST4_FIELD_NUMBER: _ClassVar[int]
    ROUTING_NUMBER_FIELD_NUMBER: _ClassVar[int]
    STATUS_DETAILS_FIELD_NUMBER: _ClassVar[int]
    id: str
    account_holder_type: str
    account_type: str
    bank_name: str
    financial_connections_account: str
    fingerprint: str
    last4: str
    routing_number: str
    status_details: str
    def __init__(self, id: _Optional[str] = ..., account_holder_type: _Optional[str] = ..., account_type: _Optional[str] = ..., bank_name: _Optional[str] = ..., financial_connections_account: _Optional[str] = ..., fingerprint: _Optional[str] = ..., last4: _Optional[str] = ..., routing_number: _Optional[str] = ..., status_details: _Optional[str] = ...) -> None: ...
