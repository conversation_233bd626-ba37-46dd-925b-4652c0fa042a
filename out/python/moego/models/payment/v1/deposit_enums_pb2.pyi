from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from typing import ClassVar as _ClassVar

DESCRIPTOR: _descriptor.FileDescriptor

class DepositStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    DEPOSIT_STATUS_UNSPECIFIED: _ClassVar[DepositStatus]
    DEPOSIT_STATUS_PROCESSING: _ClassVar[DepositStatus]
    DEPOSIT_STATUS_REQUIRE_PAYMENT_METHOD: _ClassVar[DepositStatus]
    DEPOSIT_STATUS_REQUIRE_CONFIRM: _ClassVar[DepositStatus]
    DEPOSIT_STATUS_REQUIRE_CAPTURE: _ClassVar[DepositStatus]
    DEPOSIT_STATUS_PROCESSING_CAPTURE: _ClassVar[DepositStatus]
    DEPOSIT_STATUS_PAID: _ClassVar[DepositStatus]
    DEPOSIT_STATUS_REFUNDED: _ClassVar[DepositStatus]
    DEPOSIT_STATUS_CANCEL: _ClassVar[DepositStatus]
    DEPOSIT_STATUS_FAILED: _ClassVar[DepositStatus]
DEPOSIT_STATUS_UNSPECIFIED: DepositStatus
DEPOSIT_STATUS_PROCESSING: DepositStatus
DEPOSIT_STATUS_REQUIRE_PAYMENT_METHOD: DepositStatus
DEPOSIT_STATUS_REQUIRE_CONFIRM: DepositStatus
DEPOSIT_STATUS_REQUIRE_CAPTURE: DepositStatus
DEPOSIT_STATUS_PROCESSING_CAPTURE: DepositStatus
DEPOSIT_STATUS_PAID: DepositStatus
DEPOSIT_STATUS_REFUNDED: DepositStatus
DEPOSIT_STATUS_CANCEL: DepositStatus
DEPOSIT_STATUS_FAILED: DepositStatus
