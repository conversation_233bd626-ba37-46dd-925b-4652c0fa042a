from google.type import date_pb2 as _date_pb2
from google.type import dayofweek_pb2 as _dayofweek_pb2
from google.type import timeofday_pb2 as _timeofday_pb2
from moego.models.automation.v1 import common_pb2 as _common_pb2
from moego.models.reporting.v2 import common_model_pb2 as _common_model_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ActionResultFilter(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    ACTION_FILTER_UNSPECIFIED: _ClassVar[ActionResultFilter]
    INTAKE_FORM_SUBMIT_SUCCESS: _ClassVar[ActionResultFilter]
ACTION_FILTER_UNSPECIFIED: ActionResultFilter
INTAKE_FORM_SUBMIT_SUCCESS: ActionResultFilter

class Step(_message.Message):
    __slots__ = ("id", "workflow_id", "name", "description", "parent_id", "children_ids", "hierarchical_path", "type", "data", "preview_data")
    class Type(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TYPE_UNSPECIFIED: _ClassVar[Step.Type]
        TRIGGER: _ClassVar[Step.Type]
        WAIT: _ClassVar[Step.Type]
        MESSAGE: _ClassVar[Step.Type]
        CONDITION: _ClassVar[Step.Type]
        ADVANCED_ACTION: _ClassVar[Step.Type]
    TYPE_UNSPECIFIED: Step.Type
    TRIGGER: Step.Type
    WAIT: Step.Type
    MESSAGE: Step.Type
    CONDITION: Step.Type
    ADVANCED_ACTION: Step.Type
    class Data(_message.Message):
        __slots__ = ("trigger", "wait", "message", "condition", "advanced_action")
        TRIGGER_FIELD_NUMBER: _ClassVar[int]
        WAIT_FIELD_NUMBER: _ClassVar[int]
        MESSAGE_FIELD_NUMBER: _ClassVar[int]
        CONDITION_FIELD_NUMBER: _ClassVar[int]
        ADVANCED_ACTION_FIELD_NUMBER: _ClassVar[int]
        trigger: Trigger
        wait: Wait
        message: Message
        condition: Condition
        advanced_action: AdvancedAction
        def __init__(self, trigger: _Optional[_Union[Trigger, _Mapping]] = ..., wait: _Optional[_Union[Wait, _Mapping]] = ..., message: _Optional[_Union[Message, _Mapping]] = ..., condition: _Optional[_Union[Condition, _Mapping]] = ..., advanced_action: _Optional[_Union[AdvancedAction, _Mapping]] = ...) -> None: ...
    class PreviewData(_message.Message):
        __slots__ = ("description",)
        DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
        description: str
        def __init__(self, description: _Optional[str] = ...) -> None: ...
    ID_FIELD_NUMBER: _ClassVar[int]
    WORKFLOW_ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    PARENT_ID_FIELD_NUMBER: _ClassVar[int]
    CHILDREN_IDS_FIELD_NUMBER: _ClassVar[int]
    HIERARCHICAL_PATH_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    DATA_FIELD_NUMBER: _ClassVar[int]
    PREVIEW_DATA_FIELD_NUMBER: _ClassVar[int]
    id: str
    workflow_id: int
    name: str
    description: str
    parent_id: str
    children_ids: _containers.RepeatedScalarFieldContainer[str]
    hierarchical_path: str
    type: Step.Type
    data: Step.Data
    preview_data: Step.PreviewData
    def __init__(self, id: _Optional[str] = ..., workflow_id: _Optional[int] = ..., name: _Optional[str] = ..., description: _Optional[str] = ..., parent_id: _Optional[str] = ..., children_ids: _Optional[_Iterable[str]] = ..., hierarchical_path: _Optional[str] = ..., type: _Optional[_Union[Step.Type, str]] = ..., data: _Optional[_Union[Step.Data, _Mapping]] = ..., preview_data: _Optional[_Union[Step.PreviewData, _Mapping]] = ...) -> None: ...

class Trigger(_message.Message):
    __slots__ = ("type", "data", "filters", "name")
    class Type(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TYPE_UNSPECIFIED: _ClassVar[Trigger.Type]
        SCHEDULED: _ClassVar[Trigger.Type]
        EVENT: _ClassVar[Trigger.Type]
        BEFORE: _ClassVar[Trigger.Type]
    TYPE_UNSPECIFIED: Trigger.Type
    SCHEDULED: Trigger.Type
    EVENT: Trigger.Type
    BEFORE: Trigger.Type
    class Data(_message.Message):
        __slots__ = ("scheduled", "event")
        SCHEDULED_FIELD_NUMBER: _ClassVar[int]
        EVENT_FIELD_NUMBER: _ClassVar[int]
        scheduled: Scheduled
        event: Event
        def __init__(self, scheduled: _Optional[_Union[Scheduled, _Mapping]] = ..., event: _Optional[_Union[Event, _Mapping]] = ...) -> None: ...
    TYPE_FIELD_NUMBER: _ClassVar[int]
    DATA_FIELD_NUMBER: _ClassVar[int]
    FILTERS_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    type: Trigger.Type
    data: Trigger.Data
    filters: _containers.RepeatedCompositeFieldContainer[_common_model_pb2.FilterRequest]
    name: str
    def __init__(self, type: _Optional[_Union[Trigger.Type, str]] = ..., data: _Optional[_Union[Trigger.Data, _Mapping]] = ..., filters: _Optional[_Iterable[_Union[_common_model_pb2.FilterRequest, _Mapping]]] = ..., name: _Optional[str] = ...) -> None: ...

class Scheduled(_message.Message):
    __slots__ = ("day_of_week", "time_of_day", "date", "frequency")
    class Frequency(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        FREQUENCY_UNSPECIFIED: _ClassVar[Scheduled.Frequency]
        DAILY: _ClassVar[Scheduled.Frequency]
        WEEKLY: _ClassVar[Scheduled.Frequency]
        MONTHLY: _ClassVar[Scheduled.Frequency]
    FREQUENCY_UNSPECIFIED: Scheduled.Frequency
    DAILY: Scheduled.Frequency
    WEEKLY: Scheduled.Frequency
    MONTHLY: Scheduled.Frequency
    DAY_OF_WEEK_FIELD_NUMBER: _ClassVar[int]
    TIME_OF_DAY_FIELD_NUMBER: _ClassVar[int]
    DATE_FIELD_NUMBER: _ClassVar[int]
    FREQUENCY_FIELD_NUMBER: _ClassVar[int]
    day_of_week: _dayofweek_pb2.DayOfWeek
    time_of_day: _timeofday_pb2.TimeOfDay
    date: _date_pb2.Date
    frequency: Scheduled.Frequency
    def __init__(self, day_of_week: _Optional[_Union[_dayofweek_pb2.DayOfWeek, str]] = ..., time_of_day: _Optional[_Union[_timeofday_pb2.TimeOfDay, _Mapping]] = ..., date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., frequency: _Optional[_Union[Scheduled.Frequency, str]] = ...) -> None: ...

class Event(_message.Message):
    __slots__ = ("category", "trigger", "filters")
    class Category(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        CATEGORY_UNSPECIFIED: _ClassVar[Event.Category]
        ONLINE_BOOKINGS: _ClassVar[Event.Category]
        APPOINTMENT: _ClassVar[Event.Category]
        INTAKE_FROM: _ClassVar[Event.Category]
        PACKAGE: _ClassVar[Event.Category]
        MEMBERSHIP: _ClassVar[Event.Category]
        DISCOUNT: _ClassVar[Event.Category]
        CLIENT: _ClassVar[Event.Category]
    CATEGORY_UNSPECIFIED: Event.Category
    ONLINE_BOOKINGS: Event.Category
    APPOINTMENT: Event.Category
    INTAKE_FROM: Event.Category
    PACKAGE: Event.Category
    MEMBERSHIP: Event.Category
    DISCOUNT: Event.Category
    CLIENT: Event.Category
    class EntityTrigger(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        ENTITY_UNSPECIFIED: _ClassVar[Event.EntityTrigger]
        ONLINE_BOOKINGS_SUBMITTED: _ClassVar[Event.EntityTrigger]
        ONLINE_BOOKINGS_ABANDONED: _ClassVar[Event.EntityTrigger]
        ONLINE_BOOKINGS_ACCEPTED: _ClassVar[Event.EntityTrigger]
        EVALUATION_BOOKING_SUBMITTED: _ClassVar[Event.EntityTrigger]
        APPOINTMENT_CREATED: _ClassVar[Event.EntityTrigger]
        APPOINTMENT_CANCELED: _ClassVar[Event.EntityTrigger]
        APPOINTMENT_FINISHED: _ClassVar[Event.EntityTrigger]
        APPOINTMENT_FULLY_PAID: _ClassVar[Event.EntityTrigger]
        EVALUATION_FINISHED: _ClassVar[Event.EntityTrigger]
        PACKAGE_PURCHASED: _ClassVar[Event.EntityTrigger]
        PACKAGE_REDEEMED: _ClassVar[Event.EntityTrigger]
        MEMBERSHIP_PURCHASED: _ClassVar[Event.EntityTrigger]
        MEMBERSHIP_CANCELED: _ClassVar[Event.EntityTrigger]
        DISCOUNT_CODE_REDEEMED: _ClassVar[Event.EntityTrigger]
        CLIENT_CREATED: _ClassVar[Event.EntityTrigger]
    ENTITY_UNSPECIFIED: Event.EntityTrigger
    ONLINE_BOOKINGS_SUBMITTED: Event.EntityTrigger
    ONLINE_BOOKINGS_ABANDONED: Event.EntityTrigger
    ONLINE_BOOKINGS_ACCEPTED: Event.EntityTrigger
    EVALUATION_BOOKING_SUBMITTED: Event.EntityTrigger
    APPOINTMENT_CREATED: Event.EntityTrigger
    APPOINTMENT_CANCELED: Event.EntityTrigger
    APPOINTMENT_FINISHED: Event.EntityTrigger
    APPOINTMENT_FULLY_PAID: Event.EntityTrigger
    EVALUATION_FINISHED: Event.EntityTrigger
    PACKAGE_PURCHASED: Event.EntityTrigger
    PACKAGE_REDEEMED: Event.EntityTrigger
    MEMBERSHIP_PURCHASED: Event.EntityTrigger
    MEMBERSHIP_CANCELED: Event.EntityTrigger
    DISCOUNT_CODE_REDEEMED: Event.EntityTrigger
    CLIENT_CREATED: Event.EntityTrigger
    CATEGORY_FIELD_NUMBER: _ClassVar[int]
    TRIGGER_FIELD_NUMBER: _ClassVar[int]
    FILTERS_FIELD_NUMBER: _ClassVar[int]
    category: Event.Category
    trigger: Event.EntityTrigger
    filters: _containers.RepeatedCompositeFieldContainer[_common_model_pb2.FilterRequest]
    def __init__(self, category: _Optional[_Union[Event.Category, str]] = ..., trigger: _Optional[_Union[Event.EntityTrigger, str]] = ..., filters: _Optional[_Iterable[_Union[_common_model_pb2.FilterRequest, _Mapping]]] = ...) -> None: ...

class Before(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class Wait(_message.Message):
    __slots__ = ("type", "time_duration")
    class Type(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TYPE_UNSPECIFIED: _ClassVar[Wait.Type]
        DURATION: _ClassVar[Wait.Type]
    TYPE_UNSPECIFIED: Wait.Type
    DURATION: Wait.Type
    TYPE_FIELD_NUMBER: _ClassVar[int]
    TIME_DURATION_FIELD_NUMBER: _ClassVar[int]
    type: Wait.Type
    time_duration: _common_pb2.TimeDuration
    def __init__(self, type: _Optional[_Union[Wait.Type, str]] = ..., time_duration: _Optional[_Union[_common_pb2.TimeDuration, _Mapping]] = ...) -> None: ...

class Condition(_message.Message):
    __slots__ = ("filters", "next_step_id_true", "next_step_id_false", "type", "action_result_filter")
    class Type(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TYPE_UNSPECIFIED: _ClassVar[Condition.Type]
        FILTER: _ClassVar[Condition.Type]
        ACTION_FILTER: _ClassVar[Condition.Type]
    TYPE_UNSPECIFIED: Condition.Type
    FILTER: Condition.Type
    ACTION_FILTER: Condition.Type
    FILTERS_FIELD_NUMBER: _ClassVar[int]
    NEXT_STEP_ID_TRUE_FIELD_NUMBER: _ClassVar[int]
    NEXT_STEP_ID_FALSE_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    ACTION_RESULT_FILTER_FIELD_NUMBER: _ClassVar[int]
    filters: _containers.RepeatedCompositeFieldContainer[_common_model_pb2.FilterRequest]
    next_step_id_true: str
    next_step_id_false: str
    type: Condition.Type
    action_result_filter: ActionResultFilter
    def __init__(self, filters: _Optional[_Iterable[_Union[_common_model_pb2.FilterRequest, _Mapping]]] = ..., next_step_id_true: _Optional[str] = ..., next_step_id_false: _Optional[str] = ..., type: _Optional[_Union[Condition.Type, str]] = ..., action_result_filter: _Optional[_Union[ActionResultFilter, str]] = ...) -> None: ...

class Message(_message.Message):
    __slots__ = ("type", "category", "data")
    class Type(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TYPE_UNSPECIFIED: _ClassVar[Message.Type]
        SMS: _ClassVar[Message.Type]
        EMAIL: _ClassVar[Message.Type]
    TYPE_UNSPECIFIED: Message.Type
    SMS: Message.Type
    EMAIL: Message.Type
    class Category(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        CATEGORY_UNSPECIFIED: _ClassVar[Message.Category]
        NOTIFICATION: _ClassVar[Message.Category]
        CAMPAIGN: _ClassVar[Message.Category]
    CATEGORY_UNSPECIFIED: Message.Category
    NOTIFICATION: Message.Category
    CAMPAIGN: Message.Category
    class Data(_message.Message):
        __slots__ = ("sms_data", "email_data")
        SMS_DATA_FIELD_NUMBER: _ClassVar[int]
        EMAIL_DATA_FIELD_NUMBER: _ClassVar[int]
        sms_data: SMSData
        email_data: EmailData
        def __init__(self, sms_data: _Optional[_Union[SMSData, _Mapping]] = ..., email_data: _Optional[_Union[EmailData, _Mapping]] = ...) -> None: ...
    TYPE_FIELD_NUMBER: _ClassVar[int]
    CATEGORY_FIELD_NUMBER: _ClassVar[int]
    DATA_FIELD_NUMBER: _ClassVar[int]
    type: Message.Type
    category: Message.Category
    data: Message.Data
    def __init__(self, type: _Optional[_Union[Message.Type, str]] = ..., category: _Optional[_Union[Message.Category, str]] = ..., data: _Optional[_Union[Message.Data, _Mapping]] = ...) -> None: ...

class SMSData(_message.Message):
    __slots__ = ("content",)
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    content: str
    def __init__(self, content: _Optional[str] = ...) -> None: ...

class EmailData(_message.Message):
    __slots__ = ("content", "title")
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    TITLE_FIELD_NUMBER: _ClassVar[int]
    content: str
    title: str
    def __init__(self, content: _Optional[str] = ..., title: _Optional[str] = ...) -> None: ...

class AdvancedAction(_message.Message):
    __slots__ = ("type", "data")
    class Type(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TYPE_UNSPECIFIED: _ClassVar[AdvancedAction.Type]
        ADD_CLIENT_TAG: _ClassVar[AdvancedAction.Type]
        DELETE_CLIENT_TAG: _ClassVar[AdvancedAction.Type]
        CHANGE_CLIENT_TAG: _ClassVar[AdvancedAction.Type]
        ADD_PET_CODE: _ClassVar[AdvancedAction.Type]
        CHANGE_CUSTOMER_LIFE_CYCLE: _ClassVar[AdvancedAction.Type]
        CHANGE_CUSTOMER_ACTION_STATUS: _ClassVar[AdvancedAction.Type]
        ADD_CUSTOMER_TASK: _ClassVar[AdvancedAction.Type]
    TYPE_UNSPECIFIED: AdvancedAction.Type
    ADD_CLIENT_TAG: AdvancedAction.Type
    DELETE_CLIENT_TAG: AdvancedAction.Type
    CHANGE_CLIENT_TAG: AdvancedAction.Type
    ADD_PET_CODE: AdvancedAction.Type
    CHANGE_CUSTOMER_LIFE_CYCLE: AdvancedAction.Type
    CHANGE_CUSTOMER_ACTION_STATUS: AdvancedAction.Type
    ADD_CUSTOMER_TASK: AdvancedAction.Type
    class Data(_message.Message):
        __slots__ = ("add_client_tag_data", "delete_client_tag_data", "change_client_tag_data", "add_pet_code_data", "change_customer_life_cycle_data", "change_customer_action_status_data", "add_customer_task_data")
        ADD_CLIENT_TAG_DATA_FIELD_NUMBER: _ClassVar[int]
        DELETE_CLIENT_TAG_DATA_FIELD_NUMBER: _ClassVar[int]
        CHANGE_CLIENT_TAG_DATA_FIELD_NUMBER: _ClassVar[int]
        ADD_PET_CODE_DATA_FIELD_NUMBER: _ClassVar[int]
        CHANGE_CUSTOMER_LIFE_CYCLE_DATA_FIELD_NUMBER: _ClassVar[int]
        CHANGE_CUSTOMER_ACTION_STATUS_DATA_FIELD_NUMBER: _ClassVar[int]
        ADD_CUSTOMER_TASK_DATA_FIELD_NUMBER: _ClassVar[int]
        add_client_tag_data: AddClientTagData
        delete_client_tag_data: DeleteClientTagData
        change_client_tag_data: ChangeClientTagData
        add_pet_code_data: AddPetCodeData
        change_customer_life_cycle_data: ChangeCustomerLifeCycleData
        change_customer_action_status_data: ChangeCustomerActionStatusData
        add_customer_task_data: AddCustomerTaskData
        def __init__(self, add_client_tag_data: _Optional[_Union[AddClientTagData, _Mapping]] = ..., delete_client_tag_data: _Optional[_Union[DeleteClientTagData, _Mapping]] = ..., change_client_tag_data: _Optional[_Union[ChangeClientTagData, _Mapping]] = ..., add_pet_code_data: _Optional[_Union[AddPetCodeData, _Mapping]] = ..., change_customer_life_cycle_data: _Optional[_Union[ChangeCustomerLifeCycleData, _Mapping]] = ..., change_customer_action_status_data: _Optional[_Union[ChangeCustomerActionStatusData, _Mapping]] = ..., add_customer_task_data: _Optional[_Union[AddCustomerTaskData, _Mapping]] = ...) -> None: ...
    TYPE_FIELD_NUMBER: _ClassVar[int]
    DATA_FIELD_NUMBER: _ClassVar[int]
    type: AdvancedAction.Type
    data: AdvancedAction.Data
    def __init__(self, type: _Optional[_Union[AdvancedAction.Type, str]] = ..., data: _Optional[_Union[AdvancedAction.Data, _Mapping]] = ...) -> None: ...

class AddClientTagData(_message.Message):
    __slots__ = ("customer_tag_ids",)
    CUSTOMER_TAG_IDS_FIELD_NUMBER: _ClassVar[int]
    customer_tag_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, customer_tag_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class DeleteClientTagData(_message.Message):
    __slots__ = ("customer_tag_ids",)
    CUSTOMER_TAG_IDS_FIELD_NUMBER: _ClassVar[int]
    customer_tag_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, customer_tag_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class ChangeClientTagData(_message.Message):
    __slots__ = ("source_customer_tag_ids", "target_customer_tag_ids")
    SOURCE_CUSTOMER_TAG_IDS_FIELD_NUMBER: _ClassVar[int]
    TARGET_CUSTOMER_TAG_IDS_FIELD_NUMBER: _ClassVar[int]
    source_customer_tag_ids: _containers.RepeatedScalarFieldContainer[int]
    target_customer_tag_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, source_customer_tag_ids: _Optional[_Iterable[int]] = ..., target_customer_tag_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class AddPetCodeData(_message.Message):
    __slots__ = ("pet_code_ids",)
    PET_CODE_IDS_FIELD_NUMBER: _ClassVar[int]
    pet_code_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, pet_code_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class ChangeCustomerLifeCycleData(_message.Message):
    __slots__ = ("life_cycle",)
    LIFE_CYCLE_FIELD_NUMBER: _ClassVar[int]
    life_cycle: int
    def __init__(self, life_cycle: _Optional[int] = ...) -> None: ...

class ChangeCustomerActionStatusData(_message.Message):
    __slots__ = ("action_status",)
    ACTION_STATUS_FIELD_NUMBER: _ClassVar[int]
    action_status: int
    def __init__(self, action_status: _Optional[int] = ...) -> None: ...

class AddCustomerTaskData(_message.Message):
    __slots__ = ("name", "allocate_staff_id")
    NAME_FIELD_NUMBER: _ClassVar[int]
    ALLOCATE_STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    name: str
    allocate_staff_id: int
    def __init__(self, name: _Optional[str] = ..., allocate_staff_id: _Optional[int] = ...) -> None: ...

class Branch(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
