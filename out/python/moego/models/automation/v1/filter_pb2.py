# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/automation/v1/filter.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/automation/v1/filter.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.automation.v1 import step_pb2 as moego_dot_models_dot_automation_dot_v1_dot_step__pb2
from moego.models.reporting.v2 import filter_model_pb2 as moego_dot_models_dot_reporting_dot_v2_dot_filter__model__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'moego/models/automation/v1/filter.proto\x12\x1amoego.models.automation.v1\x1a%moego/models/automation/v1/step.proto\x1a,moego/models/reporting/v2/filter_model.proto\"\xa8\x01\n\x11\x45ventFilterGroups\x12\x46\n\x08\x63\x61tegory\x18\x01 \x01(\x0e\x32*.moego.models.automation.v1.Event.CategoryR\x08\x63\x61tegory\x12K\n\rfilter_groups\x18\x02 \x03(\x0b\x32&.moego.models.reporting.v2.FilterGroupR\x0c\x66ilterGroupsB\x84\x01\n\"com.moego.idl.models.automation.v1P\x01Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/automation/v1;automationpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.automation.v1.filter_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.moego.idl.models.automation.v1P\001Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/automation/v1;automationpb'
  _globals['_EVENTFILTERGROUPS']._serialized_start=157
  _globals['_EVENTFILTERGROUPS']._serialized_end=325
# @@protoc_insertion_point(module_scope)
