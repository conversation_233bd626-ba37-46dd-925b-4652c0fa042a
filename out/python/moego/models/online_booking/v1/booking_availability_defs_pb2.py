# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/online_booking/v1/booking_availability_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/online_booking/v1/booking_availability_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.customer.v1 import customer_pet_enums_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__enums__pb2
from moego.models.offering.v1 import evaluation_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_evaluation__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n>moego/models/online_booking/v1/booking_availability_defs.proto\x12\x1emoego.models.online_booking.v1\x1a\x31moego/models/customer/v1/customer_pet_enums.proto\x1a\x30moego/models/offering/v1/evaluation_models.proto\x1a\x17validate/validate.proto\"\x99\x03\n\rBookingPetDef\x12\'\n\nvirtual_id\x18\x01 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01R\tvirtualId\x12#\n\x06pet_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x00R\x05petId\x88\x01\x01\x12\'\n\x08pet_name\x18\x03 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x01R\x07petName\x88\x01\x01\x12M\n\x08pet_type\x18\x04 \x01(\x0e\x32!.moego.models.customer.v1.PetTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x02R\x07petType\x88\x01\x01\x12\"\n\x05\x62reed\x18\x05 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x03R\x05\x62reed\x88\x01\x01\x12+\n\x06weight\x18\x06 \x01(\x01\x42\x0e\xfa\x42\x0b\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00H\x04R\x06weight\x88\x01\x01\x12)\n\tcoat_type\x18\x07 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x05R\x08\x63oatType\x88\x01\x01\x42\t\n\x07_pet_idB\x0b\n\t_pet_nameB\x0b\n\t_pet_typeB\x08\n\x06_breedB\t\n\x07_weightB\x0c\n\n_coat_type\"\x80\x04\n\x14\x42ookingPetServiceDef\x12?\n\x03pet\x18\x01 \x01(\x0b\x32-.moego.models.online_booking.v1.BookingPetDefR\x03pet\x12\x32\n\x15\x61vailable_service_ids\x18\x02 \x03(\x03R\x13\x61vailableServiceIds\x12\x65\n\x13\x63ustomized_services\x18\x03 \x03(\x0b\x32\x34.moego.models.online_booking.v1.CustomizedServiceDefR\x12\x63ustomizedServices\x12x\n\x13missing_evaluations\x18\x04 \x03(\x0b\x32G.moego.models.online_booking.v1.BookingPetServiceDef.MissingEvaluationsR\x12missingEvaluations\x1a\x91\x01\n\x12MissingEvaluations\x12\x1d\n\nservice_id\x18\x01 \x01(\x03R\tserviceId\x12\\\n\x12missing_evaluation\x18\x02 \x01(\x0b\x32-.moego.models.offering.v1.EvaluationBriefViewR\x11missingEvaluation\"\x88\x01\n\x14\x43ustomizedServiceDef\x12\x1d\n\nservice_id\x18\x01 \x01(\x03R\tserviceId\x12\x19\n\x05price\x18\x02 \x01(\x01H\x00R\x05price\x88\x01\x01\x12\x1f\n\x08\x64uration\x18\x03 \x01(\x05H\x01R\x08\x64uration\x88\x01\x01\x42\x08\n\x06_priceB\x0b\n\t_durationB\x8f\x01\n&com.moego.idl.models.online_booking.v1P\x01Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.online_booking.v1.booking_availability_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.models.online_booking.v1P\001Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb'
  _globals['_BOOKINGPETDEF'].fields_by_name['virtual_id']._loaded_options = None
  _globals['_BOOKINGPETDEF'].fields_by_name['virtual_id']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_BOOKINGPETDEF'].fields_by_name['pet_id']._loaded_options = None
  _globals['_BOOKINGPETDEF'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_BOOKINGPETDEF'].fields_by_name['pet_name']._loaded_options = None
  _globals['_BOOKINGPETDEF'].fields_by_name['pet_name']._serialized_options = b'\372B\004r\002\0302'
  _globals['_BOOKINGPETDEF'].fields_by_name['pet_type']._loaded_options = None
  _globals['_BOOKINGPETDEF'].fields_by_name['pet_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_BOOKINGPETDEF'].fields_by_name['breed']._loaded_options = None
  _globals['_BOOKINGPETDEF'].fields_by_name['breed']._serialized_options = b'\372B\004r\002\0302'
  _globals['_BOOKINGPETDEF'].fields_by_name['weight']._loaded_options = None
  _globals['_BOOKINGPETDEF'].fields_by_name['weight']._serialized_options = b'\372B\013\022\t)\000\000\000\000\000\000\000\000'
  _globals['_BOOKINGPETDEF'].fields_by_name['coat_type']._loaded_options = None
  _globals['_BOOKINGPETDEF'].fields_by_name['coat_type']._serialized_options = b'\372B\004r\002\0302'
  _globals['_BOOKINGPETDEF']._serialized_start=225
  _globals['_BOOKINGPETDEF']._serialized_end=634
  _globals['_BOOKINGPETSERVICEDEF']._serialized_start=637
  _globals['_BOOKINGPETSERVICEDEF']._serialized_end=1149
  _globals['_BOOKINGPETSERVICEDEF_MISSINGEVALUATIONS']._serialized_start=1004
  _globals['_BOOKINGPETSERVICEDEF_MISSINGEVALUATIONS']._serialized_end=1149
  _globals['_CUSTOMIZEDSERVICEDEF']._serialized_start=1152
  _globals['_CUSTOMIZEDSERVICEDEF']._serialized_end=1288
# @@protoc_insertion_point(module_scope)
