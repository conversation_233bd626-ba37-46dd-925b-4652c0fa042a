# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/online_booking/v1/business_ob_config_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/online_booking/v1/business_ob_config_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.online_booking.v1 import ob_config_enums_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_ob__config__enums__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n>moego/models/online_booking/v1/business_ob_config_models.proto\x12\x1emoego.models.online_booking.v1\x1a\x34moego/models/online_booking/v1/ob_config_enums.proto\"\xa4\x10\n\x15\x42usinessOBConfigModel\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x1b\n\tis_enable\x18\x02 \x01(\x08R\x08isEnable\x12(\n\x10\x62ook_online_name\x18\x03 \x01(\tR\x0e\x62ookOnlineName\x12\x1f\n\x0buse_version\x18\x04 \x01(\x05R\nuseVersion\x12\x1f\n\x0b\x63reate_time\x18\x05 \x01(\x03R\ncreateTime\x12\x1f\n\x0bupdate_time\x18\x06 \x01(\x03R\nupdateTime\x12`\n\x13\x61vailable_time_type\x18\x07 \x01(\x0e\x32\x30.moego.models.online_booking.v1.AvailabilityTypeR\x11\x61vailableTimeType\x12\x65\n\x17\x62y_slot_timeslot_format\x18\x08 \x01(\x0e\x32..moego.models.online_booking.v1.TimeSlotFormatR\x14\x62ySlotTimeslotFormat\x12\x31\n\x15\x62y_slot_timeslot_mins\x18\t \x01(\x05R\x12\x62ySlotTimeslotMins\x12W\n\x0ftimeslot_format\x18\n \x01(\x0e\x32..moego.models.online_booking.v1.TimeSlotFormatR\x0etimeslotFormat\x12#\n\rtimeslot_mins\x18\x0b \x01(\x05R\x0ctimeslotMins\x12\x39\n\x19\x61rrival_window_before_min\x18\x0c \x01(\x05R\x16\x61rrivalWindowBeforeMin\x12\x37\n\x18\x61rrival_window_after_min\x18\r \x01(\x05R\x15\x61rrivalWindowAfterMin\x12+\n\x11soonest_available\x18\x0e \x01(\x05R\x10soonestAvailable\x12-\n\x12\x66\x61rthest_available\x18\x0f \x01(\x05R\x11\x66\x61rthestAvailable\x12\x39\n\x19\x62y_slot_soonest_available\x18\x10 \x01(\x05R\x16\x62ySlotSoonestAvailable\x12;\n\x1a\x62y_slot_farthest_available\x18\x11 \x01(\x05R\x17\x62ySlotFarthestAvailable\x12&\n\x0fis_need_address\x18\x12 \x01(\x08R\risNeedAddress\x12^\n\x12\x61\x63\x63\x65pt_client_type\x18\x13 \x01(\x0e\x32\x30.moego.models.online_booking.v1.AcceptClientTypeR\x10\x61\x63\x63\x65ptClientType\x12;\n\x1ais_allowed_simplify_submit\x18\x14 \x01(\x08R\x17isAllowedSimplifySubmit\x12N\n\x0cpayment_type\x18\x15 \x01(\x0e\x32+.moego.models.online_booking.v1.PaymentTypeR\x0bpaymentType\x12\x1d\n\ncof_policy\x18\x16 \x01(\tR\tcofPolicy\x12K\n\x0bprepay_type\x18\x17 \x01(\x0e\x32*.moego.models.online_booking.v1.PrepayTypeR\nprepayType\x12/\n\x14is_prepay_tip_enable\x18\x18 \x01(\x08R\x11isPrepayTipEnable\x12\x61\n\x13prepay_deposit_type\x18\x19 \x01(\x0e\x32\x31.moego.models.online_booking.v1.PrepayDepositTypeR\x11prepayDepositType\x12%\n\x0e\x64\x65posit_amount\x18\x1a \x01(\x01R\rdepositAmount\x12-\n\x12\x64\x65posit_percentage\x18\x1b \x01(\x01R\x11\x64\x65positPercentage\x12#\n\rprepay_policy\x18\x1c \x01(\tR\x0cprepayPolicy\x12&\n\x0fpre_auth_policy\x18\x1d \x01(\tR\rpreAuthPolicy\x12\x32\n\x16is_pre_auth_tip_enable\x18\x1e \x01(\x08R\x12isPreAuthTipEnable\x12\x44\n\x1fis_display_staff_selection_page\x18( \x01(\x08R\x1bisDisplayStaffSelectionPage\x12;\n\x1a\x62ooking_range_start_offset\x18) \x01(\x05R\x17\x62ookingRangeStartOffset\x12h\n\x16\x62ooking_range_end_type\x18* \x01(\x0e\x32\x33.moego.models.online_booking.v1.BookingRangeEndTypeR\x13\x62ookingRangeEndType\x12\x37\n\x18\x62ooking_range_end_offset\x18+ \x01(\x05R\x15\x62ookingRangeEndOffset\x12\x33\n\x16\x62ooking_range_end_date\x18, \x01(\tR\x13\x62ookingRangeEndDate\x12\x37\n\x18is_check_existing_client\x18- \x01(\x08R\x15isCheckExistingClient\"\x84\x01\n\x1a\x42usinessOBConfigSimpleView\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x1b\n\tis_enable\x18\x02 \x01(\x08R\x08isEnable\x12(\n\x10\x62ook_online_name\x18\x03 \x01(\tR\x0e\x62ookOnlineName\"\xab\x01\n BusinessOBConfigClientPortalView\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x1b\n\tis_enable\x18\x02 \x01(\x08R\x08isEnable\x12(\n\x10\x62ook_online_name\x18\x03 \x01(\tR\x0e\x62ookOnlineName\x12\x1f\n\x0buse_version\x18\x04 \x01(\x05R\nuseVersion\"\xdc\x03\n#BusinessOBConfigModelClientListView\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12`\n\x13\x61vailable_time_type\x18\x02 \x01(\x0e\x32\x30.moego.models.online_booking.v1.AvailabilityTypeR\x11\x61vailableTimeType\x12W\n\x0ftimeslot_format\x18\x03 \x01(\x0e\x32..moego.models.online_booking.v1.TimeSlotFormatR\x0etimeslotFormat\x12\x65\n\x17\x62y_slot_timeslot_format\x18\x04 \x01(\x0e\x32..moego.models.online_booking.v1.TimeSlotFormatR\x14\x62ySlotTimeslotFormat\x12\x39\n\x19\x61rrival_window_before_min\x18\x05 \x01(\x05R\x16\x61rrivalWindowBeforeMin\x12\x37\n\x18\x61rrival_window_after_min\x18\x06 \x01(\x05R\x15\x61rrivalWindowAfterMin\"\xa0\x05\n\x1f\x42usinessOBConfigModelClientView\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12`\n\x13\x61vailable_time_type\x18\x02 \x01(\x0e\x32\x30.moego.models.online_booking.v1.AvailabilityTypeR\x11\x61vailableTimeType\x12W\n\x0ftimeslot_format\x18\x03 \x01(\x0e\x32..moego.models.online_booking.v1.TimeSlotFormatR\x0etimeslotFormat\x12\x65\n\x17\x62y_slot_timeslot_format\x18\x04 \x01(\x0e\x32..moego.models.online_booking.v1.TimeSlotFormatR\x14\x62ySlotTimeslotFormat\x12\x39\n\x19\x61rrival_window_before_min\x18\x05 \x01(\x05R\x16\x61rrivalWindowBeforeMin\x12\x37\n\x18\x61rrival_window_after_min\x18\x06 \x01(\x05R\x15\x61rrivalWindowAfterMin\x12\x1b\n\tis_enable\x18\x07 \x01(\x08R\x08isEnable\x12(\n\x10\x62ook_online_name\x18\x08 \x01(\tR\x0e\x62ookOnlineName\x12\x1f\n\x0buse_version\x18\t \x01(\x05R\nuseVersion\x12^\n\x12\x61\x63\x63\x65pt_client_type\x18\n \x01(\x0e\x32\x30.moego.models.online_booking.v1.AcceptClientTypeR\x10\x61\x63\x63\x65ptClientType\"\xb5\x0e\n BusinessOBConfigModelBookingView\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x1b\n\tis_enable\x18\x02 \x01(\x08R\x08isEnable\x12(\n\x10\x62ook_online_name\x18\x03 \x01(\tR\x0e\x62ookOnlineName\x12\x1f\n\x0buse_version\x18\x04 \x01(\x05R\nuseVersion\x12`\n\x13\x61vailable_time_type\x18\x05 \x01(\x0e\x32\x30.moego.models.online_booking.v1.AvailabilityTypeR\x11\x61vailableTimeType\x12W\n\x0ftimeslot_format\x18\x06 \x01(\x0e\x32..moego.models.online_booking.v1.TimeSlotFormatR\x0etimeslotFormat\x12\x65\n\x17\x62y_slot_timeslot_format\x18\x07 \x01(\x0e\x32..moego.models.online_booking.v1.TimeSlotFormatR\x14\x62ySlotTimeslotFormat\x12\x39\n\x19\x61rrival_window_before_min\x18\x08 \x01(\x05R\x16\x61rrivalWindowBeforeMin\x12\x37\n\x18\x61rrival_window_after_min\x18\t \x01(\x05R\x15\x61rrivalWindowAfterMin\x12+\n\x11soonest_available\x18\n \x01(\x05R\x10soonestAvailable\x12-\n\x12\x66\x61rthest_available\x18\x0b \x01(\x05R\x11\x66\x61rthestAvailable\x12\x39\n\x19\x62y_slot_soonest_available\x18\x0c \x01(\x05R\x16\x62ySlotSoonestAvailable\x12;\n\x1a\x62y_slot_farthest_available\x18\r \x01(\x05R\x17\x62ySlotFarthestAvailable\x12\x44\n\x1fis_display_staff_selection_page\x18\x0e \x01(\x08R\x1bisDisplayStaffSelectionPage\x12;\n\x1a\x62ooking_range_start_offset\x18\x0f \x01(\x05R\x17\x62ookingRangeStartOffset\x12h\n\x16\x62ooking_range_end_type\x18\x10 \x01(\x0e\x32\x33.moego.models.online_booking.v1.BookingRangeEndTypeR\x13\x62ookingRangeEndType\x12\x37\n\x18\x62ooking_range_end_offset\x18\x11 \x01(\x05R\x15\x62ookingRangeEndOffset\x12\x33\n\x16\x62ooking_range_end_date\x18\x12 \x01(\tR\x13\x62ookingRangeEndDate\x12N\n\x0cpayment_type\x18\x13 \x01(\x0e\x32+.moego.models.online_booking.v1.PaymentTypeR\x0bpaymentType\x12\x1d\n\ncof_policy\x18\x14 \x01(\tR\tcofPolicy\x12K\n\x0bprepay_type\x18\x15 \x01(\x0e\x32*.moego.models.online_booking.v1.PrepayTypeR\nprepayType\x12/\n\x14is_prepay_tip_enable\x18\x16 \x01(\x08R\x11isPrepayTipEnable\x12\x61\n\x13prepay_deposit_type\x18\x17 \x01(\x0e\x32\x31.moego.models.online_booking.v1.PrepayDepositTypeR\x11prepayDepositType\x12%\n\x0e\x64\x65posit_amount\x18\x18 \x01(\x01R\rdepositAmount\x12-\n\x12\x64\x65posit_percentage\x18\x19 \x01(\x01R\x11\x64\x65positPercentage\x12#\n\rprepay_policy\x18\x1a \x01(\tR\x0cprepayPolicy\x12&\n\x0fpre_auth_policy\x18\x1b \x01(\tR\rpreAuthPolicy\x12\x32\n\x16is_pre_auth_tip_enable\x18\x1c \x01(\x08R\x12isPreAuthTipEnable\x12;\n\x1ais_allowed_simplify_submit\x18\x1d \x01(\x08R\x17isAllowedSimplifySubmit\x12&\n\x0fis_need_address\x18\x1e \x01(\x08R\risNeedAddress\x12\x37\n\x18is_check_existing_client\x18- \x01(\x08R\x15isCheckExistingClientB\x8f\x01\n&com.moego.idl.models.online_booking.v1P\x01Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.online_booking.v1.business_ob_config_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.models.online_booking.v1P\001Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb'
  _globals['_BUSINESSOBCONFIGMODEL']._serialized_start=153
  _globals['_BUSINESSOBCONFIGMODEL']._serialized_end=2237
  _globals['_BUSINESSOBCONFIGSIMPLEVIEW']._serialized_start=2240
  _globals['_BUSINESSOBCONFIGSIMPLEVIEW']._serialized_end=2372
  _globals['_BUSINESSOBCONFIGCLIENTPORTALVIEW']._serialized_start=2375
  _globals['_BUSINESSOBCONFIGCLIENTPORTALVIEW']._serialized_end=2546
  _globals['_BUSINESSOBCONFIGMODELCLIENTLISTVIEW']._serialized_start=2549
  _globals['_BUSINESSOBCONFIGMODELCLIENTLISTVIEW']._serialized_end=3025
  _globals['_BUSINESSOBCONFIGMODELCLIENTVIEW']._serialized_start=3028
  _globals['_BUSINESSOBCONFIGMODELCLIENTVIEW']._serialized_end=3700
  _globals['_BUSINESSOBCONFIGMODELBOOKINGVIEW']._serialized_start=3703
  _globals['_BUSINESSOBCONFIGMODELBOOKINGVIEW']._serialized_end=5548
# @@protoc_insertion_point(module_scope)
