# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/online_booking/v1/automation_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/online_booking/v1/automation_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from moego.models.online_booking.v1 import automation_defs_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_automation__defs__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n6moego/models/online_booking/v1/automation_models.proto\x12\x1emoego.models.online_booking.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a\x34moego/models/online_booking/v1/automation_defs.proto\"\xcf\x03\n\x16\x41utomationSettingModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12\x1d\n\ncompany_id\x18\x03 \x01(\x03R\tcompanyId\x12U\n\x11service_item_type\x18\x04 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12,\n\x12\x65nable_auto_accept\x18\x05 \x01(\x08R\x10\x65nableAutoAccept\x12j\n\x15\x61uto_accept_condition\x18\x06 \x01(\x0b\x32\x36.moego.models.online_booking.v1.AutomationConditionDefR\x13\x61utoAcceptCondition\x12\x39\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x39\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tupdatedAtB\x8f\x01\n&com.moego.idl.models.online_booking.v1P\x01Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.online_booking.v1.automation_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.models.online_booking.v1P\001Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb'
  _globals['_AUTOMATIONSETTINGMODEL']._serialized_start=223
  _globals['_AUTOMATIONSETTINGMODEL']._serialized_end=686
# @@protoc_insertion_point(module_scope)
