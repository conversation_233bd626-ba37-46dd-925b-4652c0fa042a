from google.protobuf import timestamp_pb2 as _timestamp_pb2
from moego.models.appointment.v1 import appointment_pet_medication_schedule_defs_pb2 as _appointment_pet_medication_schedule_defs_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class MedicationModel(_message.Message):
    __slots__ = ("id", "booking_request_id", "service_detail_id", "service_detail_type", "time", "amount", "unit", "medication_name", "notes", "created_at", "updated_at", "amount_str", "selected_date")
    class MedicationSchedule(_message.Message):
        __slots__ = ("label", "time")
        LABEL_FIELD_NUMBER: _ClassVar[int]
        TIME_FIELD_NUMBER: _ClassVar[int]
        label: str
        time: int
        def __init__(self, label: _Optional[str] = ..., time: _Optional[int] = ...) -> None: ...
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DETAIL_TYPE_FIELD_NUMBER: _ClassVar[int]
    TIME_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_FIELD_NUMBER: _ClassVar[int]
    UNIT_FIELD_NUMBER: _ClassVar[int]
    MEDICATION_NAME_FIELD_NUMBER: _ClassVar[int]
    NOTES_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    UPDATED_AT_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_STR_FIELD_NUMBER: _ClassVar[int]
    SELECTED_DATE_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    service_detail_id: int
    service_detail_type: _service_enum_pb2.ServiceItemType
    time: _containers.RepeatedCompositeFieldContainer[MedicationModel.MedicationSchedule]
    amount: float
    unit: str
    medication_name: str
    notes: str
    created_at: _timestamp_pb2.Timestamp
    updated_at: _timestamp_pb2.Timestamp
    amount_str: str
    selected_date: _appointment_pet_medication_schedule_defs_pb2.AppointmentPetMedicationScheduleDef.SelectedDateDef
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., service_detail_id: _Optional[int] = ..., service_detail_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., time: _Optional[_Iterable[_Union[MedicationModel.MedicationSchedule, _Mapping]]] = ..., amount: _Optional[float] = ..., unit: _Optional[str] = ..., medication_name: _Optional[str] = ..., notes: _Optional[str] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., updated_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., amount_str: _Optional[str] = ..., selected_date: _Optional[_Union[_appointment_pet_medication_schedule_defs_pb2.AppointmentPetMedicationScheduleDef.SelectedDateDef, _Mapping]] = ...) -> None: ...
