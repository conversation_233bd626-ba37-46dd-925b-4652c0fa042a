# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/online_booking/v1/booking_availability_enums.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/online_booking/v1/booking_availability_enums.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n?moego/models/online_booking/v1/booking_availability_enums.proto\x12\x1emoego.models.online_booking.v1*\xdb\x01\n\x14PetUnavailableReason\x12&\n\"PET_UNAVAILABLE_REASON_UNSPECIFIED\x10\x00\x12\x15\n\x11OVER_WEIGHT_LIMIT\x10\x01\x12\x19\n\x15PET_INFO_NEEDS_UPDATE\x10\x02\x12\"\n\x1ePET_TYPE_NOT_ACCEPTED_BOARDING\x10\x03\x12!\n\x1dPET_TYPE_NOT_ACCEPTED_DAYCARE\x10\x04\x12\"\n\x1ePET_TYPE_NOT_ACCEPTED_GROOMING\x10\x05\x42\x8f\x01\n&com.moego.idl.models.online_booking.v1P\x01Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.online_booking.v1.booking_availability_enums_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.models.online_booking.v1P\001Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb'
  _globals['_PETUNAVAILABLEREASON']._serialized_start=100
  _globals['_PETUNAVAILABLEREASON']._serialized_end=319
# @@protoc_insertion_point(module_scope)
