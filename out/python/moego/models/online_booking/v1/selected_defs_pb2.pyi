from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class SelectedPetServiceDef(_message.Message):
    __slots__ = ("pet_id", "service_id", "add_on_ids")
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    ADD_ON_IDS_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    service_id: int
    add_on_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, pet_id: _Optional[int] = ..., service_id: _Optional[int] = ..., add_on_ids: _Optional[_Iterable[int]] = ...) -> None: ...
