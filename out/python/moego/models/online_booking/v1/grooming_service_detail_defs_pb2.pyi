from validate import validate_pb2 as _validate_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class GroomingServiceDetailDef(_message.Message):
    __slots__ = ("pet_id", "staff_id", "service_id", "start_date", "start_time")
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    staff_id: int
    service_id: int
    start_date: str
    start_time: int
    def __init__(self, pet_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., service_id: _Optional[int] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ...) -> None: ...
