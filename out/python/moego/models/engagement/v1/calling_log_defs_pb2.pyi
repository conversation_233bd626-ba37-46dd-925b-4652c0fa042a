from google.protobuf import duration_pb2 as _duration_pb2
from google.protobuf import timestamp_pb2 as _timestamp_pb2
from moego.models.engagement.v1 import calling_log_models_pb2 as _calling_log_models_pb2
from moego.models.engagement.v1 import voice_models_pb2 as _voice_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class Trend(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    TREND_UNSPECIFIED: _ClassVar[Trend]
    BENEFIT: _ClassVar[Trend]
    HARMFUL: _ClassVar[Trend]
    NEUTRAL: _ClassVar[Trend]
TREND_UNSPECIFIED: Trend
BENEFIT: Trend
HARMFUL: Trend
NEUTRAL: Trend

class CreateCallingLogDef(_message.Message):
    __slots__ = ("company_id", "business_id", "client_id", "staff_id", "summary", "phone_number", "record_transcript", "record_ai_summary", "record_url", "record_type", "direction", "status", "categories", "init_time", "record_duration", "twilio_call_sid")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CLIENT_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    SUMMARY_FIELD_NUMBER: _ClassVar[int]
    PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    RECORD_TRANSCRIPT_FIELD_NUMBER: _ClassVar[int]
    RECORD_AI_SUMMARY_FIELD_NUMBER: _ClassVar[int]
    RECORD_URL_FIELD_NUMBER: _ClassVar[int]
    RECORD_TYPE_FIELD_NUMBER: _ClassVar[int]
    DIRECTION_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    CATEGORIES_FIELD_NUMBER: _ClassVar[int]
    INIT_TIME_FIELD_NUMBER: _ClassVar[int]
    RECORD_DURATION_FIELD_NUMBER: _ClassVar[int]
    TWILIO_CALL_SID_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    client_id: int
    staff_id: int
    summary: str
    phone_number: str
    record_transcript: str
    record_ai_summary: str
    record_url: str
    record_type: _calling_log_models_pb2.RecordType
    direction: _voice_models_pb2.CallingDirection
    status: _calling_log_models_pb2.Status
    categories: _containers.RepeatedScalarFieldContainer[_calling_log_models_pb2.Category]
    init_time: _timestamp_pb2.Timestamp
    record_duration: _duration_pb2.Duration
    twilio_call_sid: str
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., client_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., summary: _Optional[str] = ..., phone_number: _Optional[str] = ..., record_transcript: _Optional[str] = ..., record_ai_summary: _Optional[str] = ..., record_url: _Optional[str] = ..., record_type: _Optional[_Union[_calling_log_models_pb2.RecordType, str]] = ..., direction: _Optional[_Union[_voice_models_pb2.CallingDirection, str]] = ..., status: _Optional[_Union[_calling_log_models_pb2.Status, str]] = ..., categories: _Optional[_Iterable[_Union[_calling_log_models_pb2.Category, str]]] = ..., init_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., record_duration: _Optional[_Union[datetime.timedelta, _duration_pb2.Duration, _Mapping]] = ..., twilio_call_sid: _Optional[str] = ...) -> None: ...

class UpdateCallingLogDef(_message.Message):
    __slots__ = ("client_id", "staff_id", "summary", "phone_number", "record_transcript", "record_ai_summary", "record_url", "record_type", "direction", "status", "categories", "init_time", "record_duration")
    CLIENT_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    SUMMARY_FIELD_NUMBER: _ClassVar[int]
    PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    RECORD_TRANSCRIPT_FIELD_NUMBER: _ClassVar[int]
    RECORD_AI_SUMMARY_FIELD_NUMBER: _ClassVar[int]
    RECORD_URL_FIELD_NUMBER: _ClassVar[int]
    RECORD_TYPE_FIELD_NUMBER: _ClassVar[int]
    DIRECTION_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    CATEGORIES_FIELD_NUMBER: _ClassVar[int]
    INIT_TIME_FIELD_NUMBER: _ClassVar[int]
    RECORD_DURATION_FIELD_NUMBER: _ClassVar[int]
    client_id: int
    staff_id: int
    summary: str
    phone_number: str
    record_transcript: str
    record_ai_summary: str
    record_url: str
    record_type: _calling_log_models_pb2.RecordType
    direction: _voice_models_pb2.CallingDirection
    status: _calling_log_models_pb2.Status
    categories: _containers.RepeatedScalarFieldContainer[_calling_log_models_pb2.Category]
    init_time: _timestamp_pb2.Timestamp
    record_duration: _duration_pb2.Duration
    def __init__(self, client_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., summary: _Optional[str] = ..., phone_number: _Optional[str] = ..., record_transcript: _Optional[str] = ..., record_ai_summary: _Optional[str] = ..., record_url: _Optional[str] = ..., record_type: _Optional[_Union[_calling_log_models_pb2.RecordType, str]] = ..., direction: _Optional[_Union[_voice_models_pb2.CallingDirection, str]] = ..., status: _Optional[_Union[_calling_log_models_pb2.Status, str]] = ..., categories: _Optional[_Iterable[_Union[_calling_log_models_pb2.Category, str]]] = ..., init_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., record_duration: _Optional[_Union[datetime.timedelta, _duration_pb2.Duration, _Mapping]] = ...) -> None: ...

class IndicatorDef(_message.Message):
    __slots__ = ("value", "percent", "trend")
    VALUE_FIELD_NUMBER: _ClassVar[int]
    PERCENT_FIELD_NUMBER: _ClassVar[int]
    TREND_FIELD_NUMBER: _ClassVar[int]
    value: float
    percent: float
    trend: Trend
    def __init__(self, value: _Optional[float] = ..., percent: _Optional[float] = ..., trend: _Optional[_Union[Trend, str]] = ...) -> None: ...
