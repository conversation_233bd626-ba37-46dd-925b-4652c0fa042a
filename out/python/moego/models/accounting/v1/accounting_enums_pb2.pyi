from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from typing import ClassVar as _ClassVar

DESCRIPTOR: _descriptor.FileDescriptor

class VisibilityClass(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    VISIBILITY_CLASS_UNSPECIFIED: _ClassVar[VisibilityClass]
    DEFAULT: _ClassVar[VisibilityClass]
    FULL_SERVICE_ONLY: _ClassVar[VisibilityClass]

class OnboardingStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    ONBOARDING_STATUS_UNSPECIFIED: _ClassVar[OnboardingStatus]
    NOT_SUBSCRIBED: _ClassVar[OnboardingStatus]
    NOT_ONBOARDED: _ClassVar[OnboardingStatus]
    ONBOARDED: _ClassVar[OnboardingStatus]
    ONBOARDING_ONLY: _ClassVar[OnboardingStatus]

class SyncEntityType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    SYNC_ENTITY_TYPE_UNSPECIFIED: _ClassVar[SyncEntityType]
    INVOICE: _ClassVar[SyncEntityType]
    PAYMENT: _ClassVar[SyncEntityType]
    REFUND: _ClassVar[SyncEntityType]
    WRITE_OFF: _ClassVar[SyncEntityType]
    DISPUTE: _ClassVar[SyncEntityType]
    DISPUTE_WON: _ClassVar[SyncEntityType]
    PAYOUT: _ClassVar[SyncEntityType]
    LOAN_PAYOUT: _ClassVar[SyncEntityType]
    LOAN_REPAYMENT: _ClassVar[SyncEntityType]
    PAYROLL: _ClassVar[SyncEntityType]
    CUSTOMER: _ClassVar[SyncEntityType]
    DISPUTE_FUND_OPERATE: _ClassVar[SyncEntityType]

class SyncStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    SYNC_STATUS_UNSPECIFIED: _ClassVar[SyncStatus]
    INIT: _ClassVar[SyncStatus]
    PROCESSING: _ClassVar[SyncStatus]
    SUCCESS: _ClassVar[SyncStatus]
    FAILED: _ClassVar[SyncStatus]
    CANCELED: _ClassVar[SyncStatus]

class ChannelType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    CHANNEL_TYPE_UNSPECIFIED: _ClassVar[ChannelType]
    LAYER: _ClassVar[ChannelType]

class Enable(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    ENABLE_UNSPECIFIED: _ClassVar[Enable]
    OPEN: _ClassVar[Enable]
    CLOSE: _ClassVar[Enable]

class AccountSide(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    ACCOUNT_SIDE_UNSPECIFIED: _ClassVar[AccountSide]
    CREDIT: _ClassVar[AccountSide]
    DEBIT: _ClassVar[AccountSide]

class USState(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    US_STATE_UNSPECIFIED: _ClassVar[USState]
    AL: _ClassVar[USState]
    AK: _ClassVar[USState]
    AZ: _ClassVar[USState]
    AR: _ClassVar[USState]
    CA: _ClassVar[USState]
    CO: _ClassVar[USState]
    CT: _ClassVar[USState]
    DE: _ClassVar[USState]
    FL: _ClassVar[USState]
    GA: _ClassVar[USState]
    HI: _ClassVar[USState]
    ID: _ClassVar[USState]
    IL: _ClassVar[USState]
    IN: _ClassVar[USState]
    IA: _ClassVar[USState]
    KS: _ClassVar[USState]
    KY: _ClassVar[USState]
    LA: _ClassVar[USState]
    ME: _ClassVar[USState]
    MD: _ClassVar[USState]
    MA: _ClassVar[USState]
    MI: _ClassVar[USState]
    MN: _ClassVar[USState]
    MS: _ClassVar[USState]
    MO: _ClassVar[USState]
    MT: _ClassVar[USState]
    NE: _ClassVar[USState]
    NV: _ClassVar[USState]
    NH: _ClassVar[USState]
    NJ: _ClassVar[USState]
    NM: _ClassVar[USState]
    NY: _ClassVar[USState]
    NC: _ClassVar[USState]
    ND: _ClassVar[USState]
    OH: _ClassVar[USState]
    OK: _ClassVar[USState]
    OR: _ClassVar[USState]
    PA: _ClassVar[USState]
    RI: _ClassVar[USState]
    SC: _ClassVar[USState]
    SD: _ClassVar[USState]
    TN: _ClassVar[USState]
    TX: _ClassVar[USState]
    UT: _ClassVar[USState]
    VT: _ClassVar[USState]
    VA: _ClassVar[USState]
    WA: _ClassVar[USState]
    WV: _ClassVar[USState]
    WI: _ClassVar[USState]
    WY: _ClassVar[USState]

class EntityType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    ENTITY_TYPE_UNSPECIFIED: _ClassVar[EntityType]
    SOLE_PROP: _ClassVar[EntityType]
    C_CORP: _ClassVar[EntityType]
    LLC: _ClassVar[EntityType]
    S_CORP: _ClassVar[EntityType]
    PARTNERSHIP: _ClassVar[EntityType]
VISIBILITY_CLASS_UNSPECIFIED: VisibilityClass
DEFAULT: VisibilityClass
FULL_SERVICE_ONLY: VisibilityClass
ONBOARDING_STATUS_UNSPECIFIED: OnboardingStatus
NOT_SUBSCRIBED: OnboardingStatus
NOT_ONBOARDED: OnboardingStatus
ONBOARDED: OnboardingStatus
ONBOARDING_ONLY: OnboardingStatus
SYNC_ENTITY_TYPE_UNSPECIFIED: SyncEntityType
INVOICE: SyncEntityType
PAYMENT: SyncEntityType
REFUND: SyncEntityType
WRITE_OFF: SyncEntityType
DISPUTE: SyncEntityType
DISPUTE_WON: SyncEntityType
PAYOUT: SyncEntityType
LOAN_PAYOUT: SyncEntityType
LOAN_REPAYMENT: SyncEntityType
PAYROLL: SyncEntityType
CUSTOMER: SyncEntityType
DISPUTE_FUND_OPERATE: SyncEntityType
SYNC_STATUS_UNSPECIFIED: SyncStatus
INIT: SyncStatus
PROCESSING: SyncStatus
SUCCESS: SyncStatus
FAILED: SyncStatus
CANCELED: SyncStatus
CHANNEL_TYPE_UNSPECIFIED: ChannelType
LAYER: ChannelType
ENABLE_UNSPECIFIED: Enable
OPEN: Enable
CLOSE: Enable
ACCOUNT_SIDE_UNSPECIFIED: AccountSide
CREDIT: AccountSide
DEBIT: AccountSide
US_STATE_UNSPECIFIED: USState
AL: USState
AK: USState
AZ: USState
AR: USState
CA: USState
CO: USState
CT: USState
DE: USState
FL: USState
GA: USState
HI: USState
ID: USState
IL: USState
IN: USState
IA: USState
KS: USState
KY: USState
LA: USState
ME: USState
MD: USState
MA: USState
MI: USState
MN: USState
MS: USState
MO: USState
MT: USState
NE: USState
NV: USState
NH: USState
NJ: USState
NM: USState
NY: USState
NC: USState
ND: USState
OH: USState
OK: USState
OR: USState
PA: USState
RI: USState
SC: USState
SD: USState
TN: USState
TX: USState
UT: USState
VT: USState
VA: USState
WA: USState
WV: USState
WI: USState
WY: USState
ENTITY_TYPE_UNSPECIFIED: EntityType
SOLE_PROP: EntityType
C_CORP: EntityType
LLC: EntityType
S_CORP: EntityType
PARTNERSHIP: EntityType
