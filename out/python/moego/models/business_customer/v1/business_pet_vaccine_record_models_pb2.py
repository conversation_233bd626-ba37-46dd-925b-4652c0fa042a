# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/business_customer/v1/business_pet_vaccine_record_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/business_customer/v1/business_pet_vaccine_record_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import date_pb2 as google_dot_type_dot_date__pb2
from moego.models.business_customer.v1 import business_pet_vaccine_record_enums_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__vaccine__record__enums__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nJmoego/models/business_customer/v1/business_pet_vaccine_record_models.proto\x12!moego.models.business_customer.v1\x1a\x16google/type/date.proto\x1aImoego/models/business_customer/v1/business_pet_vaccine_record_enums.proto\"\xfb\x02\n\x1d\x42usinessPetVaccineRecordModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n\nvaccine_id\x18\x03 \x01(\x03R\tvaccineId\x12?\n\x0f\x65xpiration_date\x18\x04 \x01(\x0b\x32\x11.google.type.DateH\x00R\x0e\x65xpirationDate\x88\x01\x01\x12#\n\rdocument_urls\x18\x05 \x03(\tR\x0c\x64ocumentUrls\x12\x18\n\x07\x64\x65leted\x18\x06 \x01(\x08R\x07\x64\x65leted\x12\x41\n\x06source\x18\x07 \x01(\x0e\x32).moego.models.business_customer.v1.SourceR\x06source\x12T\n\rverify_status\x18\x08 \x01(\x0e\x32/.moego.models.business_customer.v1.VerifyStatusR\x0cverifyStatusB\x12\n\x10_expiration_date\"\x99\x01\n$BusinessPetVaccineRecordBindingModel\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12Z\n\x07records\x18\x02 \x03(\x0b\<EMAIL>.business_customer.v1.BusinessPetVaccineRecordModelR\x07recordsB\x98\x01\n)com.moego.idl.models.business_customer.v1P\x01Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.business_customer.v1.business_pet_vaccine_record_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n)com.moego.idl.models.business_customer.v1P\001Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb'
  _globals['_BUSINESSPETVACCINERECORDMODEL']._serialized_start=213
  _globals['_BUSINESSPETVACCINERECORDMODEL']._serialized_end=592
  _globals['_BUSINESSPETVACCINERECORDBINDINGMODEL']._serialized_start=595
  _globals['_BUSINESSPETVACCINERECORDBINDINGMODEL']._serialized_end=748
# @@protoc_insertion_point(module_scope)
