# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/business_customer/v1/business_customer_setting_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/business_customer/v1/business_customer_setting_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nFmoego/models/business_customer/v1/business_customer_setting_defs.proto\x12!moego.models.business_customer.v1\"\xe3\x01\n(BusinessCustomerCreationSettingUpdateDef\x12<\n\x18\x65nable_creation_from_sms\x18\x01 \x01(\x08H\x00R\x15\x65nableCreationFromSms\x88\x01\x01\x12>\n\x19\x65nable_creation_from_call\x18\x02 \x01(\x08H\x01R\x16\x65nableCreationFromCall\x88\x01\x01\x42\x1b\n\x19_enable_creation_from_smsB\x1c\n\x1a_enable_creation_from_callB\x98\x01\n)com.moego.idl.models.business_customer.v1P\x01Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.business_customer.v1.business_customer_setting_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n)com.moego.idl.models.business_customer.v1P\001Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb'
  _globals['_BUSINESSCUSTOMERCREATIONSETTINGUPDATEDEF']._serialized_start=110
  _globals['_BUSINESSCUSTOMERCREATIONSETTINGUPDATEDEF']._serialized_end=337
# @@protoc_insertion_point(module_scope)
