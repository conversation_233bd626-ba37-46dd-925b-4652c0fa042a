from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class BusinessPetCoatTypeModel(_message.Message):
    __slots__ = ("id", "name", "sort", "deleted")
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    SORT_FIELD_NUMBER: _ClassVar[int]
    DELETED_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    sort: int
    deleted: bool
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ..., sort: _Optional[int] = ..., deleted: bool = ...) -> None: ...

class BusinessPetCoatTypeNameView(_message.Message):
    __slots__ = ("name",)
    NAME_FIELD_NUMBER: _ClassVar[int]
    name: str
    def __init__(self, name: _Optional[str] = ...) -> None: ...
