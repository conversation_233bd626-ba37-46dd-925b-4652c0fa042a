# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/business_customer/v1/business_pet_vaccine_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/business_customer/v1/business_pet_vaccine_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.business_customer.v1 import business_pet_vaccine_defs_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__vaccine__defs__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nCmoego/models/business_customer/v1/business_pet_vaccine_models.proto\x12!moego.models.business_customer.v1\x1a\x41moego/models/business_customer/v1/business_pet_vaccine_defs.proto\"\xd5\x01\n\x17\x42usinessPetVaccineModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n\x04sort\x18\x03 \x01(\x05R\x04sort\x12\x18\n\x07\x64\x65leted\x18\x04 \x01(\x08R\x07\x64\x65leted\x12h\n\x0c\x61vailability\x18\x05 \x01(\x0b\x32\x44.moego.models.business_customer.v1.BusinessPetVaccineAvailabilityDefR\x0c\x61vailability\"0\n\x1a\x42usinessPetVaccineNameView\x12\x12\n\x04name\x18\x02 \x01(\tR\x04nameB\x98\x01\n)com.moego.idl.models.business_customer.v1P\x01Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.business_customer.v1.business_pet_vaccine_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n)com.moego.idl.models.business_customer.v1P\001Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb'
  _globals['_BUSINESSPETVACCINEMODEL']._serialized_start=174
  _globals['_BUSINESSPETVACCINEMODEL']._serialized_end=387
  _globals['_BUSINESSPETVACCINENAMEVIEW']._serialized_start=389
  _globals['_BUSINESSPETVACCINENAMEVIEW']._serialized_end=437
# @@protoc_insertion_point(module_scope)
