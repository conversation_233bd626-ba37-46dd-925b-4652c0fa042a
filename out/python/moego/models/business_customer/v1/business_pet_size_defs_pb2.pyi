from validate import validate_pb2 as _validate_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class BusinessPetSizeUpsertDef(_message.Message):
    __slots__ = ("name", "weight_low", "weight_high")
    NAME_FIELD_NUMBER: _ClassVar[int]
    WEIGHT_LOW_FIELD_NUMBER: _ClassVar[int]
    WEIGHT_HIGH_FIELD_NUMBER: _ClassVar[int]
    name: str
    weight_low: int
    weight_high: int
    def __init__(self, name: _Optional[str] = ..., weight_low: _Optional[int] = ..., weight_high: _Optional[int] = ...) -> None: ...
