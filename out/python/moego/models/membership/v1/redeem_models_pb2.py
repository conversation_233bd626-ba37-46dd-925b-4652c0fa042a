# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/membership/v1/redeem_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/membership/v1/redeem_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import money_pb2 as google_dot_type_dot_money__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.moego/models/membership/v1/redeem_models.proto\x12\x1amoego.models.membership.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17google/type/money.proto\"a\n\x1a\x42oardingRedeemScenarioItem\x12\x1f\n\x0b\x62oarding_id\x18\x01 \x01(\x03R\nboardingId\x12\"\n\rorder_item_id\x18\x02 \x01(\x03R\x0borderItemId\"\xcc\x01\n\x12RedeemScenarioItem\x12G\n\x0btarget_type\x18\x01 \x01(\x0e\x32&.moego.models.membership.v1.TargetTypeR\ntargetType\x12\x1b\n\ttarget_id\x18\x02 \x01(\x03R\x08targetId\x12\x16\n\x06\x61mount\x18\x05 \x01(\x05R\x06\x61mount\x12\x14\n\x05price\x18\x06 \x01(\x01R\x05price\x12\"\n\rorder_item_id\x18\x07 \x01(\x03R\x0borderItemId\"\x9d\x01\n\rRedeemContext\x12\x46\n\x08scenario\x18\x01 \x01(\x0e\x32*.moego.models.membership.v1.RedeemScenarioR\x08scenario\x12\x44\n\x05items\x18\x02 \x03(\x0b\x32..moego.models.membership.v1.RedeemScenarioItemR\x05items\"\xae\x05\n\x12IncludeBenefitView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12#\n\rmembership_id\x18\x02 \x01(\x03R\x0cmembershipId\x12%\n\x0etotal_quantity\x18\x03 \x01(\x05R\rtotalQuantity\x12-\n\x12remaining_quantity\x18\x04 \x01(\x05R\x11remainingQuantity\x12\x1d\n\nis_limited\x18\x05 \x01(\x08R\tisLimited\x12;\n\x0bredeem_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\nredeemTime\x12\x66\n\x0citem_details\x18\x07 \x03(\x0b\x32\x43.moego.models.membership.v1.IncludeBenefitView.RedeemItemDetailViewR\x0bitemDetails\x12\x15\n\x06is_all\x18\x08 \x01(\x08R\x05isAll\x12M\n\rdiscount_unit\x18\t \x01(\x0e\x32(.moego.models.membership.v1.DiscountUnitR\x0c\x64iscountUnit\x12%\n\x0e\x64iscount_value\x18\n \x01(\x01R\rdiscountValue\x1a\xbb\x01\n\x14RedeemItemDetailView\x12\x17\n\x07item_id\x18\x01 \x01(\x03R\x06itemId\x12\x1b\n\titem_name\x18\x02 \x01(\tR\x08itemName\x12(\n\x05price\x18\x03 \x01(\x0b\x32\x12.google.type.MoneyR\x05price\x12\x43\n\titem_type\x18\x04 \x01(\x0e\x32&.moego.models.membership.v1.TargetTypeR\x08itemType\"\x87\x02\n\rRedeemHistory\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n\ninvoice_id\x18\x02 \x01(\x03R\tinvoiceId\x12\x1d\n\nbenefit_id\x18\x03 \x01(\x03R\tbenefitId\x12\x1d\n\nservice_id\x18\x04 \x01(\x03R\tserviceId\x12\x1a\n\x08quantity\x18\x05 \x01(\x05R\x08quantity\x12)\n\x10\x61ppointment_date\x18\x06 \x01(\tR\x0f\x61ppointmentDate\x12\x1f\n\x0bgrooming_id\x18\x08 \x01(\x03R\ngroomingId\x12!\n\x0cservice_name\x18\x12 \x01(\tR\x0bserviceName*\x93\x01\n\x0eRedeemScenario\x12*\n&MEMBERSHIP_REDEEM_SCENARIO_UNSPECIFIED\x10\x00\x12\x16\n\x12REDEEM_BY_CHECKOUT\x10\x01\x12\x18\n\x14REDEEM_BY_FULLY_PAID\x10\x02\x12#\n\x1fREDEEM_BY_ONLINE_BOOKING_SUBMIT\x10\x03*N\n\nTargetType\x12\x1b\n\x17TARGET_TYPE_UNSPECIFIED\x10\x00\x12\x0b\n\x07SERVICE\x10\x01\x12\t\n\x05\x41\x44\x44ON\x10\x02\x12\x0b\n\x07PRODUCT\x10\x03*Y\n\rTargetSubType\x12\x1f\n\x1bTARGET_SUB_TYPE_UNSPECIFIED\x10\x00\x12\x0c\n\x08GROOMING\x10\x01\x12\x0c\n\x08\x42OARDING\x10\x02\x12\x0b\n\x07\x44\x41YCARE\x10\x03*@\n\x0c\x44iscountUnit\x12\x14\n\x10UNIT_UNSPECIFIED\x10\x00\x12\x0b\n\x07PERCENT\x10\x01\x12\r\n\tNUMERICAL\x10\x02\x42\x84\x01\n\"com.moego.idl.models.membership.v1P\x01Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1;membershippbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.membership.v1.redeem_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.moego.idl.models.membership.v1P\001Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1;membershippb'
  _globals['_REDEEMSCENARIO']._serialized_start=1558
  _globals['_REDEEMSCENARIO']._serialized_end=1705
  _globals['_TARGETTYPE']._serialized_start=1707
  _globals['_TARGETTYPE']._serialized_end=1785
  _globals['_TARGETSUBTYPE']._serialized_start=1787
  _globals['_TARGETSUBTYPE']._serialized_end=1876
  _globals['_DISCOUNTUNIT']._serialized_start=1878
  _globals['_DISCOUNTUNIT']._serialized_end=1942
  _globals['_BOARDINGREDEEMSCENARIOITEM']._serialized_start=136
  _globals['_BOARDINGREDEEMSCENARIOITEM']._serialized_end=233
  _globals['_REDEEMSCENARIOITEM']._serialized_start=236
  _globals['_REDEEMSCENARIOITEM']._serialized_end=440
  _globals['_REDEEMCONTEXT']._serialized_start=443
  _globals['_REDEEMCONTEXT']._serialized_end=600
  _globals['_INCLUDEBENEFITVIEW']._serialized_start=603
  _globals['_INCLUDEBENEFITVIEW']._serialized_end=1289
  _globals['_INCLUDEBENEFITVIEW_REDEEMITEMDETAILVIEW']._serialized_start=1102
  _globals['_INCLUDEBENEFITVIEW_REDEEMITEMDETAILVIEW']._serialized_end=1289
  _globals['_REDEEMHISTORY']._serialized_start=1292
  _globals['_REDEEMHISTORY']._serialized_end=1555
# @@protoc_insertion_point(module_scope)
