# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/membership/v1/redeem_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/membership/v1/redeem_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.membership.v1 import redeem_models_pb2 as moego_dot_models_dot_membership_dot_v1_dot_redeem__models__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,moego/models/membership/v1/redeem_defs.proto\x12\x1amoego.models.membership.v1\x1a.moego/models/membership/v1/redeem_models.proto\"\xa2\x02\n\x10\x42\x65nefitRedeemDef\x12\x1d\n\nbenefit_id\x18\x01 \x01(\x03R\tbenefitId\x12\x1b\n\x06\x61mount\x18\x02 \x01(\x05H\x01R\x06\x61mount\x88\x01\x01\x12\x1c\n\tadd_on_id\x18\x03 \x01(\x03H\x00R\x07\x61\x64\x64OnId\x12\x1f\n\nservice_id\x18\x04 \x01(\x03H\x00R\tserviceId\x12T\n\x08\x62oarding\x18\x05 \x01(\x0b\x32\x36.moego.models.membership.v1.BoardingRedeemScenarioItemH\x00R\x08\x62oarding\x12\x1f\n\nproduct_id\x18\x06 \x01(\x03H\x00R\tproductIdB\x11\n\x0f\x66\x65\x61ture_item_idB\t\n\x07_amountB\x84\x01\n\"com.moego.idl.models.membership.v1P\x01Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1;membershippbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.membership.v1.redeem_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.moego.idl.models.membership.v1P\001Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1;membershippb'
  _globals['_BENEFITREDEEMDEF']._serialized_start=125
  _globals['_BENEFITREDEEMDEF']._serialized_end=415
# @@protoc_insertion_point(module_scope)
