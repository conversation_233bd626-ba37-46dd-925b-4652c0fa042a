from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from typing import ClassVar as _ClassVar

DESCRIPTOR: _descriptor.FileDescriptor

class StaffChangeLogRelateType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    STAFF_CHANGE_LOG_RELATE_TYPE_UNSPECIFIED: _ClassVar[StaffChangeLogRelateType]
    STAFF_CHANGE_LOG_RELATE_TYPE_EDIT_STAFF: _ClassVar[StaffChangeLogRelateType]
    STAFF_CHANGE_LOG_RELATE_TYPE_EDIT_TIPS: _ClassVar[StaffChangeLogRelateType]

class StaffChangeLogAmountType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    STAFF_CHANGE_LOG_AMOUNT_TYPE_UNSPECIFIED: _ClassVar[StaffChangeLogAmountType]
    STAFF_CHANGE_LOG_AMOUNT_TYPE_ORDER_ITEM: _ClassVar[StaffChangeLogAmountType]
    STAFF_CHANGE_LOG_AMOUNT_TYPE_TIPS: _ClassVar[StaffChangeLogAmountType]
STAFF_CHANGE_LOG_RELATE_TYPE_UNSPECIFIED: StaffChangeLogRelateType
STAFF_CHANGE_LOG_RELATE_TYPE_EDIT_STAFF: StaffChangeLogRelateType
STAFF_CHANGE_LOG_RELATE_TYPE_EDIT_TIPS: StaffChangeLogRelateType
STAFF_CHANGE_LOG_AMOUNT_TYPE_UNSPECIFIED: StaffChangeLogAmountType
STAFF_CHANGE_LOG_AMOUNT_TYPE_ORDER_ITEM: StaffChangeLogAmountType
STAFF_CHANGE_LOG_AMOUNT_TYPE_TIPS: StaffChangeLogAmountType
