from validate import validate_pb2 as _validate_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class BatchCreateTemplateItemDef(_message.Message):
    __slots__ = ("seq_num", "subject", "body")
    SEQ_NUM_FIELD_NUMBER: _ClassVar[int]
    SUBJECT_FIELD_NUMBER: _ClassVar[int]
    BODY_FIELD_NUMBER: _ClassVar[int]
    seq_num: int
    subject: str
    body: str
    def __init__(self, seq_num: _Optional[int] = ..., subject: _Optional[str] = ..., body: _Optional[str] = ...) -> None: ...
