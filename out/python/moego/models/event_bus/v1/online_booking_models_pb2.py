# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/event_bus/v1/online_booking_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/event_bus/v1/online_booking_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n5moego/models/event_bus/v1/online_booking_models.proto\x12\x19moego.models.event_bus.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a+moego/models/offering/v1/service_enum.proto\"\xf6\x01\n\x1bOnlineBookingSubmittedEvent\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x63ustomer_id\x18\x02 \x01(\x03R\ncustomerId\x12>\n\nstart_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x00R\tstartTime\x88\x01\x01\x12W\n\x12service_item_types\x18\x04 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x10serviceItemTypesB\r\n\x0b_start_time\"\xac\x01\n\x1bOnlineBookingAbandonedEvent\x12$\n\x0b\x63ustomer_id\x18\x01 \x01(\x03H\x00R\ncustomerId\x88\x01\x01\x12W\n\x12service_item_types\x18\x04 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x10serviceItemTypesB\x0e\n\x0c_customer_id\"\xe1\x01\n\x1aOnlineBookingAcceptedEvent\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x63ustomer_id\x18\x02 \x01(\x03R\ncustomerId\x12\x39\n\nstart_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tstartTime\x12W\n\x12service_item_types\x18\x04 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x10serviceItemTypesB\x80\x01\n!com.moego.idl.models.event_bus.v1P\x01ZYgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.event_bus.v1.online_booking_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n!com.moego.idl.models.event_bus.v1P\001ZYgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb'
  _globals['_ONLINEBOOKINGSUBMITTEDEVENT']._serialized_start=163
  _globals['_ONLINEBOOKINGSUBMITTEDEVENT']._serialized_end=409
  _globals['_ONLINEBOOKINGABANDONEDEVENT']._serialized_start=412
  _globals['_ONLINEBOOKINGABANDONEDEVENT']._serialized_end=584
  _globals['_ONLINEBOOKINGACCEPTEDEVENT']._serialized_start=587
  _globals['_ONLINEBOOKINGACCEPTEDEVENT']._serialized_end=812
# @@protoc_insertion_point(module_scope)
