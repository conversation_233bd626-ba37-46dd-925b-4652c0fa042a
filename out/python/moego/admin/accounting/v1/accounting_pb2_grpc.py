# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from moego.admin.accounting.v1 import accounting_pb2 as moego_dot_admin_dot_accounting_dot_v1_dot_accounting__pb2


class AccountingServiceStub(object):
    """AccountingService
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Compensate = channel.unary_unary(
                '/moego.admin.accounting.v1.AccountingService/Compensate',
                request_serializer=moego_dot_admin_dot_accounting_dot_v1_dot_accounting__pb2.CompensateRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)


class AccountingServiceServicer(object):
    """AccountingService
    """

    def Compensate(self, request, context):
        """compensate
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AccountingServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Compensate': grpc.unary_unary_rpc_method_handler(
                    servicer.Compensate,
                    request_deserializer=moego_dot_admin_dot_accounting_dot_v1_dot_accounting__pb2.CompensateRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.admin.accounting.v1.AccountingService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.admin.accounting.v1.AccountingService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AccountingService(object):
    """AccountingService
    """

    @staticmethod
    def Compensate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.accounting.v1.AccountingService/Compensate',
            moego_dot_admin_dot_accounting_dot_v1_dot_accounting__pb2.CompensateRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
