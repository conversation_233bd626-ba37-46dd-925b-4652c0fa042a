# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/admin/membership/v1/membership_admin.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/admin/membership/v1/membership_admin.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.membership.v1 import membership_defs_pb2 as moego_dot_models_dot_membership_dot_v1_dot_membership__defs__pb2
from moego.models.membership.v1 import membership_models_pb2 as moego_dot_models_dot_membership_dot_v1_dot_membership__models__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n0moego/admin/membership/v1/membership_admin.proto\x12\x19moego.admin.membership.v1\x1a\x30moego/models/membership/v1/membership_defs.proto\x1a\x32moego/models/membership/v1/membership_models.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"z\n\x16\x43reateMembershipParams\x12`\n\x0emembership_def\x18\x01 \x01(\x0b\x32/.moego.models.membership.v1.MembershipCreateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\rmembershipDef\"e\n\x16\x43reateMembershipResult\x12K\n\nmembership\x18\x01 \x01(\x0b\x32+.moego.models.membership.v1.MembershipModelR\nmembership\".\n\x13GetMembershipParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"b\n\x13GetMembershipResult\x12K\n\nmembership\x18\x01 \x01(\x0b\x32+.moego.models.membership.v1.MembershipModelR\nmembership\"n\n\x15ListMembershipsParams\x12\x46\n\npagination\x18\x0f \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x00R\npagination\x88\x01\x01\x42\r\n\x0b_pagination\"\xaa\x01\n\x15ListMembershipsResult\x12M\n\x0bmemberships\x18\x01 \x03(\x0b\x32+.moego.models.membership.v1.MembershipModelR\x0bmemberships\x12\x42\n\npagination\x18\x0f \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\"\x8a\x01\n\x16UpdateMembershipParams\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12`\n\x0emembership_def\x18\x02 \x01(\x0b\x32/.moego.models.membership.v1.MembershipUpdateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\rmembershipDef\"e\n\x16UpdateMembershipResult\x12K\n\nmembership\x18\x01 \x01(\x0b\x32+.moego.models.membership.v1.MembershipModelR\nmembership\"1\n\x16\x44\x65leteMembershipParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"\x18\n\x16\x44\x65leteMembershipResult2\xe9\x04\n\x11MembershipService\x12x\n\x10\x43reateMembership\x12\x31.moego.admin.membership.v1.CreateMembershipParams\x1a\x31.moego.admin.membership.v1.CreateMembershipResult\x12o\n\rGetMembership\x12..moego.admin.membership.v1.GetMembershipParams\x1a..moego.admin.membership.v1.GetMembershipResult\x12u\n\x0fListMemberships\x12\x30.moego.admin.membership.v1.ListMembershipsParams\x1a\x30.moego.admin.membership.v1.ListMembershipsResult\x12x\n\x10UpdateMembership\x12\x31.moego.admin.membership.v1.UpdateMembershipParams\x1a\x31.moego.admin.membership.v1.UpdateMembershipResult\x12x\n\x10\x44\x65leteMembership\x12\x31.moego.admin.membership.v1.DeleteMembershipParams\x1a\x31.moego.admin.membership.v1.DeleteMembershipResultB\x85\x01\n!com.moego.idl.admin.membership.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/membership/v1;membershipapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.admin.membership.v1.membership_admin_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n!com.moego.idl.admin.membership.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/membership/v1;membershipapipb'
  _globals['_CREATEMEMBERSHIPPARAMS'].fields_by_name['membership_def']._loaded_options = None
  _globals['_CREATEMEMBERSHIPPARAMS'].fields_by_name['membership_def']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETMEMBERSHIPPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_GETMEMBERSHIPPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEMEMBERSHIPPARAMS'].fields_by_name['membership_def']._loaded_options = None
  _globals['_UPDATEMEMBERSHIPPARAMS'].fields_by_name['membership_def']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_DELETEMEMBERSHIPPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_DELETEMEMBERSHIPPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEMEMBERSHIPPARAMS']._serialized_start=248
  _globals['_CREATEMEMBERSHIPPARAMS']._serialized_end=370
  _globals['_CREATEMEMBERSHIPRESULT']._serialized_start=372
  _globals['_CREATEMEMBERSHIPRESULT']._serialized_end=473
  _globals['_GETMEMBERSHIPPARAMS']._serialized_start=475
  _globals['_GETMEMBERSHIPPARAMS']._serialized_end=521
  _globals['_GETMEMBERSHIPRESULT']._serialized_start=523
  _globals['_GETMEMBERSHIPRESULT']._serialized_end=621
  _globals['_LISTMEMBERSHIPSPARAMS']._serialized_start=623
  _globals['_LISTMEMBERSHIPSPARAMS']._serialized_end=733
  _globals['_LISTMEMBERSHIPSRESULT']._serialized_start=736
  _globals['_LISTMEMBERSHIPSRESULT']._serialized_end=906
  _globals['_UPDATEMEMBERSHIPPARAMS']._serialized_start=909
  _globals['_UPDATEMEMBERSHIPPARAMS']._serialized_end=1047
  _globals['_UPDATEMEMBERSHIPRESULT']._serialized_start=1049
  _globals['_UPDATEMEMBERSHIPRESULT']._serialized_end=1150
  _globals['_DELETEMEMBERSHIPPARAMS']._serialized_start=1152
  _globals['_DELETEMEMBERSHIPPARAMS']._serialized_end=1201
  _globals['_DELETEMEMBERSHIPRESULT']._serialized_start=1203
  _globals['_DELETEMEMBERSHIPRESULT']._serialized_end=1227
  _globals['_MEMBERSHIPSERVICE']._serialized_start=1230
  _globals['_MEMBERSHIPSERVICE']._serialized_end=1847
# @@protoc_insertion_point(module_scope)
