# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/enterprise/campaign/v1/campaign_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/enterprise/campaign/v1/campaign_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.enterprise.v1 import campaign_models_pb2 as moego_dot_models_dot_enterprise_dot_v1_dot_campaign__models__pb2
from moego.models.enterprise.v1 import tenant_models_pb2 as moego_dot_models_dot_enterprise_dot_v1_dot_tenant__models__pb2
from moego.models.message.v1 import message_template_models_pb2 as moego_dot_models_dot_message_dot_v1_dot_message__template__models__pb2
from moego.service.enterprise.v1 import campaign_service_pb2 as moego_dot_service_dot_enterprise_dot_v1_dot_campaign__service__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/moego/enterprise/campaign/v1/campaign_api.proto\x12\x1cmoego.enterprise.campaign.v1\x1a\x30moego/models/enterprise/v1/campaign_models.proto\x1a.moego/models/enterprise/v1/tenant_models.proto\x1a\x35moego/models/message/v1/message_template_models.proto\x1a\x32moego/service/enterprise/v1/campaign_service.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\x90\x01\n\x13SendTestEmailParams\x12\"\n\x07subject\x18\x01 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xc8\x01R\x07subject\x12#\n\x07\x63ontent\x18\x02 \x01(\tB\t\xfa\x42\x06r\x04\x18\xa0\x8d\x06R\x07\x63ontent\x12\x30\n\x0frecipient_email\x18\x03 \x01(\tB\x07\xfa\x42\x04r\x02`\x01R\x0erecipientEmail\"\x15\n\x13SendTestEmailResult\"\xd5\x01\n\x14\x43reateTemplateParams\x12\x12\n\x04name\x18\x01 \x01(\tR\x04name\x12 \n\x0b\x64\x65scription\x18\x02 \x01(\tR\x0b\x64\x65scription\x12=\n\x04type\x18\x03 \x01(\x0e\x32).moego.models.enterprise.v1.Campaign.TypeR\x04type\x12\x14\n\x05\x63over\x18\x04 \x01(\tR\x05\x63over\x12\x18\n\x07subject\x18\x05 \x01(\tR\x07subject\x12\x18\n\x07\x63ontent\x18\x06 \x01(\tR\x07\x63ontent\"`\n\x14\x43reateTemplateResult\x12H\n\x08template\x18\x01 \x01(\x0b\x32,.moego.models.enterprise.v1.CampaignTemplateR\x08template\",\n\x11GetTemplateParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"]\n\x11GetTemplateResult\x12H\n\x08template\x18\x01 \x01(\x0b\x32,.moego.models.enterprise.v1.CampaignTemplateR\x08template\"\xfa\x01\n\x14UpdateTemplateParams\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n\x04name\x18\x02 \x01(\tH\x00R\x04name\x88\x01\x01\x12%\n\x0b\x64\x65scription\x18\x03 \x01(\tH\x01R\x0b\x64\x65scription\x88\x01\x01\x12\x19\n\x05\x63over\x18\x04 \x01(\tH\x02R\x05\x63over\x88\x01\x01\x12\x1d\n\x07subject\x18\x05 \x01(\tH\x03R\x07subject\x88\x01\x01\x12\x1d\n\x07\x63ontent\x18\x06 \x01(\tH\x04R\x07\x63ontent\x88\x01\x01\x42\x07\n\x05_nameB\x0e\n\x0c_descriptionB\x08\n\x06_coverB\n\n\x08_subjectB\n\n\x08_content\"`\n\x14UpdateTemplateResult\x12H\n\x08template\x18\x01 \x01(\x0b\x32,.moego.models.enterprise.v1.CampaignTemplateR\x08template\"\xaa\x01\n\x13ListTemplatesParams\x12\x41\n\npagination\x18\x01 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestR\npagination\x12P\n\x06\x66ilter\x18\x02 \x01(\x0b\x32\x38.moego.service.enterprise.v1.ListTemplatesRequest.FilterR\x06\x66ilter\"\xa5\x01\n\x13ListTemplatesResult\x12\x42\n\npagination\x18\x01 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\x12J\n\ttemplates\x18\x02 \x03(\x0b\x32,.moego.models.enterprise.v1.CampaignTemplateR\ttemplates\"k\n\x13PushTemplatesParams\x12\x10\n\x03ids\x18\x01 \x03(\x03R\x03ids\x12\x42\n\x07targets\x18\x02 \x03(\x0b\x32(.moego.models.enterprise.v1.TenantObjectR\x07targets\"/\n\x13PushTemplatesResult\x12\x18\n\x07success\x18\x01 \x01(\x08R\x07success\"_\n\x1eListTemplatePlaceholdersParams\x12=\n\x04type\x18\x01 \x01(\x0e\x32).moego.models.enterprise.v1.Campaign.TypeR\x04type\"\x83\x01\n\x1eListTemplatePlaceholdersResult\x12\x61\n\x0cplaceholders\x18\x01 \x03(\x0b\x32=.moego.models.message.v1.MessageTemplatePlaceholderSimpleViewR\x0cplaceholders2\x80\x07\n\x0f\x43\x61mpaignService\x12w\n\rSendTestEmail\x12\x31.moego.enterprise.campaign.v1.SendTestEmailParams\x1a\x31.moego.enterprise.campaign.v1.SendTestEmailResult\"\x00\x12z\n\x0e\x43reateTemplate\x12\x32.moego.enterprise.campaign.v1.CreateTemplateParams\x1a\x32.moego.enterprise.campaign.v1.CreateTemplateResult\"\x00\x12q\n\x0bGetTemplate\x12/.moego.enterprise.campaign.v1.GetTemplateParams\x1a/.moego.enterprise.campaign.v1.GetTemplateResult\"\x00\x12z\n\x0eUpdateTemplate\x12\x32.moego.enterprise.campaign.v1.UpdateTemplateParams\x1a\x32.moego.enterprise.campaign.v1.UpdateTemplateResult\"\x00\x12w\n\rListTemplates\x12\x31.moego.enterprise.campaign.v1.ListTemplatesParams\x1a\x31.moego.enterprise.campaign.v1.ListTemplatesResult\"\x00\x12w\n\rPushTemplates\x12\x31.moego.enterprise.campaign.v1.PushTemplatesParams\x1a\x31.moego.enterprise.campaign.v1.PushTemplatesResult\"\x00\x12\x96\x01\n\x18ListTemplatePlaceholders\x12<.moego.enterprise.campaign.v1.ListTemplatePlaceholdersParams\x1a<.moego.enterprise.campaign.v1.ListTemplatePlaceholdersResultB\x89\x01\n$com.moego.idl.enterprise.campaign.v1P\x01Z_github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/campaign/v1;campaignapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.enterprise.campaign.v1.campaign_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.enterprise.campaign.v1P\001Z_github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/campaign/v1;campaignapipb'
  _globals['_SENDTESTEMAILPARAMS'].fields_by_name['subject']._loaded_options = None
  _globals['_SENDTESTEMAILPARAMS'].fields_by_name['subject']._serialized_options = b'\372B\005r\003\030\310\001'
  _globals['_SENDTESTEMAILPARAMS'].fields_by_name['content']._loaded_options = None
  _globals['_SENDTESTEMAILPARAMS'].fields_by_name['content']._serialized_options = b'\372B\006r\004\030\240\215\006'
  _globals['_SENDTESTEMAILPARAMS'].fields_by_name['recipient_email']._loaded_options = None
  _globals['_SENDTESTEMAILPARAMS'].fields_by_name['recipient_email']._serialized_options = b'\372B\004r\002`\001'
  _globals['_GETTEMPLATEPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_GETTEMPLATEPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SENDTESTEMAILPARAMS']._serialized_start=354
  _globals['_SENDTESTEMAILPARAMS']._serialized_end=498
  _globals['_SENDTESTEMAILRESULT']._serialized_start=500
  _globals['_SENDTESTEMAILRESULT']._serialized_end=521
  _globals['_CREATETEMPLATEPARAMS']._serialized_start=524
  _globals['_CREATETEMPLATEPARAMS']._serialized_end=737
  _globals['_CREATETEMPLATERESULT']._serialized_start=739
  _globals['_CREATETEMPLATERESULT']._serialized_end=835
  _globals['_GETTEMPLATEPARAMS']._serialized_start=837
  _globals['_GETTEMPLATEPARAMS']._serialized_end=881
  _globals['_GETTEMPLATERESULT']._serialized_start=883
  _globals['_GETTEMPLATERESULT']._serialized_end=976
  _globals['_UPDATETEMPLATEPARAMS']._serialized_start=979
  _globals['_UPDATETEMPLATEPARAMS']._serialized_end=1229
  _globals['_UPDATETEMPLATERESULT']._serialized_start=1231
  _globals['_UPDATETEMPLATERESULT']._serialized_end=1327
  _globals['_LISTTEMPLATESPARAMS']._serialized_start=1330
  _globals['_LISTTEMPLATESPARAMS']._serialized_end=1500
  _globals['_LISTTEMPLATESRESULT']._serialized_start=1503
  _globals['_LISTTEMPLATESRESULT']._serialized_end=1668
  _globals['_PUSHTEMPLATESPARAMS']._serialized_start=1670
  _globals['_PUSHTEMPLATESPARAMS']._serialized_end=1777
  _globals['_PUSHTEMPLATESRESULT']._serialized_start=1779
  _globals['_PUSHTEMPLATESRESULT']._serialized_end=1826
  _globals['_LISTTEMPLATEPLACEHOLDERSPARAMS']._serialized_start=1828
  _globals['_LISTTEMPLATEPLACEHOLDERSPARAMS']._serialized_end=1923
  _globals['_LISTTEMPLATEPLACEHOLDERSRESULT']._serialized_start=1926
  _globals['_LISTTEMPLATEPLACEHOLDERSRESULT']._serialized_end=2057
  _globals['_CAMPAIGNSERVICE']._serialized_start=2060
  _globals['_CAMPAIGNSERVICE']._serialized_end=2956
# @@protoc_insertion_point(module_scope)
