from moego.models.account.v1 import account_models_pb2 as _account_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetAccountInfoRequest(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GetAccountInfoResponse(_message.Message):
    __slots__ = ("account", "account_security")
    ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    ACCOUNT_SECURITY_FIELD_NUMBER: _ClassVar[int]
    account: _account_models_pb2.AccountModel
    account_security: _account_models_pb2.AccountSecurityModel
    def __init__(self, account: _Optional[_Union[_account_models_pb2.AccountModel, _Mapping]] = ..., account_security: _Optional[_Union[_account_models_pb2.AccountSecurityModel, _Mapping]] = ...) -> None: ...

class UpdateProfileRequest(_message.Message):
    __slots__ = ("first_name", "last_name", "avatar_path")
    FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
    LAST_NAME_FIELD_NUMBER: _ClassVar[int]
    AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
    first_name: str
    last_name: str
    avatar_path: str
    def __init__(self, first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., avatar_path: _Optional[str] = ...) -> None: ...

class UpdateProfileResponse(_message.Message):
    __slots__ = ("account",)
    ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    account: _account_models_pb2.AccountModel
    def __init__(self, account: _Optional[_Union[_account_models_pb2.AccountModel, _Mapping]] = ...) -> None: ...

class UpdatePasswordRequest(_message.Message):
    __slots__ = ("old_password", "new_password")
    OLD_PASSWORD_FIELD_NUMBER: _ClassVar[int]
    NEW_PASSWORD_FIELD_NUMBER: _ClassVar[int]
    old_password: str
    new_password: str
    def __init__(self, old_password: _Optional[str] = ..., new_password: _Optional[str] = ...) -> None: ...

class UpdatePasswordResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
