# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/enterprise/tenant/v1/tenant_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/enterprise/tenant/v1/tenant_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.enterprise.v1 import tenant_defs_pb2 as moego_dot_models_dot_enterprise_dot_v1_dot_tenant__defs__pb2
from moego.models.enterprise.v1 import tenant_group_models_pb2 as moego_dot_models_dot_enterprise_dot_v1_dot_tenant__group__models__pb2
from moego.models.enterprise.v1 import tenant_models_pb2 as moego_dot_models_dot_enterprise_dot_v1_dot_tenant__models__pb2
from moego.models.enterprise.v1 import tenant_template_models_pb2 as moego_dot_models_dot_enterprise_dot_v1_dot_tenant__template__models__pb2
from moego.models.enterprise.v1 import territory_models_pb2 as moego_dot_models_dot_enterprise_dot_v1_dot_territory__models__pb2
from moego.models.organization.v1 import staff_defs_pb2 as moego_dot_models_dot_organization_dot_v1_dot_staff__defs__pb2
from moego.models.organization.v1 import staff_models_pb2 as moego_dot_models_dot_organization_dot_v1_dot_staff__models__pb2
from moego.utils.v2 import condition_messages_pb2 as moego_dot_utils_dot_v2_dot_condition__messages__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+moego/enterprise/tenant/v1/tenant_api.proto\x12\x1amoego.enterprise.tenant.v1\x1a,moego/models/enterprise/v1/tenant_defs.proto\x1a\x34moego/models/enterprise/v1/tenant_group_models.proto\x1a.moego/models/enterprise/v1/tenant_models.proto\x1a\x37moego/models/enterprise/v1/tenant_template_models.proto\x1a\x31moego/models/enterprise/v1/territory_models.proto\x1a-moego/models/organization/v1/staff_defs.proto\x1a/moego/models/organization/v1/staff_models.proto\x1a\'moego/utils/v2/condition_messages.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\xb0\x02\n\x12\x43reateTenantParams\x12+\n\naccount_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\taccountId\x88\x01\x01\x12\x31\n\renterprise_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x01R\x0c\x65nterpriseId\x88\x01\x01\x12\x43\n\x06tenant\x18\x03 \x01(\x0b\x32+.moego.models.enterprise.v1.CreateTenantDefR\x06tenant\x12\x46\n\tgroup_def\x18\x04 \x01(\x0b\x32$.moego.models.enterprise.v1.GroupDefH\x02R\x08groupDef\x88\x01\x01\x42\r\n\x0b_account_idB\x10\n\x0e_enterprise_idB\x0c\n\n_group_def\"U\n\x12\x43reateTenantResult\x12?\n\x06tenant\x18\x01 \x01(\x0b\x32\'.moego.models.enterprise.v1.TenantModelR\x06tenant\"\x94\x02\n\x12UpdateTenantParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12+\n\naccount_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\taccountId\x88\x01\x01\x12H\n\x06tenant\x18\x03 \x01(\x0b\x32+.moego.models.enterprise.v1.UpdateTenantDefH\x01R\x06tenant\x88\x01\x01\x12\x46\n\tgroup_def\x18\x04 \x01(\x0b\x32$.moego.models.enterprise.v1.GroupDefH\x02R\x08groupDef\x88\x01\x01\x42\r\n\x0b_account_idB\t\n\x07_tenantB\x0c\n\n_group_def\"U\n\x12UpdateTenantResult\x12?\n\x06tenant\x18\x01 \x01(\x0b\x32\'.moego.models.enterprise.v1.TenantModelR\x06tenant\"-\n\x12\x44\x65leteTenantParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"\x14\n\x12\x44\x65leteTenantResult\"\xb0\x01\n\x0fGetTenantParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12*\n\x0eneed_territory\x18\x02 \x01(\x08H\x00R\rneedTerritory\x88\x01\x01\x12/\n\x11need_tenant_group\x18\x03 \x01(\x08H\x01R\x0fneedTenantGroup\x88\x01\x01\x42\x11\n\x0f_need_territoryB\x14\n\x12_need_tenant_group\"\xee\x03\n\x0fGetTenantResult\x12?\n\x06tenant\x18\x01 \x01(\x0b\x32\'.moego.models.enterprise.v1.TenantModelR\x06tenant\x12X\n\x0ftenant_template\x18\x02 \x01(\x0b\x32/.moego.models.enterprise.v1.TenantTemplateModelR\x0etenantTemplate\x12Q\n\x0bterritories\x18\x03 \x01(\x0b\x32*.moego.models.enterprise.v1.TerritoryModelH\x00R\x0bterritories\x88\x01\x01\x12O\n\x0ctenant_group\x18\x04 \x03(\x0b\x32,.moego.models.enterprise.v1.TenantGroupModelR\x0btenantGroup\x12>\n\x05staff\x18\x05 \x01(\x0b\x32(.moego.models.organization.v1.StaffModelR\x05staff\x12L\n\x0bstaff_email\x18\x06 \x01(\x0b\x32+.moego.models.organization.v1.StaffEmailDefR\nstaffEmailB\x0e\n\x0c_territories\"\xe8\x03\n\x10ListTenantParams\x12K\n\npagination\x18\x01 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\npagination\x12/\n\x11need_tenant_group\x18\x02 \x01(\x08H\x00R\x0fneedTenantGroup\x88\x01\x01\x12\x37\n\x08order_by\x18\x03 \x01(\x0b\x32\x17.moego.utils.v2.OrderByH\x01R\x07orderBy\x88\x01\x01\x12P\n\x06\x66ilter\x18\x04 \x01(\x0b\x32\x33.moego.enterprise.tenant.v1.ListTenantParams.FilterH\x02R\x06\x66ilter\x88\x01\x01\x1a\x9c\x01\n\x06\x46ilter\x12\x1b\n\tgroup_ids\x18\x01 \x03(\x03R\x08groupIds\x12J\n\x08statuses\x18\x03 \x03(\x0e\x32..moego.models.enterprise.v1.TenantModel.StatusR\x08statuses\x12\x1d\n\x07keyword\x18\x04 \x01(\tH\x00R\x07keyword\x88\x01\x01\x42\n\n\x08_keywordB\x14\n\x12_need_tenant_groupB\x0b\n\t_order_byB\t\n\x07_filter\"\xb2\x04\n\x10ListTenantResult\x12\x41\n\x07tenants\x18\x01 \x03(\x0b\x32\'.moego.models.enterprise.v1.TenantModelR\x07tenants\x12>\n\x05staff\x18\x02 \x03(\x0b\x32(.moego.models.organization.v1.StaffModelR\x05staff\x12X\n\x0ftenant_template\x18\x03 \x03(\x0b\x32/.moego.models.enterprise.v1.TenantTemplateModelR\x0etenantTemplate\x12L\n\x0bterritories\x18\x04 \x03(\x0b\x32*.moego.models.enterprise.v1.TerritoryModelR\x0bterritories\x12O\n\x0ctenant_group\x18\x05 \x03(\x0b\x32,.moego.models.enterprise.v1.TenantGroupModelR\x0btenantGroup\x12^\n\x15tenant_group_relation\x18\x06 \x03(\x0b\x32*.moego.models.enterprise.v1.TenantGroupDefR\x13tenantGroupRelation\x12\x42\n\npagination\x18\x07 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\"\xa0\x01\n\x15ListTenantGroupParams\x12U\n\x06\x66ilter\x18\x01 \x01(\x0b\x32\x38.moego.enterprise.tenant.v1.ListTenantGroupParams.FilterH\x00R\x06\x66ilter\x88\x01\x01\x1a%\n\x06\x46ilter\x12\x1b\n\tgroup_ids\x18\x01 \x03(\x03R\x08groupIdsB\t\n\x07_filter\"j\n\x15ListTenantGroupResult\x12Q\n\rtenant_groups\x18\x01 \x03(\x0b\x32,.moego.models.enterprise.v1.TenantGroupModelR\x0ctenantGroups\"=\n\x17\x44\x65leteTenantGroupParams\x12\"\n\x08group_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07groupId\"\x19\n\x17\x44\x65leteTenantGroupResult\"\x1d\n\x1bListAllTenantAndGroupParams\"\x9e\x02\n\x1bListAllTenantAndGroupResult\x12I\n\x0ctenant_views\x18\x01 \x03(\x0b\x32&.moego.models.enterprise.v1.TenantViewR\x0btenantViews\x12^\n\x15group_tenant_relation\x18\x02 \x03(\x0b\x32*.moego.models.enterprise.v1.GroupTenantDefR\x13groupTenantRelation\x12*\n\x11\x61ssign_tenant_ids\x18\x03 \x03(\x03R\x0f\x61ssignTenantIds\x12(\n\x10\x61ssign_group_ids\x18\x04 \x03(\x03R\x0e\x61ssignGroupIds2\xb5\x07\n\rTenantService\x12n\n\x0c\x43reateTenant\x12..moego.enterprise.tenant.v1.CreateTenantParams\x1a..moego.enterprise.tenant.v1.CreateTenantResult\x12n\n\x0cUpdateTenant\x12..moego.enterprise.tenant.v1.UpdateTenantParams\x1a..moego.enterprise.tenant.v1.UpdateTenantResult\x12\x65\n\tGetTenant\x12+.moego.enterprise.tenant.v1.GetTenantParams\x1a+.moego.enterprise.tenant.v1.GetTenantResult\x12h\n\nListTenant\x12,.moego.enterprise.tenant.v1.ListTenantParams\x1a,.moego.enterprise.tenant.v1.ListTenantResult\x12n\n\x0c\x44\x65leteTenant\x12..moego.enterprise.tenant.v1.DeleteTenantParams\x1a..moego.enterprise.tenant.v1.DeleteTenantResult\x12x\n\x10ListTenantGroups\x12\x31.moego.enterprise.tenant.v1.ListTenantGroupParams\x1a\x31.moego.enterprise.tenant.v1.ListTenantGroupResult\x12}\n\x11\x44\x65leteTenantGroup\x12\x33.moego.enterprise.tenant.v1.DeleteTenantGroupParams\x1a\x33.moego.enterprise.tenant.v1.DeleteTenantGroupResult\x12\x89\x01\n\x15ListAllTenantAndGroup\x12\x37.moego.enterprise.tenant.v1.ListAllTenantAndGroupParams\x1a\x37.moego.enterprise.tenant.v1.ListAllTenantAndGroupResultB\x83\x01\n\"com.moego.idl.enterprise.tenant.v1P\x01Z[github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/tenant/v1;tenantapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.enterprise.tenant.v1.tenant_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.moego.idl.enterprise.tenant.v1P\001Z[github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/tenant/v1;tenantapipb'
  _globals['_CREATETENANTPARAMS'].fields_by_name['account_id']._loaded_options = None
  _globals['_CREATETENANTPARAMS'].fields_by_name['account_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATETENANTPARAMS'].fields_by_name['enterprise_id']._loaded_options = None
  _globals['_CREATETENANTPARAMS'].fields_by_name['enterprise_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATETENANTPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATETENANTPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATETENANTPARAMS'].fields_by_name['account_id']._loaded_options = None
  _globals['_UPDATETENANTPARAMS'].fields_by_name['account_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETETENANTPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_DELETETENANTPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETTENANTPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_GETTENANTPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTTENANTPARAMS'].fields_by_name['pagination']._loaded_options = None
  _globals['_LISTTENANTPARAMS'].fields_by_name['pagination']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_DELETETENANTGROUPPARAMS'].fields_by_name['group_id']._loaded_options = None
  _globals['_DELETETENANTGROUPPARAMS'].fields_by_name['group_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATETENANTPARAMS']._serialized_start=536
  _globals['_CREATETENANTPARAMS']._serialized_end=840
  _globals['_CREATETENANTRESULT']._serialized_start=842
  _globals['_CREATETENANTRESULT']._serialized_end=927
  _globals['_UPDATETENANTPARAMS']._serialized_start=930
  _globals['_UPDATETENANTPARAMS']._serialized_end=1206
  _globals['_UPDATETENANTRESULT']._serialized_start=1208
  _globals['_UPDATETENANTRESULT']._serialized_end=1293
  _globals['_DELETETENANTPARAMS']._serialized_start=1295
  _globals['_DELETETENANTPARAMS']._serialized_end=1340
  _globals['_DELETETENANTRESULT']._serialized_start=1342
  _globals['_DELETETENANTRESULT']._serialized_end=1362
  _globals['_GETTENANTPARAMS']._serialized_start=1365
  _globals['_GETTENANTPARAMS']._serialized_end=1541
  _globals['_GETTENANTRESULT']._serialized_start=1544
  _globals['_GETTENANTRESULT']._serialized_end=2038
  _globals['_LISTTENANTPARAMS']._serialized_start=2041
  _globals['_LISTTENANTPARAMS']._serialized_end=2529
  _globals['_LISTTENANTPARAMS_FILTER']._serialized_start=2327
  _globals['_LISTTENANTPARAMS_FILTER']._serialized_end=2483
  _globals['_LISTTENANTRESULT']._serialized_start=2532
  _globals['_LISTTENANTRESULT']._serialized_end=3094
  _globals['_LISTTENANTGROUPPARAMS']._serialized_start=3097
  _globals['_LISTTENANTGROUPPARAMS']._serialized_end=3257
  _globals['_LISTTENANTGROUPPARAMS_FILTER']._serialized_start=2327
  _globals['_LISTTENANTGROUPPARAMS_FILTER']._serialized_end=2364
  _globals['_LISTTENANTGROUPRESULT']._serialized_start=3259
  _globals['_LISTTENANTGROUPRESULT']._serialized_end=3365
  _globals['_DELETETENANTGROUPPARAMS']._serialized_start=3367
  _globals['_DELETETENANTGROUPPARAMS']._serialized_end=3428
  _globals['_DELETETENANTGROUPRESULT']._serialized_start=3430
  _globals['_DELETETENANTGROUPRESULT']._serialized_end=3455
  _globals['_LISTALLTENANTANDGROUPPARAMS']._serialized_start=3457
  _globals['_LISTALLTENANTANDGROUPPARAMS']._serialized_end=3486
  _globals['_LISTALLTENANTANDGROUPRESULT']._serialized_start=3489
  _globals['_LISTALLTENANTANDGROUPRESULT']._serialized_end=3775
  _globals['_TENANTSERVICE']._serialized_start=3778
  _globals['_TENANTSERVICE']._serialized_end=4727
# @@protoc_insertion_point(module_scope)
