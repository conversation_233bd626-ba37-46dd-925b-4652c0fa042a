from moego.models.file.v2 import file_models_pb2 as _file_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetUploadPresignedURLParams(_message.Message):
    __slots__ = ("usage", "md5", "file_name", "file_size_byte", "owner_type", "owner_id")
    USAGE_FIELD_NUMBER: _ClassVar[int]
    MD5_FIELD_NUMBER: _ClassVar[int]
    FILE_NAME_FIELD_NUMBER: _ClassVar[int]
    FILE_SIZE_BYTE_FIELD_NUMBER: _ClassVar[int]
    OWNER_TYPE_FIELD_NUMBER: _ClassVar[int]
    OWNER_ID_FIELD_NUMBER: _ClassVar[int]
    usage: str
    md5: str
    file_name: str
    file_size_byte: int
    owner_type: str
    owner_id: int
    def __init__(self, usage: _Optional[str] = ..., md5: _Optional[str] = ..., file_name: _Optional[str] = ..., file_size_byte: _Optional[int] = ..., owner_type: _Optional[str] = ..., owner_id: _Optional[int] = ...) -> None: ...

class GetUploadPresignedURLResult(_message.Message):
    __slots__ = ("presigned_url", "access_url", "content_type", "file_id")
    PRESIGNED_URL_FIELD_NUMBER: _ClassVar[int]
    ACCESS_URL_FIELD_NUMBER: _ClassVar[int]
    CONTENT_TYPE_FIELD_NUMBER: _ClassVar[int]
    FILE_ID_FIELD_NUMBER: _ClassVar[int]
    presigned_url: str
    access_url: str
    content_type: str
    file_id: int
    def __init__(self, presigned_url: _Optional[str] = ..., access_url: _Optional[str] = ..., content_type: _Optional[str] = ..., file_id: _Optional[int] = ...) -> None: ...

class QueryFileParams(_message.Message):
    __slots__ = ("file_id",)
    FILE_ID_FIELD_NUMBER: _ClassVar[int]
    file_id: int
    def __init__(self, file_id: _Optional[int] = ...) -> None: ...

class QueryFileResult(_message.Message):
    __slots__ = ("file",)
    FILE_FIELD_NUMBER: _ClassVar[int]
    file: _file_models_pb2.FileModel
    def __init__(self, file: _Optional[_Union[_file_models_pb2.FileModel, _Mapping]] = ...) -> None: ...
