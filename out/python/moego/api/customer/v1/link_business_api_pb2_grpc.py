# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from moego.api.customer.v1 import link_business_api_pb2 as moego_dot_api_dot_customer_dot_v1_dot_link__business__api__pb2


class LinkBusinessServiceStub(object):
    """step 1: enter invoice page (client.moego.pet/invoice/xxx)
    step 2: get:
    session: account
    page: customer (account_id)
    account == null && customer.account_id == null -> register (disable input phone) -> bind
    account == null && customer.account_id != null -> login
    account != null && customer.account_id == null
    phone number is same -> bind & redirect (LinkBusiness.LinkCustomer)
    phone number is diff -> register (disable input phone) -> bind
    account != null && customer.account_id != null
    account id is same -> redirect profile
    account id is diff -> login
    step 3: click button is login or not

    api: LinkBusinessService.LinkCustomer(account_id (auth customer_id), customer_id)
    1. get business customer list
    2. link customer
    2. get customer pet
    3. create and link customer pet
    3. create address
    pet metadata list
    customer_account (account_id)
    business_customer (customer_id, business_id, phone_number, account_id)
    customer_pet (customer_pet_id, business_id, customer_id, pet_id)
    client_pet (pet_id, account_id)
    client_address
    BusinessService.CreateBusiness
    CreateBusinessService.CreateBusiness

    link service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.LinkCustomerAndPet = channel.unary_unary(
                '/moego.api.customer.v1.LinkBusinessService/LinkCustomerAndPet',
                request_serializer=moego_dot_api_dot_customer_dot_v1_dot_link__business__api__pb2.LinkCustomerRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.GetLinkBusiness = channel.unary_unary(
                '/moego.api.customer.v1.LinkBusinessService/GetLinkBusiness',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=moego_dot_api_dot_customer_dot_v1_dot_link__business__api__pb2.ClientPortalLinkBusinessListResponse.FromString,
                _registered_method=True)


class LinkBusinessServiceServicer(object):
    """step 1: enter invoice page (client.moego.pet/invoice/xxx)
    step 2: get:
    session: account
    page: customer (account_id)
    account == null && customer.account_id == null -> register (disable input phone) -> bind
    account == null && customer.account_id != null -> login
    account != null && customer.account_id == null
    phone number is same -> bind & redirect (LinkBusiness.LinkCustomer)
    phone number is diff -> register (disable input phone) -> bind
    account != null && customer.account_id != null
    account id is same -> redirect profile
    account id is diff -> login
    step 3: click button is login or not

    api: LinkBusinessService.LinkCustomer(account_id (auth customer_id), customer_id)
    1. get business customer list
    2. link customer
    2. get customer pet
    3. create and link customer pet
    3. create address
    pet metadata list
    customer_account (account_id)
    business_customer (customer_id, business_id, phone_number, account_id)
    customer_pet (customer_pet_id, business_id, customer_id, pet_id)
    client_pet (pet_id, account_id)
    client_address
    BusinessService.CreateBusiness
    CreateBusinessService.CreateBusiness

    link service
    """

    def LinkCustomerAndPet(self, request, context):
        """link new customer and pet
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLinkBusiness(self, request, context):
        """get link business
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_LinkBusinessServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'LinkCustomerAndPet': grpc.unary_unary_rpc_method_handler(
                    servicer.LinkCustomerAndPet,
                    request_deserializer=moego_dot_api_dot_customer_dot_v1_dot_link__business__api__pb2.LinkCustomerRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'GetLinkBusiness': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLinkBusiness,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=moego_dot_api_dot_customer_dot_v1_dot_link__business__api__pb2.ClientPortalLinkBusinessListResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.customer.v1.LinkBusinessService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.customer.v1.LinkBusinessService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class LinkBusinessService(object):
    """step 1: enter invoice page (client.moego.pet/invoice/xxx)
    step 2: get:
    session: account
    page: customer (account_id)
    account == null && customer.account_id == null -> register (disable input phone) -> bind
    account == null && customer.account_id != null -> login
    account != null && customer.account_id == null
    phone number is same -> bind & redirect (LinkBusiness.LinkCustomer)
    phone number is diff -> register (disable input phone) -> bind
    account != null && customer.account_id != null
    account id is same -> redirect profile
    account id is diff -> login
    step 3: click button is login or not

    api: LinkBusinessService.LinkCustomer(account_id (auth customer_id), customer_id)
    1. get business customer list
    2. link customer
    2. get customer pet
    3. create and link customer pet
    3. create address
    pet metadata list
    customer_account (account_id)
    business_customer (customer_id, business_id, phone_number, account_id)
    customer_pet (customer_pet_id, business_id, customer_id, pet_id)
    client_pet (pet_id, account_id)
    client_address
    BusinessService.CreateBusiness
    CreateBusinessService.CreateBusiness

    link service
    """

    @staticmethod
    def LinkCustomerAndPet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.customer.v1.LinkBusinessService/LinkCustomerAndPet',
            moego_dot_api_dot_customer_dot_v1_dot_link__business__api__pb2.LinkCustomerRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetLinkBusiness(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.customer.v1.LinkBusinessService/GetLinkBusiness',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            moego_dot_api_dot_customer_dot_v1_dot_link__business__api__pb2.ClientPortalLinkBusinessListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
