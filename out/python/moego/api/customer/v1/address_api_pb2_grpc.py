# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from moego.api.customer.v1 import address_api_pb2 as moego_dot_api_dot_customer_dot_v1_dot_address__api__pb2
from moego.utils.v1 import id_messages_pb2 as moego_dot_utils_dot_v1_dot_id__messages__pb2


class AddressServiceStub(object):
    """address service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetAddressList = channel.unary_unary(
                '/moego.api.customer.v1.AddressService/GetAddressList',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=moego_dot_api_dot_customer_dot_v1_dot_address__api__pb2.AddressListResponse.FromString,
                _registered_method=True)
        self.AddAddress = channel.unary_unary(
                '/moego.api.customer.v1.AddressService/AddAddress',
                request_serializer=moego_dot_api_dot_customer_dot_v1_dot_address__api__pb2.AddAddressRequest.SerializeToString,
                response_deserializer=moego_dot_utils_dot_v1_dot_id__messages__pb2.Id.FromString,
                _registered_method=True)
        self.UpdateAddress = channel.unary_unary(
                '/moego.api.customer.v1.AddressService/UpdateAddress',
                request_serializer=moego_dot_api_dot_customer_dot_v1_dot_address__api__pb2.UpdateAddressRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.DeleteAddress = channel.unary_unary(
                '/moego.api.customer.v1.AddressService/DeleteAddress',
                request_serializer=moego_dot_utils_dot_v1_dot_id__messages__pb2.Id.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)


class AddressServiceServicer(object):
    """address service
    """

    def GetAddressList(self, request, context):
        """get address list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddAddress(self, request, context):
        """add address
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateAddress(self, request, context):
        """update address
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAddress(self, request, context):
        """delete address
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AddressServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetAddressList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAddressList,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=moego_dot_api_dot_customer_dot_v1_dot_address__api__pb2.AddressListResponse.SerializeToString,
            ),
            'AddAddress': grpc.unary_unary_rpc_method_handler(
                    servicer.AddAddress,
                    request_deserializer=moego_dot_api_dot_customer_dot_v1_dot_address__api__pb2.AddAddressRequest.FromString,
                    response_serializer=moego_dot_utils_dot_v1_dot_id__messages__pb2.Id.SerializeToString,
            ),
            'UpdateAddress': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateAddress,
                    request_deserializer=moego_dot_api_dot_customer_dot_v1_dot_address__api__pb2.UpdateAddressRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'DeleteAddress': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAddress,
                    request_deserializer=moego_dot_utils_dot_v1_dot_id__messages__pb2.Id.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.customer.v1.AddressService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.customer.v1.AddressService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AddressService(object):
    """address service
    """

    @staticmethod
    def GetAddressList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.customer.v1.AddressService/GetAddressList',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            moego_dot_api_dot_customer_dot_v1_dot_address__api__pb2.AddressListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AddAddress(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.customer.v1.AddressService/AddAddress',
            moego_dot_api_dot_customer_dot_v1_dot_address__api__pb2.AddAddressRequest.SerializeToString,
            moego_dot_utils_dot_v1_dot_id__messages__pb2.Id.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateAddress(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.customer.v1.AddressService/UpdateAddress',
            moego_dot_api_dot_customer_dot_v1_dot_address__api__pb2.UpdateAddressRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteAddress(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.customer.v1.AddressService/DeleteAddress',
            moego_dot_utils_dot_v1_dot_id__messages__pb2.Id.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
