# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/reporting/v2/dashboard_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/reporting/v2/dashboard_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import interval_pb2 as google_dot_type_dot_interval__pb2
from moego.models.reporting.v2 import common_model_pb2 as moego_dot_models_dot_reporting_dot_v2_dot_common__model__pb2
from moego.models.reporting.v2 import dashboard_model_pb2 as moego_dot_models_dot_reporting_dot_v2_dot_dashboard__model__pb2
from moego.models.reporting.v2 import diagram_model_pb2 as moego_dot_models_dot_reporting_dot_v2_dot_diagram__model__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n*moego/api/reporting/v2/dashboard_api.proto\x12\x16moego.api.reporting.v2\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1agoogle/type/interval.proto\x1a,moego/models/reporting/v2/common_model.proto\x1a/moego/models/reporting/v2/dashboard_model.proto\x1a-moego/models/reporting/v2/diagram_model.proto\x1a\x17validate/validate.proto\"\xe7\x01\n\x1aQueryDashboardPagesRequest\x12S\n\x04tabs\x18\x01 \x03(\x0e\x32,.moego.models.reporting.v2.DashboardPage.TabB\x11\xfa\x42\x0e\x92\x01\x0b\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x04tabs\x12\x61\n\x0ereporting_type\x18\x02 \x01(\x0e\x32).moego.models.reporting.v2.ReportingSceneB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\rreportingType\x88\x01\x01\x42\x11\n\x0f_reporting_type\"p\n\x1bQueryDashboardPagesResponse\x12Q\n\x0f\x64\x61shboard_pages\x18\x01 \x03(\x0b\x32(.moego.models.reporting.v2.DashboardPageR\x0e\x64\x61shboardPages\"\x93\x03\n\x19\x46\x65tchDashboardDataRequest\x12/\n\x0b\x64iagram_ids\x18\x01 \x03(\tB\x0e\xfa\x42\x0b\x92\x01\x08\"\x06r\x04\x10\x01\x18\x64R\ndiagramIds\x12\x31\n\x0c\x62usiness_ids\x18\x02 \x03(\x04\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\x32\x02 \x00R\x0b\x62usinessIds\x12\x46\n\x0e\x63urrent_period\x18\x04 \x01(\x0b\x32\x15.google.type.IntervalB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\rcurrentPeriod\x12\x43\n\x0fprevious_period\x18\x05 \x01(\x0b\x32\x15.google.type.IntervalH\x00R\x0epreviousPeriod\x88\x01\x01\x12-\n\x13group_by_field_keys\x18\x06 \x03(\tR\x10groupByFieldKeys\x12\x42\n\x07\x66ilters\x18\x07 \x03(\x0b\x32(.moego.models.reporting.v2.FilterRequestR\x07\x66iltersB\x12\n\x10_previous_period\"\xad\x01\n\x1a\x46\x65tchDashboardDataResponse\x12I\n\x0c\x64iagram_data\x18\x01 \x03(\x0b\x32&.moego.models.reporting.v2.DiagramDataR\x0b\x64iagramData\x12\x44\n\x10last_synced_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0elastSyncedTime2\x8f\x02\n\x10\x44\x61shboardService\x12~\n\x13QueryDashboardPages\x12\x32.moego.api.reporting.v2.QueryDashboardPagesRequest\x1a\x33.moego.api.reporting.v2.QueryDashboardPagesResponse\x12{\n\x12\x46\x65tchDashboardData\x12\x31.moego.api.reporting.v2.FetchDashboardDataRequest\x1a\x32.moego.api.reporting.v2.FetchDashboardDataResponseB~\n\x1e\x63om.moego.idl.api.reporting.v2P\x01ZZgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/reporting/v2;reportingapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.reporting.v2.dashboard_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\036com.moego.idl.api.reporting.v2P\001ZZgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/reporting/v2;reportingapipb'
  _globals['_QUERYDASHBOARDPAGESREQUEST'].fields_by_name['tabs']._loaded_options = None
  _globals['_QUERYDASHBOARDPAGESREQUEST'].fields_by_name['tabs']._serialized_options = b'\372B\016\222\001\013\030\001\"\007\202\001\004\020\001 \000'
  _globals['_QUERYDASHBOARDPAGESREQUEST'].fields_by_name['reporting_type']._loaded_options = None
  _globals['_QUERYDASHBOARDPAGESREQUEST'].fields_by_name['reporting_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_FETCHDASHBOARDDATAREQUEST'].fields_by_name['diagram_ids']._loaded_options = None
  _globals['_FETCHDASHBOARDDATAREQUEST'].fields_by_name['diagram_ids']._serialized_options = b'\372B\013\222\001\010\"\006r\004\020\001\030d'
  _globals['_FETCHDASHBOARDDATAREQUEST'].fields_by_name['business_ids']._loaded_options = None
  _globals['_FETCHDASHBOARDDATAREQUEST'].fields_by_name['business_ids']._serialized_options = b'\372B\013\222\001\010\030\001\"\0042\002 \000'
  _globals['_FETCHDASHBOARDDATAREQUEST'].fields_by_name['current_period']._loaded_options = None
  _globals['_FETCHDASHBOARDDATAREQUEST'].fields_by_name['current_period']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_QUERYDASHBOARDPAGESREQUEST']._serialized_start=299
  _globals['_QUERYDASHBOARDPAGESREQUEST']._serialized_end=530
  _globals['_QUERYDASHBOARDPAGESRESPONSE']._serialized_start=532
  _globals['_QUERYDASHBOARDPAGESRESPONSE']._serialized_end=644
  _globals['_FETCHDASHBOARDDATAREQUEST']._serialized_start=647
  _globals['_FETCHDASHBOARDDATAREQUEST']._serialized_end=1050
  _globals['_FETCHDASHBOARDDATARESPONSE']._serialized_start=1053
  _globals['_FETCHDASHBOARDDATARESPONSE']._serialized_end=1226
  _globals['_DASHBOARDSERVICE']._serialized_start=1229
  _globals['_DASHBOARDSERVICE']._serialized_end=1500
# @@protoc_insertion_point(module_scope)
