from moego.models.user_profile.v1 import user_profile_models_pb2 as _user_profile_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetUserProfilesParams(_message.Message):
    __slots__ = ("users",)
    USERS_FIELD_NUMBER: _ClassVar[int]
    users: _containers.RepeatedCompositeFieldContainer[_user_profile_models_pb2.User]
    def __init__(self, users: _Optional[_Iterable[_Union[_user_profile_models_pb2.User, _Mapping]]] = ...) -> None: ...

class GetUserProfilesResult(_message.Message):
    __slots__ = ("user_profiles",)
    USER_PROFILES_FIELD_NUMBER: _ClassVar[int]
    user_profiles: _containers.RepeatedCompositeFieldContainer[_user_profile_models_pb2.UserProfile]
    def __init__(self, user_profiles: _Optional[_Iterable[_Union[_user_profile_models_pb2.UserProfile, _Mapping]]] = ...) -> None: ...
