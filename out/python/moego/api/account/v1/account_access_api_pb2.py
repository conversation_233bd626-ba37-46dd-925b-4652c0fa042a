# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/account/v1/account_access_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/account/v1/account_access_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from moego.models.account.v1 import account_defs_pb2 as moego_dot_models_dot_account_dot_v1_dot_account__defs__pb2
from moego.models.risk_control.v1 import verification_code_defs_pb2 as moego_dot_models_dot_risk__control_dot_v1_dot_verification__code__defs__pb2
from moego.models.risk_control.v1 import verification_code_enums_pb2 as moego_dot_models_dot_risk__control_dot_v1_dot_verification__code__enums__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n-moego/api/account/v1/account_access_api.proto\x12\x14moego.api.account.v1\x1a\x1bgoogle/protobuf/empty.proto\x1a*moego/models/account/v1/account_defs.proto\x1a\x39moego/models/risk_control/v1/verification_code_defs.proto\x1a:moego/models/risk_control/v1/verification_code_enums.proto\x1a\x17validate/validate.proto\"\x96\x01\n\x1f\x43heckIdentifierAvailableRequest\x12!\n\x05\x65mail\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x18\x64`\x01H\x00R\x05\x65mail\x12=\n\x0cphone_number\x18\x02 \x01(\tB\x18\xfa\x42\x15r\x13\x32\x11^\\+[1-9]\\d{1,18}$H\x00R\x0bphoneNumberB\x11\n\nidentifier\x12\x03\xf8\x42\x01\"6\n CheckIdentifierAvailableResponse\x12\x12\n\x04used\x18\x01 \x01(\x08R\x04used\"\xf1\x02\n\x16\x41\x63\x63ountRegisterRequest\x12\x1f\n\x05\x65mail\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x18\x64`\x01R\x05\x65mail\x12\x43\n\x0cphone_number\x18\x02 \x01(\tB\x1b\xfa\x42\x18r\x16\x32\x11^\\+[1-9]\\d{1,18}$\xd0\x01\x01H\x00R\x0bphoneNumber\x88\x01\x01\x12%\n\x08password\x18\x03 \x01(\tB\t\xfa\x42\x06r\x04\x10\x06\x18\x64R\x08password\x12&\n\nfirst_name\x18\x04 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32R\tfirstName\x12$\n\tlast_name\x18\x05 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32R\x08lastName\x12Z\n\x0cverification\x18\x06 \x01(\x0b\x32\x31.moego.models.risk_control.v1.VerificationCodeDefH\x01R\x0cverification\x88\x01\x01\x42\x0f\n\r_phone_numberB\x0f\n\r_verification\"\x19\n\x17\x41\x63\x63ountRegisterResponse\",\n\x08TokenDef\x12 \n\x05token\x18\x01 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\x80\x02R\x05token\"X\n\x10\x45mailPasswordDef\x12\x1f\n\x05\x65mail\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x10\x03\x18\x64R\x05\x65mail\x12#\n\x08password\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x64R\x08password\"\xe7\x01\n\x1ePhoneNumberVerificationCodeDef\x12;\n\x0cphone_number\x18\x01 \x01(\tB\x18\xfa\x42\x15r\x13\x32\x11^\\+[1-9]\\d{1,18}$R\x0bphoneNumber\x12^\n\x08scenario\x18\x02 \x01(\x0e\x32\x36.moego.models.risk_control.v1.VerificationCodeScenarioB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x08scenario\x12\x14\n\x05token\x18\x03 \x01(\tR\x05token\x12\x12\n\x04\x63ode\x18\x04 \x01(\tR\x04\x63ode\"\xbd\x01\n\x13\x41\x63\x63ountLoginRequest\x12;\n\x08\x62y_token\x18\x01 \x01(\x0b\x32\x1e.moego.api.account.v1.TokenDefH\x00R\x07\x62yToken\x12T\n\x11\x62y_email_password\x18\x02 \x01(\x0b\x32&.moego.api.account.v1.EmailPasswordDefH\x00R\x0f\x62yEmailPasswordB\x13\n\x0clogin_method\x12\x03\xf8\x42\x01\"\x16\n\x14\x41\x63\x63ountLoginResponse\"\xef\x01\n\x16RegisterOrLoginRequest\x12g\n\x14\x62y_phone_verify_code\x18\x01 \x01(\x0b\x32\x34.moego.api.account.v1.PhoneNumberVerificationCodeDefH\x00R\x11\x62yPhoneVerifyCode\x12H\n\tnamespace\x18\x02 \x01(\x0b\x32%.moego.models.account.v1.NamespaceDefH\x01R\tnamespace\x88\x01\x01\x42\x14\n\raccess_method\x12\x03\xf8\x42\x01\x42\x0c\n\n_namespace\"9\n\x17RegisterOrLoginResponse\x12\x1e\n\nregistered\x18\x01 \x01(\x08R\nregistered\"\x14\n\x12\x46orkSessionRequest\"+\n\x13\x46orkSessionResponse\x12\x14\n\x05token\x18\x01 \x01(\tR\x05token2\xf9\x04\n\x14\x41\x63\x63ountAccessService\x12\x89\x01\n\x18\x43heckIdentifierAvailable\x12\x35.moego.api.account.v1.CheckIdentifierAvailableRequest\x1a\x36.moego.api.account.v1.CheckIdentifierAvailableResponse\x12g\n\x08Register\x12,.moego.api.account.v1.AccountRegisterRequest\x1a-.moego.api.account.v1.AccountRegisterResponse\x12^\n\x05Login\x12).moego.api.account.v1.AccountLoginRequest\x1a*.moego.api.account.v1.AccountLoginResponse\x12n\n\x0fRegisterOrLogin\x12,.moego.api.account.v1.RegisterOrLoginRequest\x1a-.moego.api.account.v1.RegisterOrLoginResponse\x12\x38\n\x06Logout\x12\x16.google.protobuf.Empty\x1a\x16.google.protobuf.Empty\x12\x62\n\x0b\x46orkSession\x12(.moego.api.account.v1.ForkSessionRequest\x1a).moego.api.account.v1.ForkSessionResponseBx\n\x1c\x63om.moego.idl.api.account.v1P\x01ZVgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/account/v1;accountapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.account.v1.account_access_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.moego.idl.api.account.v1P\001ZVgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/account/v1;accountapipb'
  _globals['_CHECKIDENTIFIERAVAILABLEREQUEST'].oneofs_by_name['identifier']._loaded_options = None
  _globals['_CHECKIDENTIFIERAVAILABLEREQUEST'].oneofs_by_name['identifier']._serialized_options = b'\370B\001'
  _globals['_CHECKIDENTIFIERAVAILABLEREQUEST'].fields_by_name['email']._loaded_options = None
  _globals['_CHECKIDENTIFIERAVAILABLEREQUEST'].fields_by_name['email']._serialized_options = b'\372B\006r\004\030d`\001'
  _globals['_CHECKIDENTIFIERAVAILABLEREQUEST'].fields_by_name['phone_number']._loaded_options = None
  _globals['_CHECKIDENTIFIERAVAILABLEREQUEST'].fields_by_name['phone_number']._serialized_options = b'\372B\025r\0232\021^\\+[1-9]\\d{1,18}$'
  _globals['_ACCOUNTREGISTERREQUEST'].fields_by_name['email']._loaded_options = None
  _globals['_ACCOUNTREGISTERREQUEST'].fields_by_name['email']._serialized_options = b'\372B\006r\004\030d`\001'
  _globals['_ACCOUNTREGISTERREQUEST'].fields_by_name['phone_number']._loaded_options = None
  _globals['_ACCOUNTREGISTERREQUEST'].fields_by_name['phone_number']._serialized_options = b'\372B\030r\0262\021^\\+[1-9]\\d{1,18}$\320\001\001'
  _globals['_ACCOUNTREGISTERREQUEST'].fields_by_name['password']._loaded_options = None
  _globals['_ACCOUNTREGISTERREQUEST'].fields_by_name['password']._serialized_options = b'\372B\006r\004\020\006\030d'
  _globals['_ACCOUNTREGISTERREQUEST'].fields_by_name['first_name']._loaded_options = None
  _globals['_ACCOUNTREGISTERREQUEST'].fields_by_name['first_name']._serialized_options = b'\372B\004r\002\0302'
  _globals['_ACCOUNTREGISTERREQUEST'].fields_by_name['last_name']._loaded_options = None
  _globals['_ACCOUNTREGISTERREQUEST'].fields_by_name['last_name']._serialized_options = b'\372B\004r\002\0302'
  _globals['_TOKENDEF'].fields_by_name['token']._loaded_options = None
  _globals['_TOKENDEF'].fields_by_name['token']._serialized_options = b'\372B\007r\005\020\001\030\200\002'
  _globals['_EMAILPASSWORDDEF'].fields_by_name['email']._loaded_options = None
  _globals['_EMAILPASSWORDDEF'].fields_by_name['email']._serialized_options = b'\372B\006r\004\020\003\030d'
  _globals['_EMAILPASSWORDDEF'].fields_by_name['password']._loaded_options = None
  _globals['_EMAILPASSWORDDEF'].fields_by_name['password']._serialized_options = b'\372B\004r\002\030d'
  _globals['_PHONENUMBERVERIFICATIONCODEDEF'].fields_by_name['phone_number']._loaded_options = None
  _globals['_PHONENUMBERVERIFICATIONCODEDEF'].fields_by_name['phone_number']._serialized_options = b'\372B\025r\0232\021^\\+[1-9]\\d{1,18}$'
  _globals['_PHONENUMBERVERIFICATIONCODEDEF'].fields_by_name['scenario']._loaded_options = None
  _globals['_PHONENUMBERVERIFICATIONCODEDEF'].fields_by_name['scenario']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_ACCOUNTLOGINREQUEST'].oneofs_by_name['login_method']._loaded_options = None
  _globals['_ACCOUNTLOGINREQUEST'].oneofs_by_name['login_method']._serialized_options = b'\370B\001'
  _globals['_REGISTERORLOGINREQUEST'].oneofs_by_name['access_method']._loaded_options = None
  _globals['_REGISTERORLOGINREQUEST'].oneofs_by_name['access_method']._serialized_options = b'\370B\001'
  _globals['_CHECKIDENTIFIERAVAILABLEREQUEST']._serialized_start=289
  _globals['_CHECKIDENTIFIERAVAILABLEREQUEST']._serialized_end=439
  _globals['_CHECKIDENTIFIERAVAILABLERESPONSE']._serialized_start=441
  _globals['_CHECKIDENTIFIERAVAILABLERESPONSE']._serialized_end=495
  _globals['_ACCOUNTREGISTERREQUEST']._serialized_start=498
  _globals['_ACCOUNTREGISTERREQUEST']._serialized_end=867
  _globals['_ACCOUNTREGISTERRESPONSE']._serialized_start=869
  _globals['_ACCOUNTREGISTERRESPONSE']._serialized_end=894
  _globals['_TOKENDEF']._serialized_start=896
  _globals['_TOKENDEF']._serialized_end=940
  _globals['_EMAILPASSWORDDEF']._serialized_start=942
  _globals['_EMAILPASSWORDDEF']._serialized_end=1030
  _globals['_PHONENUMBERVERIFICATIONCODEDEF']._serialized_start=1033
  _globals['_PHONENUMBERVERIFICATIONCODEDEF']._serialized_end=1264
  _globals['_ACCOUNTLOGINREQUEST']._serialized_start=1267
  _globals['_ACCOUNTLOGINREQUEST']._serialized_end=1456
  _globals['_ACCOUNTLOGINRESPONSE']._serialized_start=1458
  _globals['_ACCOUNTLOGINRESPONSE']._serialized_end=1480
  _globals['_REGISTERORLOGINREQUEST']._serialized_start=1483
  _globals['_REGISTERORLOGINREQUEST']._serialized_end=1722
  _globals['_REGISTERORLOGINRESPONSE']._serialized_start=1724
  _globals['_REGISTERORLOGINRESPONSE']._serialized_end=1781
  _globals['_FORKSESSIONREQUEST']._serialized_start=1783
  _globals['_FORKSESSIONREQUEST']._serialized_end=1803
  _globals['_FORKSESSIONRESPONSE']._serialized_start=1805
  _globals['_FORKSESSIONRESPONSE']._serialized_end=1848
  _globals['_ACCOUNTACCESSSERVICE']._serialized_start=1851
  _globals['_ACCOUNTACCESSSERVICE']._serialized_end=2484
# @@protoc_insertion_point(module_scope)
