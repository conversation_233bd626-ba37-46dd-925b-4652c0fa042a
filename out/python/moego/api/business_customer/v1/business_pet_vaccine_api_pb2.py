# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/business_customer/v1/business_pet_vaccine_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/business_customer/v1/business_pet_vaccine_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.business_customer.v1 import business_pet_vaccine_defs_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__vaccine__defs__pb2
from moego.models.business_customer.v1 import business_pet_vaccine_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__vaccine__models__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n=moego/api/business_customer/v1/business_pet_vaccine_api.proto\x12\x1emoego.api.business_customer.v1\x1a\x41moego/models/business_customer/v1/business_pet_vaccine_defs.proto\x1a\x43moego/models/business_customer/v1/business_pet_vaccine_models.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a\x17validate/validate.proto\"\x1e\n\x1cListPetVaccineTemplateParams\"y\n\x1cListPetVaccineTemplateResult\x12Y\n\x08vaccines\x18\x01 \x03(\x0b\x32=.moego.models.business_customer.v1.BusinessPetVaccineNameViewR\x08vaccines\"\x16\n\x14ListPetVaccineParams\"\xc8\x03\n\x14ListPetVaccineResult\x12V\n\x08vaccines\x18\x01 \x03(\x0b\x32:.moego.models.business_customer.v1.BusinessPetVaccineModelR\x08vaccines\x12\x95\x01\n\x1evaccine_requirement_by_service\x18\x02 \x03(\x0b\x32P.moego.api.business_customer.v1.ListPetVaccineResult.VaccineRequirementByServiceR\x1bvaccineRequirementByService\x1a\xbf\x01\n\x1bVaccineRequirementByService\x12\x1d\n\nvaccine_id\x18\x01 \x01(\x03R\tvaccineId\x12\x80\x01\n\x1erequired_by_service_item_types\x18\x02 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x11\xfa\x42\x0e\x92\x01\x0b\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x1arequiredByServiceItemTypes\"\xff\x01\n\x16\x43reatePetVaccineParams\x12\x62\n\x07vaccine\x18\x01 \x01(\x0b\x32>.moego.models.business_customer.v1.BusinessPetVaccineCreateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x07vaccine\x12\x80\x01\n\x1erequired_by_service_item_types\x18\x02 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x11\xfa\x42\x0e\x92\x01\x0b\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x1arequiredByServiceItemTypes\"n\n\x16\x43reatePetVaccineResult\x12T\n\x07vaccine\x18\x01 \x01(\x0b\x32:.moego.models.business_customer.v1.BusinessPetVaccineModelR\x07vaccine\"\xb9\x03\n\x16UpdatePetVaccineParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12\x62\n\x07vaccine\x18\x02 \x01(\x0b\x32>.moego.models.business_customer.v1.BusinessPetVaccineUpdateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x07vaccine\x12\x93\x01\n\x1erequired_by_service_item_types\x18\x03 \x01(\x0b\x32J.moego.api.business_customer.v1.UpdatePetVaccineParams.ServiceItemTypeListH\x00R\x1arequiredByServiceItemTypes\x88\x01\x01\x1ai\n\x13ServiceItemTypeList\x12R\n\x05types\x18\x01 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x11\xfa\x42\x0e\x92\x01\x0b\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x05typesB!\n\x1f_required_by_service_item_types\"8\n\x14SortPetVaccineParams\x12 \n\x03ids\x18\x01 \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\"\x02 \x00R\x03ids\"\x16\n\x14SortPetVaccineResult\"\x18\n\x16UpdatePetVaccineResult\"1\n\x16\x44\x65letePetVaccineParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"\x18\n\x16\x44\x65letePetVaccineResult2\xbd\x06\n\x19\x42usinessPetVaccineService\x12\x94\x01\n\x16ListPetVaccineTemplate\x12<.moego.api.business_customer.v1.ListPetVaccineTemplateParams\x1a<.moego.api.business_customer.v1.ListPetVaccineTemplateResult\x12|\n\x0eListPetVaccine\x12\x34.moego.api.business_customer.v1.ListPetVaccineParams\x1a\x34.moego.api.business_customer.v1.ListPetVaccineResult\x12\x82\x01\n\x10\x43reatePetVaccine\x12\x36.moego.api.business_customer.v1.CreatePetVaccineParams\x1a\x36.moego.api.business_customer.v1.CreatePetVaccineResult\x12\x82\x01\n\x10UpdatePetVaccine\x12\x36.moego.api.business_customer.v1.UpdatePetVaccineParams\x1a\x36.moego.api.business_customer.v1.UpdatePetVaccineResult\x12|\n\x0eSortPetVaccine\x12\x34.moego.api.business_customer.v1.SortPetVaccineParams\x1a\x34.moego.api.business_customer.v1.SortPetVaccineResult\x12\x82\x01\n\x10\x44\x65letePetVaccine\x12\x36.moego.api.business_customer.v1.DeletePetVaccineParams\x1a\x36.moego.api.business_customer.v1.DeletePetVaccineResultB\x95\x01\n&com.moego.idl.api.business_customer.v1P\x01Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.business_customer.v1.business_pet_vaccine_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.api.business_customer.v1P\001Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb'
  _globals['_LISTPETVACCINERESULT_VACCINEREQUIREMENTBYSERVICE'].fields_by_name['required_by_service_item_types']._loaded_options = None
  _globals['_LISTPETVACCINERESULT_VACCINEREQUIREMENTBYSERVICE'].fields_by_name['required_by_service_item_types']._serialized_options = b'\372B\016\222\001\013\030\001\"\007\202\001\004\020\001 \000'
  _globals['_CREATEPETVACCINEPARAMS'].fields_by_name['vaccine']._loaded_options = None
  _globals['_CREATEPETVACCINEPARAMS'].fields_by_name['vaccine']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CREATEPETVACCINEPARAMS'].fields_by_name['required_by_service_item_types']._loaded_options = None
  _globals['_CREATEPETVACCINEPARAMS'].fields_by_name['required_by_service_item_types']._serialized_options = b'\372B\016\222\001\013\030\001\"\007\202\001\004\020\001 \000'
  _globals['_UPDATEPETVACCINEPARAMS_SERVICEITEMTYPELIST'].fields_by_name['types']._loaded_options = None
  _globals['_UPDATEPETVACCINEPARAMS_SERVICEITEMTYPELIST'].fields_by_name['types']._serialized_options = b'\372B\016\222\001\013\030\001\"\007\202\001\004\020\001 \000'
  _globals['_UPDATEPETVACCINEPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATEPETVACCINEPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETVACCINEPARAMS'].fields_by_name['vaccine']._loaded_options = None
  _globals['_UPDATEPETVACCINEPARAMS'].fields_by_name['vaccine']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_SORTPETVACCINEPARAMS'].fields_by_name['ids']._loaded_options = None
  _globals['_SORTPETVACCINEPARAMS'].fields_by_name['ids']._serialized_options = b'\372B\013\222\001\010\030\001\"\004\"\002 \000'
  _globals['_DELETEPETVACCINEPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_DELETEPETVACCINEPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTPETVACCINETEMPLATEPARAMS']._serialized_start=303
  _globals['_LISTPETVACCINETEMPLATEPARAMS']._serialized_end=333
  _globals['_LISTPETVACCINETEMPLATERESULT']._serialized_start=335
  _globals['_LISTPETVACCINETEMPLATERESULT']._serialized_end=456
  _globals['_LISTPETVACCINEPARAMS']._serialized_start=458
  _globals['_LISTPETVACCINEPARAMS']._serialized_end=480
  _globals['_LISTPETVACCINERESULT']._serialized_start=483
  _globals['_LISTPETVACCINERESULT']._serialized_end=939
  _globals['_LISTPETVACCINERESULT_VACCINEREQUIREMENTBYSERVICE']._serialized_start=748
  _globals['_LISTPETVACCINERESULT_VACCINEREQUIREMENTBYSERVICE']._serialized_end=939
  _globals['_CREATEPETVACCINEPARAMS']._serialized_start=942
  _globals['_CREATEPETVACCINEPARAMS']._serialized_end=1197
  _globals['_CREATEPETVACCINERESULT']._serialized_start=1199
  _globals['_CREATEPETVACCINERESULT']._serialized_end=1309
  _globals['_UPDATEPETVACCINEPARAMS']._serialized_start=1312
  _globals['_UPDATEPETVACCINEPARAMS']._serialized_end=1753
  _globals['_UPDATEPETVACCINEPARAMS_SERVICEITEMTYPELIST']._serialized_start=1613
  _globals['_UPDATEPETVACCINEPARAMS_SERVICEITEMTYPELIST']._serialized_end=1718
  _globals['_SORTPETVACCINEPARAMS']._serialized_start=1755
  _globals['_SORTPETVACCINEPARAMS']._serialized_end=1811
  _globals['_SORTPETVACCINERESULT']._serialized_start=1813
  _globals['_SORTPETVACCINERESULT']._serialized_end=1835
  _globals['_UPDATEPETVACCINERESULT']._serialized_start=1837
  _globals['_UPDATEPETVACCINERESULT']._serialized_end=1861
  _globals['_DELETEPETVACCINEPARAMS']._serialized_start=1863
  _globals['_DELETEPETVACCINEPARAMS']._serialized_end=1912
  _globals['_DELETEPETVACCINERESULT']._serialized_start=1914
  _globals['_DELETEPETVACCINERESULT']._serialized_end=1938
  _globals['_BUSINESSPETVACCINESERVICE']._serialized_start=1941
  _globals['_BUSINESSPETVACCINESERVICE']._serialized_end=2770
# @@protoc_insertion_point(module_scope)
