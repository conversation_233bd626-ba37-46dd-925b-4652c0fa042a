# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.api.online_booking.v1 import ob_availability_setting_api_pb2 as moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2


class OBAvailabilitySettingServiceStub(object):
    """the ob_availability_setting service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetBoardingServiceAvailability = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/GetBoardingServiceAvailability',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetBoardingServiceAvailabilityParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetBoardingServiceAvailabilityResult.FromString,
                _registered_method=True)
        self.UpdateBoardingServiceAvailability = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/UpdateBoardingServiceAvailability',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateBoardingServiceAvailabilityParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateBoardingServiceAvailabilityResult.FromString,
                _registered_method=True)
        self.GetDaycareServiceAvailability = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/GetDaycareServiceAvailability',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetDaycareServiceAvailabilityParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetDaycareServiceAvailabilityResult.FromString,
                _registered_method=True)
        self.UpdateDaycareServiceAvailability = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/UpdateDaycareServiceAvailability',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateDaycareServiceAvailabilityParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateDaycareServiceAvailabilityResult.FromString,
                _registered_method=True)
        self.GetEvaluationServiceAvailability = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/GetEvaluationServiceAvailability',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetEvaluationServiceAvailabilityParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetEvaluationServiceAvailabilityResult.FromString,
                _registered_method=True)
        self.UpdateEvaluationServiceAvailability = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/UpdateEvaluationServiceAvailability',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateEvaluationServiceAvailabilityParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateEvaluationServiceAvailabilityResult.FromString,
                _registered_method=True)
        self.GetGroomingServiceAvailability = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/GetGroomingServiceAvailability',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetGroomingServiceAvailabilityParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetGroomingServiceAvailabilityResult.FromString,
                _registered_method=True)
        self.UpdateGroomingServiceAvailability = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/UpdateGroomingServiceAvailability',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateGroomingServiceAvailabilityParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateGroomingServiceAvailabilityResult.FromString,
                _registered_method=True)
        self.ListArrivalPickUpTimeOverrides = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/ListArrivalPickUpTimeOverrides',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.ListArrivalPickUpTimeOverridesParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.ListArrivalPickUpTimeOverridesResult.FromString,
                _registered_method=True)
        self.BatchCreateArrivalPickUpTimeOverride = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/BatchCreateArrivalPickUpTimeOverride',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchCreateArrivalPickUpTimeOverrideParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchCreateArrivalPickUpTimeOverrideResult.FromString,
                _registered_method=True)
        self.BatchDeleteArrivalPickUpTimeOverride = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/BatchDeleteArrivalPickUpTimeOverride',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchDeleteArrivalPickUpTimeOverrideParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchDeleteArrivalPickUpTimeOverrideResult.FromString,
                _registered_method=True)
        self.BatchUpdateArrivalPickUpTimeOverride = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/BatchUpdateArrivalPickUpTimeOverride',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchUpdateArrivalPickUpTimeOverrideParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchUpdateArrivalPickUpTimeOverrideResult.FromString,
                _registered_method=True)
        self.CreateCapacityOverride = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/CreateCapacityOverride',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.CreateCapacityOverrideParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.CreateCapacityOverrideResult.FromString,
                _registered_method=True)
        self.DeleteCapacityOverride = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/DeleteCapacityOverride',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.DeleteCapacityOverrideParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.DeleteCapacityOverrideResult.FromString,
                _registered_method=True)
        self.UpdateCapacityOverride = channel.unary_unary(
                '/moego.api.online_booking.v1.OBAvailabilitySettingService/UpdateCapacityOverride',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateCapacityOverrideParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateCapacityOverrideResult.FromString,
                _registered_method=True)


class OBAvailabilitySettingServiceServicer(object):
    """the ob_availability_setting service
    """

    def GetBoardingServiceAvailability(self, request, context):
        """get boarding service availability
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateBoardingServiceAvailability(self, request, context):
        """update boarding service availability
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDaycareServiceAvailability(self, request, context):
        """get daycare service availability
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateDaycareServiceAvailability(self, request, context):
        """update daycare service availability
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetEvaluationServiceAvailability(self, request, context):
        """get evaluation service availability
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateEvaluationServiceAvailability(self, request, context):
        """update evaluation service availability
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetGroomingServiceAvailability(self, request, context):
        """get grooming service availability
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateGroomingServiceAvailability(self, request, context):
        """update grooming service availability
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListArrivalPickUpTimeOverrides(self, request, context):
        """list arrival pick up time overrides
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchCreateArrivalPickUpTimeOverride(self, request, context):
        """batch create arrival pick up time override
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchDeleteArrivalPickUpTimeOverride(self, request, context):
        """batch delete arrival pick up time override
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchUpdateArrivalPickUpTimeOverride(self, request, context):
        """batch update arrival pick up time override
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateCapacityOverride(self, request, context):
        """create capacity override
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteCapacityOverride(self, request, context):
        """delete capacity override
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateCapacityOverride(self, request, context):
        """update capacity override
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_OBAvailabilitySettingServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetBoardingServiceAvailability': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBoardingServiceAvailability,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetBoardingServiceAvailabilityParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetBoardingServiceAvailabilityResult.SerializeToString,
            ),
            'UpdateBoardingServiceAvailability': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateBoardingServiceAvailability,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateBoardingServiceAvailabilityParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateBoardingServiceAvailabilityResult.SerializeToString,
            ),
            'GetDaycareServiceAvailability': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDaycareServiceAvailability,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetDaycareServiceAvailabilityParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetDaycareServiceAvailabilityResult.SerializeToString,
            ),
            'UpdateDaycareServiceAvailability': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateDaycareServiceAvailability,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateDaycareServiceAvailabilityParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateDaycareServiceAvailabilityResult.SerializeToString,
            ),
            'GetEvaluationServiceAvailability': grpc.unary_unary_rpc_method_handler(
                    servicer.GetEvaluationServiceAvailability,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetEvaluationServiceAvailabilityParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetEvaluationServiceAvailabilityResult.SerializeToString,
            ),
            'UpdateEvaluationServiceAvailability': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateEvaluationServiceAvailability,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateEvaluationServiceAvailabilityParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateEvaluationServiceAvailabilityResult.SerializeToString,
            ),
            'GetGroomingServiceAvailability': grpc.unary_unary_rpc_method_handler(
                    servicer.GetGroomingServiceAvailability,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetGroomingServiceAvailabilityParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetGroomingServiceAvailabilityResult.SerializeToString,
            ),
            'UpdateGroomingServiceAvailability': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateGroomingServiceAvailability,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateGroomingServiceAvailabilityParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateGroomingServiceAvailabilityResult.SerializeToString,
            ),
            'ListArrivalPickUpTimeOverrides': grpc.unary_unary_rpc_method_handler(
                    servicer.ListArrivalPickUpTimeOverrides,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.ListArrivalPickUpTimeOverridesParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.ListArrivalPickUpTimeOverridesResult.SerializeToString,
            ),
            'BatchCreateArrivalPickUpTimeOverride': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchCreateArrivalPickUpTimeOverride,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchCreateArrivalPickUpTimeOverrideParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchCreateArrivalPickUpTimeOverrideResult.SerializeToString,
            ),
            'BatchDeleteArrivalPickUpTimeOverride': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchDeleteArrivalPickUpTimeOverride,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchDeleteArrivalPickUpTimeOverrideParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchDeleteArrivalPickUpTimeOverrideResult.SerializeToString,
            ),
            'BatchUpdateArrivalPickUpTimeOverride': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchUpdateArrivalPickUpTimeOverride,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchUpdateArrivalPickUpTimeOverrideParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchUpdateArrivalPickUpTimeOverrideResult.SerializeToString,
            ),
            'CreateCapacityOverride': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateCapacityOverride,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.CreateCapacityOverrideParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.CreateCapacityOverrideResult.SerializeToString,
            ),
            'DeleteCapacityOverride': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteCapacityOverride,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.DeleteCapacityOverrideParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.DeleteCapacityOverrideResult.SerializeToString,
            ),
            'UpdateCapacityOverride': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateCapacityOverride,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateCapacityOverrideParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateCapacityOverrideResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.online_booking.v1.OBAvailabilitySettingService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.online_booking.v1.OBAvailabilitySettingService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class OBAvailabilitySettingService(object):
    """the ob_availability_setting service
    """

    @staticmethod
    def GetBoardingServiceAvailability(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/GetBoardingServiceAvailability',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetBoardingServiceAvailabilityParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetBoardingServiceAvailabilityResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateBoardingServiceAvailability(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/UpdateBoardingServiceAvailability',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateBoardingServiceAvailabilityParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateBoardingServiceAvailabilityResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDaycareServiceAvailability(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/GetDaycareServiceAvailability',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetDaycareServiceAvailabilityParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetDaycareServiceAvailabilityResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateDaycareServiceAvailability(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/UpdateDaycareServiceAvailability',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateDaycareServiceAvailabilityParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateDaycareServiceAvailabilityResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetEvaluationServiceAvailability(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/GetEvaluationServiceAvailability',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetEvaluationServiceAvailabilityParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetEvaluationServiceAvailabilityResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateEvaluationServiceAvailability(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/UpdateEvaluationServiceAvailability',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateEvaluationServiceAvailabilityParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateEvaluationServiceAvailabilityResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetGroomingServiceAvailability(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/GetGroomingServiceAvailability',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetGroomingServiceAvailabilityParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.GetGroomingServiceAvailabilityResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateGroomingServiceAvailability(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/UpdateGroomingServiceAvailability',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateGroomingServiceAvailabilityParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateGroomingServiceAvailabilityResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListArrivalPickUpTimeOverrides(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/ListArrivalPickUpTimeOverrides',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.ListArrivalPickUpTimeOverridesParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.ListArrivalPickUpTimeOverridesResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchCreateArrivalPickUpTimeOverride(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/BatchCreateArrivalPickUpTimeOverride',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchCreateArrivalPickUpTimeOverrideParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchCreateArrivalPickUpTimeOverrideResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchDeleteArrivalPickUpTimeOverride(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/BatchDeleteArrivalPickUpTimeOverride',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchDeleteArrivalPickUpTimeOverrideParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchDeleteArrivalPickUpTimeOverrideResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchUpdateArrivalPickUpTimeOverride(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/BatchUpdateArrivalPickUpTimeOverride',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchUpdateArrivalPickUpTimeOverrideParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.BatchUpdateArrivalPickUpTimeOverrideResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateCapacityOverride(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/CreateCapacityOverride',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.CreateCapacityOverrideParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.CreateCapacityOverrideResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteCapacityOverride(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/DeleteCapacityOverride',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.DeleteCapacityOverrideParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.DeleteCapacityOverrideResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateCapacityOverride(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBAvailabilitySettingService/UpdateCapacityOverride',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateCapacityOverrideParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__availability__setting__api__pb2.UpdateCapacityOverrideResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
