from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class SetCustomerBlockStatusParams(_message.Message):
    __slots__ = ("service_item_types", "customer_ids", "need_block")
    SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_IDS_FIELD_NUMBER: _ClassVar[int]
    NEED_BLOCK_FIELD_NUMBER: _ClassVar[int]
    service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    customer_ids: _containers.RepeatedScalarFieldContainer[int]
    need_block: bool
    def __init__(self, service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., customer_ids: _Optional[_Iterable[int]] = ..., need_block: bool = ...) -> None: ...

class SetCustomerBlockStatusResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
