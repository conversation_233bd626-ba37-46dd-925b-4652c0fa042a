from google.type import interval_pb2 as _interval_pb2
from google.type import money_pb2 as _money_pb2
from moego.models.finance_tools.v1 import cash_drawer_defs_pb2 as _cash_drawer_defs_pb2
from moego.models.finance_tools.v1 import cash_drawer_models_pb2 as _cash_drawer_models_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ListReportsRequest(_message.Message):
    __slots__ = ("pagination",)
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationRequest
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ...) -> None: ...

class ListReportsResponse(_message.Message):
    __slots__ = ("pagination", "reports")
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    REPORTS_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationResponse
    reports: _containers.RepeatedCompositeFieldContainer[_cash_drawer_models_pb2.CashDrawerReport]
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ..., reports: _Optional[_Iterable[_Union[_cash_drawer_models_pb2.CashDrawerReport, _Mapping]]] = ...) -> None: ...

class GetLastReportRequest(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GetLastReportResponse(_message.Message):
    __slots__ = ("last_report",)
    LAST_REPORT_FIELD_NUMBER: _ClassVar[int]
    last_report: _cash_drawer_models_pb2.CashDrawerReport
    def __init__(self, last_report: _Optional[_Union[_cash_drawer_models_pb2.CashDrawerReport, _Mapping]] = ...) -> None: ...

class GetReportedCashTotalRequest(_message.Message):
    __slots__ = ("range",)
    RANGE_FIELD_NUMBER: _ClassVar[int]
    range: _interval_pb2.Interval
    def __init__(self, range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ...) -> None: ...

class GetReportedCashTotalResponse(_message.Message):
    __slots__ = ("reported_cash_total",)
    REPORTED_CASH_TOTAL_FIELD_NUMBER: _ClassVar[int]
    reported_cash_total: _money_pb2.Money
    def __init__(self, reported_cash_total: _Optional[_Union[_money_pb2.Money, _Mapping]] = ...) -> None: ...

class ListCashAdjustmentsRequest(_message.Message):
    __slots__ = ("pagination", "asc", "range", "report_id")
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    ASC_FIELD_NUMBER: _ClassVar[int]
    RANGE_FIELD_NUMBER: _ClassVar[int]
    REPORT_ID_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationRequest
    asc: bool
    range: _interval_pb2.Interval
    report_id: int
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., asc: bool = ..., range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., report_id: _Optional[int] = ...) -> None: ...

class ListCashAdjustmentsResponse(_message.Message):
    __slots__ = ("pagination", "adjustments")
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    ADJUSTMENTS_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationResponse
    adjustments: _containers.RepeatedCompositeFieldContainer[_cash_drawer_models_pb2.CashDrawerAdjustment]
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ..., adjustments: _Optional[_Iterable[_Union[_cash_drawer_models_pb2.CashDrawerAdjustment, _Mapping]]] = ...) -> None: ...

class CreateCashAdjustmentRequest(_message.Message):
    __slots__ = ("adjustment",)
    ADJUSTMENT_FIELD_NUMBER: _ClassVar[int]
    adjustment: _cash_drawer_defs_pb2.CreateCashDrawerAdjustmentDef
    def __init__(self, adjustment: _Optional[_Union[_cash_drawer_defs_pb2.CreateCashDrawerAdjustmentDef, _Mapping]] = ...) -> None: ...

class CreateCashAdjustmentResponse(_message.Message):
    __slots__ = ("adjustment",)
    ADJUSTMENT_FIELD_NUMBER: _ClassVar[int]
    adjustment: _cash_drawer_models_pb2.CashDrawerAdjustment
    def __init__(self, adjustment: _Optional[_Union[_cash_drawer_models_pb2.CashDrawerAdjustment, _Mapping]] = ...) -> None: ...

class CreateReportRequest(_message.Message):
    __slots__ = ("report",)
    REPORT_FIELD_NUMBER: _ClassVar[int]
    report: _cash_drawer_defs_pb2.CreateCashDrawerReportDef
    def __init__(self, report: _Optional[_Union[_cash_drawer_defs_pb2.CreateCashDrawerReportDef, _Mapping]] = ...) -> None: ...

class CreateReportResponse(_message.Message):
    __slots__ = ("report",)
    REPORT_FIELD_NUMBER: _ClassVar[int]
    report: _cash_drawer_models_pb2.CashDrawerReport
    def __init__(self, report: _Optional[_Union[_cash_drawer_models_pb2.CashDrawerReport, _Mapping]] = ...) -> None: ...

class UpdateReportRequest(_message.Message):
    __slots__ = ("report",)
    REPORT_FIELD_NUMBER: _ClassVar[int]
    report: _cash_drawer_defs_pb2.UpdateCashDrawerReportDef
    def __init__(self, report: _Optional[_Union[_cash_drawer_defs_pb2.UpdateCashDrawerReportDef, _Mapping]] = ...) -> None: ...

class UpdateReportResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
