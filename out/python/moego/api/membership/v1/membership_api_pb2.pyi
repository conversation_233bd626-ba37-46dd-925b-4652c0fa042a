from google.protobuf import timestamp_pb2 as _timestamp_pb2
from moego.models.membership.v1 import membership_defs_pb2 as _membership_defs_pb2
from moego.models.membership.v1 import membership_models_pb2 as _membership_models_pb2
from moego.models.membership.v1 import redeem_models_pb2 as _redeem_models_pb2
from moego.models.organization.v1 import tax_models_pb2 as _tax_models_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ListMembershipsForSaleParams(_message.Message):
    __slots__ = ("filter", "pagination")
    class Filter(_message.Message):
        __slots__ = ("pet_ids", "status", "name_like")
        PET_IDS_FIELD_NUMBER: _ClassVar[int]
        STATUS_FIELD_NUMBER: _ClassVar[int]
        NAME_LIKE_FIELD_NUMBER: _ClassVar[int]
        pet_ids: _containers.RepeatedScalarFieldContainer[int]
        status: _membership_models_pb2.MembershipModel.Status
        name_like: str
        def __init__(self, pet_ids: _Optional[_Iterable[int]] = ..., status: _Optional[_Union[_membership_models_pb2.MembershipModel.Status, str]] = ..., name_like: _Optional[str] = ...) -> None: ...
    FILTER_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    filter: ListMembershipsForSaleParams.Filter
    pagination: _pagination_messages_pb2.PaginationRequest
    def __init__(self, filter: _Optional[_Union[ListMembershipsForSaleParams.Filter, _Mapping]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ...) -> None: ...

class ListMembershipsForSaleResult(_message.Message):
    __slots__ = ("memberships", "pagination")
    MEMBERSHIPS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    memberships: _containers.RepeatedCompositeFieldContainer[_membership_models_pb2.MembershipModel]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, memberships: _Optional[_Iterable[_Union[_membership_models_pb2.MembershipModel, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class CreateMembershipParams(_message.Message):
    __slots__ = ("membership_def", "membership_discount_benefits", "membership_quantity_benefits")
    MEMBERSHIP_DEF_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_DISCOUNT_BENEFITS_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_QUANTITY_BENEFITS_FIELD_NUMBER: _ClassVar[int]
    membership_def: _membership_defs_pb2.MembershipCreateDef
    membership_discount_benefits: _membership_defs_pb2.CreateMembershipDiscountBenefitsDef
    membership_quantity_benefits: _membership_defs_pb2.CreateMembershipQuantityBenefitsDef
    def __init__(self, membership_def: _Optional[_Union[_membership_defs_pb2.MembershipCreateDef, _Mapping]] = ..., membership_discount_benefits: _Optional[_Union[_membership_defs_pb2.CreateMembershipDiscountBenefitsDef, _Mapping]] = ..., membership_quantity_benefits: _Optional[_Union[_membership_defs_pb2.CreateMembershipQuantityBenefitsDef, _Mapping]] = ...) -> None: ...

class CreateMembershipResult(_message.Message):
    __slots__ = ("membership", "membership_discount_benefits", "membership_quantity_benefits")
    MEMBERSHIP_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_DISCOUNT_BENEFITS_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_QUANTITY_BENEFITS_FIELD_NUMBER: _ClassVar[int]
    membership: _membership_models_pb2.MembershipModel
    membership_discount_benefits: _membership_defs_pb2.MembershipDiscountBenefitsDef
    membership_quantity_benefits: _membership_defs_pb2.MembershipQuantityBenefitsDef
    def __init__(self, membership: _Optional[_Union[_membership_models_pb2.MembershipModel, _Mapping]] = ..., membership_discount_benefits: _Optional[_Union[_membership_defs_pb2.MembershipDiscountBenefitsDef, _Mapping]] = ..., membership_quantity_benefits: _Optional[_Union[_membership_defs_pb2.MembershipQuantityBenefitsDef, _Mapping]] = ...) -> None: ...

class GetMembershipParams(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class GetMembershipResult(_message.Message):
    __slots__ = ("membership", "tax", "membership_discount_benefits", "membership_quantity_benefits")
    MEMBERSHIP_FIELD_NUMBER: _ClassVar[int]
    TAX_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_DISCOUNT_BENEFITS_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_QUANTITY_BENEFITS_FIELD_NUMBER: _ClassVar[int]
    membership: _membership_models_pb2.MembershipModel
    tax: _tax_models_pb2.TaxRuleModel
    membership_discount_benefits: _membership_defs_pb2.MembershipDiscountBenefitsDef
    membership_quantity_benefits: _membership_defs_pb2.MembershipQuantityBenefitsDef
    def __init__(self, membership: _Optional[_Union[_membership_models_pb2.MembershipModel, _Mapping]] = ..., tax: _Optional[_Union[_tax_models_pb2.TaxRuleModel, _Mapping]] = ..., membership_discount_benefits: _Optional[_Union[_membership_defs_pb2.MembershipDiscountBenefitsDef, _Mapping]] = ..., membership_quantity_benefits: _Optional[_Union[_membership_defs_pb2.MembershipQuantityBenefitsDef, _Mapping]] = ...) -> None: ...

class ListMembershipsParams(_message.Message):
    __slots__ = ("status", "name_like", "pagination")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    NAME_LIKE_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    status: _membership_models_pb2.MembershipModel.Status
    name_like: str
    pagination: _pagination_messages_pb2.PaginationRequest
    def __init__(self, status: _Optional[_Union[_membership_models_pb2.MembershipModel.Status, str]] = ..., name_like: _Optional[str] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ...) -> None: ...

class ListMembershipsResult(_message.Message):
    __slots__ = ("memberships", "membership_summaries", "membership_discount_benefits", "membership_quantity_benefits", "pagination")
    MEMBERSHIPS_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_SUMMARIES_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_DISCOUNT_BENEFITS_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_QUANTITY_BENEFITS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    memberships: _containers.RepeatedCompositeFieldContainer[_membership_models_pb2.MembershipModel]
    membership_summaries: _containers.RepeatedCompositeFieldContainer[_membership_models_pb2.MembershipSummaryModel]
    membership_discount_benefits: _containers.RepeatedCompositeFieldContainer[_membership_defs_pb2.MembershipDiscountBenefitsDef]
    membership_quantity_benefits: _containers.RepeatedCompositeFieldContainer[_membership_defs_pb2.MembershipQuantityBenefitsDef]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, memberships: _Optional[_Iterable[_Union[_membership_models_pb2.MembershipModel, _Mapping]]] = ..., membership_summaries: _Optional[_Iterable[_Union[_membership_models_pb2.MembershipSummaryModel, _Mapping]]] = ..., membership_discount_benefits: _Optional[_Iterable[_Union[_membership_defs_pb2.MembershipDiscountBenefitsDef, _Mapping]]] = ..., membership_quantity_benefits: _Optional[_Iterable[_Union[_membership_defs_pb2.MembershipQuantityBenefitsDef, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class UpdateMembershipParams(_message.Message):
    __slots__ = ("id", "revision", "membership_def", "membership_discount_benefits", "membership_quantity_benefits")
    ID_FIELD_NUMBER: _ClassVar[int]
    REVISION_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_DEF_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_DISCOUNT_BENEFITS_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_QUANTITY_BENEFITS_FIELD_NUMBER: _ClassVar[int]
    id: int
    revision: int
    membership_def: _membership_defs_pb2.MembershipUpdateDef
    membership_discount_benefits: _membership_defs_pb2.UpdateMembershipDiscountBenefitsDef
    membership_quantity_benefits: _membership_defs_pb2.UpdateMembershipQuantityBenefitsDef
    def __init__(self, id: _Optional[int] = ..., revision: _Optional[int] = ..., membership_def: _Optional[_Union[_membership_defs_pb2.MembershipUpdateDef, _Mapping]] = ..., membership_discount_benefits: _Optional[_Union[_membership_defs_pb2.UpdateMembershipDiscountBenefitsDef, _Mapping]] = ..., membership_quantity_benefits: _Optional[_Union[_membership_defs_pb2.UpdateMembershipQuantityBenefitsDef, _Mapping]] = ...) -> None: ...

class UpdateMembershipResult(_message.Message):
    __slots__ = ("membership", "membership_discount_benefits", "membership_quantity_benefits")
    MEMBERSHIP_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_DISCOUNT_BENEFITS_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_QUANTITY_BENEFITS_FIELD_NUMBER: _ClassVar[int]
    membership: _membership_models_pb2.MembershipModel
    membership_discount_benefits: _membership_defs_pb2.MembershipDiscountBenefitsDef
    membership_quantity_benefits: _membership_defs_pb2.MembershipQuantityBenefitsDef
    def __init__(self, membership: _Optional[_Union[_membership_models_pb2.MembershipModel, _Mapping]] = ..., membership_discount_benefits: _Optional[_Union[_membership_defs_pb2.MembershipDiscountBenefitsDef, _Mapping]] = ..., membership_quantity_benefits: _Optional[_Union[_membership_defs_pb2.MembershipQuantityBenefitsDef, _Mapping]] = ...) -> None: ...

class DeleteMembershipParams(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class DeleteMembershipResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ListRecommendMembershipsParams(_message.Message):
    __slots__ = ("customer_id", "context", "filter")
    class Filter(_message.Message):
        __slots__ = ("target_membership_ids",)
        TARGET_MEMBERSHIP_IDS_FIELD_NUMBER: _ClassVar[int]
        target_membership_ids: _containers.RepeatedScalarFieldContainer[int]
        def __init__(self, target_membership_ids: _Optional[_Iterable[int]] = ...) -> None: ...
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    CONTEXT_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    customer_id: int
    context: _redeem_models_pb2.RedeemContext
    filter: ListRecommendMembershipsParams.Filter
    def __init__(self, customer_id: _Optional[int] = ..., context: _Optional[_Union[_redeem_models_pb2.RedeemContext, _Mapping]] = ..., filter: _Optional[_Union[ListRecommendMembershipsParams.Filter, _Mapping]] = ...) -> None: ...

class ListRecommendMembershipsResult(_message.Message):
    __slots__ = ("recommended", "all", "benefit_combination")
    RECOMMENDED_FIELD_NUMBER: _ClassVar[int]
    ALL_FIELD_NUMBER: _ClassVar[int]
    BENEFIT_COMBINATION_FIELD_NUMBER: _ClassVar[int]
    recommended: _containers.RepeatedCompositeFieldContainer[_membership_models_pb2.MembershipModel]
    all: _containers.RepeatedCompositeFieldContainer[_membership_models_pb2.MembershipModel]
    benefit_combination: _containers.RepeatedCompositeFieldContainer[_membership_models_pb2.BenefitRecommendView]
    def __init__(self, recommended: _Optional[_Iterable[_Union[_membership_models_pb2.MembershipModel, _Mapping]]] = ..., all: _Optional[_Iterable[_Union[_membership_models_pb2.MembershipModel, _Mapping]]] = ..., benefit_combination: _Optional[_Iterable[_Union[_membership_models_pb2.BenefitRecommendView, _Mapping]]] = ...) -> None: ...

class ListMembershipsForCustomerParams(_message.Message):
    __slots__ = ("customer_id",)
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    customer_id: int
    def __init__(self, customer_id: _Optional[int] = ...) -> None: ...

class ListMembershipsForCustomerResult(_message.Message):
    __slots__ = ("memberships",)
    MEMBERSHIPS_FIELD_NUMBER: _ClassVar[int]
    memberships: _containers.RepeatedCompositeFieldContainer[_membership_models_pb2.MembershipModel]
    def __init__(self, memberships: _Optional[_Iterable[_Union[_membership_models_pb2.MembershipModel, _Mapping]]] = ...) -> None: ...

class QueryBenefitSummaryParams(_message.Message):
    __slots__ = ("customer_id", "membership_id")
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_ID_FIELD_NUMBER: _ClassVar[int]
    customer_id: int
    membership_id: int
    def __init__(self, customer_id: _Optional[int] = ..., membership_id: _Optional[int] = ...) -> None: ...

class QueryBenefitSummaryResult(_message.Message):
    __slots__ = ("discounts", "quantities")
    DISCOUNTS_FIELD_NUMBER: _ClassVar[int]
    QUANTITIES_FIELD_NUMBER: _ClassVar[int]
    discounts: _containers.RepeatedCompositeFieldContainer[_membership_models_pb2.BenefitSummaryView]
    quantities: _containers.RepeatedCompositeFieldContainer[_membership_models_pb2.BenefitSummaryView]
    def __init__(self, discounts: _Optional[_Iterable[_Union[_membership_models_pb2.BenefitSummaryView, _Mapping]]] = ..., quantities: _Optional[_Iterable[_Union[_membership_models_pb2.BenefitSummaryView, _Mapping]]] = ...) -> None: ...

class GetRedeemHistoryParams(_message.Message):
    __slots__ = ("customer_id", "membership_id", "pagination")
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_ID_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    customer_id: int
    membership_id: int
    pagination: _pagination_messages_pb2.PaginationRequest
    def __init__(self, customer_id: _Optional[int] = ..., membership_id: _Optional[int] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ...) -> None: ...

class GetRedeemHistoryResult(_message.Message):
    __slots__ = ("included_benefits", "redeem_history", "pagination")
    INCLUDED_BENEFITS_FIELD_NUMBER: _ClassVar[int]
    REDEEM_HISTORY_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    included_benefits: _containers.RepeatedCompositeFieldContainer[_redeem_models_pb2.IncludeBenefitView]
    redeem_history: _containers.RepeatedCompositeFieldContainer[_redeem_models_pb2.RedeemHistory]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, included_benefits: _Optional[_Iterable[_Union[_redeem_models_pb2.IncludeBenefitView, _Mapping]]] = ..., redeem_history: _Optional[_Iterable[_Union[_redeem_models_pb2.RedeemHistory, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class ApplyMembershipParams(_message.Message):
    __slots__ = ("order_id", "customer_id", "membership_ids")
    ORDER_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_IDS_FIELD_NUMBER: _ClassVar[int]
    order_id: int
    customer_id: int
    membership_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, order_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., membership_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class ApplyMembershipResult(_message.Message):
    __slots__ = ("message",)
    MESSAGE_FIELD_NUMBER: _ClassVar[int]
    message: str
    def __init__(self, message: _Optional[str] = ...) -> None: ...

class RemoveMembershipParams(_message.Message):
    __slots__ = ("order_id", "customer_id", "membership_ids")
    ORDER_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_IDS_FIELD_NUMBER: _ClassVar[int]
    order_id: int
    customer_id: int
    membership_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, order_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., membership_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class RemoveMembershipResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ListAllPerkCycleParams(_message.Message):
    __slots__ = ("membership_id", "customer_id")
    MEMBERSHIP_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    membership_id: int
    customer_id: int
    def __init__(self, membership_id: _Optional[int] = ..., customer_id: _Optional[int] = ...) -> None: ...

class ListAllPerkCycleResult(_message.Message):
    __slots__ = ("perk_cycle_item",)
    PERK_CYCLE_ITEM_FIELD_NUMBER: _ClassVar[int]
    perk_cycle_item: _containers.RepeatedCompositeFieldContainer[_membership_defs_pb2.PerkCycleItemDef]
    def __init__(self, perk_cycle_item: _Optional[_Iterable[_Union[_membership_defs_pb2.PerkCycleItemDef, _Mapping]]] = ...) -> None: ...

class GetPerkUsageDetailParams(_message.Message):
    __slots__ = ("filter", "membership_id", "customer_id")
    class Filter(_message.Message):
        __slots__ = ("validity_start_time",)
        VALIDITY_START_TIME_FIELD_NUMBER: _ClassVar[int]
        validity_start_time: _timestamp_pb2.Timestamp
        def __init__(self, validity_start_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...
    FILTER_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    filter: GetPerkUsageDetailParams.Filter
    membership_id: int
    customer_id: int
    def __init__(self, filter: _Optional[_Union[GetPerkUsageDetailParams.Filter, _Mapping]] = ..., membership_id: _Optional[int] = ..., customer_id: _Optional[int] = ...) -> None: ...

class GetPerkUsageDetailResult(_message.Message):
    __slots__ = ("included_benefits",)
    INCLUDED_BENEFITS_FIELD_NUMBER: _ClassVar[int]
    included_benefits: _containers.RepeatedCompositeFieldContainer[_redeem_models_pb2.IncludeBenefitView]
    def __init__(self, included_benefits: _Optional[_Iterable[_Union[_redeem_models_pb2.IncludeBenefitView, _Mapping]]] = ...) -> None: ...

class TransferCreditsParams(_message.Message):
    __slots__ = ("customer_id", "quantity_id", "validity_start_time", "transfer_quantity_num", "transfer_credit_num")
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    QUANTITY_ID_FIELD_NUMBER: _ClassVar[int]
    VALIDITY_START_TIME_FIELD_NUMBER: _ClassVar[int]
    TRANSFER_QUANTITY_NUM_FIELD_NUMBER: _ClassVar[int]
    TRANSFER_CREDIT_NUM_FIELD_NUMBER: _ClassVar[int]
    customer_id: int
    quantity_id: int
    validity_start_time: _timestamp_pb2.Timestamp
    transfer_quantity_num: int
    transfer_credit_num: int
    def __init__(self, customer_id: _Optional[int] = ..., quantity_id: _Optional[int] = ..., validity_start_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., transfer_quantity_num: _Optional[int] = ..., transfer_credit_num: _Optional[int] = ...) -> None: ...

class TransferCreditsResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
