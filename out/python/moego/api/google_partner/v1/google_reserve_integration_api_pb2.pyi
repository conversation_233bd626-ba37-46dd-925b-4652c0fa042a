from moego.models.google_partner.v1 import google_reserve_integration_models_pb2 as _google_reserve_integration_models_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class UpdateGoogleReserveIntegrationParam(_message.Message):
    __slots__ = ("enabled",)
    ENABLED_FIELD_NUMBER: _ClassVar[int]
    enabled: bool
    def __init__(self, enabled: bool = ...) -> None: ...

class GetOrInsertGoogleReserveIntegrationParam(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
