# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.api.order.v1 import service_charge_api_pb2 as moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2
from moego.models.order.v1 import service_charge_model_pb2 as moego_dot_models_dot_order_dot_v1_dot_service__charge__model__pb2


class ServiceChargeServiceStub(object):
    """service charge api
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetServiceChargeList = channel.unary_unary(
                '/moego.api.order.v1.ServiceChargeService/GetServiceChargeList',
                request_serializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.GetServiceChargeListRequest.SerializeToString,
                response_deserializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.GetServiceChargeListResponse.FromString,
                _registered_method=True)
        self.AddServiceCharge = channel.unary_unary(
                '/moego.api.order.v1.ServiceChargeService/AddServiceCharge',
                request_serializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.AddServiceChargeRequest.SerializeToString,
                response_deserializer=moego_dot_models_dot_order_dot_v1_dot_service__charge__model__pb2.ServiceCharge.FromString,
                _registered_method=True)
        self.UpdateServiceCharge = channel.unary_unary(
                '/moego.api.order.v1.ServiceChargeService/UpdateServiceCharge',
                request_serializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.UpdateServiceChargeRequest.SerializeToString,
                response_deserializer=moego_dot_models_dot_order_dot_v1_dot_service__charge__model__pb2.ServiceCharge.FromString,
                _registered_method=True)
        self.SortServiceCharge = channel.unary_unary(
                '/moego.api.order.v1.ServiceChargeService/SortServiceCharge',
                request_serializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.SortServiceChargeRequest.SerializeToString,
                response_deserializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.OperateServiceChargeResponse.FromString,
                _registered_method=True)
        self.DeleteServiceCharge = channel.unary_unary(
                '/moego.api.order.v1.ServiceChargeService/DeleteServiceCharge',
                request_serializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.DeleteServiceChargeRequest.SerializeToString,
                response_deserializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.OperateServiceChargeResponse.FromString,
                _registered_method=True)


class ServiceChargeServiceServicer(object):
    """service charge api
    """

    def GetServiceChargeList(self, request, context):
        """get service charge list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddServiceCharge(self, request, context):
        """add service charge
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateServiceCharge(self, request, context):
        """update service charge
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SortServiceCharge(self, request, context):
        """sort service charge
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteServiceCharge(self, request, context):
        """delete service charge
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ServiceChargeServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetServiceChargeList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetServiceChargeList,
                    request_deserializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.GetServiceChargeListRequest.FromString,
                    response_serializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.GetServiceChargeListResponse.SerializeToString,
            ),
            'AddServiceCharge': grpc.unary_unary_rpc_method_handler(
                    servicer.AddServiceCharge,
                    request_deserializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.AddServiceChargeRequest.FromString,
                    response_serializer=moego_dot_models_dot_order_dot_v1_dot_service__charge__model__pb2.ServiceCharge.SerializeToString,
            ),
            'UpdateServiceCharge': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateServiceCharge,
                    request_deserializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.UpdateServiceChargeRequest.FromString,
                    response_serializer=moego_dot_models_dot_order_dot_v1_dot_service__charge__model__pb2.ServiceCharge.SerializeToString,
            ),
            'SortServiceCharge': grpc.unary_unary_rpc_method_handler(
                    servicer.SortServiceCharge,
                    request_deserializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.SortServiceChargeRequest.FromString,
                    response_serializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.OperateServiceChargeResponse.SerializeToString,
            ),
            'DeleteServiceCharge': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteServiceCharge,
                    request_deserializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.DeleteServiceChargeRequest.FromString,
                    response_serializer=moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.OperateServiceChargeResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.order.v1.ServiceChargeService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.order.v1.ServiceChargeService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ServiceChargeService(object):
    """service charge api
    """

    @staticmethod
    def GetServiceChargeList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.order.v1.ServiceChargeService/GetServiceChargeList',
            moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.GetServiceChargeListRequest.SerializeToString,
            moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.GetServiceChargeListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AddServiceCharge(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.order.v1.ServiceChargeService/AddServiceCharge',
            moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.AddServiceChargeRequest.SerializeToString,
            moego_dot_models_dot_order_dot_v1_dot_service__charge__model__pb2.ServiceCharge.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateServiceCharge(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.order.v1.ServiceChargeService/UpdateServiceCharge',
            moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.UpdateServiceChargeRequest.SerializeToString,
            moego_dot_models_dot_order_dot_v1_dot_service__charge__model__pb2.ServiceCharge.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SortServiceCharge(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.order.v1.ServiceChargeService/SortServiceCharge',
            moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.SortServiceChargeRequest.SerializeToString,
            moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.OperateServiceChargeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteServiceCharge(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.order.v1.ServiceChargeService/DeleteServiceCharge',
            moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.DeleteServiceChargeRequest.SerializeToString,
            moego_dot_api_dot_order_dot_v1_dot_service__charge__api__pb2.OperateServiceChargeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
