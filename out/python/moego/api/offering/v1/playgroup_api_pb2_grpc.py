# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.api.offering.v1 import playgroup_api_pb2 as moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2


class PlaygroupServiceStub(object):
    """playgroup service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ListPlaygroup = channel.unary_unary(
                '/moego.api.offering.v1.PlaygroupService/ListPlaygroup',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.ListPlaygroupParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.ListPlaygroupResult.FromString,
                _registered_method=True)
        self.CreatePlaygroup = channel.unary_unary(
                '/moego.api.offering.v1.PlaygroupService/CreatePlaygroup',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.CreatePlaygroupParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.CreatePlaygroupResult.FromString,
                _registered_method=True)
        self.UpdatePlaygroup = channel.unary_unary(
                '/moego.api.offering.v1.PlaygroupService/UpdatePlaygroup',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.UpdatePlaygroupParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.UpdatePlaygroupResult.FromString,
                _registered_method=True)
        self.DeletePlaygroup = channel.unary_unary(
                '/moego.api.offering.v1.PlaygroupService/DeletePlaygroup',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.DeletePlaygroupParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.DeletePlaygroupResult.FromString,
                _registered_method=True)
        self.SortPlaygroup = channel.unary_unary(
                '/moego.api.offering.v1.PlaygroupService/SortPlaygroup',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.SortPlaygroupParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.SortPlaygroupResult.FromString,
                _registered_method=True)


class PlaygroupServiceServicer(object):
    """playgroup service
    """

    def ListPlaygroup(self, request, context):
        """list playgroup
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreatePlaygroup(self, request, context):
        """create playgroup
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdatePlaygroup(self, request, context):
        """update playgroup
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePlaygroup(self, request, context):
        """delete playgroup
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SortPlaygroup(self, request, context):
        """sort playgroup
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PlaygroupServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ListPlaygroup': grpc.unary_unary_rpc_method_handler(
                    servicer.ListPlaygroup,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.ListPlaygroupParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.ListPlaygroupResult.SerializeToString,
            ),
            'CreatePlaygroup': grpc.unary_unary_rpc_method_handler(
                    servicer.CreatePlaygroup,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.CreatePlaygroupParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.CreatePlaygroupResult.SerializeToString,
            ),
            'UpdatePlaygroup': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdatePlaygroup,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.UpdatePlaygroupParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.UpdatePlaygroupResult.SerializeToString,
            ),
            'DeletePlaygroup': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePlaygroup,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.DeletePlaygroupParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.DeletePlaygroupResult.SerializeToString,
            ),
            'SortPlaygroup': grpc.unary_unary_rpc_method_handler(
                    servicer.SortPlaygroup,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.SortPlaygroupParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.SortPlaygroupResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.offering.v1.PlaygroupService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.offering.v1.PlaygroupService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PlaygroupService(object):
    """playgroup service
    """

    @staticmethod
    def ListPlaygroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.PlaygroupService/ListPlaygroup',
            moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.ListPlaygroupParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.ListPlaygroupResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreatePlaygroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.PlaygroupService/CreatePlaygroup',
            moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.CreatePlaygroupParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.CreatePlaygroupResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdatePlaygroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.PlaygroupService/UpdatePlaygroup',
            moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.UpdatePlaygroupParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.UpdatePlaygroupResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeletePlaygroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.PlaygroupService/DeletePlaygroup',
            moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.DeletePlaygroupParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.DeletePlaygroupResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SortPlaygroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.PlaygroupService/SortPlaygroup',
            moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.SortPlaygroupParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_playgroup__api__pb2.SortPlaygroupResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
