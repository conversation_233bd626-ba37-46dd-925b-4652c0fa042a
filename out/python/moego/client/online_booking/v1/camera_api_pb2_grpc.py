# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.client.online_booking.v1 import camera_api_pb2 as moego_dot_client_dot_online__booking_dot_v1_dot_camera__api__pb2


class CameraServiceStub(object):
    """camera service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetCameraList = channel.unary_unary(
                '/moego.client.online_booking.v1.CameraService/GetCameraList',
                request_serializer=moego_dot_client_dot_online__booking_dot_v1_dot_camera__api__pb2.GetCameraListParams.SerializeToString,
                response_deserializer=moego_dot_client_dot_online__booking_dot_v1_dot_camera__api__pb2.GetCameraListResult.FromString,
                _registered_method=True)


class CameraServiceServicer(object):
    """camera service
    """

    def GetCameraList(self, request, context):
        """get camera list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CameraServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetCameraList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCameraList,
                    request_deserializer=moego_dot_client_dot_online__booking_dot_v1_dot_camera__api__pb2.GetCameraListParams.FromString,
                    response_serializer=moego_dot_client_dot_online__booking_dot_v1_dot_camera__api__pb2.GetCameraListResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.client.online_booking.v1.CameraService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.client.online_booking.v1.CameraService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class CameraService(object):
    """camera service
    """

    @staticmethod
    def GetCameraList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.online_booking.v1.CameraService/GetCameraList',
            moego_dot_client_dot_online__booking_dot_v1_dot_camera__api__pb2.GetCameraListParams.SerializeToString,
            moego_dot_client_dot_online__booking_dot_v1_dot_camera__api__pb2.GetCameraListResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
