from moego.models.business_customer.v1 import business_customer_pet_defs_pb2 as _business_customer_pet_defs_pb2
from moego.models.business_customer.v1 import business_customer_pet_models_pb2 as _business_customer_pet_models_pb2
from moego.models.business_customer.v1 import business_pet_vaccine_record_models_pb2 as _business_pet_vaccine_record_models_pb2
from moego.models.business_customer.v1 import business_pet_vaccine_request_defs_pb2 as _business_pet_vaccine_request_defs_pb2
from moego.models.business_customer.v1 import business_pet_vaccine_request_models_pb2 as _business_pet_vaccine_request_models_pb2
from moego.models.customer.v1 import customer_pet_vaccine_defs_pb2 as _customer_pet_vaccine_defs_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class PetVaccineExpirationStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    PET_VACCINE_EXPIRATION_STATUS_UNSPECIFIED: _ClassVar[PetVaccineExpirationStatus]
    PET_VACCINE_EXPIRATION_STATUS_NO_RECORD: _ClassVar[PetVaccineExpirationStatus]
    PET_VACCINE_EXPIRATION_STATUS_NOT_EXPIRED: _ClassVar[PetVaccineExpirationStatus]
    PET_VACCINE_EXPIRATION_STATUS_EXPIRED: _ClassVar[PetVaccineExpirationStatus]
    PET_VACCINE_EXPIRATION_STATUS_EXPIRED_BEFORE_NEXT_APPOINTMENT: _ClassVar[PetVaccineExpirationStatus]
    PET_VACCINE_EXPIRATION_STATUS_EXPIRED_IN_30_DAYS: _ClassVar[PetVaccineExpirationStatus]
PET_VACCINE_EXPIRATION_STATUS_UNSPECIFIED: PetVaccineExpirationStatus
PET_VACCINE_EXPIRATION_STATUS_NO_RECORD: PetVaccineExpirationStatus
PET_VACCINE_EXPIRATION_STATUS_NOT_EXPIRED: PetVaccineExpirationStatus
PET_VACCINE_EXPIRATION_STATUS_EXPIRED: PetVaccineExpirationStatus
PET_VACCINE_EXPIRATION_STATUS_EXPIRED_BEFORE_NEXT_APPOINTMENT: PetVaccineExpirationStatus
PET_VACCINE_EXPIRATION_STATUS_EXPIRED_IN_30_DAYS: PetVaccineExpirationStatus

class CreatePetParams(_message.Message):
    __slots__ = ("name", "domain")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    DEF_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., **kwargs) -> None: ...

class CreatePetResult(_message.Message):
    __slots__ = ("pet",)
    PET_FIELD_NUMBER: _ClassVar[int]
    pet: _business_customer_pet_models_pb2.BusinessCustomerPetModel
    def __init__(self, pet: _Optional[_Union[_business_customer_pet_models_pb2.BusinessCustomerPetModel, _Mapping]] = ...) -> None: ...

class GetPetParams(_message.Message):
    __slots__ = ("name", "domain", "id")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    ID_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    id: int
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., id: _Optional[int] = ...) -> None: ...

class GetPetResult(_message.Message):
    __slots__ = ("pet",)
    PET_FIELD_NUMBER: _ClassVar[int]
    pet: _business_customer_pet_models_pb2.BusinessCustomerPetModel
    def __init__(self, pet: _Optional[_Union[_business_customer_pet_models_pb2.BusinessCustomerPetModel, _Mapping]] = ...) -> None: ...

class ListPetsParams(_message.Message):
    __slots__ = ("name", "domain", "include_vaccine_records", "include_pending_vaccine_requests", "include_passed_away")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    INCLUDE_VACCINE_RECORDS_FIELD_NUMBER: _ClassVar[int]
    INCLUDE_PENDING_VACCINE_REQUESTS_FIELD_NUMBER: _ClassVar[int]
    INCLUDE_PASSED_AWAY_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    include_vaccine_records: bool
    include_pending_vaccine_requests: bool
    include_passed_away: bool
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., include_vaccine_records: bool = ..., include_pending_vaccine_requests: bool = ..., include_passed_away: bool = ...) -> None: ...

class ListPetsResult(_message.Message):
    __slots__ = ("pets", "vaccine_records", "pending_vaccine_requests")
    PETS_FIELD_NUMBER: _ClassVar[int]
    VACCINE_RECORDS_FIELD_NUMBER: _ClassVar[int]
    PENDING_VACCINE_REQUESTS_FIELD_NUMBER: _ClassVar[int]
    pets: _containers.RepeatedCompositeFieldContainer[_business_customer_pet_models_pb2.BusinessCustomerPetModel]
    vaccine_records: _containers.RepeatedCompositeFieldContainer[_business_pet_vaccine_record_models_pb2.BusinessPetVaccineRecordBindingModel]
    pending_vaccine_requests: _containers.RepeatedCompositeFieldContainer[_business_pet_vaccine_request_models_pb2.BusinessPetVaccineRequestBindingModel]
    def __init__(self, pets: _Optional[_Iterable[_Union[_business_customer_pet_models_pb2.BusinessCustomerPetModel, _Mapping]]] = ..., vaccine_records: _Optional[_Iterable[_Union[_business_pet_vaccine_record_models_pb2.BusinessPetVaccineRecordBindingModel, _Mapping]]] = ..., pending_vaccine_requests: _Optional[_Iterable[_Union[_business_pet_vaccine_request_models_pb2.BusinessPetVaccineRequestBindingModel, _Mapping]]] = ...) -> None: ...

class UpdatePetParams(_message.Message):
    __slots__ = ("name", "domain", "id", "vaccines")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    ID_FIELD_NUMBER: _ClassVar[int]
    DEF_FIELD_NUMBER: _ClassVar[int]
    VACCINES_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    id: int
    vaccines: _containers.RepeatedCompositeFieldContainer[_customer_pet_vaccine_defs_pb2.VaccineDef]
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., id: _Optional[int] = ..., vaccines: _Optional[_Iterable[_Union[_customer_pet_vaccine_defs_pb2.VaccineDef, _Mapping]]] = ..., **kwargs) -> None: ...

class UpdatePetResult(_message.Message):
    __slots__ = ("pet",)
    PET_FIELD_NUMBER: _ClassVar[int]
    pet: _business_customer_pet_models_pb2.BusinessCustomerPetModel
    def __init__(self, pet: _Optional[_Union[_business_customer_pet_models_pb2.BusinessCustomerPetModel, _Mapping]] = ...) -> None: ...

class SubmitPetVaccineRequestParams(_message.Message):
    __slots__ = ("name", "domain", "pet_id", "id", "pet_vaccine_request")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    ID_FIELD_NUMBER: _ClassVar[int]
    PET_VACCINE_REQUEST_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    pet_id: int
    id: int
    pet_vaccine_request: _business_pet_vaccine_request_defs_pb2.BusinessPetVaccineRequestCreateDef
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., pet_id: _Optional[int] = ..., id: _Optional[int] = ..., pet_vaccine_request: _Optional[_Union[_business_pet_vaccine_request_defs_pb2.BusinessPetVaccineRequestCreateDef, _Mapping]] = ...) -> None: ...

class SubmitPetVaccineRequestResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ListPetVaccineExpirationStatusParams(_message.Message):
    __slots__ = ("name", "domain")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ...) -> None: ...

class ListPetVaccineExpirationStatusResult(_message.Message):
    __slots__ = ("pet_vaccine_expiration_status", "pet_vaccine_record_expiration_status")
    class PetVaccineExpirationStatusEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: PetVaccineExpirationStatus
        def __init__(self, key: _Optional[int] = ..., value: _Optional[_Union[PetVaccineExpirationStatus, str]] = ...) -> None: ...
    class PetVaccineRecordExpirationStatusEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: PetVaccineExpirationStatus
        def __init__(self, key: _Optional[int] = ..., value: _Optional[_Union[PetVaccineExpirationStatus, str]] = ...) -> None: ...
    PET_VACCINE_EXPIRATION_STATUS_FIELD_NUMBER: _ClassVar[int]
    PET_VACCINE_RECORD_EXPIRATION_STATUS_FIELD_NUMBER: _ClassVar[int]
    pet_vaccine_expiration_status: _containers.ScalarMap[int, PetVaccineExpirationStatus]
    pet_vaccine_record_expiration_status: _containers.ScalarMap[int, PetVaccineExpirationStatus]
    def __init__(self, pet_vaccine_expiration_status: _Optional[_Mapping[int, PetVaccineExpirationStatus]] = ..., pet_vaccine_record_expiration_status: _Optional[_Mapping[int, PetVaccineExpirationStatus]] = ...) -> None: ...
