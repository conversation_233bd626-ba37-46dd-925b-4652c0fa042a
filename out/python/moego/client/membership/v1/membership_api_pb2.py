# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/client/membership/v1/membership_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/client/membership/v1/membership_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.business_customer.v1 import business_customer_defs_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__customer__defs__pb2
from moego.models.business_customer.v1 import business_customer_pet_defs_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__customer__pet__defs__pb2
from moego.models.membership.v1 import membership_defs_pb2 as moego_dot_models_dot_membership_dot_v1_dot_membership__defs__pb2
from moego.models.membership.v1 import membership_models_pb2 as moego_dot_models_dot_membership_dot_v1_dot_membership__models__pb2
from moego.models.membership.v1 import sell_link_models_pb2 as moego_dot_models_dot_membership_dot_v1_dot_sell__link__models__pb2
from moego.models.online_booking.v1 import booking_availability_defs_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_booking__availability__defs__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/moego/client/membership/v1/membership_api.proto\x12\x1amoego.client.membership.v1\x1a>moego/models/business_customer/v1/business_customer_defs.proto\x1a\x42moego/models/business_customer/v1/business_customer_pet_defs.proto\x1a\x30moego/models/membership/v1/membership_defs.proto\x1a\x32moego/models/membership/v1/membership_models.proto\x1a\x31moego/models/membership/v1/sell_link_models.proto\x1a>moego/models/online_booking/v1/booking_availability_defs.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\xd1\x02\n\x15ListMembershipsParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12[\n\x06status\x18\x03 \x01(\x0e\x32\x32.moego.models.membership.v1.MembershipModel.StatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\x06status\x88\x01\x01\x12)\n\tname_like\x18\x04 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x02R\x08nameLike\x88\x01\x01\x12\x46\n\npagination\x18\x05 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x03R\npagination\x88\x01\x01\x42\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\t\n\x07_statusB\x0c\n\n_name_likeB\r\n\x0b_pagination\"\xaa\x01\n\x15ListMembershipsResult\x12M\n\x0bmemberships\x18\x01 \x03(\x0b\x32+.moego.models.membership.v1.MembershipModelR\x0bmemberships\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\"\xce\x05\n\x14\x43reateSellLinkParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12,\n\rmembership_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0cmembershipId\x12=\n\x13membership_revision\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x01R\x12membershipRevision\x88\x01\x01\x12~\n\x16\x63ustomer_with_pet_info\x18\x05 \x01(\x0b\x32\x44.moego.client.membership.v1.CreateSellLinkParams.CustomerWithPetInfoH\x02R\x13\x63ustomerWithPetInfo\x88\x01\x01\x1a\xd3\x02\n\x13\x43ustomerWithPetInfo\x12\x9b\x01\n\x1d\x63ustomer_with_additional_info\x18\x01 \x01(\x0b\x32N.moego.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x1a\x63ustomerWithAdditionalInfo\x12\x9d\x01\n\x19pets_with_additional_info\x18\x02 \x03(\x0b\x32Q.moego.models.business_customer.v1.BusinessCustomerPetWithAdditionalInfoCreateDefB\x0f\xfa\x42\x0c\x92\x01\t\x10\x14\"\x05\x8a\x01\x02\x10\x01R\x16petsWithAdditionalInfoB\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\x16\n\x14_membership_revisionB\x19\n\x17_customer_with_pet_info\"\x81\x01\n\x14\x43reateSellLinkResult\x12\x46\n\tsell_link\x18\x01 \x01(\x0b\x32).moego.models.membership.v1.SellLinkModelR\x08sellLink\x12!\n\x0cpublic_token\x18\x02 \x01(\tR\x0bpublicToken\"\x8e\x04\n\x1cListMembershipsForSaleParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12[\n\x06status\x18\x03 \x01(\x0e\x32\x32.moego.models.membership.v1.MembershipModel.StatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\x06status\x88\x01\x01\x12)\n\tname_like\x18\x04 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x02R\x08nameLike\x88\x01\x01\x12\\\n\x06\x66ilter\x18\x05 \x01(\x0b\x32?.moego.client.membership.v1.ListMembershipsForSaleParams.FilterH\x03R\x06\x66ilter\x88\x01\x01\x12\x46\n\npagination\x18\x0f \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x04R\npagination\x88\x01\x01\x1aK\n\x06\x46ilter\x12\x41\n\x04pets\x18\x01 \x03(\x0b\x32-.moego.models.online_booking.v1.BookingPetDefR\x04petsB\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\t\n\x07_statusB\x0c\n\n_name_likeB\t\n\x07_filterB\r\n\x0b_pagination\"\xb1\x01\n\x1cListMembershipsForSaleResult\x12M\n\x0bmemberships\x18\x01 \x03(\x0b\x32+.moego.models.membership.v1.MembershipModelR\x0bmemberships\x12\x42\n\npagination\x18\x0f \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\"\xd9\x05\n\x1a\x43reateSellLinkForAppParams\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12,\n\rmembership_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0cmembershipId\x12=\n\x13membership_revision\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x00R\x12membershipRevision\x88\x01\x01\x12\x84\x01\n\x16\x63ustomer_with_pet_info\x18\x05 \x01(\x0b\x32J.moego.client.membership.v1.CreateSellLinkForAppParams.CustomerWithPetInfoH\x01R\x13\x63ustomerWithPetInfo\x88\x01\x01\x1a\xd3\x02\n\x13\x43ustomerWithPetInfo\x12\x9b\x01\n\x1d\x63ustomer_with_additional_info\x18\x01 \x01(\x0b\x32N.moego.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x1a\x63ustomerWithAdditionalInfo\x12\x9d\x01\n\x19pets_with_additional_info\x18\x02 \x03(\x0b\x32Q.moego.models.business_customer.v1.BusinessCustomerPetWithAdditionalInfoCreateDefB\x0f\xfa\x42\x0c\x92\x01\t\x10\x14\"\x05\x8a\x01\x02\x10\x01R\x16petsWithAdditionalInfoB\x16\n\x14_membership_revisionB\x19\n\x17_customer_with_pet_info\"\x87\x01\n\x1a\x43reateSellLinkForAppResult\x12\x46\n\tsell_link\x18\x01 \x01(\x0b\x32).moego.models.membership.v1.SellLinkModelR\x08sellLink\x12!\n\x0cpublic_token\x18\x02 \x01(\tR\x0bpublicToken\"\xf7\x03\n\"ListMembershipsForSaleForAppParams\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\x12[\n\x06status\x18\x02 \x01(\x0e\x32\x32.moego.models.membership.v1.MembershipModel.StatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x06status\x88\x01\x01\x12)\n\tname_like\x18\x03 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x01R\x08nameLike\x88\x01\x01\x12\x62\n\x06\x66ilter\x18\x04 \x01(\x0b\x32\x45.moego.client.membership.v1.ListMembershipsForSaleForAppParams.FilterH\x02R\x06\x66ilter\x88\x01\x01\x12\x46\n\npagination\x18\x0f \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x03R\npagination\x88\x01\x01\x1aK\n\x06\x46ilter\x12\x41\n\x04pets\x18\x01 \x03(\x0b\x32-.moego.models.online_booking.v1.BookingPetDefR\x04petsB\t\n\x07_statusB\x0c\n\n_name_likeB\t\n\x07_filterB\r\n\x0b_pagination\"\xb1\x03\n\"ListMembershipsForSaleForAppResult\x12M\n\x0bmemberships\x18\x01 \x03(\x0b\x32+.moego.models.membership.v1.MembershipModelR\x0bmemberships\x12{\n\x1cmembership_discount_benefits\x18\x02 \x03(\x0b\x32\x39.moego.models.membership.v1.MembershipDiscountBenefitsDefR\x1amembershipDiscountBenefits\x12{\n\x1cmembership_quantity_benefits\x18\x03 \x03(\x0b\x32\x39.moego.models.membership.v1.MembershipQuantityBenefitsDefR\x1amembershipQuantityBenefits\x12\x42\n\npagination\x18\x0f \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination2\xbb\x05\n\x11MembershipService\x12w\n\x0fListMemberships\x12\x31.moego.client.membership.v1.ListMembershipsParams\x1a\x31.moego.client.membership.v1.ListMembershipsResult\x12t\n\x0e\x43reateSellLink\x12\x30.moego.client.membership.v1.CreateSellLinkParams\x1a\x30.moego.client.membership.v1.CreateSellLinkResult\x12\x8c\x01\n\x16ListMembershipsForSale\x12\x38.moego.client.membership.v1.ListMembershipsForSaleParams\x1a\x38.moego.client.membership.v1.ListMembershipsForSaleResult\x12\x86\x01\n\x14\x43reateSellLinkForApp\x12\x36.moego.client.membership.v1.CreateSellLinkForAppParams\x1a\x36.moego.client.membership.v1.CreateSellLinkForAppResult\x12\x9e\x01\n\x1cListMembershipsForSaleForApp\x12>.moego.client.membership.v1.ListMembershipsForSaleForAppParams\x1a>.moego.client.membership.v1.ListMembershipsForSaleForAppResultB\x87\x01\n\"com.moego.idl.client.membership.v1P\x01Z_github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/membership/v1;membershipapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.client.membership.v1.membership_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.moego.idl.client.membership.v1P\001Z_github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/membership/v1;membershipapipb'
  _globals['_LISTMEMBERSHIPSPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_LISTMEMBERSHIPSPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_LISTMEMBERSHIPSPARAMS'].fields_by_name['status']._loaded_options = None
  _globals['_LISTMEMBERSHIPSPARAMS'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTMEMBERSHIPSPARAMS'].fields_by_name['name_like']._loaded_options = None
  _globals['_LISTMEMBERSHIPSPARAMS'].fields_by_name['name_like']._serialized_options = b'\372B\004r\002\0302'
  _globals['_CREATESELLLINKPARAMS_CUSTOMERWITHPETINFO'].fields_by_name['customer_with_additional_info']._loaded_options = None
  _globals['_CREATESELLLINKPARAMS_CUSTOMERWITHPETINFO'].fields_by_name['customer_with_additional_info']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CREATESELLLINKPARAMS_CUSTOMERWITHPETINFO'].fields_by_name['pets_with_additional_info']._loaded_options = None
  _globals['_CREATESELLLINKPARAMS_CUSTOMERWITHPETINFO'].fields_by_name['pets_with_additional_info']._serialized_options = b'\372B\014\222\001\t\020\024\"\005\212\001\002\020\001'
  _globals['_CREATESELLLINKPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_CREATESELLLINKPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_CREATESELLLINKPARAMS'].fields_by_name['membership_id']._loaded_options = None
  _globals['_CREATESELLLINKPARAMS'].fields_by_name['membership_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATESELLLINKPARAMS'].fields_by_name['membership_revision']._loaded_options = None
  _globals['_CREATESELLLINKPARAMS'].fields_by_name['membership_revision']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_LISTMEMBERSHIPSFORSALEPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_LISTMEMBERSHIPSFORSALEPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_LISTMEMBERSHIPSFORSALEPARAMS'].fields_by_name['status']._loaded_options = None
  _globals['_LISTMEMBERSHIPSFORSALEPARAMS'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTMEMBERSHIPSFORSALEPARAMS'].fields_by_name['name_like']._loaded_options = None
  _globals['_LISTMEMBERSHIPSFORSALEPARAMS'].fields_by_name['name_like']._serialized_options = b'\372B\004r\002\0302'
  _globals['_CREATESELLLINKFORAPPPARAMS_CUSTOMERWITHPETINFO'].fields_by_name['customer_with_additional_info']._loaded_options = None
  _globals['_CREATESELLLINKFORAPPPARAMS_CUSTOMERWITHPETINFO'].fields_by_name['customer_with_additional_info']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CREATESELLLINKFORAPPPARAMS_CUSTOMERWITHPETINFO'].fields_by_name['pets_with_additional_info']._loaded_options = None
  _globals['_CREATESELLLINKFORAPPPARAMS_CUSTOMERWITHPETINFO'].fields_by_name['pets_with_additional_info']._serialized_options = b'\372B\014\222\001\t\020\024\"\005\212\001\002\020\001'
  _globals['_CREATESELLLINKFORAPPPARAMS'].fields_by_name['membership_id']._loaded_options = None
  _globals['_CREATESELLLINKFORAPPPARAMS'].fields_by_name['membership_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATESELLLINKFORAPPPARAMS'].fields_by_name['membership_revision']._loaded_options = None
  _globals['_CREATESELLLINKFORAPPPARAMS'].fields_by_name['membership_revision']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_LISTMEMBERSHIPSFORSALEFORAPPPARAMS'].fields_by_name['status']._loaded_options = None
  _globals['_LISTMEMBERSHIPSFORSALEFORAPPPARAMS'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTMEMBERSHIPSFORSALEFORAPPPARAMS'].fields_by_name['name_like']._loaded_options = None
  _globals['_LISTMEMBERSHIPSFORSALEFORAPPPARAMS'].fields_by_name['name_like']._serialized_options = b'\372B\004r\002\0302'
  _globals['_LISTMEMBERSHIPSPARAMS']._serialized_start=496
  _globals['_LISTMEMBERSHIPSPARAMS']._serialized_end=833
  _globals['_LISTMEMBERSHIPSRESULT']._serialized_start=836
  _globals['_LISTMEMBERSHIPSRESULT']._serialized_end=1006
  _globals['_CREATESELLLINKPARAMS']._serialized_start=1009
  _globals['_CREATESELLLINKPARAMS']._serialized_end=1727
  _globals['_CREATESELLLINKPARAMS_CUSTOMERWITHPETINFO']._serialized_start=1319
  _globals['_CREATESELLLINKPARAMS_CUSTOMERWITHPETINFO']._serialized_end=1658
  _globals['_CREATESELLLINKRESULT']._serialized_start=1730
  _globals['_CREATESELLLINKRESULT']._serialized_end=1859
  _globals['_LISTMEMBERSHIPSFORSALEPARAMS']._serialized_start=1862
  _globals['_LISTMEMBERSHIPSFORSALEPARAMS']._serialized_end=2388
  _globals['_LISTMEMBERSHIPSFORSALEPARAMS_FILTER']._serialized_start=2244
  _globals['_LISTMEMBERSHIPSFORSALEPARAMS_FILTER']._serialized_end=2319
  _globals['_LISTMEMBERSHIPSFORSALERESULT']._serialized_start=2391
  _globals['_LISTMEMBERSHIPSFORSALERESULT']._serialized_end=2568
  _globals['_CREATESELLLINKFORAPPPARAMS']._serialized_start=2571
  _globals['_CREATESELLLINKFORAPPPARAMS']._serialized_end=3300
  _globals['_CREATESELLLINKFORAPPPARAMS_CUSTOMERWITHPETINFO']._serialized_start=1319
  _globals['_CREATESELLLINKFORAPPPARAMS_CUSTOMERWITHPETINFO']._serialized_end=1658
  _globals['_CREATESELLLINKFORAPPRESULT']._serialized_start=3303
  _globals['_CREATESELLLINKFORAPPRESULT']._serialized_end=3438
  _globals['_LISTMEMBERSHIPSFORSALEFORAPPPARAMS']._serialized_start=3441
  _globals['_LISTMEMBERSHIPSFORSALEFORAPPPARAMS']._serialized_end=3944
  _globals['_LISTMEMBERSHIPSFORSALEFORAPPPARAMS_FILTER']._serialized_start=2244
  _globals['_LISTMEMBERSHIPSFORSALEFORAPPPARAMS_FILTER']._serialized_end=2319
  _globals['_LISTMEMBERSHIPSFORSALEFORAPPRESULT']._serialized_start=3947
  _globals['_LISTMEMBERSHIPSFORSALEFORAPPRESULT']._serialized_end=4380
  _globals['_MEMBERSHIPSERVICE']._serialized_start=4383
  _globals['_MEMBERSHIPSERVICE']._serialized_end=5082
# @@protoc_insertion_point(module_scope)
