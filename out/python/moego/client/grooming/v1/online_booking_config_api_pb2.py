# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/client/grooming/v1/online_booking_config_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/client/grooming/v1/online_booking_config_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.online_booking.v1 import business_ob_config_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_business__ob__config__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8moego/client/grooming/v1/online_booking_config_api.proto\x12\x18moego.client.grooming.v1\x1a>moego/models/online_booking/v1/business_ob_config_models.proto\x1a\x17validate/validate.proto\"I\n\x1dGetOnlineBookingConfigRequest\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\"z\n\x1eGetOnlineBookingConfigResponse\x12X\n\x06\x63onfig\x18\x01 \x01(\x0b\<EMAIL>.online_booking.v1.BusinessOBConfigModelBookingViewR\x06\x63onfig2\xaa\x01\n\x1aOnlineBookingConfigService\x12\x8b\x01\n\x16GetOnlineBookingConfig\x12\x37.moego.client.grooming.v1.GetOnlineBookingConfigRequest\x1a\x38.moego.client.grooming.v1.GetOnlineBookingConfigResponseB\x81\x01\n com.moego.idl.client.grooming.v1P\x01Z[github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/grooming/v1;groomingapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.client.grooming.v1.online_booking_config_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.client.grooming.v1P\001Z[github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/grooming/v1;groomingapipb'
  _globals['_GETONLINEBOOKINGCONFIGREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETONLINEBOOKINGCONFIGREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETONLINEBOOKINGCONFIGREQUEST']._serialized_start=175
  _globals['_GETONLINEBOOKINGCONFIGREQUEST']._serialized_end=248
  _globals['_GETONLINEBOOKINGCONFIGRESPONSE']._serialized_start=250
  _globals['_GETONLINEBOOKINGCONFIGRESPONSE']._serialized_end=372
  _globals['_ONLINEBOOKINGCONFIGSERVICE']._serialized_start=375
  _globals['_ONLINEBOOKINGCONFIGSERVICE']._serialized_end=545
# @@protoc_insertion_point(module_scope)
