from google.protobuf import timestamp_pb2 as _timestamp_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ListColorRequest(_message.Message):
    __slots__ = ("color_ids", "color_name", "company_id")
    COLOR_IDS_FIELD_NUMBER: _ClassVar[int]
    COLOR_NAME_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    color_ids: _containers.RepeatedScalarFieldContainer[int]
    color_name: str
    company_id: int
    def __init__(self, color_ids: _Optional[_Iterable[int]] = ..., color_name: _Optional[str] = ..., company_id: _Optional[int] = ...) -> None: ...

class ListColorResponse(_message.Message):
    __slots__ = ("colors",)
    class Color(_message.Message):
        __slots__ = ("id", "name", "company_id", "status", "create_time", "update_time")
        ID_FIELD_NUMBER: _ClassVar[int]
        NAME_FIELD_NUMBER: _ClassVar[int]
        COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
        STATUS_FIELD_NUMBER: _ClassVar[int]
        CREATE_TIME_FIELD_NUMBER: _ClassVar[int]
        UPDATE_TIME_FIELD_NUMBER: _ClassVar[int]
        id: int
        name: str
        company_id: int
        status: int
        create_time: _timestamp_pb2.Timestamp
        update_time: _timestamp_pb2.Timestamp
        def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ..., company_id: _Optional[int] = ..., status: _Optional[int] = ..., create_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., update_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...
    COLORS_FIELD_NUMBER: _ClassVar[int]
    colors: _containers.RepeatedCompositeFieldContainer[ListColorResponse.Color]
    def __init__(self, colors: _Optional[_Iterable[_Union[ListColorResponse.Color, _Mapping]]] = ...) -> None: ...

class UpdateColorRequest(_message.Message):
    __slots__ = ("company_id", "color_id", "color_name", "status")
    class Status(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        STATUS_UNSPECIFIED: _ClassVar[UpdateColorRequest.Status]
        NORMAL: _ClassVar[UpdateColorRequest.Status]
        DELETED: _ClassVar[UpdateColorRequest.Status]
    STATUS_UNSPECIFIED: UpdateColorRequest.Status
    NORMAL: UpdateColorRequest.Status
    DELETED: UpdateColorRequest.Status
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    COLOR_ID_FIELD_NUMBER: _ClassVar[int]
    COLOR_NAME_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    color_id: int
    color_name: str
    status: UpdateColorRequest.Status
    def __init__(self, company_id: _Optional[int] = ..., color_id: _Optional[int] = ..., color_name: _Optional[str] = ..., status: _Optional[_Union[UpdateColorRequest.Status, str]] = ...) -> None: ...

class UpdateColorResponse(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class BindingColorRequest(_message.Message):
    __slots__ = ("color_id", "pet_id", "status")
    class Status(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        STATUS_UNSPECIFIED: _ClassVar[BindingColorRequest.Status]
        NORMAL: _ClassVar[BindingColorRequest.Status]
        DELETED: _ClassVar[BindingColorRequest.Status]
    STATUS_UNSPECIFIED: BindingColorRequest.Status
    NORMAL: BindingColorRequest.Status
    DELETED: BindingColorRequest.Status
    COLOR_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    color_id: int
    pet_id: int
    status: BindingColorRequest.Status
    def __init__(self, color_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., status: _Optional[_Union[BindingColorRequest.Status, str]] = ...) -> None: ...

class BindingColorResponse(_message.Message):
    __slots__ = ("result",)
    RESULT_FIELD_NUMBER: _ClassVar[int]
    result: bool
    def __init__(self, result: bool = ...) -> None: ...

class ListColorBindingRequest(_message.Message):
    __slots__ = ("pet_id", "color_id")
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    COLOR_ID_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    color_id: int
    def __init__(self, pet_id: _Optional[int] = ..., color_id: _Optional[int] = ...) -> None: ...

class ListColorBindingResponse(_message.Message):
    __slots__ = ("bindings",)
    class ColorBinding(_message.Message):
        __slots__ = ("id", "pet_id", "color_id", "status")
        ID_FIELD_NUMBER: _ClassVar[int]
        PET_ID_FIELD_NUMBER: _ClassVar[int]
        COLOR_ID_FIELD_NUMBER: _ClassVar[int]
        STATUS_FIELD_NUMBER: _ClassVar[int]
        id: int
        pet_id: int
        color_id: int
        status: int
        def __init__(self, id: _Optional[int] = ..., pet_id: _Optional[int] = ..., color_id: _Optional[int] = ..., status: _Optional[int] = ...) -> None: ...
    BINDINGS_FIELD_NUMBER: _ClassVar[int]
    bindings: _containers.RepeatedCompositeFieldContainer[ListColorBindingResponse.ColorBinding]
    def __init__(self, bindings: _Optional[_Iterable[_Union[ListColorBindingResponse.ColorBinding, _Mapping]]] = ...) -> None: ...
