# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.business_customer.v1 import business_pet_medication_schedule_service_pb2 as moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2


class BusinessPetMedicationScheduleServiceStub(object):
    """Business pet medication schedule service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateMedicationSchedule = channel.unary_unary(
                '/moego.service.business_customer.v1.BusinessPetMedicationScheduleService/CreateMedicationSchedule',
                request_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.CreateMedicationScheduleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.CreateMedicationScheduleResponse.FromString,
                _registered_method=True)
        self.BatchCreateMedicationSchedule = channel.unary_unary(
                '/moego.service.business_customer.v1.BusinessPetMedicationScheduleService/BatchCreateMedicationSchedule',
                request_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.BatchCreateMedicationScheduleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.BatchCreateMedicationScheduleResponse.FromString,
                _registered_method=True)
        self.UpdateMedicationSchedule = channel.unary_unary(
                '/moego.service.business_customer.v1.BusinessPetMedicationScheduleService/UpdateMedicationSchedule',
                request_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.UpdateMedicationScheduleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.UpdateMedicationScheduleResponse.FromString,
                _registered_method=True)
        self.DeleteMedicationSchedule = channel.unary_unary(
                '/moego.service.business_customer.v1.BusinessPetMedicationScheduleService/DeleteMedicationSchedule',
                request_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.DeleteMedicationScheduleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.DeleteMedicationScheduleResponse.FromString,
                _registered_method=True)
        self.ListPetMedicationSchedule = channel.unary_unary(
                '/moego.service.business_customer.v1.BusinessPetMedicationScheduleService/ListPetMedicationSchedule',
                request_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.ListPetMedicationScheduleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.ListPetMedicationScheduleResponse.FromString,
                _registered_method=True)


class BusinessPetMedicationScheduleServiceServicer(object):
    """Business pet medication schedule service
    """

    def CreateMedicationSchedule(self, request, context):
        """Create medication schedule
        Medication display rules: {Medication schedule} {Amount} {Medication unit} {Medication name} {Medication notes}
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchCreateMedicationSchedule(self, request, context):
        """Batch create medication schedule
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateMedicationSchedule(self, request, context):
        """Update medication schedule
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteMedicationSchedule(self, request, context):
        """Delete medication schedule
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListPetMedicationSchedule(self, request, context):
        """List pet's medication schedule
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_BusinessPetMedicationScheduleServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateMedicationSchedule': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateMedicationSchedule,
                    request_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.CreateMedicationScheduleRequest.FromString,
                    response_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.CreateMedicationScheduleResponse.SerializeToString,
            ),
            'BatchCreateMedicationSchedule': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchCreateMedicationSchedule,
                    request_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.BatchCreateMedicationScheduleRequest.FromString,
                    response_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.BatchCreateMedicationScheduleResponse.SerializeToString,
            ),
            'UpdateMedicationSchedule': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateMedicationSchedule,
                    request_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.UpdateMedicationScheduleRequest.FromString,
                    response_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.UpdateMedicationScheduleResponse.SerializeToString,
            ),
            'DeleteMedicationSchedule': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteMedicationSchedule,
                    request_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.DeleteMedicationScheduleRequest.FromString,
                    response_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.DeleteMedicationScheduleResponse.SerializeToString,
            ),
            'ListPetMedicationSchedule': grpc.unary_unary_rpc_method_handler(
                    servicer.ListPetMedicationSchedule,
                    request_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.ListPetMedicationScheduleRequest.FromString,
                    response_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.ListPetMedicationScheduleResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.business_customer.v1.BusinessPetMedicationScheduleService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.business_customer.v1.BusinessPetMedicationScheduleService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class BusinessPetMedicationScheduleService(object):
    """Business pet medication schedule service
    """

    @staticmethod
    def CreateMedicationSchedule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.business_customer.v1.BusinessPetMedicationScheduleService/CreateMedicationSchedule',
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.CreateMedicationScheduleRequest.SerializeToString,
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.CreateMedicationScheduleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchCreateMedicationSchedule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.business_customer.v1.BusinessPetMedicationScheduleService/BatchCreateMedicationSchedule',
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.BatchCreateMedicationScheduleRequest.SerializeToString,
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.BatchCreateMedicationScheduleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateMedicationSchedule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.business_customer.v1.BusinessPetMedicationScheduleService/UpdateMedicationSchedule',
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.UpdateMedicationScheduleRequest.SerializeToString,
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.UpdateMedicationScheduleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteMedicationSchedule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.business_customer.v1.BusinessPetMedicationScheduleService/DeleteMedicationSchedule',
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.DeleteMedicationScheduleRequest.SerializeToString,
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.DeleteMedicationScheduleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListPetMedicationSchedule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.business_customer.v1.BusinessPetMedicationScheduleService/ListPetMedicationSchedule',
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.ListPetMedicationScheduleRequest.SerializeToString,
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__medication__schedule__service__pb2.ListPetMedicationScheduleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
