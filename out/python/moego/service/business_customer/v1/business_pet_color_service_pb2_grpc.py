# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.business_customer.v1 import business_pet_color_service_pb2 as moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2


class BusinessPetColorServiceStub(object):
    """business pet color service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ListColor = channel.unary_unary(
                '/moego.service.business_customer.v1.BusinessPetColorService/ListColor',
                request_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.ListColorRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.ListColorResponse.FromString,
                _registered_method=True)
        self.UpdateColor = channel.unary_unary(
                '/moego.service.business_customer.v1.BusinessPetColorService/UpdateColor',
                request_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.UpdateColorRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.UpdateColorResponse.FromString,
                _registered_method=True)
        self.BindingColor = channel.unary_unary(
                '/moego.service.business_customer.v1.BusinessPetColorService/BindingColor',
                request_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.BindingColorRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.BindingColorResponse.FromString,
                _registered_method=True)
        self.ListColorBinding = channel.unary_unary(
                '/moego.service.business_customer.v1.BusinessPetColorService/ListColorBinding',
                request_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.ListColorBindingRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.ListColorBindingResponse.FromString,
                _registered_method=True)


class BusinessPetColorServiceServicer(object):
    """business pet color service
    """

    def ListColor(self, request, context):
        """list color
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateColor(self, request, context):
        """update color
        if not exist, will create a new color
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BindingColor(self, request, context):
        """binding pet and color
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListColorBinding(self, request, context):
        """list binding
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_BusinessPetColorServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ListColor': grpc.unary_unary_rpc_method_handler(
                    servicer.ListColor,
                    request_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.ListColorRequest.FromString,
                    response_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.ListColorResponse.SerializeToString,
            ),
            'UpdateColor': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateColor,
                    request_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.UpdateColorRequest.FromString,
                    response_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.UpdateColorResponse.SerializeToString,
            ),
            'BindingColor': grpc.unary_unary_rpc_method_handler(
                    servicer.BindingColor,
                    request_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.BindingColorRequest.FromString,
                    response_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.BindingColorResponse.SerializeToString,
            ),
            'ListColorBinding': grpc.unary_unary_rpc_method_handler(
                    servicer.ListColorBinding,
                    request_deserializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.ListColorBindingRequest.FromString,
                    response_serializer=moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.ListColorBindingResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.business_customer.v1.BusinessPetColorService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.business_customer.v1.BusinessPetColorService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class BusinessPetColorService(object):
    """business pet color service
    """

    @staticmethod
    def ListColor(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.business_customer.v1.BusinessPetColorService/ListColor',
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.ListColorRequest.SerializeToString,
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.ListColorResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateColor(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.business_customer.v1.BusinessPetColorService/UpdateColor',
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.UpdateColorRequest.SerializeToString,
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.UpdateColorResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BindingColor(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.business_customer.v1.BusinessPetColorService/BindingColor',
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.BindingColorRequest.SerializeToString,
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.BindingColorResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListColorBinding(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.business_customer.v1.BusinessPetColorService/ListColorBinding',
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.ListColorBindingRequest.SerializeToString,
            moego_dot_service_dot_business__customer_dot_v1_dot_business__pet__color__service__pb2.ListColorBindingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
