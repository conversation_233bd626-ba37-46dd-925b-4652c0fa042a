# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/branded_app/v1/branded_app_config_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/branded_app/v1/branded_app_config_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.account.v1 import account_enums_pb2 as moego_dot_models_dot_account_dot_v1_dot_account__enums__pb2
from moego.models.branded_app.v1 import branded_app_config_models_pb2 as moego_dot_models_dot_branded__app_dot_v1_dot_branded__app__config__models__pb2
from moego.models.branded_app.v1 import branded_pack_config_models_pb2 as moego_dot_models_dot_branded__app_dot_v1_dot_branded__pack__config__models__pb2
from moego.models.branded_app.v1 import branded_theme_config_models_pb2 as moego_dot_models_dot_branded__app_dot_v1_dot_branded__theme__config__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n=moego/service/branded_app/v1/branded_app_config_service.proto\x12\x1cmoego.service.branded_app.v1\x1a+moego/models/account/v1/account_enums.proto\x1a;moego/models/branded_app/v1/branded_app_config_models.proto\x1a<moego/models/branded_app/v1/branded_pack_config_models.proto\x1a=moego/models/branded_app/v1/branded_theme_config_models.proto\x1a\x17validate/validate.proto\"\xe6\x02\n\x1aGetBrandedAppConfigRequest\x12\x30\n\x0e\x62randed_app_id\x18\x01 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x00R\x0c\x62randedAppId\x12o\n\x0e\x62randed_entity\x18\x02 \x01(\x0b\x32\x46.moego.service.branded_app.v1.GetBrandedAppConfigRequest.BrandedEntityH\x00R\rbrandedEntity\x1a\x95\x01\n\rBrandedEntity\x12\\\n\x0c\x62randed_type\x18\x01 \x01(\x0e\x32-.moego.models.account.v1.AccountNamespaceTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x0b\x62randedType\x12&\n\nbranded_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00R\tbrandedIdB\r\n\x06\x66ilter\x12\x03\xf8\x42\x01\"i\n\x1bGetBrandedAppConfigResponse\x12J\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\x32.moego.models.branded_app.v1.BrandedAppConfigModelR\x06\x63onfig\"\x1d\n\x1bListBrandedAppConfigRequest\"l\n\x1cListBrandedAppConfigResponse\x12L\n\x07\x63onfigs\x18\x01 \x03(\x0b\x32\x32.moego.models.branded_app.v1.BrandedAppConfigModelR\x07\x63onfigs\"N\n\x1cGetDefaultThemeConfigRequest\x12.\n\x0e\x62randed_app_id\x18\x01 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01R\x0c\x62randedAppId\"z\n\x1dGetDefaultThemeConfigResponse\x12Y\n\rdefault_theme\x18\x01 \x01(\x0b\x32\x34.moego.models.branded_app.v1.BrandedThemeConfigModelR\x0c\x64\x65\x66\x61ultTheme\"P\n\x1eGetBrandedAppPackConfigRequest\x12.\n\x0e\x62randed_app_id\x18\x01 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01R\x0c\x62randedAppId\"w\n\x1fGetBrandedAppPackConfigResponse\x12T\n\x0bpack_config\x18\x01 \x01(\x0b\x32\x33.moego.models.branded_app.v1.BrandedPackConfigModelR\npackConfig\"!\n\x1fListBrandedAppPackConfigRequest\"z\n ListBrandedAppPackConfigResponse\x12V\n\x0cpack_configs\x18\x01 \x03(\x0b\x32\x33.moego.models.branded_app.v1.BrandedPackConfigModelR\x0bpackConfigs2\x8f\x07\n\x17\x42randedAppConfigService\x12\x8a\x01\n\x13GetBrandedAppConfig\x12\x38.moego.service.branded_app.v1.GetBrandedAppConfigRequest\x1a\x39.moego.service.branded_app.v1.GetBrandedAppConfigResponse\x12\x8e\x01\n\x17MustGetBrandedAppConfig\x12\x38.moego.service.branded_app.v1.GetBrandedAppConfigRequest\x1a\x39.moego.service.branded_app.v1.GetBrandedAppConfigResponse\x12\x8d\x01\n\x14ListBrandedAppConfig\x12\x39.moego.service.branded_app.v1.ListBrandedAppConfigRequest\x1a:.moego.service.branded_app.v1.ListBrandedAppConfigResponse\x12\x90\x01\n\x15GetDefaultThemeConfig\x12:.moego.service.branded_app.v1.GetDefaultThemeConfigRequest\x1a;.moego.service.branded_app.v1.GetDefaultThemeConfigResponse\x12\x96\x01\n\x17GetBrandedAppPackConfig\x12<.moego.service.branded_app.v1.GetBrandedAppPackConfigRequest\x1a=.moego.service.branded_app.v1.GetBrandedAppPackConfigResponse\x12\x99\x01\n\x18ListBrandedAppPackConfig\x12=.moego.service.branded_app.v1.ListBrandedAppPackConfigRequest\x1a>.moego.service.branded_app.v1.ListBrandedAppPackConfigResponseB\x8b\x01\n$com.moego.idl.service.branded_app.v1P\x01Zagithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/branded_app/v1;brandedappsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.branded_app.v1.branded_app_config_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.service.branded_app.v1P\001Zagithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/branded_app/v1;brandedappsvcpb'
  _globals['_GETBRANDEDAPPCONFIGREQUEST_BRANDEDENTITY'].fields_by_name['branded_type']._loaded_options = None
  _globals['_GETBRANDEDAPPCONFIGREQUEST_BRANDEDENTITY'].fields_by_name['branded_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETBRANDEDAPPCONFIGREQUEST_BRANDEDENTITY'].fields_by_name['branded_id']._loaded_options = None
  _globals['_GETBRANDEDAPPCONFIGREQUEST_BRANDEDENTITY'].fields_by_name['branded_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_GETBRANDEDAPPCONFIGREQUEST'].oneofs_by_name['filter']._loaded_options = None
  _globals['_GETBRANDEDAPPCONFIGREQUEST'].oneofs_by_name['filter']._serialized_options = b'\370B\001'
  _globals['_GETBRANDEDAPPCONFIGREQUEST'].fields_by_name['branded_app_id']._loaded_options = None
  _globals['_GETBRANDEDAPPCONFIGREQUEST'].fields_by_name['branded_app_id']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_GETDEFAULTTHEMECONFIGREQUEST'].fields_by_name['branded_app_id']._loaded_options = None
  _globals['_GETDEFAULTTHEMECONFIGREQUEST'].fields_by_name['branded_app_id']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_GETBRANDEDAPPPACKCONFIGREQUEST'].fields_by_name['branded_app_id']._loaded_options = None
  _globals['_GETBRANDEDAPPPACKCONFIGREQUEST'].fields_by_name['branded_app_id']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_GETBRANDEDAPPCONFIGREQUEST']._serialized_start=352
  _globals['_GETBRANDEDAPPCONFIGREQUEST']._serialized_end=710
  _globals['_GETBRANDEDAPPCONFIGREQUEST_BRANDEDENTITY']._serialized_start=546
  _globals['_GETBRANDEDAPPCONFIGREQUEST_BRANDEDENTITY']._serialized_end=695
  _globals['_GETBRANDEDAPPCONFIGRESPONSE']._serialized_start=712
  _globals['_GETBRANDEDAPPCONFIGRESPONSE']._serialized_end=817
  _globals['_LISTBRANDEDAPPCONFIGREQUEST']._serialized_start=819
  _globals['_LISTBRANDEDAPPCONFIGREQUEST']._serialized_end=848
  _globals['_LISTBRANDEDAPPCONFIGRESPONSE']._serialized_start=850
  _globals['_LISTBRANDEDAPPCONFIGRESPONSE']._serialized_end=958
  _globals['_GETDEFAULTTHEMECONFIGREQUEST']._serialized_start=960
  _globals['_GETDEFAULTTHEMECONFIGREQUEST']._serialized_end=1038
  _globals['_GETDEFAULTTHEMECONFIGRESPONSE']._serialized_start=1040
  _globals['_GETDEFAULTTHEMECONFIGRESPONSE']._serialized_end=1162
  _globals['_GETBRANDEDAPPPACKCONFIGREQUEST']._serialized_start=1164
  _globals['_GETBRANDEDAPPPACKCONFIGREQUEST']._serialized_end=1244
  _globals['_GETBRANDEDAPPPACKCONFIGRESPONSE']._serialized_start=1246
  _globals['_GETBRANDEDAPPPACKCONFIGRESPONSE']._serialized_end=1365
  _globals['_LISTBRANDEDAPPPACKCONFIGREQUEST']._serialized_start=1367
  _globals['_LISTBRANDEDAPPPACKCONFIGREQUEST']._serialized_end=1400
  _globals['_LISTBRANDEDAPPPACKCONFIGRESPONSE']._serialized_start=1402
  _globals['_LISTBRANDEDAPPPACKCONFIGRESPONSE']._serialized_end=1524
  _globals['_BRANDEDAPPCONFIGSERVICE']._serialized_start=1527
  _globals['_BRANDEDAPPCONFIGSERVICE']._serialized_end=2438
# @@protoc_insertion_point(module_scope)
