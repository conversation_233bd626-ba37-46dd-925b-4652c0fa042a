# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/notification/v1/notification_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/notification/v1/notification_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from moego.models.notification.v1 import notification_defs_pb2 as moego_dot_models_dot_notification_dot_v1_dot_notification__defs__pb2
from moego.models.notification.v1 import notification_enums_pb2 as moego_dot_models_dot_notification_dot_v1_dot_notification__enums__pb2
from moego.models.notification.v1 import notification_extra_defs_pb2 as moego_dot_models_dot_notification_dot_v1_dot_notification__extra__defs__pb2
from moego.models.notification.v1 import notification_models_pb2 as moego_dot_models_dot_notification_dot_v1_dot_notification__models__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8moego/service/notification/v1/notification_service.proto\x12\x1dmoego.service.notification.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x34moego/models/notification/v1/notification_defs.proto\x1a\x35moego/models/notification/v1/notification_enums.proto\x1a:moego/models/notification/v1/notification_extra_defs.proto\x1a\x36moego/models/notification/v1/notification_models.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\xe5\x04\n\x1e\x43reateInboxNotificationRequest\x12R\n\x06source\x18\x02 \x01(\x0e\x32\x30.moego.models.notification.v1.NotificationSourceB\x08\xfa\x42\x05\x82\x01\x02\x10\x01R\x06source\x12\x1b\n\tsender_id\x18\x03 \x01(\x03R\x08senderId\x12(\n\x0breceiver_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nreceiverId\x12%\n\x05title\x18\x05 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\xff\x01H\x00R\x05title\x88\x01\x01\x12(\n\x07\x63ontent\x18\x06 \x01(\tB\t\xfa\x42\x06r\x04\x18\x80\x80@H\x01R\x07\x63ontent\x88\x01\x01\x12R\n\x06method\x18\x07 \x01(\x0e\x32\x30.moego.models.notification.v1.NotificationMethodB\x08\xfa\x42\x05\x82\x01\x02\x10\x01R\x06method\x12L\n\x04type\x18\x08 \x01(\x0e\x32..moego.models.notification.v1.NotificationTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01R\x04type\x12H\n\x05\x65xtra\x18\t \x01(\x0b\x32\x32.moego.models.notification.v1.NotificationExtraDefR\x05\x65xtra\x12H\n\x08\x61pp_push\x18\n \x01(\x0b\x32(.moego.models.notification.v1.AppPushDefH\x02R\x07\x61ppPush\x88\x01\x01\x42\x08\n\x06_titleB\n\n\x08_contentB\x0b\n\t_app_push\"S\n\x1f\x43reateInboxNotificationResponse\x12\x30\n\x0fnotification_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0enotificationId\"P\n\x1cReadInboxNotificationRequest\x12\x30\n\x0fnotification_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0enotificationId\"X\n\x1dReadInboxNotificationResponse\x12\x37\n\tread_time\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x08readTime\"R\n\x1e\x44\x65leteInboxNotificationRequest\x12\x30\n\x0fnotification_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0enotificationId\"`\n\x1f\x44\x65leteInboxNotificationResponse\x12=\n\x0c\x64\x65leted_time\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0b\x64\x65letedTime\"\xfd\x03\n\x1aGetNotificationListRequest\x12W\n\x05types\x18\x01 \x03(\x0e\x32..moego.models.notification.v1.NotificationTypeB\x11\xfa\x42\x0e\x92\x01\x0b\x08\x00\"\x05\x82\x01\x02\x10\x01(\x01R\x05types\x12\x46\n\npagination\x18\x02 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x00R\npagination\x88\x01\x01\x12G\n\x05sorts\x18\x03 \x03(\x0b\x32\x31.moego.models.notification.v1.NotificationSortDefR\x05sorts\x12]\n\x07sources\x18\x04 \x03(\x0e\x32\x30.moego.models.notification.v1.NotificationSourceB\x11\xfa\x42\x0e\x92\x01\x0b\x08\x00\"\x05\x82\x01\x02\x10\x01(\x01R\x07sources\x12]\n\x07methods\x18\x05 \x03(\x0e\x32\x30.moego.models.notification.v1.NotificationMethodB\x11\xfa\x42\x0e\x92\x01\x0b\x08\x00\"\x05\x82\x01\x02\x10\x01(\x01R\x07methods\x12(\n\x0breceiver_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nreceiverIdB\r\n\x0b_pagination\"\xb8\x01\n\x1bGetNotificationListResponse\x12U\n\rnotifications\x18\x01 \x03(\x0b\x32/.moego.models.notification.v1.NotificationModelR\rnotifications\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination2\xef\x04\n\x13NotificationService\x12\x98\x01\n\x17\x43reateInboxNotification\x12=.moego.service.notification.v1.CreateInboxNotificationRequest\x1a>.moego.service.notification.v1.CreateInboxNotificationResponse\x12\x92\x01\n\x15ReadInboxNotification\x12;.moego.service.notification.v1.ReadInboxNotificationRequest\x1a<.moego.service.notification.v1.ReadInboxNotificationResponse\x12\x98\x01\n\x17\x44\x65leteInboxNotification\x12=.moego.service.notification.v1.DeleteInboxNotificationRequest\x1a>.moego.service.notification.v1.DeleteInboxNotificationResponse\x12\x8c\x01\n\x13GetNotificationList\x12\x39.moego.service.notification.v1.GetNotificationListRequest\x1a:.moego.service.notification.v1.GetNotificationListResponseB\x8f\x01\n%com.moego.idl.service.notification.v1P\x01Zdgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/notification/v1;notificationsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.notification.v1.notification_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.moego.idl.service.notification.v1P\001Zdgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/notification/v1;notificationsvcpb'
  _globals['_CREATEINBOXNOTIFICATIONREQUEST'].fields_by_name['source']._loaded_options = None
  _globals['_CREATEINBOXNOTIFICATIONREQUEST'].fields_by_name['source']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_CREATEINBOXNOTIFICATIONREQUEST'].fields_by_name['receiver_id']._loaded_options = None
  _globals['_CREATEINBOXNOTIFICATIONREQUEST'].fields_by_name['receiver_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEINBOXNOTIFICATIONREQUEST'].fields_by_name['title']._loaded_options = None
  _globals['_CREATEINBOXNOTIFICATIONREQUEST'].fields_by_name['title']._serialized_options = b'\372B\007r\005\020\001\030\377\001'
  _globals['_CREATEINBOXNOTIFICATIONREQUEST'].fields_by_name['content']._loaded_options = None
  _globals['_CREATEINBOXNOTIFICATIONREQUEST'].fields_by_name['content']._serialized_options = b'\372B\006r\004\030\200\200@'
  _globals['_CREATEINBOXNOTIFICATIONREQUEST'].fields_by_name['method']._loaded_options = None
  _globals['_CREATEINBOXNOTIFICATIONREQUEST'].fields_by_name['method']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_CREATEINBOXNOTIFICATIONREQUEST'].fields_by_name['type']._loaded_options = None
  _globals['_CREATEINBOXNOTIFICATIONREQUEST'].fields_by_name['type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_CREATEINBOXNOTIFICATIONRESPONSE'].fields_by_name['notification_id']._loaded_options = None
  _globals['_CREATEINBOXNOTIFICATIONRESPONSE'].fields_by_name['notification_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_READINBOXNOTIFICATIONREQUEST'].fields_by_name['notification_id']._loaded_options = None
  _globals['_READINBOXNOTIFICATIONREQUEST'].fields_by_name['notification_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEINBOXNOTIFICATIONREQUEST'].fields_by_name['notification_id']._loaded_options = None
  _globals['_DELETEINBOXNOTIFICATIONREQUEST'].fields_by_name['notification_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETNOTIFICATIONLISTREQUEST'].fields_by_name['types']._loaded_options = None
  _globals['_GETNOTIFICATIONLISTREQUEST'].fields_by_name['types']._serialized_options = b'\372B\016\222\001\013\010\000\"\005\202\001\002\020\001(\001'
  _globals['_GETNOTIFICATIONLISTREQUEST'].fields_by_name['sources']._loaded_options = None
  _globals['_GETNOTIFICATIONLISTREQUEST'].fields_by_name['sources']._serialized_options = b'\372B\016\222\001\013\010\000\"\005\202\001\002\020\001(\001'
  _globals['_GETNOTIFICATIONLISTREQUEST'].fields_by_name['methods']._loaded_options = None
  _globals['_GETNOTIFICATIONLISTREQUEST'].fields_by_name['methods']._serialized_options = b'\372B\016\222\001\013\010\000\"\005\202\001\002\020\001(\001'
  _globals['_GETNOTIFICATIONLISTREQUEST'].fields_by_name['receiver_id']._loaded_options = None
  _globals['_GETNOTIFICATIONLISTREQUEST'].fields_by_name['receiver_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEINBOXNOTIFICATIONREQUEST']._serialized_start=417
  _globals['_CREATEINBOXNOTIFICATIONREQUEST']._serialized_end=1030
  _globals['_CREATEINBOXNOTIFICATIONRESPONSE']._serialized_start=1032
  _globals['_CREATEINBOXNOTIFICATIONRESPONSE']._serialized_end=1115
  _globals['_READINBOXNOTIFICATIONREQUEST']._serialized_start=1117
  _globals['_READINBOXNOTIFICATIONREQUEST']._serialized_end=1197
  _globals['_READINBOXNOTIFICATIONRESPONSE']._serialized_start=1199
  _globals['_READINBOXNOTIFICATIONRESPONSE']._serialized_end=1287
  _globals['_DELETEINBOXNOTIFICATIONREQUEST']._serialized_start=1289
  _globals['_DELETEINBOXNOTIFICATIONREQUEST']._serialized_end=1371
  _globals['_DELETEINBOXNOTIFICATIONRESPONSE']._serialized_start=1373
  _globals['_DELETEINBOXNOTIFICATIONRESPONSE']._serialized_end=1469
  _globals['_GETNOTIFICATIONLISTREQUEST']._serialized_start=1472
  _globals['_GETNOTIFICATIONLISTREQUEST']._serialized_end=1981
  _globals['_GETNOTIFICATIONLISTRESPONSE']._serialized_start=1984
  _globals['_GETNOTIFICATIONLISTRESPONSE']._serialized_end=2168
  _globals['_NOTIFICATIONSERVICE']._serialized_start=2171
  _globals['_NOTIFICATIONSERVICE']._serialized_end=2794
# @@protoc_insertion_point(module_scope)
