# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.appointment.v1 import service_charge_detail_service_pb2 as moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2


class ServiceChargeDetailServiceStub(object):
    """the service_charge_detail service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.AddServiceChargeDetail = channel.unary_unary(
                '/moego.service.appointment.v1.ServiceChargeDetailService/AddServiceChargeDetail',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.AddServiceChargeDetailRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.AddServiceChargeDetailResponse.FromString,
                _registered_method=True)
        self.DeleteServiceChargeDetail = channel.unary_unary(
                '/moego.service.appointment.v1.ServiceChargeDetailService/DeleteServiceChargeDetail',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.DeleteServiceChargeDetailRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.DeleteServiceChargeDetailResponse.FromString,
                _registered_method=True)
        self.UpdateUpcomingServiceChargeDetails = channel.unary_unary(
                '/moego.service.appointment.v1.ServiceChargeDetailService/UpdateUpcomingServiceChargeDetails',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.UpdateUpcomingServiceChargeDetailsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.UpdateUpcomingServiceChargeDetailsResponse.FromString,
                _registered_method=True)
        self.ListServiceChargeDetails = channel.unary_unary(
                '/moego.service.appointment.v1.ServiceChargeDetailService/ListServiceChargeDetails',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.ListServiceChargeDetailsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.ListServiceChargeDetailsResponse.FromString,
                _registered_method=True)


class ServiceChargeDetailServiceServicer(object):
    """the service_charge_detail service
    """

    def AddServiceChargeDetail(self, request, context):
        """add service_charge_detail
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteServiceChargeDetail(self, request, context):
        """delete service_charge_detail
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateUpcomingServiceChargeDetails(self, request, context):
        """Update upcoming appointment service charge details
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListServiceChargeDetails(self, request, context):
        """List service charge details
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ServiceChargeDetailServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'AddServiceChargeDetail': grpc.unary_unary_rpc_method_handler(
                    servicer.AddServiceChargeDetail,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.AddServiceChargeDetailRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.AddServiceChargeDetailResponse.SerializeToString,
            ),
            'DeleteServiceChargeDetail': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteServiceChargeDetail,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.DeleteServiceChargeDetailRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.DeleteServiceChargeDetailResponse.SerializeToString,
            ),
            'UpdateUpcomingServiceChargeDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateUpcomingServiceChargeDetails,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.UpdateUpcomingServiceChargeDetailsRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.UpdateUpcomingServiceChargeDetailsResponse.SerializeToString,
            ),
            'ListServiceChargeDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.ListServiceChargeDetails,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.ListServiceChargeDetailsRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.ListServiceChargeDetailsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.appointment.v1.ServiceChargeDetailService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.appointment.v1.ServiceChargeDetailService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ServiceChargeDetailService(object):
    """the service_charge_detail service
    """

    @staticmethod
    def AddServiceChargeDetail(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.ServiceChargeDetailService/AddServiceChargeDetail',
            moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.AddServiceChargeDetailRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.AddServiceChargeDetailResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteServiceChargeDetail(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.ServiceChargeDetailService/DeleteServiceChargeDetail',
            moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.DeleteServiceChargeDetailRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.DeleteServiceChargeDetailResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateUpcomingServiceChargeDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.ServiceChargeDetailService/UpdateUpcomingServiceChargeDetails',
            moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.UpdateUpcomingServiceChargeDetailsRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.UpdateUpcomingServiceChargeDetailsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListServiceChargeDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.ServiceChargeDetailService/ListServiceChargeDetails',
            moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.ListServiceChargeDetailsRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_service__charge__detail__service__pb2.ListServiceChargeDetailsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
