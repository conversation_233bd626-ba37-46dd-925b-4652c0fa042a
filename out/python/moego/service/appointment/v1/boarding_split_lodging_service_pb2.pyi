from moego.models.appointment.v1 import boarding_split_lodging_models_pb2 as _boarding_split_lodging_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ListBoardingSplitLodgingsRequest(_message.Message):
    __slots__ = ("appointment_ids",)
    APPOINTMENT_IDS_FIELD_NUMBER: _ClassVar[int]
    appointment_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, appointment_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class ListBoardingSplitLodgingsResponse(_message.Message):
    __slots__ = ("boarding_split_lodgings",)
    BOARDING_SPLIT_LODGINGS_FIELD_NUMBER: _ClassVar[int]
    boarding_split_lodgings: _containers.RepeatedCompositeFieldContainer[_boarding_split_lodging_models_pb2.BoardingSplitLodgingModel]
    def __init__(self, boarding_split_lodgings: _Optional[_Iterable[_Union[_boarding_split_lodging_models_pb2.BoardingSplitLodgingModel, _Mapping]]] = ...) -> None: ...
