# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/order/v1/order_merge_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/order/v1/order_merge_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.business_customer.v1 import business_customer_merge_defs_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__customer__merge__defs__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n0moego/service/order/v1/order_merge_service.proto\x12\x16moego.service.order.v1\x1a\x44moego/models/business_customer/v1/business_customer_merge_defs.proto\x1a\x17validate/validate.proto\"\xb3\x01\n\x1dMergeCustomerOrderDataRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12j\n\x0emerge_relation\x18\x02 \x01(\x0b\x32\x43.moego.models.business_customer.v1.BusinessCustomerMergeRelationDefR\rmergeRelation\":\n\x1eMergeCustomerOrderDataResponse\x12\x18\n\x07success\x18\x01 \x01(\x08R\x07success2\x9d\x01\n\x11OrderMergeService\x12\x87\x01\n\x16MergeCustomerOrderData\x12\x35.moego.service.order.v1.MergeCustomerOrderDataRequest\x1a\x36.moego.service.order.v1.MergeCustomerOrderDataResponseBz\n\x1e\x63om.moego.idl.service.order.v1P\x01ZVgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1;ordersvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.order.v1.order_merge_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\036com.moego.idl.service.order.v1P\001ZVgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1;ordersvcpb'
  _globals['_MERGECUSTOMERORDERDATAREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_MERGECUSTOMERORDERDATAREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_MERGECUSTOMERORDERDATAREQUEST']._serialized_start=172
  _globals['_MERGECUSTOMERORDERDATAREQUEST']._serialized_end=351
  _globals['_MERGECUSTOMERORDERDATARESPONSE']._serialized_start=353
  _globals['_MERGECUSTOMERORDERDATARESPONSE']._serialized_end=411
  _globals['_ORDERMERGESERVICE']._serialized_start=414
  _globals['_ORDERMERGESERVICE']._serialized_end=571
# @@protoc_insertion_point(module_scope)
