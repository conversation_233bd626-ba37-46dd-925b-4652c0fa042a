# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.order.v1 import customer_order_service_pb2 as moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2


class CustomerOrderServiceStub(object):
    """customer's order service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetCustomerRecentOrders = channel.unary_unary(
                '/moego.service.order.v1.CustomerOrderService/GetCustomerRecentOrders',
                request_serializer=moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerRecentOrdersRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerRecentOrdersResponse.FromString,
                _registered_method=True)
        self.GetCustomerRecentItems = channel.unary_unary(
                '/moego.service.order.v1.CustomerOrderService/GetCustomerRecentItems',
                request_serializer=moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerRecentItemsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerRecentItemsResponse.FromString,
                _registered_method=True)
        self.GetCustomerPaymentSummary = channel.unary_unary(
                '/moego.service.order.v1.CustomerOrderService/GetCustomerPaymentSummary',
                request_serializer=moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerPaymentSummaryRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerPaymentSummaryResponse.FromString,
                _registered_method=True)


class CustomerOrderServiceServicer(object):
    """customer's order service
    """

    def GetCustomerRecentOrders(self, request, context):
        """get customer recent orders
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCustomerRecentItems(self, request, context):
        """get customer recent items
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCustomerPaymentSummary(self, request, context):
        """get customer payment summary
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CustomerOrderServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetCustomerRecentOrders': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCustomerRecentOrders,
                    request_deserializer=moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerRecentOrdersRequest.FromString,
                    response_serializer=moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerRecentOrdersResponse.SerializeToString,
            ),
            'GetCustomerRecentItems': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCustomerRecentItems,
                    request_deserializer=moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerRecentItemsRequest.FromString,
                    response_serializer=moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerRecentItemsResponse.SerializeToString,
            ),
            'GetCustomerPaymentSummary': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCustomerPaymentSummary,
                    request_deserializer=moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerPaymentSummaryRequest.FromString,
                    response_serializer=moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerPaymentSummaryResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.order.v1.CustomerOrderService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.order.v1.CustomerOrderService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class CustomerOrderService(object):
    """customer's order service
    """

    @staticmethod
    def GetCustomerRecentOrders(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.order.v1.CustomerOrderService/GetCustomerRecentOrders',
            moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerRecentOrdersRequest.SerializeToString,
            moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerRecentOrdersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCustomerRecentItems(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.order.v1.CustomerOrderService/GetCustomerRecentItems',
            moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerRecentItemsRequest.SerializeToString,
            moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerRecentItemsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCustomerPaymentSummary(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.order.v1.CustomerOrderService/GetCustomerPaymentSummary',
            moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerPaymentSummaryRequest.SerializeToString,
            moego_dot_service_dot_order_dot_v1_dot_customer__order__service__pb2.GetCustomerPaymentSummaryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
