# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.order.v2 import split_tips_service_pb2 as moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2


class SplitTipsServiceStub(object):
    """split tips internal api
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetTipsSplitDetails = channel.unary_unary(
                '/moego.service.order.v2.SplitTipsService/GetTipsSplitDetails',
                request_serializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.GetTipsSplitRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.GetTipsSplitResponse.FromString,
                _registered_method=True)
        self.PreviewEditTipsSplit = channel.unary_unary(
                '/moego.service.order.v2.SplitTipsService/PreviewEditTipsSplit',
                request_serializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.EditStaffAndTipsSplitRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.PreviewEditTipsSplitResponse.FromString,
                _registered_method=True)
        self.EditTipsSplit = channel.unary_unary(
                '/moego.service.order.v2.SplitTipsService/EditTipsSplit',
                request_serializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.EditStaffAndTipsSplitRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.EditTipsSplitResponse.FromString,
                _registered_method=True)
        self.GetTipsSplitChangedStatus = channel.unary_unary(
                '/moego.service.order.v2.SplitTipsService/GetTipsSplitChangedStatus',
                request_serializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.GetTipsSplitChangedStatusRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.GetTipsSplitChangedStatusResponse.FromString,
                _registered_method=True)
        self.ClearTipsSplitChangedStatus = channel.unary_unary(
                '/moego.service.order.v2.SplitTipsService/ClearTipsSplitChangedStatus',
                request_serializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.ClearTipsSplitChangedStatusRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.ClearTipsSplitChangedStatusResponse.FromString,
                _registered_method=True)
        self.ListTipsSplitDetailsBySource = channel.unary_unary(
                '/moego.service.order.v2.SplitTipsService/ListTipsSplitDetailsBySource',
                request_serializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.ListTipsSplitDetailsBySourceRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.ListTipsSplitDetailsBySourceResponse.FromString,
                _registered_method=True)


class SplitTipsServiceServicer(object):
    """split tips internal api
    """

    def GetTipsSplitDetails(self, request, context):
        """get tips details for source type
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PreviewEditTipsSplit(self, request, context):
        """preview edit staff and split tips
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def EditTipsSplit(self, request, context):
        """update edit staff and split tips
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTipsSplitChangedStatus(self, request, context):
        """get tips split changed status
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClearTipsSplitChangedStatus(self, request, context):
        """clear tips split changed status
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListTipsSplitDetailsBySource(self, request, context):
        """ListTipsSplitDetailsBySource 通过 SourceID+SourceType 批量查询 Tips 分配详情.
        主要提供给 老 Report 进行实时查询.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SplitTipsServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetTipsSplitDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTipsSplitDetails,
                    request_deserializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.GetTipsSplitRequest.FromString,
                    response_serializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.GetTipsSplitResponse.SerializeToString,
            ),
            'PreviewEditTipsSplit': grpc.unary_unary_rpc_method_handler(
                    servicer.PreviewEditTipsSplit,
                    request_deserializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.EditStaffAndTipsSplitRequest.FromString,
                    response_serializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.PreviewEditTipsSplitResponse.SerializeToString,
            ),
            'EditTipsSplit': grpc.unary_unary_rpc_method_handler(
                    servicer.EditTipsSplit,
                    request_deserializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.EditStaffAndTipsSplitRequest.FromString,
                    response_serializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.EditTipsSplitResponse.SerializeToString,
            ),
            'GetTipsSplitChangedStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTipsSplitChangedStatus,
                    request_deserializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.GetTipsSplitChangedStatusRequest.FromString,
                    response_serializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.GetTipsSplitChangedStatusResponse.SerializeToString,
            ),
            'ClearTipsSplitChangedStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.ClearTipsSplitChangedStatus,
                    request_deserializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.ClearTipsSplitChangedStatusRequest.FromString,
                    response_serializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.ClearTipsSplitChangedStatusResponse.SerializeToString,
            ),
            'ListTipsSplitDetailsBySource': grpc.unary_unary_rpc_method_handler(
                    servicer.ListTipsSplitDetailsBySource,
                    request_deserializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.ListTipsSplitDetailsBySourceRequest.FromString,
                    response_serializer=moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.ListTipsSplitDetailsBySourceResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.order.v2.SplitTipsService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.order.v2.SplitTipsService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class SplitTipsService(object):
    """split tips internal api
    """

    @staticmethod
    def GetTipsSplitDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.order.v2.SplitTipsService/GetTipsSplitDetails',
            moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.GetTipsSplitRequest.SerializeToString,
            moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.GetTipsSplitResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PreviewEditTipsSplit(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.order.v2.SplitTipsService/PreviewEditTipsSplit',
            moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.EditStaffAndTipsSplitRequest.SerializeToString,
            moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.PreviewEditTipsSplitResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def EditTipsSplit(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.order.v2.SplitTipsService/EditTipsSplit',
            moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.EditStaffAndTipsSplitRequest.SerializeToString,
            moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.EditTipsSplitResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTipsSplitChangedStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.order.v2.SplitTipsService/GetTipsSplitChangedStatus',
            moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.GetTipsSplitChangedStatusRequest.SerializeToString,
            moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.GetTipsSplitChangedStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ClearTipsSplitChangedStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.order.v2.SplitTipsService/ClearTipsSplitChangedStatus',
            moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.ClearTipsSplitChangedStatusRequest.SerializeToString,
            moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.ClearTipsSplitChangedStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListTipsSplitDetailsBySource(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.order.v2.SplitTipsService/ListTipsSplitDetailsBySource',
            moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.ListTipsSplitDetailsBySourceRequest.SerializeToString,
            moego_dot_service_dot_order_dot_v2_dot_split__tips__service__pb2.ListTipsSplitDetailsBySourceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
