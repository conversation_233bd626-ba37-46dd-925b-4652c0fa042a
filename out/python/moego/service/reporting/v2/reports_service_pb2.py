# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/reporting/v2/reports_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/reporting/v2/reports_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import calendar_period_pb2 as google_dot_type_dot_calendar__period__pb2
from google.type import interval_pb2 as google_dot_type_dot_interval__pb2
from moego.models.reporting.v2 import common_model_pb2 as moego_dot_models_dot_reporting_dot_v2_dot_common__model__pb2
from moego.models.reporting.v2 import diagram_model_pb2 as moego_dot_models_dot_reporting_dot_v2_dot_diagram__model__pb2
from moego.models.reporting.v2 import report_def_pb2 as moego_dot_models_dot_reporting_dot_v2_dot_report__def__pb2
from moego.models.reporting.v2 import report_meta_def_pb2 as moego_dot_models_dot_reporting_dot_v2_dot_report__meta__def__pb2
from moego.models.reporting.v2 import report_models_pb2 as moego_dot_models_dot_reporting_dot_v2_dot_report__models__pb2
from moego.utils.v2 import condition_messages_pb2 as moego_dot_utils_dot_v2_dot_condition__messages__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n0moego/service/reporting/v2/reports_service.proto\x12\x1amoego.service.reporting.v2\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a!google/type/calendar_period.proto\x1a\x1agoogle/type/interval.proto\x1a,moego/models/reporting/v2/common_model.proto\x1a-moego/models/reporting/v2/diagram_model.proto\x1a*moego/models/reporting/v2/report_def.proto\x1a/moego/models/reporting/v2/report_meta_def.proto\x1a-moego/models/reporting/v2/report_models.proto\x1a\'moego/utils/v2/condition_messages.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\x81\x02\n\x16QueryReportPagesParams\x12P\n\x04tabs\x18\x01 \x03(\x0e\x32).moego.models.reporting.v2.ReportPage.TabB\x11\xfa\x42\x0e\x92\x01\x0b\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x04tabs\x12\x43\n\ntoken_info\x18\x02 \x01(\x0b\x32$.moego.models.reporting.v2.TokenInfoR\ttokenInfo\x12P\n\x0ereporting_type\x18\x03 \x01(\x0e\x32).moego.models.reporting.v2.ReportingSceneR\rreportingType\"\x9b\x01\n\x16QueryReportPagesResult\x12;\n\x05pages\x18\x01 \x03(\x0b\x32%.moego.models.reporting.v2.ReportPageR\x05pages\x12\x44\n\x10last_synced_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0elastSyncedTime\"\xfc\x02\n\x18MarkReportFavoriteParams\x12(\n\ndiagram_id\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x64R\tdiagramId\x12_\n\x06\x61\x63tion\x18\x02 \x01(\x0e\x32;.moego.service.reporting.v2.MarkReportFavoriteParams.ActionB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x06\x61\x63tion\x12\x43\n\ntoken_info\x18\x03 \x01(\x0b\x32$.moego.models.reporting.v2.TokenInfoR\ttokenInfo\x12P\n\x0ereporting_type\x18\x04 \x01(\x0e\x32).moego.models.reporting.v2.ReportingSceneR\rreportingType\">\n\x06\x41\x63tion\x12\x1f\n\x1b\x46\x41VORITE_ACTION_UNSPECIFIED\x10\x00\x12\x07\n\x03\x41\x44\x44\x10\x01\x12\n\n\x06REMOVE\x10\x02\"2\n\x18MarkReportFavoriteResult\x12\x16\n\x06result\x18\x01 \x01(\x08R\x06result\"\xc1\x02\n\x1fSaveReportCustomizeConfigParams\x12(\n\ndiagram_id\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x64R\tdiagramId\x12]\n\x11\x63ustomized_config\x18\x02 \x01(\x0b\x32\x30.moego.models.reporting.v2.TableCustomizedConfigR\x10\x63ustomizedConfig\x12\x43\n\ntoken_info\x18\x03 \x01(\x0b\x32$.moego.models.reporting.v2.TokenInfoR\ttokenInfo\x12P\n\x0ereporting_type\x18\x04 \x01(\x0e\x32).moego.models.reporting.v2.ReportingSceneR\rreportingType\"\x91\x02\n\x16QueryReportMetasParams\x12/\n\x0b\x64iagram_ids\x18\x01 \x03(\tB\x0e\xfa\x42\x0b\x92\x01\x08\"\x06r\x04\x10\x01\x18\x64R\ndiagramIds\x12\x43\n\ntoken_info\x18\x02 \x01(\x0b\x32$.moego.models.reporting.v2.TokenInfoR\ttokenInfo\x12P\n\x0ereporting_type\x18\x03 \x01(\x0e\x32).moego.models.reporting.v2.ReportingSceneR\rreportingType\x12/\n\x0btenants_ids\x18\x04 \x03(\x04\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\x32\x02 \x00R\ntenantsIds\"b\n\x17QueryReportsMetasResult\x12G\n\x0creport_metas\x18\x01 \x03(\x0b\x32$.moego.models.reporting.v2.TableMetaR\x0breportMetas\"\xfb\x06\n\x15\x46\x65tchReportDataParams\x12(\n\ndiagram_id\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x64R\tdiagramId\x12\x31\n\x0c\x62usiness_ids\x18\x02 \x03(\x04\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\x32\x02 \x00R\x0b\x62usinessIds\x12\x46\n\x0e\x63urrent_period\x18\x03 \x01(\x0b\x32\x15.google.type.IntervalB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\rcurrentPeriod\x12\x43\n\x0fprevious_period\x18\x04 \x01(\x0b\x32\x15.google.type.IntervalH\x00R\x0epreviousPeriod\x88\x01\x01\x12\x42\n\x07\x66ilters\x18\x05 \x03(\x0b\x32(.moego.models.reporting.v2.FilterRequestR\x07\x66ilters\x12-\n\x13group_by_field_keys\x18\x06 \x03(\tR\x10groupByFieldKeys\x12\x41\n\npagination\x18\x07 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestR\npagination\x12\x34\n\torder_bys\x18\x08 \x03(\x0b\x32\x17.moego.utils.v2.OrderByR\x08orderBys\x12\x43\n\ntoken_info\x18\t \x01(\x0b\x32$.moego.models.reporting.v2.TokenInfoR\ttokenInfo\x12H\n\x0fgroup_by_period\x18\n \x01(\x0e\x32\x1b.google.type.CalendarPeriodH\x01R\rgroupByPeriod\x88\x01\x01\x12R\n\rfilter_groups\x18\x0b \x03(\x0b\x32-.moego.models.reporting.v2.FilterRequestGroupR\x0c\x66ilterGroups\x12P\n\x0ereporting_type\x18\x0c \x01(\x0e\x32).moego.models.reporting.v2.ReportingSceneR\rreportingType\x12/\n\x0btenants_ids\x18\r \x03(\x04\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\x32\x02 \x00R\ntenantsIdsB\x12\n\x10_previous_periodB\x12\n\x10_group_by_period\"\xe6\x01\n\x15\x46\x65tchReportDataResult\x12\x43\n\ntable_data\x18\x01 \x01(\x0b\x32$.moego.models.reporting.v2.TableDataR\ttableData\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\x12\x44\n\x10last_synced_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0elastSyncedTime\"\xef\x06\n\x16\x45xportReportDataParams\x12(\n\ndiagram_id\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x64R\tdiagramId\x12\x42\n\x07\x66ilters\x18\x02 \x03(\x0b\x32(.moego.models.reporting.v2.FilterRequestR\x07\x66ilters\x12\x43\n\ntoken_info\x18\x03 \x01(\x0b\x32$.moego.models.reporting.v2.TokenInfoR\ttokenInfo\x12\x31\n\x0c\x62usiness_ids\x18\x04 \x03(\x04\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\x32\x02 \x00R\x0b\x62usinessIds\x12\x46\n\x0e\x63urrent_period\x18\x05 \x01(\x0b\x32\x15.google.type.IntervalB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\rcurrentPeriod\x12\x43\n\x0fprevious_period\x18\x06 \x01(\x0b\x32\x15.google.type.IntervalH\x00R\x0epreviousPeriod\x88\x01\x01\x12-\n\x13group_by_field_keys\x18\x07 \x03(\tR\x10groupByFieldKeys\x12\x34\n\torder_bys\x18\x08 \x03(\x0b\x32\x17.moego.utils.v2.OrderByR\x08orderBys\x12H\n\x0fgroup_by_period\x18\t \x01(\x0e\x32\x1b.google.type.CalendarPeriodH\x01R\rgroupByPeriod\x88\x01\x01\x12P\n\x0ereporting_type\x18\n \x01(\x0e\x32).moego.models.reporting.v2.ReportingSceneR\rreportingType\x12/\n\x0btenants_ids\x18\x0b \x03(\x04\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\x32\x02 \x00R\ntenantsIds\x12R\n\rfilter_groups\x18\x0c \x03(\x0b\x32-.moego.models.reporting.v2.FilterRequestGroupR\x0c\x66ilterGroups\x12$\n\x0b\x61ll_tenants\x18\r \x01(\x08H\x02R\nallTenants\x88\x01\x01\x42\x12\n\x10_previous_periodB\x12\n\x10_group_by_periodB\x0e\n\x0c_all_tenants\"1\n\x16\x45xportReportDataResult\x12\x17\n\x07\x66ile_id\x18\x01 \x01(\x03R\x06\x66ileId\"\x9f\x01\n\x14QueryPageMetaRequest\x12:\n\x04tabs\x18\x01 \x03(\x0e\x32&.moego.models.reporting.v2.InsightsTabR\x04tabs\x12K\n\x05scene\x18\x02 \x01(\x0e\x32).moego.models.reporting.v2.ReportingSceneB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x05scene\"\x9b\x01\n\x15QueryPageMetaResponse\x12<\n\x05pages\x18\x01 \x03(\x0b\x32&.moego.models.reporting.v2.PageMetaDefR\x05pages\x12\x44\n\x10last_synced_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0elastSyncedTime\"\xc3\x01\n\x11QueryMetasRequest\x12/\n\x0b\x64iagram_ids\x18\x01 \x03(\tB\x0e\xfa\x42\x0b\x92\x01\x08\"\x06r\x04\x10\x01\x18\x64R\ndiagramIds\x12?\n\x05scene\x18\x02 \x01(\x0e\x32).moego.models.reporting.v2.ReportingSceneR\x05scene\x12<\n\x05scope\x18\x03 \x01(\x0b\x32&.moego.models.reporting.v2.ScopeFilterR\x05scope\"R\n\x12QueryMetasResponse\x12<\n\x05metas\x18\x01 \x03(\x0b\x32&.moego.models.reporting.v2.DiagramMetaR\x05metas\"\x94\x05\n\x10\x46\x65tchDataRequest\x12(\n\ndiagram_id\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x64R\tdiagramId\x12<\n\x05scope\x18\x02 \x01(\x0b\x32&.moego.models.reporting.v2.ScopeFilterR\x05scope\x12I\n\ntime_range\x18\x03 \x01(\x0b\x32%.moego.models.reporting.v2.TimeFilterH\x00R\ttimeRange\x88\x01\x01\x12\x46\n\npagination\x18\x04 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x01R\npagination\x88\x01\x01\x12\x34\n\torder_bys\x18\x05 \x03(\x0b\x32\x17.moego.utils.v2.OrderByR\x08orderBys\x12\x42\n\x07\x66ilters\x18\x06 \x03(\x0b\x32(.moego.models.reporting.v2.FilterRequestR\x07\x66ilters\x12M\n\tdimension\x18\x07 \x01(\x0b\x32*.moego.models.reporting.v2.DimensionFilterH\x02R\tdimension\x88\x01\x01\x12?\n\x05scene\x18\x08 \x01(\x0e\x32).moego.models.reporting.v2.ReportingSceneR\x05scene\x12\x1f\n\x0bmetric_keys\x18\t \x03(\tR\nmetricKeys\x12.\n\x13\x64ynamic_column_mode\x18\n \x01(\x08R\x11\x64ynamicColumnModeB\r\n\x0b_time_rangeB\r\n\x0b_paginationB\x0c\n\n_dimension\"\xb0\x01\n\x11\x46\x65tchDataResponse\x12;\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\'.moego.models.reporting.v2.FetchDataDefR\x04\x64\x61ta\x12I\n\x10last_synced_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x00R\x0elastSyncedTime\x88\x01\x01\x42\x13\n\x11_last_synced_time\"\xed\x03\n\x11\x45xportDataRequest\x12(\n\ndiagram_id\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x64R\tdiagramId\x12<\n\x05scope\x18\x02 \x01(\x0b\x32&.moego.models.reporting.v2.ScopeFilterR\x05scope\x12I\n\ntime_range\x18\x03 \x01(\x0b\x32%.moego.models.reporting.v2.TimeFilterH\x00R\ttimeRange\x88\x01\x01\x12\x34\n\torder_bys\x18\x04 \x03(\x0b\x32\x17.moego.utils.v2.OrderByR\x08orderBys\x12\x42\n\x07\x66ilters\x18\x05 \x03(\x0b\x32(.moego.models.reporting.v2.FilterRequestR\x07\x66ilters\x12M\n\tdimension\x18\x06 \x01(\x0b\x32*.moego.models.reporting.v2.DimensionFilterH\x01R\tdimension\x88\x01\x01\x12?\n\x05scene\x18\x07 \x01(\x0e\x32).moego.models.reporting.v2.ReportingSceneR\x05sceneB\r\n\x0b_time_rangeB\x0c\n\n_dimension\"-\n\x12\x45xportDataResponse\x12\x17\n\x07\x66ile_id\x18\x01 \x01(\x03R\x06\x66ileId2\xa9\t\n\rReportService\x12z\n\x10QueryReportPages\x12\x32.moego.service.reporting.v2.QueryReportPagesParams\x1a\x32.moego.service.reporting.v2.QueryReportPagesResult\x12\x80\x01\n\x12MarkReportFavorite\x12\x34.moego.service.reporting.v2.MarkReportFavoriteParams\x1a\x34.moego.service.reporting.v2.MarkReportFavoriteResult\x12p\n\x19SaveReportCustomizeConfig\x12;.moego.service.reporting.v2.SaveReportCustomizeConfigParams\x1a\x16.google.protobuf.Empty\x12{\n\x10QueryReportMetas\x12\x32.moego.service.reporting.v2.QueryReportMetasParams\x1a\x33.moego.service.reporting.v2.QueryReportsMetasResult\x12w\n\x0f\x46\x65tchReportData\x12\x31.moego.service.reporting.v2.FetchReportDataParams\x1a\x31.moego.service.reporting.v2.FetchReportDataResult\x12z\n\x10\x45xportReportData\x12\x32.moego.service.reporting.v2.ExportReportDataParams\x1a\x32.moego.service.reporting.v2.ExportReportDataResult\x12q\n\nQueryPages\x12\x30.moego.service.reporting.v2.QueryPageMetaRequest\x1a\x31.moego.service.reporting.v2.QueryPageMetaResponse\x12k\n\nQueryMetas\x12-.moego.service.reporting.v2.QueryMetasRequest\x1a..moego.service.reporting.v2.QueryMetasResponse\x12h\n\tFetchData\x12,.moego.service.reporting.v2.FetchDataRequest\x1a-.moego.service.reporting.v2.FetchDataResponse\x12k\n\nExportData\x12-.moego.service.reporting.v2.ExportDataRequest\x1a..moego.service.reporting.v2.ExportDataResponseB\x86\x01\n\"com.moego.idl.service.reporting.v2P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/reporting/v2;reportingsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.reporting.v2.reports_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.moego.idl.service.reporting.v2P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/reporting/v2;reportingsvcpb'
  _globals['_QUERYREPORTPAGESPARAMS'].fields_by_name['tabs']._loaded_options = None
  _globals['_QUERYREPORTPAGESPARAMS'].fields_by_name['tabs']._serialized_options = b'\372B\016\222\001\013\030\001\"\007\202\001\004\020\001 \000'
  _globals['_MARKREPORTFAVORITEPARAMS'].fields_by_name['diagram_id']._loaded_options = None
  _globals['_MARKREPORTFAVORITEPARAMS'].fields_by_name['diagram_id']._serialized_options = b'\372B\006r\004\020\001\030d'
  _globals['_MARKREPORTFAVORITEPARAMS'].fields_by_name['action']._loaded_options = None
  _globals['_MARKREPORTFAVORITEPARAMS'].fields_by_name['action']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_SAVEREPORTCUSTOMIZECONFIGPARAMS'].fields_by_name['diagram_id']._loaded_options = None
  _globals['_SAVEREPORTCUSTOMIZECONFIGPARAMS'].fields_by_name['diagram_id']._serialized_options = b'\372B\006r\004\020\001\030d'
  _globals['_QUERYREPORTMETASPARAMS'].fields_by_name['diagram_ids']._loaded_options = None
  _globals['_QUERYREPORTMETASPARAMS'].fields_by_name['diagram_ids']._serialized_options = b'\372B\013\222\001\010\"\006r\004\020\001\030d'
  _globals['_QUERYREPORTMETASPARAMS'].fields_by_name['tenants_ids']._loaded_options = None
  _globals['_QUERYREPORTMETASPARAMS'].fields_by_name['tenants_ids']._serialized_options = b'\372B\013\222\001\010\030\001\"\0042\002 \000'
  _globals['_FETCHREPORTDATAPARAMS'].fields_by_name['diagram_id']._loaded_options = None
  _globals['_FETCHREPORTDATAPARAMS'].fields_by_name['diagram_id']._serialized_options = b'\372B\006r\004\020\001\030d'
  _globals['_FETCHREPORTDATAPARAMS'].fields_by_name['business_ids']._loaded_options = None
  _globals['_FETCHREPORTDATAPARAMS'].fields_by_name['business_ids']._serialized_options = b'\372B\013\222\001\010\030\001\"\0042\002 \000'
  _globals['_FETCHREPORTDATAPARAMS'].fields_by_name['current_period']._loaded_options = None
  _globals['_FETCHREPORTDATAPARAMS'].fields_by_name['current_period']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_FETCHREPORTDATAPARAMS'].fields_by_name['tenants_ids']._loaded_options = None
  _globals['_FETCHREPORTDATAPARAMS'].fields_by_name['tenants_ids']._serialized_options = b'\372B\013\222\001\010\030\001\"\0042\002 \000'
  _globals['_EXPORTREPORTDATAPARAMS'].fields_by_name['diagram_id']._loaded_options = None
  _globals['_EXPORTREPORTDATAPARAMS'].fields_by_name['diagram_id']._serialized_options = b'\372B\006r\004\020\001\030d'
  _globals['_EXPORTREPORTDATAPARAMS'].fields_by_name['business_ids']._loaded_options = None
  _globals['_EXPORTREPORTDATAPARAMS'].fields_by_name['business_ids']._serialized_options = b'\372B\013\222\001\010\030\001\"\0042\002 \000'
  _globals['_EXPORTREPORTDATAPARAMS'].fields_by_name['current_period']._loaded_options = None
  _globals['_EXPORTREPORTDATAPARAMS'].fields_by_name['current_period']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_EXPORTREPORTDATAPARAMS'].fields_by_name['tenants_ids']._loaded_options = None
  _globals['_EXPORTREPORTDATAPARAMS'].fields_by_name['tenants_ids']._serialized_options = b'\372B\013\222\001\010\030\001\"\0042\002 \000'
  _globals['_QUERYPAGEMETAREQUEST'].fields_by_name['scene']._loaded_options = None
  _globals['_QUERYPAGEMETAREQUEST'].fields_by_name['scene']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_QUERYMETASREQUEST'].fields_by_name['diagram_ids']._loaded_options = None
  _globals['_QUERYMETASREQUEST'].fields_by_name['diagram_ids']._serialized_options = b'\372B\013\222\001\010\"\006r\004\020\001\030d'
  _globals['_FETCHDATAREQUEST'].fields_by_name['diagram_id']._loaded_options = None
  _globals['_FETCHDATAREQUEST'].fields_by_name['diagram_id']._serialized_options = b'\372B\006r\004\020\001\030d'
  _globals['_EXPORTDATAREQUEST'].fields_by_name['diagram_id']._loaded_options = None
  _globals['_EXPORTDATAREQUEST'].fields_by_name['diagram_id']._serialized_options = b'\372B\006r\004\020\001\030d'
  _globals['_QUERYREPORTPAGESPARAMS']._serialized_start=547
  _globals['_QUERYREPORTPAGESPARAMS']._serialized_end=804
  _globals['_QUERYREPORTPAGESRESULT']._serialized_start=807
  _globals['_QUERYREPORTPAGESRESULT']._serialized_end=962
  _globals['_MARKREPORTFAVORITEPARAMS']._serialized_start=965
  _globals['_MARKREPORTFAVORITEPARAMS']._serialized_end=1345
  _globals['_MARKREPORTFAVORITEPARAMS_ACTION']._serialized_start=1283
  _globals['_MARKREPORTFAVORITEPARAMS_ACTION']._serialized_end=1345
  _globals['_MARKREPORTFAVORITERESULT']._serialized_start=1347
  _globals['_MARKREPORTFAVORITERESULT']._serialized_end=1397
  _globals['_SAVEREPORTCUSTOMIZECONFIGPARAMS']._serialized_start=1400
  _globals['_SAVEREPORTCUSTOMIZECONFIGPARAMS']._serialized_end=1721
  _globals['_QUERYREPORTMETASPARAMS']._serialized_start=1724
  _globals['_QUERYREPORTMETASPARAMS']._serialized_end=1997
  _globals['_QUERYREPORTSMETASRESULT']._serialized_start=1999
  _globals['_QUERYREPORTSMETASRESULT']._serialized_end=2097
  _globals['_FETCHREPORTDATAPARAMS']._serialized_start=2100
  _globals['_FETCHREPORTDATAPARAMS']._serialized_end=2991
  _globals['_FETCHREPORTDATARESULT']._serialized_start=2994
  _globals['_FETCHREPORTDATARESULT']._serialized_end=3224
  _globals['_EXPORTREPORTDATAPARAMS']._serialized_start=3227
  _globals['_EXPORTREPORTDATAPARAMS']._serialized_end=4106
  _globals['_EXPORTREPORTDATARESULT']._serialized_start=4108
  _globals['_EXPORTREPORTDATARESULT']._serialized_end=4157
  _globals['_QUERYPAGEMETAREQUEST']._serialized_start=4160
  _globals['_QUERYPAGEMETAREQUEST']._serialized_end=4319
  _globals['_QUERYPAGEMETARESPONSE']._serialized_start=4322
  _globals['_QUERYPAGEMETARESPONSE']._serialized_end=4477
  _globals['_QUERYMETASREQUEST']._serialized_start=4480
  _globals['_QUERYMETASREQUEST']._serialized_end=4675
  _globals['_QUERYMETASRESPONSE']._serialized_start=4677
  _globals['_QUERYMETASRESPONSE']._serialized_end=4759
  _globals['_FETCHDATAREQUEST']._serialized_start=4762
  _globals['_FETCHDATAREQUEST']._serialized_end=5422
  _globals['_FETCHDATARESPONSE']._serialized_start=5425
  _globals['_FETCHDATARESPONSE']._serialized_end=5601
  _globals['_EXPORTDATAREQUEST']._serialized_start=5604
  _globals['_EXPORTDATAREQUEST']._serialized_end=6097
  _globals['_EXPORTDATARESPONSE']._serialized_start=6099
  _globals['_EXPORTDATARESPONSE']._serialized_end=6144
  _globals['_REPORTSERVICE']._serialized_start=6147
  _globals['_REPORTSERVICE']._serialized_end=7340
# @@protoc_insertion_point(module_scope)
