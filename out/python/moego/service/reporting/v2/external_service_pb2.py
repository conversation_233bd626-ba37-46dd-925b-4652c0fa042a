# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/reporting/v2/external_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/reporting/v2/external_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import money_pb2 as google_dot_type_dot_money__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1moego/service/reporting/v2/external_service.proto\x12\x1amoego.service.reporting.v2\x1a\x17google/type/money.proto\"c\n\x1fListCustomerUnpaidAmountRequest\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\x12!\n\x0c\x63ustomer_ids\x18\x02 \x03(\x03R\x0b\x63ustomerIds\"\x93\x02\n ListCustomerUnpaidAmountResponse\x12\x89\x01\n\x17\x63ustomer_unpaid_amounts\x18\x01 \x03(\x0b\x32Q.moego.service.reporting.v2.ListCustomerUnpaidAmountResponse.CustomerUnpaidAmountR\x15\x63ustomerUnpaidAmounts\x1a\x63\n\x14\x43ustomerUnpaidAmount\x12\x1f\n\x0b\x63ustomer_id\x18\x01 \x01(\x03R\ncustomerId\x12*\n\x06\x61mount\x18\x02 \x01(\x0b\x32\x12.google.type.MoneyR\x06\x61mount2\xa9\x01\n\x0f\x45xternalService\x12\x95\x01\n\x18ListCustomerUnpaidAmount\x12;.moego.service.reporting.v2.ListCustomerUnpaidAmountRequest\x1a<.moego.service.reporting.v2.ListCustomerUnpaidAmountResponseB\x86\x01\n\"com.moego.idl.service.reporting.v2P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/reporting/v2;reportingsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.reporting.v2.external_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.moego.idl.service.reporting.v2P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/reporting/v2;reportingsvcpb'
  _globals['_LISTCUSTOMERUNPAIDAMOUNTREQUEST']._serialized_start=106
  _globals['_LISTCUSTOMERUNPAIDAMOUNTREQUEST']._serialized_end=205
  _globals['_LISTCUSTOMERUNPAIDAMOUNTRESPONSE']._serialized_start=208
  _globals['_LISTCUSTOMERUNPAIDAMOUNTRESPONSE']._serialized_end=483
  _globals['_LISTCUSTOMERUNPAIDAMOUNTRESPONSE_CUSTOMERUNPAIDAMOUNT']._serialized_start=384
  _globals['_LISTCUSTOMERUNPAIDAMOUNTRESPONSE_CUSTOMERUNPAIDAMOUNT']._serialized_end=483
  _globals['_EXTERNALSERVICE']._serialized_start=486
  _globals['_EXTERNALSERVICE']._serialized_end=655
# @@protoc_insertion_point(module_scope)
