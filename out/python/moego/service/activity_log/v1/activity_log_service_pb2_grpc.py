# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.activity_log.v1 import activity_log_service_pb2 as moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2


class ActivityLogServiceStub(object):
    """Activity log service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateActivityLog = channel.unary_unary(
                '/moego.service.activity_log.v1.ActivityLogService/CreateActivityLog',
                request_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.CreateActivityLogRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.CreateActivityLogResponse.FromString,
                _registered_method=True)
        self.SearchActivityLogPage = channel.unary_unary(
                '/moego.service.activity_log.v1.ActivityLogService/SearchActivityLogPage',
                request_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchActivityLogPageInput.SerializeToString,
                response_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchActivityLogPageOutput.FromString,
                _registered_method=True)
        self.GetActivityLogDetails = channel.unary_unary(
                '/moego.service.activity_log.v1.ActivityLogService/GetActivityLogDetails',
                request_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.GetActivityLogDetailsInput.SerializeToString,
                response_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.GetActivityLogDetailsOutput.FromString,
                _registered_method=True)
        self.ListActivityLogDetails = channel.unary_unary(
                '/moego.service.activity_log.v1.ActivityLogService/ListActivityLogDetails',
                request_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.ListActivityLogDetailsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.ListActivityLogDetailsResponse.FromString,
                _registered_method=True)
        self.SearchOperatorPage = channel.unary_unary(
                '/moego.service.activity_log.v1.ActivityLogService/SearchOperatorPage',
                request_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchOperatorPageInput.SerializeToString,
                response_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchOperatorPageOutput.FromString,
                _registered_method=True)
        self.SearchResourceTypePage = channel.unary_unary(
                '/moego.service.activity_log.v1.ActivityLogService/SearchResourceTypePage',
                request_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchResourceTypePageInput.SerializeToString,
                response_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchResourceTypePageOutput.FromString,
                _registered_method=True)
        self.SearchActionPage = channel.unary_unary(
                '/moego.service.activity_log.v1.ActivityLogService/SearchActionPage',
                request_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchActionPageInput.SerializeToString,
                response_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchActionPageOutput.FromString,
                _registered_method=True)
        self.SearchOwnerPage = channel.unary_unary(
                '/moego.service.activity_log.v1.ActivityLogService/SearchOwnerPage',
                request_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchOwnerPageInput.SerializeToString,
                response_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchOwnerPageOutput.FromString,
                _registered_method=True)


class ActivityLogServiceServicer(object):
    """Activity log service
    """

    def CreateActivityLog(self, request, context):
        """Create an Activity Log
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchActivityLogPage(self, request, context):
        """Search activity logs with pagination
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetActivityLogDetails(self, request, context):
        """Get activity log details
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListActivityLogDetails(self, request, context):
        """List activity logs details, not include affected activity logs
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchOperatorPage(self, request, context):
        """Search operators with pagination
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchResourceTypePage(self, request, context):
        """Search resource types with pagination
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchActionPage(self, request, context):
        """Search actions with pagination
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchOwnerPage(self, request, context):
        """Search owners with pagination
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ActivityLogServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateActivityLog': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateActivityLog,
                    request_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.CreateActivityLogRequest.FromString,
                    response_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.CreateActivityLogResponse.SerializeToString,
            ),
            'SearchActivityLogPage': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchActivityLogPage,
                    request_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchActivityLogPageInput.FromString,
                    response_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchActivityLogPageOutput.SerializeToString,
            ),
            'GetActivityLogDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.GetActivityLogDetails,
                    request_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.GetActivityLogDetailsInput.FromString,
                    response_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.GetActivityLogDetailsOutput.SerializeToString,
            ),
            'ListActivityLogDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.ListActivityLogDetails,
                    request_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.ListActivityLogDetailsRequest.FromString,
                    response_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.ListActivityLogDetailsResponse.SerializeToString,
            ),
            'SearchOperatorPage': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchOperatorPage,
                    request_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchOperatorPageInput.FromString,
                    response_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchOperatorPageOutput.SerializeToString,
            ),
            'SearchResourceTypePage': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchResourceTypePage,
                    request_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchResourceTypePageInput.FromString,
                    response_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchResourceTypePageOutput.SerializeToString,
            ),
            'SearchActionPage': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchActionPage,
                    request_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchActionPageInput.FromString,
                    response_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchActionPageOutput.SerializeToString,
            ),
            'SearchOwnerPage': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchOwnerPage,
                    request_deserializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchOwnerPageInput.FromString,
                    response_serializer=moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchOwnerPageOutput.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.activity_log.v1.ActivityLogService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.activity_log.v1.ActivityLogService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ActivityLogService(object):
    """Activity log service
    """

    @staticmethod
    def CreateActivityLog(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.activity_log.v1.ActivityLogService/CreateActivityLog',
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.CreateActivityLogRequest.SerializeToString,
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.CreateActivityLogResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SearchActivityLogPage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.activity_log.v1.ActivityLogService/SearchActivityLogPage',
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchActivityLogPageInput.SerializeToString,
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchActivityLogPageOutput.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetActivityLogDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.activity_log.v1.ActivityLogService/GetActivityLogDetails',
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.GetActivityLogDetailsInput.SerializeToString,
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.GetActivityLogDetailsOutput.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListActivityLogDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.activity_log.v1.ActivityLogService/ListActivityLogDetails',
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.ListActivityLogDetailsRequest.SerializeToString,
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.ListActivityLogDetailsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SearchOperatorPage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.activity_log.v1.ActivityLogService/SearchOperatorPage',
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchOperatorPageInput.SerializeToString,
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchOperatorPageOutput.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SearchResourceTypePage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.activity_log.v1.ActivityLogService/SearchResourceTypePage',
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchResourceTypePageInput.SerializeToString,
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchResourceTypePageOutput.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SearchActionPage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.activity_log.v1.ActivityLogService/SearchActionPage',
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchActionPageInput.SerializeToString,
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchActionPageOutput.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SearchOwnerPage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.activity_log.v1.ActivityLogService/SearchOwnerPage',
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchOwnerPageInput.SerializeToString,
            moego_dot_service_dot_activity__log_dot_v1_dot_activity__log__service__pb2.SearchOwnerPageOutput.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
