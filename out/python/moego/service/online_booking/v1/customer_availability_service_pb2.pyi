from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class SetCustomerBlockStatusRequest(_message.Message):
    __slots__ = ("service_item_types", "customer_ids", "need_block", "company_id", "staff_id")
    SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_IDS_FIELD_NUMBER: _ClassVar[int]
    NEED_BLOCK_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    customer_ids: _containers.RepeatedScalarFieldContainer[int]
    need_block: bool
    company_id: int
    staff_id: int
    def __init__(self, service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., customer_ids: _Optional[_Iterable[int]] = ..., need_block: bool = ..., company_id: _Optional[int] = ..., staff_id: _Optional[int] = ...) -> None: ...

class SetCustomerBlockStatusResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ListBlockedCustomerRequest(_message.Message):
    __slots__ = ("service_item_types", "customer_ids", "pagination", "company_id")
    SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_IDS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    customer_ids: _containers.RepeatedScalarFieldContainer[int]
    pagination: _pagination_messages_pb2.PaginationRequest
    company_id: int
    def __init__(self, service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., customer_ids: _Optional[_Iterable[int]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., company_id: _Optional[int] = ...) -> None: ...

class ListBlockedCustomerResponse(_message.Message):
    __slots__ = ("customer_block_infos", "pagination")
    class CustomerBlockInfo(_message.Message):
        __slots__ = ("customer_id", "service_item_types")
        CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
        SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
        customer_id: int
        service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
        def __init__(self, customer_id: _Optional[int] = ..., service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ...) -> None: ...
    CUSTOMER_BLOCK_INFOS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    customer_block_infos: _containers.RepeatedCompositeFieldContainer[ListBlockedCustomerResponse.CustomerBlockInfo]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, customer_block_infos: _Optional[_Iterable[_Union[ListBlockedCustomerResponse.CustomerBlockInfo, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...
