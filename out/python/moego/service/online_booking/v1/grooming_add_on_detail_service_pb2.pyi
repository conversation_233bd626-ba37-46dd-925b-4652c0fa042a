from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf import wrappers_pb2 as _wrappers_pb2
from moego.models.online_booking.v1 import grooming_add_on_detail_models_pb2 as _grooming_add_on_detail_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CreateGroomingAddOnDetailRequest(_message.Message):
    __slots__ = ("pet_id", "staff_id", "add_on_id", "service_time", "service_price", "start_date", "start_time", "end_date", "end_time", "created_at", "updated_at")
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    ADD_ON_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TIME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    UPDATED_AT_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    staff_id: int
    add_on_id: int
    service_time: int
    service_price: float
    start_date: str
    start_time: int
    end_date: str
    end_time: int
    created_at: _timestamp_pb2.Timestamp
    updated_at: _timestamp_pb2.Timestamp
    def __init__(self, pet_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., add_on_id: _Optional[int] = ..., service_time: _Optional[int] = ..., service_price: _Optional[float] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., updated_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class GetGroomingAddOnDetailResponse(_message.Message):
    __slots__ = ("record",)
    RECORD_FIELD_NUMBER: _ClassVar[int]
    record: _grooming_add_on_detail_models_pb2.GroomingAddOnDetailModel
    def __init__(self, record: _Optional[_Union[_grooming_add_on_detail_models_pb2.GroomingAddOnDetailModel, _Mapping]] = ...) -> None: ...

class UpdateGroomingAddOnDetailRequest(_message.Message):
    __slots__ = ("id", "booking_request_id", "service_detail_id", "pet_id", "staff_id", "add_on_id", "service_time", "service_price", "start_date", "start_time", "end_date", "end_time", "created_at", "updated_at")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    ADD_ON_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TIME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    UPDATED_AT_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    service_detail_id: int
    pet_id: int
    staff_id: int
    add_on_id: int
    service_time: int
    service_price: float
    start_date: str
    start_time: int
    end_date: str
    end_time: int
    created_at: _timestamp_pb2.Timestamp
    updated_at: _timestamp_pb2.Timestamp
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., service_detail_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., add_on_id: _Optional[int] = ..., service_time: _Optional[int] = ..., service_price: _Optional[float] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., updated_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...
