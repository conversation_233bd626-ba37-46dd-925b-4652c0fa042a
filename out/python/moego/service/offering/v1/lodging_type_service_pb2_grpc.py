# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.offering.v1 import lodging_type_service_pb2 as moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2


class LodgingTypeServiceStub(object):
    """lodging type service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateLodgingType = channel.unary_unary(
                '/moego.service.offering.v1.LodgingTypeService/CreateLodgingType',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.CreateLodgingTypeRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.CreateLodgingTypeResponse.FromString,
                _registered_method=True)
        self.UpdateLodgingType = channel.unary_unary(
                '/moego.service.offering.v1.LodgingTypeService/UpdateLodgingType',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.UpdateLodgingTypeRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.UpdateLodgingTypeResponse.FromString,
                _registered_method=True)
        self.DeleteLodgingType = channel.unary_unary(
                '/moego.service.offering.v1.LodgingTypeService/DeleteLodgingType',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.DeleteLodgingTypeRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.DeleteLodgingTypeResponse.FromString,
                _registered_method=True)
        self.GetLodgingTypeList = channel.unary_unary(
                '/moego.service.offering.v1.LodgingTypeService/GetLodgingTypeList',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.GetLodgingTypeListRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.GetLodgingTypeListResponse.FromString,
                _registered_method=True)
        self.MGetLodgingType = channel.unary_unary(
                '/moego.service.offering.v1.LodgingTypeService/MGetLodgingType',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.MGetLodgingTypeRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.MGetLodgingTypeResponse.FromString,
                _registered_method=True)
        self.SortLodgingTypeByIds = channel.unary_unary(
                '/moego.service.offering.v1.LodgingTypeService/SortLodgingTypeByIds',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.SortLodgingTypeByIdsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.SortLodgingTypeByIdsResponse.FromString,
                _registered_method=True)


class LodgingTypeServiceServicer(object):
    """lodging type service
    """

    def CreateLodgingType(self, request, context):
        """create lodging type
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateLodgingType(self, request, context):
        """update lodging type
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteLodgingType(self, request, context):
        """delete lodging type
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLodgingTypeList(self, request, context):
        """get lodging type list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MGetLodgingType(self, request, context):
        """mget
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SortLodgingTypeByIds(self, request, context):
        """sort lodging type by ids
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_LodgingTypeServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateLodgingType': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateLodgingType,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.CreateLodgingTypeRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.CreateLodgingTypeResponse.SerializeToString,
            ),
            'UpdateLodgingType': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateLodgingType,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.UpdateLodgingTypeRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.UpdateLodgingTypeResponse.SerializeToString,
            ),
            'DeleteLodgingType': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteLodgingType,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.DeleteLodgingTypeRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.DeleteLodgingTypeResponse.SerializeToString,
            ),
            'GetLodgingTypeList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLodgingTypeList,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.GetLodgingTypeListRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.GetLodgingTypeListResponse.SerializeToString,
            ),
            'MGetLodgingType': grpc.unary_unary_rpc_method_handler(
                    servicer.MGetLodgingType,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.MGetLodgingTypeRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.MGetLodgingTypeResponse.SerializeToString,
            ),
            'SortLodgingTypeByIds': grpc.unary_unary_rpc_method_handler(
                    servicer.SortLodgingTypeByIds,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.SortLodgingTypeByIdsRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.SortLodgingTypeByIdsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.offering.v1.LodgingTypeService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.offering.v1.LodgingTypeService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class LodgingTypeService(object):
    """lodging type service
    """

    @staticmethod
    def CreateLodgingType(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.LodgingTypeService/CreateLodgingType',
            moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.CreateLodgingTypeRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.CreateLodgingTypeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateLodgingType(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.LodgingTypeService/UpdateLodgingType',
            moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.UpdateLodgingTypeRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.UpdateLodgingTypeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteLodgingType(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.LodgingTypeService/DeleteLodgingType',
            moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.DeleteLodgingTypeRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.DeleteLodgingTypeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetLodgingTypeList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.LodgingTypeService/GetLodgingTypeList',
            moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.GetLodgingTypeListRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.GetLodgingTypeListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def MGetLodgingType(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.LodgingTypeService/MGetLodgingType',
            moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.MGetLodgingTypeRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.MGetLodgingTypeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SortLodgingTypeByIds(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.LodgingTypeService/SortLodgingTypeByIds',
            moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.SortLodgingTypeByIdsRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_lodging__type__service__pb2.SortLodgingTypeByIdsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
