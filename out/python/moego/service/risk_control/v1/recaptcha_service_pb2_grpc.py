# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.risk_control.v1 import recaptcha_service_pb2 as moego_dot_service_dot_risk__control_dot_v1_dot_recaptcha__service__pb2


class RecaptchaServiceStub(object):
    """the recaptcha service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Challenge = channel.unary_unary(
                '/moego.service.risk_control.v1.RecaptchaService/Challenge',
                request_serializer=moego_dot_service_dot_risk__control_dot_v1_dot_recaptcha__service__pb2.RecaptchaChallengeInput.SerializeToString,
                response_deserializer=moego_dot_service_dot_risk__control_dot_v1_dot_recaptcha__service__pb2.RecaptchaChallengeOutput.FromString,
                _registered_method=True)
        self.Verify = channel.unary_unary(
                '/moego.service.risk_control.v1.RecaptchaService/Verify',
                request_serializer=moego_dot_service_dot_risk__control_dot_v1_dot_recaptcha__service__pb2.RiskControlVerifyInput.SerializeToString,
                response_deserializer=moego_dot_service_dot_risk__control_dot_v1_dot_recaptcha__service__pb2.RiskControlVerifyOutput.FromString,
                _registered_method=True)


class RecaptchaServiceServicer(object):
    """the recaptcha service
    """

    def Challenge(self, request, context):
        """challenge
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Verify(self, request, context):
        """verify risk control
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_RecaptchaServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Challenge': grpc.unary_unary_rpc_method_handler(
                    servicer.Challenge,
                    request_deserializer=moego_dot_service_dot_risk__control_dot_v1_dot_recaptcha__service__pb2.RecaptchaChallengeInput.FromString,
                    response_serializer=moego_dot_service_dot_risk__control_dot_v1_dot_recaptcha__service__pb2.RecaptchaChallengeOutput.SerializeToString,
            ),
            'Verify': grpc.unary_unary_rpc_method_handler(
                    servicer.Verify,
                    request_deserializer=moego_dot_service_dot_risk__control_dot_v1_dot_recaptcha__service__pb2.RiskControlVerifyInput.FromString,
                    response_serializer=moego_dot_service_dot_risk__control_dot_v1_dot_recaptcha__service__pb2.RiskControlVerifyOutput.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.risk_control.v1.RecaptchaService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.risk_control.v1.RecaptchaService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class RecaptchaService(object):
    """the recaptcha service
    """

    @staticmethod
    def Challenge(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.risk_control.v1.RecaptchaService/Challenge',
            moego_dot_service_dot_risk__control_dot_v1_dot_recaptcha__service__pb2.RecaptchaChallengeInput.SerializeToString,
            moego_dot_service_dot_risk__control_dot_v1_dot_recaptcha__service__pb2.RecaptchaChallengeOutput.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Verify(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.risk_control.v1.RecaptchaService/Verify',
            moego_dot_service_dot_risk__control_dot_v1_dot_recaptcha__service__pb2.RiskControlVerifyInput.SerializeToString,
            moego_dot_service_dot_risk__control_dot_v1_dot_recaptcha__service__pb2.RiskControlVerifyOutput.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
