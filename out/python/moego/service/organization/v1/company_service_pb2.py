# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/organization/v1/company_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/organization/v1/company_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.organization.v1 import clock_in_out_setting_defs_pb2 as moego_dot_models_dot_organization_dot_v1_dot_clock__in__out__setting__defs__pb2
from moego.models.organization.v1 import clock_in_out_setting_model_pb2 as moego_dot_models_dot_organization_dot_v1_dot_clock__in__out__setting__model__pb2
from moego.models.organization.v1 import company_defs_pb2 as moego_dot_models_dot_organization_dot_v1_dot_company__defs__pb2
from moego.models.organization.v1 import company_models_pb2 as moego_dot_models_dot_organization_dot_v1_dot_company__models__pb2
from moego.models.organization.v1 import country_defs_pb2 as moego_dot_models_dot_organization_dot_v1_dot_country__defs__pb2
from moego.models.organization.v1 import location_defs_pb2 as moego_dot_models_dot_organization_dot_v1_dot_location__defs__pb2
from moego.models.organization.v1 import location_enums_pb2 as moego_dot_models_dot_organization_dot_v1_dot_location__enums__pb2
from moego.models.organization.v1 import migrate_enums_pb2 as moego_dot_models_dot_organization_dot_v1_dot_migrate__enums__pb2
from moego.models.organization.v1 import tax_defs_pb2 as moego_dot_models_dot_organization_dot_v1_dot_tax__defs__pb2
from moego.models.organization.v1 import tax_models_pb2 as moego_dot_models_dot_organization_dot_v1_dot_tax__models__pb2
from moego.models.organization.v1 import time_zone_pb2 as moego_dot_models_dot_organization_dot_v1_dot_time__zone__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n3moego/service/organization/v1/company_service.proto\x12\x1dmoego.service.organization.v1\x1a<moego/models/organization/v1/clock_in_out_setting_defs.proto\x1a=moego/models/organization/v1/clock_in_out_setting_model.proto\x1a/moego/models/organization/v1/company_defs.proto\x1a\x31moego/models/organization/v1/company_models.proto\x1a/moego/models/organization/v1/country_defs.proto\x1a\x30moego/models/organization/v1/location_defs.proto\x1a\x31moego/models/organization/v1/location_enums.proto\x1a\x30moego/models/organization/v1/migrate_enums.proto\x1a+moego/models/organization/v1/tax_defs.proto\x1a-moego/models/organization/v1/tax_models.proto\x1a,moego/models/organization/v1/time_zone.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\xa3\x05\n\x14\x43reateCompanyRequest\x12&\n\naccount_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\taccountId\x12U\n\x08location\x18\x02 \x01(\x0b\x32/.moego.models.organization.v1.CreateLocationDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x08location\x12\x45\n\x06source\x18\x03 \x01(\x0e\x32(.moego.models.organization.v1.SourceTypeH\x00R\x06source\x88\x01\x01\x12G\n\x07\x63ountry\x18\x04 \x01(\x0b\x32(.moego.models.organization.v1.CountryDefH\x01R\x07\x63ountry\x88\x01\x01\x12H\n\ttime_zone\x18\x05 \x01(\x0b\x32&.moego.models.organization.v1.TimeZoneH\x02R\x08timeZone\x88\x01\x01\x12\x33\n\rknow_about_us\x18\x06 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\xff\x01H\x03R\x0bknowAboutUs\x88\x01\x01\x12,\n\x0cphone_number\x18\x07 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x32R\x0bphoneNumber\x12-\n\rcurrency_code\x18\x08 \x01(\tB\x08\xfa\x42\x05r\x03\x98\x01\x03R\x0c\x63urrencyCode\x12\x30\n\x0f\x63urrency_symbol\x18\t \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01R\x0e\x63urrencySymbol\x12&\n\x0c\x63ompany_type\x18\n \x01(\x05H\x04R\x0b\x63ompanyType\x88\x01\x01\x42\t\n\x07_sourceB\n\n\x08_countryB\x0c\n\n_time_zoneB\x10\n\x0e_know_about_usB\x0f\n\r_company_type\"W\n\x15\x43reateCompanyResponse\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\"P\n\x1aQueryCompaniesByIdsRequest\x12\x32\n\x0b\x63ompany_ids\x18\x01 \x03(\x03\x42\x11\xfa\x42\x0e\x92\x01\x0b\x08\x01\x10\xf4\x03\"\x04\"\x02\x38\x00R\ncompanyIds\"\x98\x02\n\x1bQueryCompaniesByIdsResponse\x12\x85\x01\n\x15\x63ompany_id_to_company\x18\x01 \x03(\x0b\x32R.moego.service.organization.v1.QueryCompaniesByIdsResponse.CompanyIdToCompanyEntryR\x12\x63ompanyIdToCompany\x1aq\n\x17\x43ompanyIdToCompanyEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12@\n\x05value\x18\x02 \x01(\x0b\x32*.moego.models.organization.v1.CompanyModelR\x05value:\x02\x38\x01\"\xee\x01\n%UpdateCompanyPreferenceSettingRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12n\n\x12preference_setting\x18\x02 \x01(\x0b\x32?.moego.models.organization.v1.UpdateCompanyPreferenceSettingDefR\x11preferenceSetting\x12-\n\x0etoken_staff_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0ctokenStaffId\"B\n&UpdateCompanyPreferenceSettingResponse\x12\x18\n\x07success\x18\x01 \x01(\x08R\x07success\"L\n\"GetCompanyPreferenceSettingRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\"\x91\x01\n#GetCompanyPreferenceSettingResponse\x12j\n\x12preference_setting\x18\x01 \x01(\x0b\x32;.moego.models.organization.v1.CompanyPreferenceSettingModelR\x11preferenceSetting\"A\n\x17IsMoegoPayEnableRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\"I\n\x18IsMoegoPayEnableResponse\x12-\n\x13is_moego_pay_enable\x18\x01 \x01(\x08R\x10isMoegoPayEnable\"~\n\x15IsRetailEnableRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12-\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x42\x0e\n\x0c_business_id\"B\n\x16IsRetailEnableResponse\x12(\n\x10is_retail_enable\x18\x01 \x01(\x08R\x0eisRetailEnable\"\xa1\x02\n\x11\x41\x64\x64TaxRuleRequest\x12M\n\x08tax_rule\x18\x01 \x01(\x0b\x32(.moego.models.organization.v1.TaxRuleDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x07taxRule\x12/\n\x0etoken_staff_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x0ctokenStaffId\x12=\n\x14internal_operator_id\x18\x03 \x01(\tB\t\xfa\x42\x06r\x04\x10\x02\x18\x32H\x00R\x12internalOperatorId\x12\x31\n\x10token_company_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0etokenCompanyIdB\x1a\n\x13operator_identifier\x12\x03\xf8\x42\x01\"$\n\x12\x41\x64\x64TaxRuleResponse\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\"\xbd\x02\n\x14UpdateTaxRuleRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12M\n\x08tax_rule\x18\x02 \x01(\x0b\x32(.moego.models.organization.v1.TaxRuleDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x07taxRule\x12/\n\x0etoken_staff_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x0ctokenStaffId\x12=\n\x14internal_operator_id\x18\x04 \x01(\tB\t\xfa\x42\x06r\x04\x10\x02\x18\x32H\x00R\x12internalOperatorId\x12\x31\n\x10token_company_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0etokenCompanyIdB\x1a\n\x13operator_identifier\x12\x03\xf8\x42\x01\"1\n\x15UpdateTaxRuleResponse\x12\x18\n\x07success\x18\x01 \x01(\x08R\x07success\"\xee\x01\n\x14\x44\x65leteTaxRuleRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12/\n\x0etoken_staff_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x0ctokenStaffId\x12=\n\x14internal_operator_id\x18\x03 \x01(\tB\t\xfa\x42\x06r\x04\x10\x02\x18\x32H\x00R\x12internalOperatorId\x12\x31\n\x10token_company_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0etokenCompanyIdB\x1a\n\x13operator_identifier\x12\x03\xf8\x42\x01\"1\n\x15\x44\x65leteTaxRuleResponse\x12\x18\n\x07success\x18\x01 \x01(\x08R\x07success\"\xd6\x01\n\x15GetTaxRuleListRequest\x12/\n\x0etoken_staff_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x0ctokenStaffId\x12=\n\x14internal_operator_id\x18\x02 \x01(\tB\t\xfa\x42\x06r\x04\x10\x02\x18\x32H\x00R\x12internalOperatorId\x12\x31\n\x10token_company_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0etokenCompanyIdB\x1a\n\x13operator_identifier\x12\x03\xf8\x42\x01\"X\n\x16GetTaxRuleListResponse\x12>\n\x04rule\x18\x01 \x03(\x0b\x32*.moego.models.organization.v1.TaxRuleModelR\x04rule\"G\n\x1dGetBusinessIdForMobileRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\"A\n\x1eGetBusinessIdForMobileResponse\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\"\x9c\x01\n\x1eSetCompanyMigrateStatusRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12R\n\x0emigrate_status\x18\x02 \x01(\x0e\x32+.moego.models.organization.v1.MigrateStatusR\rmigrateStatus\"!\n\x1fSetCompanyMigrateStatusResponse\"\x1f\n\x1dMarkCompanyAsMigratedResponse\"\x80\x01\n\x17IsCompanyMigrateRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12-\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x42\x0e\n\x0c_business_id\"H\n\x18IsCompanyMigrateResponse\x12,\n\x12is_company_migrate\x18\x01 \x01(\x08R\x10isCompanyMigrate\"Q\n\x19IsCompaniesMigrateRequest\x12\x34\n\x0b\x63ompany_ids\x18\x01 \x03(\x03\x42\x13\xfa\x42\x10\x92\x01\r\x08\x01\x10\xf4\x03\x18\x01\"\x04\"\x02 \x00R\ncompanyIds\"\xf6\x01\n\x1aIsCompaniesMigrateResponse\x12\x8d\x01\n\x18is_companies_migrate_map\x18\x01 \x03(\x0b\x32T.moego.service.organization.v1.IsCompaniesMigrateResponse.IsCompaniesMigrateMapEntryR\x15isCompaniesMigrateMap\x1aH\n\x1aIsCompaniesMigrateMapEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n\x05value\x18\x02 \x01(\x08R\x05value:\x02\x38\x01\"P\n\x1bGetClockInOutSettingRequest\x12\x31\n\x10token_company_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0etokenCompanyId\"\x85\x01\n\x1cGetClockInOutSettingResponse\x12\x65\n\x14\x63lock_in_out_setting\x18\x01 \x01(\x0b\x32\x34.moego.models.organization.v1.ClockInOutSettingModelR\x11\x63lockInOutSetting\"\xf7\x01\n\x1eUpdateClockInOutSettingRequest\x12\x31\n\x10token_company_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0etokenCompanyId\x12-\n\x0etoken_staff_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0ctokenStaffId\x12s\n\x14\x63lock_in_out_setting\x18\x06 \x01(\x0b\x32\x38.moego.models.organization.v1.UpdateClockInOutSettingDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x11\x63lockInOutSetting\";\n\x1fUpdateClockInOutSettingResponse\x12\x18\n\x07success\x18\x01 \x01(\x08R\x07success\"\xa9\x01\n\"ListCompaniesByEnterpriseIdRequest\x12,\n\renterprise_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0c\x65nterpriseId\x12\x46\n\npagination\x18\x02 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x00R\npagination\x88\x01\x01\x42\r\n\x0b_pagination\"\xc7\x01\n#ListCompaniesByEnterpriseIdResponse\x12H\n\tcompanies\x18\x01 \x03(\x0b\x32*.moego.models.organization.v1.CompanyModelR\tcompanies\x12G\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseH\x00R\npagination\x88\x01\x01\x42\r\n\x0b_pagination\"B\n!CompanyQuestionRecordQueryRequest\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\"N\n\"CompanyQuestionRecordQueryResponse\x12(\n\x10is_fill_question\x18\x01 \x01(\x08R\x0eisFillQuestion\"\x9a\x03\n\x1c\x43ompanyQuestionRecordRequest\x12+\n\rpet_per_month\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32R\x0bpetPerMonth\x12\x35\n\x0ftotal_locations\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x00R\x0etotalLocations\x88\x01\x01\x12+\n\ntotal_vans\x18\x03 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x01R\ttotalVans\x88\x01\x01\x12$\n\tmove_from\x18\x04 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32R\x08moveFrom\x12(\n\x0bsource_from\x18\x05 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32R\nsourceFrom\x12\x38\n\x11source_from_other\x18\x06 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x02R\x0fsourceFromOther\x88\x01\x01\x12&\n\ncompany_id\x18\x07 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyIdB\x12\n\x10_total_locationsB\r\n\x0b_total_vansB\x14\n\x12_source_from_other\"9\n\x1d\x43ompanyQuestionRecordResponse\x12\x18\n\x07success\x18\x01 \x01(\x08R\x07success\"c\n\x12SortCompanyRequest\x12&\n\naccount_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\taccountId\x12%\n\x03ids\x18\x02 \x03(\x03\x42\x13\xfa\x42\x10\x92\x01\r\x08\x01\x10\xff\x01\x18\x01\"\x04\"\x02 \x00R\x03ids\"\x15\n\x13SortCompanyResponse\"\xe1\x02\n\x12ListCompanyRequest\x12+\n\naccount_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\taccountId\x88\x01\x01\x12+\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x01R\tcompanyId\x88\x01\x01\x12\x17\n\x04name\x18\x03 \x01(\tH\x02R\x04name\x88\x01\x01\x12\x1d\n\x07\x63ountry\x18\x04 \x01(\tH\x03R\x07\x63ountry\x88\x01\x01\x12\x31\n\renterprise_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x04R\x0c\x65nterpriseId\x88\x01\x01\x12\x41\n\npagination\x18\x0f \x01(\x0b\x32!.moego.utils.v2.PaginationRequestR\npaginationB\r\n\x0b_account_idB\r\n\x0b_company_idB\x07\n\x05_nameB\n\n\x08_countryB\x10\n\x0e_enterprise_id\"\xa3\x01\n\x13ListCompanyResponse\x12H\n\tcompanies\x18\x01 \x03(\x0b\x32*.moego.models.organization.v1.CompanyModelR\tcompanies\x12\x42\n\npagination\x18\x0f \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\"\x88\x04\n\x14UpdateCompanyRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12!\n\x04name\x18\x02 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x00R\x04name\x88\x01\x01\x12(\n\renable_square\x18\x03 \x01(\x08H\x01R\x0c\x65nableSquare\x88\x01\x01\x12\x35\n\x14\x65nable_stripe_reader\x18\x04 \x01(\x08H\x02R\x12\x65nableStripeReader\x88\x01\x01\x12+\n\naccount_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x03R\taccountId\x88\x01\x01\x12&\n\x0clocation_num\x18\x06 \x01(\x05H\x04R\x0blocationNum\x88\x01\x01\x12\x1c\n\x07van_num\x18\x07 \x01(\x05H\x05R\x06vanNum\x88\x01\x01\x12\x31\n\renterprise_id\x18\x08 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x06R\x0c\x65nterpriseId\x88\x01\x01\x12\"\n\x05level\x18\t \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x07R\x05level\x88\x01\x01\x42\x07\n\x05_nameB\x10\n\x0e_enable_squareB\x17\n\x15_enable_stripe_readerB\r\n\x0b_account_idB\x0f\n\r_location_numB\n\n\x08_van_numB\x10\n\x0e_enterprise_idB\x08\n\x06_level\"/\n\x15UpdateCompanyResponse\x12\x16\n\x06result\x18\x01 \x01(\x08R\x06result\"Z\n$ListCompanyPreferenceSettingsRequest\x12\x32\n\x0b\x63ompany_ids\x18\x01 \x03(\x03\x42\x11\xfa\x42\x0e\x92\x01\x0b\x08\x01\x10\xf4\x03\"\x04\"\x02 \x00R\ncompanyIds\"\xc5\x02\n%ListCompanyPreferenceSettingsResponse\x12\x94\x01\n\x16\x63ompany_preference_map\x18\x01 \x03(\x0b\x32^.moego.service.organization.v1.ListCompanyPreferenceSettingsResponse.CompanyPreferenceMapEntryR\x14\x63ompanyPreferenceMap\x1a\x84\x01\n\x19\x43ompanyPreferenceMapEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12Q\n\x05value\x18\x02 \x01(\x0b\x32;.moego.models.organization.v1.CompanyPreferenceSettingModelR\x05value:\x02\x38\x01\"\x91\x04\n%CreateCompanyFromEnterpriseHubRequest\x12&\n\naccount_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\taccountId\x12.\n\x13template_company_id\x18\x02 \x01(\x03R\x11templateCompanyId\x12\"\n\x05\x65mail\x18\x03 \x01(\tB\x07\xfa\x42\x04r\x02`\x01H\x00R\x05\x65mail\x88\x01\x01\x12.\n\nfirst_name\x18\x04 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\xff\x01H\x01R\tfirstName\x88\x01\x01\x12,\n\tlast_name\x18\x05 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\xff\x01H\x02R\x08lastName\x88\x01\x01\x12&\n\x0clocation_num\x18\x06 \x01(\x05H\x03R\x0blocationNum\x88\x01\x01\x12\x1c\n\x07van_num\x18\x07 \x01(\x05H\x04R\x06vanNum\x88\x01\x01\x12\x31\n\x0cphone_number\x18\x08 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x32H\x05R\x0bphoneNumber\x88\x01\x01\x12,\n\renterprise_id\x18\t \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0c\x65nterpriseId\x12\x12\n\x04name\x18\n \x01(\tR\x04nameB\x08\n\x06_emailB\r\n\x0b_first_nameB\x0c\n\n_last_nameB\x0f\n\r_location_numB\n\n\x08_van_numB\x0f\n\r_phone_number2\xf5\x1a\n\x0e\x43ompanyService\x12z\n\rCreateCompany\x12\x33.moego.service.organization.v1.CreateCompanyRequest\x1a\x34.moego.service.organization.v1.CreateCompanyResponse\x12\x8c\x01\n\x13QueryCompaniesByIds\x12\x39.moego.service.organization.v1.QueryCompaniesByIdsRequest\x1a:.moego.service.organization.v1.QueryCompaniesByIdsResponse\x12\x83\x01\n\x10IsMoegoPayEnable\x12\x36.moego.service.organization.v1.IsMoegoPayEnableRequest\x1a\x37.moego.service.organization.v1.IsMoegoPayEnableResponse\x12\xad\x01\n\x1eUpdateCompanyPreferenceSetting\x12\x44.moego.service.organization.v1.UpdateCompanyPreferenceSettingRequest\x1a\x45.moego.service.organization.v1.UpdateCompanyPreferenceSettingResponse\x12\xa4\x01\n\x1bGetCompanyPreferenceSetting\x12\x41.moego.service.organization.v1.GetCompanyPreferenceSettingRequest\x1a\x42.moego.service.organization.v1.GetCompanyPreferenceSettingResponse\x12}\n\x0eIsRetailEnable\x12\x34.moego.service.organization.v1.IsRetailEnableRequest\x1a\x35.moego.service.organization.v1.IsRetailEnableResponse\x12q\n\nAddTaxRule\x12\x30.moego.service.organization.v1.AddTaxRuleRequest\x1a\x31.moego.service.organization.v1.AddTaxRuleResponse\x12z\n\rUpdateTaxRule\x12\x33.moego.service.organization.v1.UpdateTaxRuleRequest\x1a\x34.moego.service.organization.v1.UpdateTaxRuleResponse\x12z\n\rDeleteTaxRule\x12\x33.moego.service.organization.v1.DeleteTaxRuleRequest\x1a\x34.moego.service.organization.v1.DeleteTaxRuleResponse\x12}\n\x0eGetTaxRuleList\x12\x34.moego.service.organization.v1.GetTaxRuleListRequest\x1a\x35.moego.service.organization.v1.GetTaxRuleListResponse\x12\x95\x01\n\x16GetBusinessIdForMobile\x12<.moego.service.organization.v1.GetBusinessIdForMobileRequest\x1a=.moego.service.organization.v1.GetBusinessIdForMobileResponse\x12\x83\x01\n\x10IsCompanyMigrate\x12\x36.moego.service.organization.v1.IsCompanyMigrateRequest\x1a\x37.moego.service.organization.v1.IsCompanyMigrateResponse\x12\x8f\x01\n\x14GetClockInOutSetting\x12:.moego.service.organization.v1.GetClockInOutSettingRequest\x1a;.moego.service.organization.v1.GetClockInOutSettingResponse\x12\x98\x01\n\x17UpdateClockInOutSetting\x12=.moego.service.organization.v1.UpdateClockInOutSettingRequest\x1a>.moego.service.organization.v1.UpdateClockInOutSettingResponse\x12\x89\x01\n\x12IsCompaniesMigrate\x12\x38.moego.service.organization.v1.IsCompaniesMigrateRequest\x1a\x39.moego.service.organization.v1.IsCompaniesMigrateResponse\x12\x98\x01\n\x17SetCompanyMigrateStatus\x12=.moego.service.organization.v1.SetCompanyMigrateStatusRequest\x1a>.moego.service.organization.v1.SetCompanyMigrateStatusResponse\x12\xa4\x01\n\x1bListCompaniesByEnterpriseId\x12\x41.moego.service.organization.v1.ListCompaniesByEnterpriseIdRequest\x1a\x42.moego.service.organization.v1.ListCompaniesByEnterpriseIdResponse\x12\xa1\x01\n\x18GetCompanyQuestionRecord\<EMAIL>\x1a\x41.moego.service.organization.v1.CompanyQuestionRecordQueryResponse\"\x00\x12\x98\x01\n\x19\x43ompanyQuestionRecordSave\x12;.moego.service.organization.v1.CompanyQuestionRecordRequest\x1a<.moego.service.organization.v1.CompanyQuestionRecordResponse\"\x00\x12v\n\x0bSortCompany\x12\x31.moego.service.organization.v1.SortCompanyRequest\x1a\x32.moego.service.organization.v1.SortCompanyResponse\"\x00\x12v\n\x0bListCompany\x12\x31.moego.service.organization.v1.ListCompanyRequest\x1a\x32.moego.service.organization.v1.ListCompanyResponse\"\x00\x12\xaa\x01\n\x1dListCompanyPreferenceSettings\x12\x43.moego.service.organization.v1.ListCompanyPreferenceSettingsRequest\x1a\x44.moego.service.organization.v1.ListCompanyPreferenceSettingsResponse\x12|\n\rUpdateCompany\x12\x33.moego.service.organization.v1.UpdateCompanyRequest\x1a\x34.moego.service.organization.v1.UpdateCompanyResponse\"\x00\x12\x9e\x01\n\x1e\x43reateCompanyFromEnterpriseHub\x12\x44.moego.service.organization.v1.CreateCompanyFromEnterpriseHubRequest\x1a\x34.moego.service.organization.v1.CreateCompanyResponse\"\x00\x42\x8f\x01\n%com.moego.idl.service.organization.v1P\x01Zdgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1;organizationsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.organization.v1.company_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.moego.idl.service.organization.v1P\001Zdgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1;organizationsvcpb'
  _globals['_CREATECOMPANYREQUEST'].fields_by_name['account_id']._loaded_options = None
  _globals['_CREATECOMPANYREQUEST'].fields_by_name['account_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATECOMPANYREQUEST'].fields_by_name['location']._loaded_options = None
  _globals['_CREATECOMPANYREQUEST'].fields_by_name['location']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CREATECOMPANYREQUEST'].fields_by_name['know_about_us']._loaded_options = None
  _globals['_CREATECOMPANYREQUEST'].fields_by_name['know_about_us']._serialized_options = b'\372B\007r\005\020\001\030\377\001'
  _globals['_CREATECOMPANYREQUEST'].fields_by_name['phone_number']._loaded_options = None
  _globals['_CREATECOMPANYREQUEST'].fields_by_name['phone_number']._serialized_options = b'\372B\006r\004\020\001\0302'
  _globals['_CREATECOMPANYREQUEST'].fields_by_name['currency_code']._loaded_options = None
  _globals['_CREATECOMPANYREQUEST'].fields_by_name['currency_code']._serialized_options = b'\372B\005r\003\230\001\003'
  _globals['_CREATECOMPANYREQUEST'].fields_by_name['currency_symbol']._loaded_options = None
  _globals['_CREATECOMPANYREQUEST'].fields_by_name['currency_symbol']._serialized_options = b'\372B\004r\002\020\001'
  _globals['_QUERYCOMPANIESBYIDSREQUEST'].fields_by_name['company_ids']._loaded_options = None
  _globals['_QUERYCOMPANIESBYIDSREQUEST'].fields_by_name['company_ids']._serialized_options = b'\372B\016\222\001\013\010\001\020\364\003\"\004\"\0028\000'
  _globals['_QUERYCOMPANIESBYIDSRESPONSE_COMPANYIDTOCOMPANYENTRY']._loaded_options = None
  _globals['_QUERYCOMPANIESBYIDSRESPONSE_COMPANYIDTOCOMPANYENTRY']._serialized_options = b'8\001'
  _globals['_UPDATECOMPANYPREFERENCESETTINGREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_UPDATECOMPANYPREFERENCESETTINGREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATECOMPANYPREFERENCESETTINGREQUEST'].fields_by_name['token_staff_id']._loaded_options = None
  _globals['_UPDATECOMPANYPREFERENCESETTINGREQUEST'].fields_by_name['token_staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETCOMPANYPREFERENCESETTINGREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETCOMPANYPREFERENCESETTINGREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ISMOEGOPAYENABLEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_ISMOEGOPAYENABLEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ISRETAILENABLEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_ISRETAILENABLEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ISRETAILENABLEREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_ISRETAILENABLEREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ADDTAXRULEREQUEST'].oneofs_by_name['operator_identifier']._loaded_options = None
  _globals['_ADDTAXRULEREQUEST'].oneofs_by_name['operator_identifier']._serialized_options = b'\370B\001'
  _globals['_ADDTAXRULEREQUEST'].fields_by_name['tax_rule']._loaded_options = None
  _globals['_ADDTAXRULEREQUEST'].fields_by_name['tax_rule']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_ADDTAXRULEREQUEST'].fields_by_name['token_staff_id']._loaded_options = None
  _globals['_ADDTAXRULEREQUEST'].fields_by_name['token_staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ADDTAXRULEREQUEST'].fields_by_name['internal_operator_id']._loaded_options = None
  _globals['_ADDTAXRULEREQUEST'].fields_by_name['internal_operator_id']._serialized_options = b'\372B\006r\004\020\002\0302'
  _globals['_ADDTAXRULEREQUEST'].fields_by_name['token_company_id']._loaded_options = None
  _globals['_ADDTAXRULEREQUEST'].fields_by_name['token_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATETAXRULEREQUEST'].oneofs_by_name['operator_identifier']._loaded_options = None
  _globals['_UPDATETAXRULEREQUEST'].oneofs_by_name['operator_identifier']._serialized_options = b'\370B\001'
  _globals['_UPDATETAXRULEREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATETAXRULEREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATETAXRULEREQUEST'].fields_by_name['tax_rule']._loaded_options = None
  _globals['_UPDATETAXRULEREQUEST'].fields_by_name['tax_rule']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPDATETAXRULEREQUEST'].fields_by_name['token_staff_id']._loaded_options = None
  _globals['_UPDATETAXRULEREQUEST'].fields_by_name['token_staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATETAXRULEREQUEST'].fields_by_name['internal_operator_id']._loaded_options = None
  _globals['_UPDATETAXRULEREQUEST'].fields_by_name['internal_operator_id']._serialized_options = b'\372B\006r\004\020\002\0302'
  _globals['_UPDATETAXRULEREQUEST'].fields_by_name['token_company_id']._loaded_options = None
  _globals['_UPDATETAXRULEREQUEST'].fields_by_name['token_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETETAXRULEREQUEST'].oneofs_by_name['operator_identifier']._loaded_options = None
  _globals['_DELETETAXRULEREQUEST'].oneofs_by_name['operator_identifier']._serialized_options = b'\370B\001'
  _globals['_DELETETAXRULEREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_DELETETAXRULEREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETETAXRULEREQUEST'].fields_by_name['token_staff_id']._loaded_options = None
  _globals['_DELETETAXRULEREQUEST'].fields_by_name['token_staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETETAXRULEREQUEST'].fields_by_name['internal_operator_id']._loaded_options = None
  _globals['_DELETETAXRULEREQUEST'].fields_by_name['internal_operator_id']._serialized_options = b'\372B\006r\004\020\002\0302'
  _globals['_DELETETAXRULEREQUEST'].fields_by_name['token_company_id']._loaded_options = None
  _globals['_DELETETAXRULEREQUEST'].fields_by_name['token_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETTAXRULELISTREQUEST'].oneofs_by_name['operator_identifier']._loaded_options = None
  _globals['_GETTAXRULELISTREQUEST'].oneofs_by_name['operator_identifier']._serialized_options = b'\370B\001'
  _globals['_GETTAXRULELISTREQUEST'].fields_by_name['token_staff_id']._loaded_options = None
  _globals['_GETTAXRULELISTREQUEST'].fields_by_name['token_staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETTAXRULELISTREQUEST'].fields_by_name['internal_operator_id']._loaded_options = None
  _globals['_GETTAXRULELISTREQUEST'].fields_by_name['internal_operator_id']._serialized_options = b'\372B\006r\004\020\002\0302'
  _globals['_GETTAXRULELISTREQUEST'].fields_by_name['token_company_id']._loaded_options = None
  _globals['_GETTAXRULELISTREQUEST'].fields_by_name['token_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETBUSINESSIDFORMOBILEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETBUSINESSIDFORMOBILEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SETCOMPANYMIGRATESTATUSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_SETCOMPANYMIGRATESTATUSREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ISCOMPANYMIGRATEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_ISCOMPANYMIGRATEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ISCOMPANYMIGRATEREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_ISCOMPANYMIGRATEREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ISCOMPANIESMIGRATEREQUEST'].fields_by_name['company_ids']._loaded_options = None
  _globals['_ISCOMPANIESMIGRATEREQUEST'].fields_by_name['company_ids']._serialized_options = b'\372B\020\222\001\r\010\001\020\364\003\030\001\"\004\"\002 \000'
  _globals['_ISCOMPANIESMIGRATERESPONSE_ISCOMPANIESMIGRATEMAPENTRY']._loaded_options = None
  _globals['_ISCOMPANIESMIGRATERESPONSE_ISCOMPANIESMIGRATEMAPENTRY']._serialized_options = b'8\001'
  _globals['_GETCLOCKINOUTSETTINGREQUEST'].fields_by_name['token_company_id']._loaded_options = None
  _globals['_GETCLOCKINOUTSETTINGREQUEST'].fields_by_name['token_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATECLOCKINOUTSETTINGREQUEST'].fields_by_name['token_company_id']._loaded_options = None
  _globals['_UPDATECLOCKINOUTSETTINGREQUEST'].fields_by_name['token_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATECLOCKINOUTSETTINGREQUEST'].fields_by_name['token_staff_id']._loaded_options = None
  _globals['_UPDATECLOCKINOUTSETTINGREQUEST'].fields_by_name['token_staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATECLOCKINOUTSETTINGREQUEST'].fields_by_name['clock_in_out_setting']._loaded_options = None
  _globals['_UPDATECLOCKINOUTSETTINGREQUEST'].fields_by_name['clock_in_out_setting']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_LISTCOMPANIESBYENTERPRISEIDREQUEST'].fields_by_name['enterprise_id']._loaded_options = None
  _globals['_LISTCOMPANIESBYENTERPRISEIDREQUEST'].fields_by_name['enterprise_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_COMPANYQUESTIONRECORDREQUEST'].fields_by_name['pet_per_month']._loaded_options = None
  _globals['_COMPANYQUESTIONRECORDREQUEST'].fields_by_name['pet_per_month']._serialized_options = b'\372B\004r\002\0302'
  _globals['_COMPANYQUESTIONRECORDREQUEST'].fields_by_name['total_locations']._loaded_options = None
  _globals['_COMPANYQUESTIONRECORDREQUEST'].fields_by_name['total_locations']._serialized_options = b'\372B\004r\002\0302'
  _globals['_COMPANYQUESTIONRECORDREQUEST'].fields_by_name['total_vans']._loaded_options = None
  _globals['_COMPANYQUESTIONRECORDREQUEST'].fields_by_name['total_vans']._serialized_options = b'\372B\004r\002\0302'
  _globals['_COMPANYQUESTIONRECORDREQUEST'].fields_by_name['move_from']._loaded_options = None
  _globals['_COMPANYQUESTIONRECORDREQUEST'].fields_by_name['move_from']._serialized_options = b'\372B\004r\002\0302'
  _globals['_COMPANYQUESTIONRECORDREQUEST'].fields_by_name['source_from']._loaded_options = None
  _globals['_COMPANYQUESTIONRECORDREQUEST'].fields_by_name['source_from']._serialized_options = b'\372B\004r\002\0302'
  _globals['_COMPANYQUESTIONRECORDREQUEST'].fields_by_name['source_from_other']._loaded_options = None
  _globals['_COMPANYQUESTIONRECORDREQUEST'].fields_by_name['source_from_other']._serialized_options = b'\372B\004r\002\0302'
  _globals['_COMPANYQUESTIONRECORDREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_COMPANYQUESTIONRECORDREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SORTCOMPANYREQUEST'].fields_by_name['account_id']._loaded_options = None
  _globals['_SORTCOMPANYREQUEST'].fields_by_name['account_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SORTCOMPANYREQUEST'].fields_by_name['ids']._loaded_options = None
  _globals['_SORTCOMPANYREQUEST'].fields_by_name['ids']._serialized_options = b'\372B\020\222\001\r\010\001\020\377\001\030\001\"\004\"\002 \000'
  _globals['_LISTCOMPANYREQUEST'].fields_by_name['account_id']._loaded_options = None
  _globals['_LISTCOMPANYREQUEST'].fields_by_name['account_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTCOMPANYREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTCOMPANYREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTCOMPANYREQUEST'].fields_by_name['enterprise_id']._loaded_options = None
  _globals['_LISTCOMPANYREQUEST'].fields_by_name['enterprise_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATECOMPANYREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_UPDATECOMPANYREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATECOMPANYREQUEST'].fields_by_name['name']._loaded_options = None
  _globals['_UPDATECOMPANYREQUEST'].fields_by_name['name']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_UPDATECOMPANYREQUEST'].fields_by_name['account_id']._loaded_options = None
  _globals['_UPDATECOMPANYREQUEST'].fields_by_name['account_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_UPDATECOMPANYREQUEST'].fields_by_name['enterprise_id']._loaded_options = None
  _globals['_UPDATECOMPANYREQUEST'].fields_by_name['enterprise_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_UPDATECOMPANYREQUEST'].fields_by_name['level']._loaded_options = None
  _globals['_UPDATECOMPANYREQUEST'].fields_by_name['level']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_LISTCOMPANYPREFERENCESETTINGSREQUEST'].fields_by_name['company_ids']._loaded_options = None
  _globals['_LISTCOMPANYPREFERENCESETTINGSREQUEST'].fields_by_name['company_ids']._serialized_options = b'\372B\016\222\001\013\010\001\020\364\003\"\004\"\002 \000'
  _globals['_LISTCOMPANYPREFERENCESETTINGSRESPONSE_COMPANYPREFERENCEMAPENTRY']._loaded_options = None
  _globals['_LISTCOMPANYPREFERENCESETTINGSRESPONSE_COMPANYPREFERENCEMAPENTRY']._serialized_options = b'8\001'
  _globals['_CREATECOMPANYFROMENTERPRISEHUBREQUEST'].fields_by_name['account_id']._loaded_options = None
  _globals['_CREATECOMPANYFROMENTERPRISEHUBREQUEST'].fields_by_name['account_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATECOMPANYFROMENTERPRISEHUBREQUEST'].fields_by_name['email']._loaded_options = None
  _globals['_CREATECOMPANYFROMENTERPRISEHUBREQUEST'].fields_by_name['email']._serialized_options = b'\372B\004r\002`\001'
  _globals['_CREATECOMPANYFROMENTERPRISEHUBREQUEST'].fields_by_name['first_name']._loaded_options = None
  _globals['_CREATECOMPANYFROMENTERPRISEHUBREQUEST'].fields_by_name['first_name']._serialized_options = b'\372B\007r\005\020\001\030\377\001'
  _globals['_CREATECOMPANYFROMENTERPRISEHUBREQUEST'].fields_by_name['last_name']._loaded_options = None
  _globals['_CREATECOMPANYFROMENTERPRISEHUBREQUEST'].fields_by_name['last_name']._serialized_options = b'\372B\007r\005\020\001\030\377\001'
  _globals['_CREATECOMPANYFROMENTERPRISEHUBREQUEST'].fields_by_name['phone_number']._loaded_options = None
  _globals['_CREATECOMPANYFROMENTERPRISEHUBREQUEST'].fields_by_name['phone_number']._serialized_options = b'\372B\006r\004\020\001\0302'
  _globals['_CREATECOMPANYFROMENTERPRISEHUBREQUEST'].fields_by_name['enterprise_id']._loaded_options = None
  _globals['_CREATECOMPANYFROMENTERPRISEHUBREQUEST'].fields_by_name['enterprise_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATECOMPANYREQUEST']._serialized_start=717
  _globals['_CREATECOMPANYREQUEST']._serialized_end=1392
  _globals['_CREATECOMPANYRESPONSE']._serialized_start=1394
  _globals['_CREATECOMPANYRESPONSE']._serialized_end=1481
  _globals['_QUERYCOMPANIESBYIDSREQUEST']._serialized_start=1483
  _globals['_QUERYCOMPANIESBYIDSREQUEST']._serialized_end=1563
  _globals['_QUERYCOMPANIESBYIDSRESPONSE']._serialized_start=1566
  _globals['_QUERYCOMPANIESBYIDSRESPONSE']._serialized_end=1846
  _globals['_QUERYCOMPANIESBYIDSRESPONSE_COMPANYIDTOCOMPANYENTRY']._serialized_start=1733
  _globals['_QUERYCOMPANIESBYIDSRESPONSE_COMPANYIDTOCOMPANYENTRY']._serialized_end=1846
  _globals['_UPDATECOMPANYPREFERENCESETTINGREQUEST']._serialized_start=1849
  _globals['_UPDATECOMPANYPREFERENCESETTINGREQUEST']._serialized_end=2087
  _globals['_UPDATECOMPANYPREFERENCESETTINGRESPONSE']._serialized_start=2089
  _globals['_UPDATECOMPANYPREFERENCESETTINGRESPONSE']._serialized_end=2155
  _globals['_GETCOMPANYPREFERENCESETTINGREQUEST']._serialized_start=2157
  _globals['_GETCOMPANYPREFERENCESETTINGREQUEST']._serialized_end=2233
  _globals['_GETCOMPANYPREFERENCESETTINGRESPONSE']._serialized_start=2236
  _globals['_GETCOMPANYPREFERENCESETTINGRESPONSE']._serialized_end=2381
  _globals['_ISMOEGOPAYENABLEREQUEST']._serialized_start=2383
  _globals['_ISMOEGOPAYENABLEREQUEST']._serialized_end=2448
  _globals['_ISMOEGOPAYENABLERESPONSE']._serialized_start=2450
  _globals['_ISMOEGOPAYENABLERESPONSE']._serialized_end=2523
  _globals['_ISRETAILENABLEREQUEST']._serialized_start=2525
  _globals['_ISRETAILENABLEREQUEST']._serialized_end=2651
  _globals['_ISRETAILENABLERESPONSE']._serialized_start=2653
  _globals['_ISRETAILENABLERESPONSE']._serialized_end=2719
  _globals['_ADDTAXRULEREQUEST']._serialized_start=2722
  _globals['_ADDTAXRULEREQUEST']._serialized_end=3011
  _globals['_ADDTAXRULERESPONSE']._serialized_start=3013
  _globals['_ADDTAXRULERESPONSE']._serialized_end=3049
  _globals['_UPDATETAXRULEREQUEST']._serialized_start=3052
  _globals['_UPDATETAXRULEREQUEST']._serialized_end=3369
  _globals['_UPDATETAXRULERESPONSE']._serialized_start=3371
  _globals['_UPDATETAXRULERESPONSE']._serialized_end=3420
  _globals['_DELETETAXRULEREQUEST']._serialized_start=3423
  _globals['_DELETETAXRULEREQUEST']._serialized_end=3661
  _globals['_DELETETAXRULERESPONSE']._serialized_start=3663
  _globals['_DELETETAXRULERESPONSE']._serialized_end=3712
  _globals['_GETTAXRULELISTREQUEST']._serialized_start=3715
  _globals['_GETTAXRULELISTREQUEST']._serialized_end=3929
  _globals['_GETTAXRULELISTRESPONSE']._serialized_start=3931
  _globals['_GETTAXRULELISTRESPONSE']._serialized_end=4019
  _globals['_GETBUSINESSIDFORMOBILEREQUEST']._serialized_start=4021
  _globals['_GETBUSINESSIDFORMOBILEREQUEST']._serialized_end=4092
  _globals['_GETBUSINESSIDFORMOBILERESPONSE']._serialized_start=4094
  _globals['_GETBUSINESSIDFORMOBILERESPONSE']._serialized_end=4159
  _globals['_SETCOMPANYMIGRATESTATUSREQUEST']._serialized_start=4162
  _globals['_SETCOMPANYMIGRATESTATUSREQUEST']._serialized_end=4318
  _globals['_SETCOMPANYMIGRATESTATUSRESPONSE']._serialized_start=4320
  _globals['_SETCOMPANYMIGRATESTATUSRESPONSE']._serialized_end=4353
  _globals['_MARKCOMPANYASMIGRATEDRESPONSE']._serialized_start=4355
  _globals['_MARKCOMPANYASMIGRATEDRESPONSE']._serialized_end=4386
  _globals['_ISCOMPANYMIGRATEREQUEST']._serialized_start=4389
  _globals['_ISCOMPANYMIGRATEREQUEST']._serialized_end=4517
  _globals['_ISCOMPANYMIGRATERESPONSE']._serialized_start=4519
  _globals['_ISCOMPANYMIGRATERESPONSE']._serialized_end=4591
  _globals['_ISCOMPANIESMIGRATEREQUEST']._serialized_start=4593
  _globals['_ISCOMPANIESMIGRATEREQUEST']._serialized_end=4674
  _globals['_ISCOMPANIESMIGRATERESPONSE']._serialized_start=4677
  _globals['_ISCOMPANIESMIGRATERESPONSE']._serialized_end=4923
  _globals['_ISCOMPANIESMIGRATERESPONSE_ISCOMPANIESMIGRATEMAPENTRY']._serialized_start=4851
  _globals['_ISCOMPANIESMIGRATERESPONSE_ISCOMPANIESMIGRATEMAPENTRY']._serialized_end=4923
  _globals['_GETCLOCKINOUTSETTINGREQUEST']._serialized_start=4925
  _globals['_GETCLOCKINOUTSETTINGREQUEST']._serialized_end=5005
  _globals['_GETCLOCKINOUTSETTINGRESPONSE']._serialized_start=5008
  _globals['_GETCLOCKINOUTSETTINGRESPONSE']._serialized_end=5141
  _globals['_UPDATECLOCKINOUTSETTINGREQUEST']._serialized_start=5144
  _globals['_UPDATECLOCKINOUTSETTINGREQUEST']._serialized_end=5391
  _globals['_UPDATECLOCKINOUTSETTINGRESPONSE']._serialized_start=5393
  _globals['_UPDATECLOCKINOUTSETTINGRESPONSE']._serialized_end=5452
  _globals['_LISTCOMPANIESBYENTERPRISEIDREQUEST']._serialized_start=5455
  _globals['_LISTCOMPANIESBYENTERPRISEIDREQUEST']._serialized_end=5624
  _globals['_LISTCOMPANIESBYENTERPRISEIDRESPONSE']._serialized_start=5627
  _globals['_LISTCOMPANIESBYENTERPRISEIDRESPONSE']._serialized_end=5826
  _globals['_COMPANYQUESTIONRECORDQUERYREQUEST']._serialized_start=5828
  _globals['_COMPANYQUESTIONRECORDQUERYREQUEST']._serialized_end=5894
  _globals['_COMPANYQUESTIONRECORDQUERYRESPONSE']._serialized_start=5896
  _globals['_COMPANYQUESTIONRECORDQUERYRESPONSE']._serialized_end=5974
  _globals['_COMPANYQUESTIONRECORDREQUEST']._serialized_start=5977
  _globals['_COMPANYQUESTIONRECORDREQUEST']._serialized_end=6387
  _globals['_COMPANYQUESTIONRECORDRESPONSE']._serialized_start=6389
  _globals['_COMPANYQUESTIONRECORDRESPONSE']._serialized_end=6446
  _globals['_SORTCOMPANYREQUEST']._serialized_start=6448
  _globals['_SORTCOMPANYREQUEST']._serialized_end=6547
  _globals['_SORTCOMPANYRESPONSE']._serialized_start=6549
  _globals['_SORTCOMPANYRESPONSE']._serialized_end=6570
  _globals['_LISTCOMPANYREQUEST']._serialized_start=6573
  _globals['_LISTCOMPANYREQUEST']._serialized_end=6926
  _globals['_LISTCOMPANYRESPONSE']._serialized_start=6929
  _globals['_LISTCOMPANYRESPONSE']._serialized_end=7092
  _globals['_UPDATECOMPANYREQUEST']._serialized_start=7095
  _globals['_UPDATECOMPANYREQUEST']._serialized_end=7615
  _globals['_UPDATECOMPANYRESPONSE']._serialized_start=7617
  _globals['_UPDATECOMPANYRESPONSE']._serialized_end=7664
  _globals['_LISTCOMPANYPREFERENCESETTINGSREQUEST']._serialized_start=7666
  _globals['_LISTCOMPANYPREFERENCESETTINGSREQUEST']._serialized_end=7756
  _globals['_LISTCOMPANYPREFERENCESETTINGSRESPONSE']._serialized_start=7759
  _globals['_LISTCOMPANYPREFERENCESETTINGSRESPONSE']._serialized_end=8084
  _globals['_LISTCOMPANYPREFERENCESETTINGSRESPONSE_COMPANYPREFERENCEMAPENTRY']._serialized_start=7952
  _globals['_LISTCOMPANYPREFERENCESETTINGSRESPONSE_COMPANYPREFERENCEMAPENTRY']._serialized_end=8084
  _globals['_CREATECOMPANYFROMENTERPRISEHUBREQUEST']._serialized_start=8087
  _globals['_CREATECOMPANYFROMENTERPRISEHUBREQUEST']._serialized_end=8616
  _globals['_COMPANYSERVICE']._serialized_start=8619
  _globals['_COMPANYSERVICE']._serialized_end=12064
# @@protoc_insertion_point(module_scope)
