# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/ai_assistant/v1/natural_language_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/ai_assistant/v1/natural_language_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.ai_assistant.v1 import natural_language_models_pb2 as moego_dot_models_dot_ai__assistant_dot_v1_dot_natural__language__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n<moego/service/ai_assistant/v1/natural_language_service.proto\x12\x1dmoego.service.ai_assistant.v1\x1a:moego/models/ai_assistant/v1/natural_language_models.proto\x1a\x17validate/validate.proto\"=\n\x15\x44\x65tectLanguageRequest\x12$\n\x07\x63ontent\x18\x01 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\xe8\x07R\x07\x63ontent\"n\n\x16\x44\x65tectLanguageResponse\x12G\n\x08language\x18\x01 \x01(\x0b\x32&.moego.models.ai_assistant.v1.LanguageH\x00R\x08language\x88\x01\x01\x42\x0b\n\t_language2\x97\x01\n\x16NaturalLanguageService\x12}\n\x0e\x44\x65tectLanguage\x12\x34.moego.service.ai_assistant.v1.DetectLanguageRequest\x1a\x35.moego.service.ai_assistant.v1.DetectLanguageResponseB\x8e\x01\n%com.moego.idl.service.ai_assistant.v1P\x01Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/ai_assistant/v1;aiassistantsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.ai_assistant.v1.natural_language_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.moego.idl.service.ai_assistant.v1P\001Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/ai_assistant/v1;aiassistantsvcpb'
  _globals['_DETECTLANGUAGEREQUEST'].fields_by_name['content']._loaded_options = None
  _globals['_DETECTLANGUAGEREQUEST'].fields_by_name['content']._serialized_options = b'\372B\007r\005\020\001\030\350\007'
  _globals['_DETECTLANGUAGEREQUEST']._serialized_start=180
  _globals['_DETECTLANGUAGEREQUEST']._serialized_end=241
  _globals['_DETECTLANGUAGERESPONSE']._serialized_start=243
  _globals['_DETECTLANGUAGERESPONSE']._serialized_end=353
  _globals['_NATURALLANGUAGESERVICE']._serialized_start=356
  _globals['_NATURALLANGUAGESERVICE']._serialized_end=507
# @@protoc_insertion_point(module_scope)
