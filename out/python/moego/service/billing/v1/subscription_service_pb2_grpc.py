# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.billing.v1 import subscription_service_pb2 as moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2


class SubscriptionServiceStub(object):
    """subscription service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateSubscription = channel.unary_unary(
                '/moego.service.billing.v1.SubscriptionService/CreateSubscription',
                request_serializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.CreateSubscriptionRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.CreateSubscriptionResponse.FromString,
                _registered_method=True)
        self.UpdateSubscription = channel.unary_unary(
                '/moego.service.billing.v1.SubscriptionService/UpdateSubscription',
                request_serializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.UpdateSubscriptionRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.UpdateSubscriptionResponse.FromString,
                _registered_method=True)
        self.CancelSubscription = channel.unary_unary(
                '/moego.service.billing.v1.SubscriptionService/CancelSubscription',
                request_serializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.CancelSubscriptionRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.CancelSubscriptionResponse.FromString,
                _registered_method=True)
        self.ConvertVendorSubscriptionIds = channel.unary_unary(
                '/moego.service.billing.v1.SubscriptionService/ConvertVendorSubscriptionIds',
                request_serializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ConvertVendorSubscriptionIdsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ConvertVendorSubscriptionIdsResponse.FromString,
                _registered_method=True)
        self.GetSubscription = channel.unary_unary(
                '/moego.service.billing.v1.SubscriptionService/GetSubscription',
                request_serializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.GetSubscriptionRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.GetSubscriptionResponse.FromString,
                _registered_method=True)
        self.ScheduleNextBillingCycle = channel.unary_unary(
                '/moego.service.billing.v1.SubscriptionService/ScheduleNextBillingCycle',
                request_serializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ScheduleNextBillingCycleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ScheduleNextBillingCycleResponse.FromString,
                _registered_method=True)
        self.ListSubscriptions = channel.unary_unary(
                '/moego.service.billing.v1.SubscriptionService/ListSubscriptions',
                request_serializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ListSubscriptionsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ListSubscriptionsResponse.FromString,
                _registered_method=True)


class SubscriptionServiceServicer(object):
    """subscription service
    """

    def CreateSubscription(self, request, context):
        """create subscription
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateSubscription(self, request, context):
        """update subscription
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelSubscription(self, request, context):
        """cancel subscription
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ConvertVendorSubscriptionIds(self, request, context):
        """convert vendor subscription ids
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSubscription(self, request, context):
        """get subscription
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ScheduleNextBillingCycle(self, request, context):
        """schedule next billing cycle, it will automatically handle existing schedule and idempotency
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListSubscriptions(self, request, context):
        """list subscriptions
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SubscriptionServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateSubscription': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateSubscription,
                    request_deserializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.CreateSubscriptionRequest.FromString,
                    response_serializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.CreateSubscriptionResponse.SerializeToString,
            ),
            'UpdateSubscription': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateSubscription,
                    request_deserializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.UpdateSubscriptionRequest.FromString,
                    response_serializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.UpdateSubscriptionResponse.SerializeToString,
            ),
            'CancelSubscription': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelSubscription,
                    request_deserializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.CancelSubscriptionRequest.FromString,
                    response_serializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.CancelSubscriptionResponse.SerializeToString,
            ),
            'ConvertVendorSubscriptionIds': grpc.unary_unary_rpc_method_handler(
                    servicer.ConvertVendorSubscriptionIds,
                    request_deserializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ConvertVendorSubscriptionIdsRequest.FromString,
                    response_serializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ConvertVendorSubscriptionIdsResponse.SerializeToString,
            ),
            'GetSubscription': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSubscription,
                    request_deserializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.GetSubscriptionRequest.FromString,
                    response_serializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.GetSubscriptionResponse.SerializeToString,
            ),
            'ScheduleNextBillingCycle': grpc.unary_unary_rpc_method_handler(
                    servicer.ScheduleNextBillingCycle,
                    request_deserializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ScheduleNextBillingCycleRequest.FromString,
                    response_serializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ScheduleNextBillingCycleResponse.SerializeToString,
            ),
            'ListSubscriptions': grpc.unary_unary_rpc_method_handler(
                    servicer.ListSubscriptions,
                    request_deserializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ListSubscriptionsRequest.FromString,
                    response_serializer=moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ListSubscriptionsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.billing.v1.SubscriptionService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.billing.v1.SubscriptionService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class SubscriptionService(object):
    """subscription service
    """

    @staticmethod
    def CreateSubscription(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.billing.v1.SubscriptionService/CreateSubscription',
            moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.CreateSubscriptionRequest.SerializeToString,
            moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.CreateSubscriptionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateSubscription(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.billing.v1.SubscriptionService/UpdateSubscription',
            moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.UpdateSubscriptionRequest.SerializeToString,
            moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.UpdateSubscriptionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CancelSubscription(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.billing.v1.SubscriptionService/CancelSubscription',
            moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.CancelSubscriptionRequest.SerializeToString,
            moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.CancelSubscriptionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ConvertVendorSubscriptionIds(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.billing.v1.SubscriptionService/ConvertVendorSubscriptionIds',
            moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ConvertVendorSubscriptionIdsRequest.SerializeToString,
            moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ConvertVendorSubscriptionIdsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSubscription(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.billing.v1.SubscriptionService/GetSubscription',
            moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.GetSubscriptionRequest.SerializeToString,
            moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.GetSubscriptionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ScheduleNextBillingCycle(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.billing.v1.SubscriptionService/ScheduleNextBillingCycle',
            moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ScheduleNextBillingCycleRequest.SerializeToString,
            moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ScheduleNextBillingCycleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListSubscriptions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.billing.v1.SubscriptionService/ListSubscriptions',
            moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ListSubscriptionsRequest.SerializeToString,
            moego_dot_service_dot_billing_dot_v1_dot_subscription__service__pb2.ListSubscriptionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
