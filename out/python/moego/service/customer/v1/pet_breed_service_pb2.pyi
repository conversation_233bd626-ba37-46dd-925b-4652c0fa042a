from google.protobuf import empty_pb2 as _empty_pb2
from moego.models.customer.v1 import customer_pet_breed_models_pb2 as _customer_pet_breed_models_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class PetBreedListOutput(_message.Message):
    __slots__ = ("pet_breed_list",)
    PET_BREED_LIST_FIELD_NUMBER: _ClassVar[int]
    pet_breed_list: _containers.RepeatedCompositeFieldContainer[_customer_pet_breed_models_pb2.PetBreedModel]
    def __init__(self, pet_breed_list: _Optional[_Iterable[_Union[_customer_pet_breed_models_pb2.PetBreedModel, _Mapping]]] = ...) -> None: ...
