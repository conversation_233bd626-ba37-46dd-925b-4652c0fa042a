from moego.models.automation.v1 import filter_pb2 as _filter_pb2
from moego.models.automation.v1 import workflow_pb2 as _workflow_pb2
from moego.models.automation.v1 import workflow_defs_pb2 as _workflow_defs_pb2
from moego.models.business_customer.v1 import business_customer_models_pb2 as _business_customer_models_pb2
from moego.models.reporting.v2 import common_model_pb2 as _common_model_pb2
from moego.models.reporting.v2 import filter_model_pb2 as _filter_model_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetWorkflowConfigRequest(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GetWorkflowConfigResponse(_message.Message):
    __slots__ = ("workflow_configs", "filter_groups", "event_filter_groups")
    WORKFLOW_CONFIGS_FIELD_NUMBER: _ClassVar[int]
    FILTER_GROUPS_FIELD_NUMBER: _ClassVar[int]
    EVENT_FILTER_GROUPS_FIELD_NUMBER: _ClassVar[int]
    workflow_configs: _containers.RepeatedCompositeFieldContainer[_workflow_pb2.WorkflowConfig]
    filter_groups: _containers.RepeatedCompositeFieldContainer[_filter_model_pb2.FilterGroup]
    event_filter_groups: _containers.RepeatedCompositeFieldContainer[_filter_pb2.EventFilterGroups]
    def __init__(self, workflow_configs: _Optional[_Iterable[_Union[_workflow_pb2.WorkflowConfig, _Mapping]]] = ..., filter_groups: _Optional[_Iterable[_Union[_filter_model_pb2.FilterGroup, _Mapping]]] = ..., event_filter_groups: _Optional[_Iterable[_Union[_filter_pb2.EventFilterGroups, _Mapping]]] = ...) -> None: ...

class CreateWorkflowRequest(_message.Message):
    __slots__ = ("workflow",)
    WORKFLOW_FIELD_NUMBER: _ClassVar[int]
    workflow: _workflow_defs_pb2.CreateWorkflowDef
    def __init__(self, workflow: _Optional[_Union[_workflow_defs_pb2.CreateWorkflowDef, _Mapping]] = ...) -> None: ...

class CreateWorkflowResponse(_message.Message):
    __slots__ = ("workflow",)
    WORKFLOW_FIELD_NUMBER: _ClassVar[int]
    workflow: _workflow_pb2.Workflow
    def __init__(self, workflow: _Optional[_Union[_workflow_pb2.Workflow, _Mapping]] = ...) -> None: ...

class UpdateWorkflowContentRequest(_message.Message):
    __slots__ = ("workflow_id", "steps", "consumer_data")
    WORKFLOW_ID_FIELD_NUMBER: _ClassVar[int]
    STEPS_FIELD_NUMBER: _ClassVar[int]
    CONSUMER_DATA_FIELD_NUMBER: _ClassVar[int]
    workflow_id: int
    steps: _containers.RepeatedCompositeFieldContainer[_workflow_defs_pb2.CreateStepDef]
    consumer_data: _workflow_pb2.Workflow.ConsumerData
    def __init__(self, workflow_id: _Optional[int] = ..., steps: _Optional[_Iterable[_Union[_workflow_defs_pb2.CreateStepDef, _Mapping]]] = ..., consumer_data: _Optional[_Union[_workflow_pb2.Workflow.ConsumerData, _Mapping]] = ...) -> None: ...

class UpdateWorkflowContentResponse(_message.Message):
    __slots__ = ("workflow",)
    WORKFLOW_FIELD_NUMBER: _ClassVar[int]
    workflow: _workflow_pb2.Workflow
    def __init__(self, workflow: _Optional[_Union[_workflow_pb2.Workflow, _Mapping]] = ...) -> None: ...

class UpdateWorkflowInfoRequest(_message.Message):
    __slots__ = ("workflow_id", "name", "desc", "status", "setting", "workflow_enterprise_apply", "shut_down_pending_steps")
    WORKFLOW_ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    DESC_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SETTING_FIELD_NUMBER: _ClassVar[int]
    WORKFLOW_ENTERPRISE_APPLY_FIELD_NUMBER: _ClassVar[int]
    SHUT_DOWN_PENDING_STEPS_FIELD_NUMBER: _ClassVar[int]
    workflow_id: int
    name: str
    desc: str
    status: _workflow_pb2.Workflow.Status
    setting: _workflow_pb2.WorkflowSetting
    workflow_enterprise_apply: _workflow_pb2.WorkflowEnterpriseApply
    shut_down_pending_steps: bool
    def __init__(self, workflow_id: _Optional[int] = ..., name: _Optional[str] = ..., desc: _Optional[str] = ..., status: _Optional[_Union[_workflow_pb2.Workflow.Status, str]] = ..., setting: _Optional[_Union[_workflow_pb2.WorkflowSetting, _Mapping]] = ..., workflow_enterprise_apply: _Optional[_Union[_workflow_pb2.WorkflowEnterpriseApply, _Mapping]] = ..., shut_down_pending_steps: bool = ...) -> None: ...

class UpdateWorkflowInfoResponse(_message.Message):
    __slots__ = ("workflow",)
    WORKFLOW_FIELD_NUMBER: _ClassVar[int]
    workflow: _workflow_pb2.Workflow
    def __init__(self, workflow: _Optional[_Union[_workflow_pb2.Workflow, _Mapping]] = ...) -> None: ...

class ListWorkflowCategoriesRequest(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ListWorkflowCategoriesResponse(_message.Message):
    __slots__ = ("categories",)
    CATEGORIES_FIELD_NUMBER: _ClassVar[int]
    categories: _containers.RepeatedCompositeFieldContainer[_workflow_pb2.WorkflowCategory]
    def __init__(self, categories: _Optional[_Iterable[_Union[_workflow_pb2.WorkflowCategory, _Mapping]]] = ...) -> None: ...

class ListWorkflowsRequest(_message.Message):
    __slots__ = ("pagination", "filter")
    class Filter(_message.Message):
        __slots__ = ("name", "status", "category_id")
        NAME_FIELD_NUMBER: _ClassVar[int]
        STATUS_FIELD_NUMBER: _ClassVar[int]
        CATEGORY_ID_FIELD_NUMBER: _ClassVar[int]
        name: str
        status: _containers.RepeatedScalarFieldContainer[_workflow_pb2.Workflow.Status]
        category_id: int
        def __init__(self, name: _Optional[str] = ..., status: _Optional[_Iterable[_Union[_workflow_pb2.Workflow.Status, str]]] = ..., category_id: _Optional[int] = ...) -> None: ...
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationRequest
    filter: ListWorkflowsRequest.Filter
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., filter: _Optional[_Union[ListWorkflowsRequest.Filter, _Mapping]] = ...) -> None: ...

class ListWorkflowsResponse(_message.Message):
    __slots__ = ("workflows", "pagination")
    WORKFLOWS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    workflows: _containers.RepeatedCompositeFieldContainer[_workflow_pb2.Workflow]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, workflows: _Optional[_Iterable[_Union[_workflow_pb2.Workflow, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class ListEnterpriseWorkflowsRequest(_message.Message):
    __slots__ = ("pagination", "filter")
    class Filter(_message.Message):
        __slots__ = ("name", "status", "tenants_group_ids", "tenants_ids")
        NAME_FIELD_NUMBER: _ClassVar[int]
        STATUS_FIELD_NUMBER: _ClassVar[int]
        TENANTS_GROUP_IDS_FIELD_NUMBER: _ClassVar[int]
        TENANTS_IDS_FIELD_NUMBER: _ClassVar[int]
        name: str
        status: _containers.RepeatedScalarFieldContainer[_workflow_pb2.Workflow.Status]
        tenants_group_ids: _containers.RepeatedScalarFieldContainer[int]
        tenants_ids: _containers.RepeatedScalarFieldContainer[int]
        def __init__(self, name: _Optional[str] = ..., status: _Optional[_Iterable[_Union[_workflow_pb2.Workflow.Status, str]]] = ..., tenants_group_ids: _Optional[_Iterable[int]] = ..., tenants_ids: _Optional[_Iterable[int]] = ...) -> None: ...
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationRequest
    filter: ListEnterpriseWorkflowsRequest.Filter
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., filter: _Optional[_Union[ListEnterpriseWorkflowsRequest.Filter, _Mapping]] = ...) -> None: ...

class ListEnterpriseWorkflowsResponse(_message.Message):
    __slots__ = ("workflows", "pagination")
    WORKFLOWS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    workflows: _containers.RepeatedCompositeFieldContainer[_workflow_pb2.Workflow]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, workflows: _Optional[_Iterable[_Union[_workflow_pb2.Workflow, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class ListWorkflowRecordsRequest(_message.Message):
    __slots__ = ("workflow_id", "pagination", "filter")
    class Filter(_message.Message):
        __slots__ = ("customer_name",)
        CUSTOMER_NAME_FIELD_NUMBER: _ClassVar[int]
        customer_name: str
        def __init__(self, customer_name: _Optional[str] = ...) -> None: ...
    WORKFLOW_ID_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    workflow_id: int
    pagination: _pagination_messages_pb2.PaginationRequest
    filter: ListWorkflowRecordsRequest.Filter
    def __init__(self, workflow_id: _Optional[int] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., filter: _Optional[_Union[ListWorkflowRecordsRequest.Filter, _Mapping]] = ...) -> None: ...

class ListWorkflowRecordsResponse(_message.Message):
    __slots__ = ("workflow_records", "pagination")
    WORKFLOW_RECORDS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    workflow_records: _containers.RepeatedCompositeFieldContainer[_workflow_pb2.WorkflowRecord]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, workflow_records: _Optional[_Iterable[_Union[_workflow_pb2.WorkflowRecord, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class ListWorkflowTemplatesRequest(_message.Message):
    __slots__ = ("pagination", "filter")
    class Filter(_message.Message):
        __slots__ = ("name", "category_id", "recommend_type")
        NAME_FIELD_NUMBER: _ClassVar[int]
        CATEGORY_ID_FIELD_NUMBER: _ClassVar[int]
        RECOMMEND_TYPE_FIELD_NUMBER: _ClassVar[int]
        name: str
        category_id: int
        recommend_type: _workflow_pb2.Workflow.RecommendType
        def __init__(self, name: _Optional[str] = ..., category_id: _Optional[int] = ..., recommend_type: _Optional[_Union[_workflow_pb2.Workflow.RecommendType, str]] = ...) -> None: ...
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationRequest
    filter: ListWorkflowTemplatesRequest.Filter
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., filter: _Optional[_Union[ListWorkflowTemplatesRequest.Filter, _Mapping]] = ...) -> None: ...

class ListWorkflowTemplatesResponse(_message.Message):
    __slots__ = ("workflows", "pagination")
    WORKFLOWS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    workflows: _containers.RepeatedCompositeFieldContainer[_workflow_pb2.Workflow]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, workflows: _Optional[_Iterable[_Union[_workflow_pb2.Workflow, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class GetWorkflowInfoRequest(_message.Message):
    __slots__ = ("workflow_id",)
    WORKFLOW_ID_FIELD_NUMBER: _ClassVar[int]
    workflow_id: int
    def __init__(self, workflow_id: _Optional[int] = ...) -> None: ...

class GetWorkflowInfoResponse(_message.Message):
    __slots__ = ("workflow",)
    WORKFLOW_FIELD_NUMBER: _ClassVar[int]
    workflow: _workflow_pb2.Workflow
    def __init__(self, workflow: _Optional[_Union[_workflow_pb2.Workflow, _Mapping]] = ...) -> None: ...

class GetWorkflowTemplateInfoRequest(_message.Message):
    __slots__ = ("workflow_id",)
    WORKFLOW_ID_FIELD_NUMBER: _ClassVar[int]
    workflow_id: int
    def __init__(self, workflow_id: _Optional[int] = ...) -> None: ...

class GetWorkflowTemplateInfoResponse(_message.Message):
    __slots__ = ("workflow",)
    WORKFLOW_FIELD_NUMBER: _ClassVar[int]
    workflow: _workflow_pb2.Workflow
    def __init__(self, workflow: _Optional[_Union[_workflow_pb2.Workflow, _Mapping]] = ...) -> None: ...

class CreateWorkflowTemplateRequest(_message.Message):
    __slots__ = ("workflow",)
    WORKFLOW_FIELD_NUMBER: _ClassVar[int]
    workflow: _workflow_defs_pb2.CreateWorkflowDef
    def __init__(self, workflow: _Optional[_Union[_workflow_defs_pb2.CreateWorkflowDef, _Mapping]] = ...) -> None: ...

class CreateWorkflowTemplateResponse(_message.Message):
    __slots__ = ("workflow",)
    WORKFLOW_FIELD_NUMBER: _ClassVar[int]
    workflow: _workflow_pb2.Workflow
    def __init__(self, workflow: _Optional[_Union[_workflow_pb2.Workflow, _Mapping]] = ...) -> None: ...

class UpdateWorkflowSettingRequest(_message.Message):
    __slots__ = ("company_id", "setting")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    SETTING_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    setting: _workflow_pb2.WorkflowSetting
    def __init__(self, company_id: _Optional[int] = ..., setting: _Optional[_Union[_workflow_pb2.WorkflowSetting, _Mapping]] = ...) -> None: ...

class UpdateWorkflowSettingResponse(_message.Message):
    __slots__ = ("setting",)
    SETTING_FIELD_NUMBER: _ClassVar[int]
    setting: _workflow_pb2.WorkflowSetting
    def __init__(self, setting: _Optional[_Union[_workflow_pb2.WorkflowSetting, _Mapping]] = ...) -> None: ...

class GetWorkflowSettingRequest(_message.Message):
    __slots__ = ("company_id",)
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    def __init__(self, company_id: _Optional[int] = ...) -> None: ...

class GetWorkflowSettingResponse(_message.Message):
    __slots__ = ("setting",)
    SETTING_FIELD_NUMBER: _ClassVar[int]
    setting: _workflow_pb2.WorkflowSetting
    def __init__(self, setting: _Optional[_Union[_workflow_pb2.WorkflowSetting, _Mapping]] = ...) -> None: ...

class FilterCustomerRequest(_message.Message):
    __slots__ = ("filters", "pagination", "company_id", "tenants_ids", "customer_id")
    FILTERS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    TENANTS_IDS_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    filters: _containers.RepeatedCompositeFieldContainer[_common_model_pb2.FilterRequest]
    pagination: _pagination_messages_pb2.PaginationRequest
    company_id: int
    tenants_ids: _containers.RepeatedScalarFieldContainer[int]
    customer_id: int
    def __init__(self, filters: _Optional[_Iterable[_Union[_common_model_pb2.FilterRequest, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., company_id: _Optional[int] = ..., tenants_ids: _Optional[_Iterable[int]] = ..., customer_id: _Optional[int] = ...) -> None: ...

class FilterCustomerResponse(_message.Message):
    __slots__ = ("customer", "pagination")
    CUSTOMER_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    customer: _containers.RepeatedCompositeFieldContainer[_business_customer_models_pb2.BusinessCustomerInfoModel]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, customer: _Optional[_Iterable[_Union[_business_customer_models_pb2.BusinessCustomerInfoModel, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...
