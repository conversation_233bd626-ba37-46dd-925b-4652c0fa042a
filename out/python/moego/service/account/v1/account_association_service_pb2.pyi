from moego.models.account.v1 import account_association_defs_pb2 as _account_association_defs_pb2
from moego.models.account.v1 import account_association_enums_pb2 as _account_association_enums_pb2
from moego.models.account.v1 import account_association_models_pb2 as _account_association_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetAccountAssociationRequest(_message.Message):
    __slots__ = ("account_and_platform", "platform_account")
    ACCOUNT_AND_PLATFORM_FIELD_NUMBER: _ClassVar[int]
    PLATFORM_ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    account_and_platform: _account_association_defs_pb2.AccountAndPlatformQueryDef
    platform_account: _account_association_defs_pb2.PlatformAccountQueryDef
    def __init__(self, account_and_platform: _Optional[_Union[_account_association_defs_pb2.AccountAndPlatformQueryDef, _Mapping]] = ..., platform_account: _Optional[_Union[_account_association_defs_pb2.PlatformAccountQueryDef, _Mapping]] = ...) -> None: ...

class GetAccountAssociationResponse(_message.Message):
    __slots__ = ("association",)
    ASSOCIATION_FIELD_NUMBER: _ClassVar[int]
    association: _account_association_models_pb2.AccountAssociationModel
    def __init__(self, association: _Optional[_Union[_account_association_models_pb2.AccountAssociationModel, _Mapping]] = ...) -> None: ...

class ListAccountAssociationRequest(_message.Message):
    __slots__ = ("account_id", "platform")
    ACCOUNT_ID_FIELD_NUMBER: _ClassVar[int]
    PLATFORM_FIELD_NUMBER: _ClassVar[int]
    account_id: int
    platform: _account_association_enums_pb2.AccountAssociationPlatform
    def __init__(self, account_id: _Optional[int] = ..., platform: _Optional[_Union[_account_association_enums_pb2.AccountAssociationPlatform, str]] = ...) -> None: ...

class ListAccountAssociationResponse(_message.Message):
    __slots__ = ("associations",)
    ASSOCIATIONS_FIELD_NUMBER: _ClassVar[int]
    associations: _containers.RepeatedCompositeFieldContainer[_account_association_models_pb2.AccountAssociationModel]
    def __init__(self, associations: _Optional[_Iterable[_Union[_account_association_models_pb2.AccountAssociationModel, _Mapping]]] = ...) -> None: ...

class AddAccountAssociationRequest(_message.Message):
    __slots__ = ("association",)
    ASSOCIATION_FIELD_NUMBER: _ClassVar[int]
    association: _account_association_defs_pb2.AccountAssociationCreateDef
    def __init__(self, association: _Optional[_Union[_account_association_defs_pb2.AccountAssociationCreateDef, _Mapping]] = ...) -> None: ...

class AddAccountAssociationResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ResetAccountAssociationRequest(_message.Message):
    __slots__ = ("association",)
    ASSOCIATION_FIELD_NUMBER: _ClassVar[int]
    association: _account_association_defs_pb2.AccountAssociationCreateDef
    def __init__(self, association: _Optional[_Union[_account_association_defs_pb2.AccountAssociationCreateDef, _Mapping]] = ...) -> None: ...

class ResetAccountAssociationResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
