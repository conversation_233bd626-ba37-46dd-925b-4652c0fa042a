# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/account/v1/session_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/account/v1/session_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from moego.models.account.v1 import session_models_pb2 as moego_dot_models_dot_account_dot_v1_dot_session__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.moego/service/account/v1/session_service.proto\x12\x18moego.service.account.v1\x1a\x1egoogle/protobuf/duration.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a,moego/models/account/v1/session_models.proto\x1a\x17validate/validate.proto\"M\n\x16GetSessionTokenRequest\x12/\n\rsession_token\x18\x01 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\x80\x02R\x0csessionToken:\x02\x18\x01\"k\n\x11GetSessionRequest\x12\x10\n\x02id\x18\x01 \x01(\x03H\x00R\x02id\x12\x31\n\rsession_token\x18\x02 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\x80\x02H\x00R\x0csessionTokenB\x11\n\nidentifier\x12\x03\xf8\x42\x01\"\x9b\x05\n\x14\x43reateSessionRequest\x12\x1d\n\naccount_id\x18\x01 \x01(\x03R\taccountId\x12\x17\n\x02ip\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02p\x01R\x02ip\x12\'\n\nuser_agent\x18\x03 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x02R\tuserAgent\x12.\n\x0creferer_link\x18\x04 \x01(\tB\x0b\xfa\x42\x08r\x06\x18\x80\x02\xd0\x01\x01R\x0brefererLink\x12,\n\x12referer_session_id\x18\x05 \x01(\x03R\x10refererSessionId\x12$\n\tdevice_id\x18\r \x01(\tB\x07\xfa\x42\x04r\x02\x18@R\x08\x64\x65viceId\x12+\n\x0cimpersonator\x18\x06 \x01(\tB\x07\xfa\x42\x04r\x02\x18@R\x0cimpersonator\x12>\n\x07max_age\x18\x07 \x01(\x0b\x32\x19.google.protobuf.DurationB\n\xfa\x42\x07\xaa\x01\x04\x08\x01*\x00R\x06maxAge\x12!\n\x06source\x18\x08 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x32R\x06source\x12!\n\trenewable\x18\t \x01(\x08H\x00R\trenewable\x88\x01\x01\x12\x35\n\x14\x61llow_frozen_account\x18\x0b \x01(\x08H\x01R\x12\x61llowFrozenAccount\x88\x01\x01\x12\x37\n\x15\x61llow_deleted_account\x18\x0c \x01(\x08H\x02R\x13\x61llowDeletedAccount\x88\x01\x01\x12:\n\x0csession_data\x18\n \x01(\x0b\x32\x17.google.protobuf.StructR\x0bsessionDataB\x0c\n\n_renewableB\x17\n\x15_allow_frozen_accountB\x18\n\x16_allow_deleted_account\"\xd7\x03\n\x14UpdateSessionRequest\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1c\n\x02ip\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02p\x01H\x00R\x02ip\x88\x01\x01\x12,\n\nuser_agent\x18\x03 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x02H\x01R\tuserAgent\x88\x01\x01\x12\"\n\naccount_id\x18\x04 \x01(\x03H\x02R\taccountId\x88\x01\x01\x12\x31\n\x12referer_session_id\x18\x05 \x01(\x03H\x03R\x10refererSessionId\x88\x01\x01\x12\x41\n\x07max_age\x18\x07 \x01(\x0b\x32\x19.google.protobuf.DurationB\x08\xfa\x42\x05\xaa\x01\x02*\x00H\x04R\x06maxAge\x88\x01\x01\x12!\n\trenewable\x18\t \x01(\x08H\x05R\trenewable\x88\x01\x01\x12?\n\x0csession_data\x18\n \x01(\x0b\x32\x17.google.protobuf.StructH\x06R\x0bsessionData\x88\x01\x01\x42\x05\n\x03_ipB\r\n\x0b_user_agentB\r\n\x0b_account_idB\x15\n\x13_referer_session_idB\n\n\x08_max_ageB\x0c\n\n_renewableB\x0f\n\r_session_data\"\xfc\x04\n\x19\x42\x61tchUpdateSessionRequest\x12\x14\n\x03ids\x18\x01 \x03(\x03\x42\x02\x18\x01R\x03ids\x12&\n\naccount_id\x18\x02 \x01(\x03\x42\x02\x18\x01H\x01R\taccountId\x88\x01\x01\x12\x31\n\x12referer_session_id\x18\x03 \x01(\x03H\x02R\x10refererSessionId\x88\x01\x01\x12\x41\n\x07max_age\x18\x04 \x01(\x0b\x32\x19.google.protobuf.DurationB\x08\xfa\x42\x05\xaa\x01\x02*\x00H\x03R\x06maxAge\x88\x01\x01\x12R\n\x06\x62y_ids\x18\n \x01(\x0b\x32\x39.moego.service.account.v1.BatchUpdateSessionRequest.ByIdsH\x00R\x05\x62yIds\x12{\n\x15\x62y_account_and_source\x18\x0b \x01(\x0b\x32\x46.moego.service.account.v1.BatchUpdateSessionRequest.ByAccountAndSourceH\x00R\x12\x62yAccountAndSource\x1a.\n\x05\x42yIds\x12%\n\x03ids\x18\x01 \x03(\x03\x42\x13\xfa\x42\x10\x92\x01\r\x08\x01\x10\xe8\x07\x18\x01\"\x04\"\x02 \x00R\x03ids\x1a\x66\n\x12\x42yAccountAndSource\x12\x1d\n\naccount_id\x18\x01 \x01(\x03R\taccountId\x12&\n\x06source\x18\x02 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x32H\x00R\x06source\x88\x01\x01\x42\t\n\x07_sourceB\x10\n\tcondition\x12\x03\xf8\x42\x01\x42\r\n\x0b_account_idB\x15\n\x13_referer_session_idB\n\n\x08_max_age\"c\n\x1a\x42\x61tchUpdateSessionResponse\x12\x45\n\x08sessions\x18\x01 \x03(\x0b\x32%.moego.models.account.v1.SessionModelB\x02\x18\x01R\x08sessions\"q\n\x15\x43reateSessionResponse\x12#\n\rsession_token\x18\x01 \x01(\tR\x0csessionToken\x12\x0e\n\x02id\x18\x02 \x01(\x03R\x02id\x12#\n\rfirst_session\x18\x03 \x01(\x08R\x0c\x66irstSession\"\x89\x01\n\x16ValidateSessionRequest\x12-\n\rsession_token\x18\x01 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x02R\x0csessionToken\x12\x17\n\x02ip\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02p\x01R\x02ip\x12\'\n\nuser_agent\x18\x03 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x02R\tuserAgent\"t\n GetSessionListByAccountIdRequest\x12\x1d\n\naccount_id\x18\x01 \x01(\x03R\taccountId\x12\x31\n\x14include_impersonator\x18\x02 \x01(\x08R\x13includeImpersonator\"f\n!GetSessionListByAccountIdResponse\x12\x41\n\x08sessions\x18\x01 \x03(\x0b\x32%.moego.models.account.v1.SessionModelR\x08sessions\"*\n\x18\x44\x65leteSessionByIdRequest\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\"\x81\x01\n#DeleteAllSessionsByAccountIdRequest\x12\x1d\n\naccount_id\x18\x01 \x01(\x03R\taccountId\x12;\n\x13ignored_session_ids\x18\x02 \x03(\x03\x42\x0b\xfa\x42\x08\x92\x01\x05\x10\xf4\x03(\x01R\x11ignoredSessionIds2\xaf\x07\n\x0eSessionService\x12q\n\x11GetSessionByToken\x12\x30.moego.service.account.v1.GetSessionTokenRequest\x1a%.moego.models.account.v1.SessionModel\"\x03\x88\x02\x01\x12`\n\nGetSession\x12+.moego.service.account.v1.GetSessionRequest\x1a%.moego.models.account.v1.SessionModel\x12\x94\x01\n\x19GetSessionListByAccountId\x12:.moego.service.account.v1.GetSessionListByAccountIdRequest\x1a;.moego.service.account.v1.GetSessionListByAccountIdResponse\x12p\n\rCreateSession\x12..moego.service.account.v1.CreateSessionRequest\x1a/.moego.service.account.v1.CreateSessionResponse\x12\x66\n\rUpdateSession\x12..moego.service.account.v1.UpdateSessionRequest\x1a%.moego.models.account.v1.SessionModel\x12\x7f\n\x12\x42\x61tchUpdateSession\x12\x33.moego.service.account.v1.BatchUpdateSessionRequest\x1a\x34.moego.service.account.v1.BatchUpdateSessionResponse\x12_\n\x11\x44\x65leteSessionById\x12\x32.moego.service.account.v1.DeleteSessionByIdRequest\x1a\x16.google.protobuf.Empty\x12u\n\x1c\x44\x65leteAllSessionsByAccountId\x12=.moego.service.account.v1.DeleteAllSessionsByAccountIdRequest\x1a\x16.google.protobuf.EmptyB\x80\x01\n com.moego.idl.service.account.v1P\x01ZZgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1;accountsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.account.v1.session_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.service.account.v1P\001ZZgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1;accountsvcpb'
  _globals['_GETSESSIONTOKENREQUEST'].fields_by_name['session_token']._loaded_options = None
  _globals['_GETSESSIONTOKENREQUEST'].fields_by_name['session_token']._serialized_options = b'\372B\007r\005\020\001\030\200\002'
  _globals['_GETSESSIONTOKENREQUEST']._loaded_options = None
  _globals['_GETSESSIONTOKENREQUEST']._serialized_options = b'\030\001'
  _globals['_GETSESSIONREQUEST'].oneofs_by_name['identifier']._loaded_options = None
  _globals['_GETSESSIONREQUEST'].oneofs_by_name['identifier']._serialized_options = b'\370B\001'
  _globals['_GETSESSIONREQUEST'].fields_by_name['session_token']._loaded_options = None
  _globals['_GETSESSIONREQUEST'].fields_by_name['session_token']._serialized_options = b'\372B\007r\005\020\001\030\200\002'
  _globals['_CREATESESSIONREQUEST'].fields_by_name['ip']._loaded_options = None
  _globals['_CREATESESSIONREQUEST'].fields_by_name['ip']._serialized_options = b'\372B\004r\002p\001'
  _globals['_CREATESESSIONREQUEST'].fields_by_name['user_agent']._loaded_options = None
  _globals['_CREATESESSIONREQUEST'].fields_by_name['user_agent']._serialized_options = b'\372B\005r\003\030\200\002'
  _globals['_CREATESESSIONREQUEST'].fields_by_name['referer_link']._loaded_options = None
  _globals['_CREATESESSIONREQUEST'].fields_by_name['referer_link']._serialized_options = b'\372B\010r\006\030\200\002\320\001\001'
  _globals['_CREATESESSIONREQUEST'].fields_by_name['device_id']._loaded_options = None
  _globals['_CREATESESSIONREQUEST'].fields_by_name['device_id']._serialized_options = b'\372B\004r\002\030@'
  _globals['_CREATESESSIONREQUEST'].fields_by_name['impersonator']._loaded_options = None
  _globals['_CREATESESSIONREQUEST'].fields_by_name['impersonator']._serialized_options = b'\372B\004r\002\030@'
  _globals['_CREATESESSIONREQUEST'].fields_by_name['max_age']._loaded_options = None
  _globals['_CREATESESSIONREQUEST'].fields_by_name['max_age']._serialized_options = b'\372B\007\252\001\004\010\001*\000'
  _globals['_CREATESESSIONREQUEST'].fields_by_name['source']._loaded_options = None
  _globals['_CREATESESSIONREQUEST'].fields_by_name['source']._serialized_options = b'\372B\006r\004\020\001\0302'
  _globals['_UPDATESESSIONREQUEST'].fields_by_name['ip']._loaded_options = None
  _globals['_UPDATESESSIONREQUEST'].fields_by_name['ip']._serialized_options = b'\372B\004r\002p\001'
  _globals['_UPDATESESSIONREQUEST'].fields_by_name['user_agent']._loaded_options = None
  _globals['_UPDATESESSIONREQUEST'].fields_by_name['user_agent']._serialized_options = b'\372B\005r\003\030\200\002'
  _globals['_UPDATESESSIONREQUEST'].fields_by_name['max_age']._loaded_options = None
  _globals['_UPDATESESSIONREQUEST'].fields_by_name['max_age']._serialized_options = b'\372B\005\252\001\002*\000'
  _globals['_BATCHUPDATESESSIONREQUEST_BYIDS'].fields_by_name['ids']._loaded_options = None
  _globals['_BATCHUPDATESESSIONREQUEST_BYIDS'].fields_by_name['ids']._serialized_options = b'\372B\020\222\001\r\010\001\020\350\007\030\001\"\004\"\002 \000'
  _globals['_BATCHUPDATESESSIONREQUEST_BYACCOUNTANDSOURCE'].fields_by_name['source']._loaded_options = None
  _globals['_BATCHUPDATESESSIONREQUEST_BYACCOUNTANDSOURCE'].fields_by_name['source']._serialized_options = b'\372B\006r\004\020\001\0302'
  _globals['_BATCHUPDATESESSIONREQUEST'].oneofs_by_name['condition']._loaded_options = None
  _globals['_BATCHUPDATESESSIONREQUEST'].oneofs_by_name['condition']._serialized_options = b'\370B\001'
  _globals['_BATCHUPDATESESSIONREQUEST'].fields_by_name['ids']._loaded_options = None
  _globals['_BATCHUPDATESESSIONREQUEST'].fields_by_name['ids']._serialized_options = b'\030\001'
  _globals['_BATCHUPDATESESSIONREQUEST'].fields_by_name['account_id']._loaded_options = None
  _globals['_BATCHUPDATESESSIONREQUEST'].fields_by_name['account_id']._serialized_options = b'\030\001'
  _globals['_BATCHUPDATESESSIONREQUEST'].fields_by_name['max_age']._loaded_options = None
  _globals['_BATCHUPDATESESSIONREQUEST'].fields_by_name['max_age']._serialized_options = b'\372B\005\252\001\002*\000'
  _globals['_BATCHUPDATESESSIONRESPONSE'].fields_by_name['sessions']._loaded_options = None
  _globals['_BATCHUPDATESESSIONRESPONSE'].fields_by_name['sessions']._serialized_options = b'\030\001'
  _globals['_VALIDATESESSIONREQUEST'].fields_by_name['session_token']._loaded_options = None
  _globals['_VALIDATESESSIONREQUEST'].fields_by_name['session_token']._serialized_options = b'\372B\005r\003\030\200\002'
  _globals['_VALIDATESESSIONREQUEST'].fields_by_name['ip']._loaded_options = None
  _globals['_VALIDATESESSIONREQUEST'].fields_by_name['ip']._serialized_options = b'\372B\004r\002p\001'
  _globals['_VALIDATESESSIONREQUEST'].fields_by_name['user_agent']._loaded_options = None
  _globals['_VALIDATESESSIONREQUEST'].fields_by_name['user_agent']._serialized_options = b'\372B\005r\003\030\200\002'
  _globals['_DELETEALLSESSIONSBYACCOUNTIDREQUEST'].fields_by_name['ignored_session_ids']._loaded_options = None
  _globals['_DELETEALLSESSIONSBYACCOUNTIDREQUEST'].fields_by_name['ignored_session_ids']._serialized_options = b'\372B\010\222\001\005\020\364\003(\001'
  _globals['_SESSIONSERVICE'].methods_by_name['GetSessionByToken']._loaded_options = None
  _globals['_SESSIONSERVICE'].methods_by_name['GetSessionByToken']._serialized_options = b'\210\002\001'
  _globals['_GETSESSIONTOKENREQUEST']._serialized_start=238
  _globals['_GETSESSIONTOKENREQUEST']._serialized_end=315
  _globals['_GETSESSIONREQUEST']._serialized_start=317
  _globals['_GETSESSIONREQUEST']._serialized_end=424
  _globals['_CREATESESSIONREQUEST']._serialized_start=427
  _globals['_CREATESESSIONREQUEST']._serialized_end=1094
  _globals['_UPDATESESSIONREQUEST']._serialized_start=1097
  _globals['_UPDATESESSIONREQUEST']._serialized_end=1568
  _globals['_BATCHUPDATESESSIONREQUEST']._serialized_start=1571
  _globals['_BATCHUPDATESESSIONREQUEST']._serialized_end=2207
  _globals['_BATCHUPDATESESSIONREQUEST_BYIDS']._serialized_start=1989
  _globals['_BATCHUPDATESESSIONREQUEST_BYIDS']._serialized_end=2035
  _globals['_BATCHUPDATESESSIONREQUEST_BYACCOUNTANDSOURCE']._serialized_start=2037
  _globals['_BATCHUPDATESESSIONREQUEST_BYACCOUNTANDSOURCE']._serialized_end=2139
  _globals['_BATCHUPDATESESSIONRESPONSE']._serialized_start=2209
  _globals['_BATCHUPDATESESSIONRESPONSE']._serialized_end=2308
  _globals['_CREATESESSIONRESPONSE']._serialized_start=2310
  _globals['_CREATESESSIONRESPONSE']._serialized_end=2423
  _globals['_VALIDATESESSIONREQUEST']._serialized_start=2426
  _globals['_VALIDATESESSIONREQUEST']._serialized_end=2563
  _globals['_GETSESSIONLISTBYACCOUNTIDREQUEST']._serialized_start=2565
  _globals['_GETSESSIONLISTBYACCOUNTIDREQUEST']._serialized_end=2681
  _globals['_GETSESSIONLISTBYACCOUNTIDRESPONSE']._serialized_start=2683
  _globals['_GETSESSIONLISTBYACCOUNTIDRESPONSE']._serialized_end=2785
  _globals['_DELETESESSIONBYIDREQUEST']._serialized_start=2787
  _globals['_DELETESESSIONBYIDREQUEST']._serialized_end=2829
  _globals['_DELETEALLSESSIONSBYACCOUNTIDREQUEST']._serialized_start=2832
  _globals['_DELETEALLSESSIONSBYACCOUNTIDREQUEST']._serialized_end=2961
  _globals['_SESSIONSERVICE']._serialized_start=2964
  _globals['_SESSIONSERVICE']._serialized_end=3907
# @@protoc_insertion_point(module_scope)
