from moego.models.message.v1 import template_defs_pb2 as _template_defs_pb2
from moego.models.message.v1 import template_models_pb2 as _template_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class BatchCreateTemplatesRequest(_message.Message):
    __slots__ = ("templates",)
    TEMPLATES_FIELD_NUMBER: _ClassVar[int]
    templates: _containers.RepeatedCompositeFieldContainer[_template_defs_pb2.BatchCreateTemplateItemDef]
    def __init__(self, templates: _Optional[_Iterable[_Union[_template_defs_pb2.BatchCreateTemplateItemDef, _Mapping]]] = ...) -> None: ...

class BatchCreateTemplatesResponse(_message.Message):
    __slots__ = ("templates",)
    class TemplatesEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: _template_models_pb2.TemplateModel
        def __init__(self, key: _Optional[int] = ..., value: _Optional[_Union[_template_models_pb2.TemplateModel, _Mapping]] = ...) -> None: ...
    TEMPLATES_FIELD_NUMBER: _ClassVar[int]
    templates: _containers.MessageMap[int, _template_models_pb2.TemplateModel]
    def __init__(self, templates: _Optional[_Mapping[int, _template_models_pb2.TemplateModel]] = ...) -> None: ...

class MGetTemplatesRequest(_message.Message):
    __slots__ = ("ids",)
    IDS_FIELD_NUMBER: _ClassVar[int]
    ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, ids: _Optional[_Iterable[int]] = ...) -> None: ...

class MGetTemplatesResponse(_message.Message):
    __slots__ = ("templates",)
    TEMPLATES_FIELD_NUMBER: _ClassVar[int]
    templates: _containers.RepeatedCompositeFieldContainer[_template_models_pb2.TemplateModel]
    def __init__(self, templates: _Optional[_Iterable[_Union[_template_models_pb2.TemplateModel, _Mapping]]] = ...) -> None: ...
