package com.moego.common.exception;

import static com.moego.common.exception.ExceptionHandlingUtil.handleCommonException;

import com.moego.common.enums.ResponseCodeEnum;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.Iterator;
import java.util.Set;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @since 2023/12/26
 */
@RestControllerAdvice
@Order(ValidationExceptionHandlerAdvice.ORDER)
public class ValidationExceptionHandlerAdvice {

    public static final int ORDER = ExceptionHandlerAdvice.ORDER - 10;

    @ExceptionHandler(value = ConstraintViolationException.class)
    public CommonException.CommonResponse handleConstraintViolationException(
            ConstraintViolationException ex, HttpServletResponse httpServletResponse) {
        Set<ConstraintViolation<?>> constraintViolations = ex.getConstraintViolations();
        Iterator<ConstraintViolation<?>> iterator = constraintViolations.iterator();
        StringBuilder sb = new StringBuilder();
        while (iterator.hasNext()) {
            ConstraintViolation<?> cvl = iterator.next();
            sb.append(cvl.getMessage());
            sb.append(";");
        }
        return handleCommonException(
                new CommonException(ResponseCodeEnum.PARAMS_ERROR, sb.toString()), httpServletResponse);
    }
}
