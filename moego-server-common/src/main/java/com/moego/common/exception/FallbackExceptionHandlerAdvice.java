package com.moego.common.exception;

import com.moego.common.enums.ResponseCodeEnum;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.TypeMismatchException;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;

/**
 * Fallback exception handler, process exceptions that not handled by {@link ExceptionHandlerAdvice}, e.g. {@link Exception}.
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
@Order(FallbackExceptionHandlerAdvice.ORDER)
public class FallbackExceptionHandlerAdvice {

    /**
     * After {@link ExceptionHandlerAdvice}.
     */
    public static final int ORDER = ExceptionHandlerAdvice.ORDER + 1;

    @ExceptionHandler(Exception.class)
    public CommonException.CommonResponse handleRuntimeException(
            Exception ex, HttpServletResponse httpServletResponse) {
        // 根据org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver#doResolveException中的默认处理做兼容处理
        if (ex instanceof ServletRequestBindingException
                || /* MissingServletRequestParameterException 是 直接子类 */ ex instanceof TypeMismatchException
                || ex instanceof HttpMessageNotReadableException
                || ex instanceof MissingServletRequestPartException
                || ex instanceof BindException
                || ex instanceof HttpMessageNotWritableException) {
            return handleCommonException(
                    new CommonException(ResponseCodeEnum.PARAMS_ERROR, ex.getMessage(), ex), httpServletResponse);
        } else if (ex instanceof MultipartException) {
            // 文件过大异常，特殊处理
            String data = ex.getMessage();
            Throwable cause = ex.getCause();
            while (cause != null) {
                data = cause.getMessage();
                cause = cause.getCause();
            }
            String[] originMessages = Optional.ofNullable(data).orElse("").split(":");
            String errorMsg = originMessages[originMessages.length - 1];
            return handleCommonException(
                    new CommonException(ResponseCodeEnum.PARAMS_ERROR, errorMsg, ex), httpServletResponse);
        } else if (
        // 老旧库对非法请求的抛出异常类型，在 spring-4.3 中已经弃用，高版本统一使用 NoHandlerFoundException
        // ex instanceof org.springframework.web.servlet.mvc.multiaction.NoSuchRequestHandlingMethodException ||
        ex instanceof NoHandlerFoundException) {
            httpServletResponse.setStatus(HttpStatus.NOT_FOUND.value());
            return handleCommonException(
                    new CommonException(ResponseCodeEnum.PARAMS_ERROR, ex.getMessage(), ex), httpServletResponse);
        } else if (ex instanceof HttpRequestMethodNotSupportedException) {
            httpServletResponse.setStatus(HttpStatus.METHOD_NOT_ALLOWED.value());
            return handleCommonException(
                    new CommonException(ResponseCodeEnum.PARAMS_ERROR, ex.getMessage(), ex), httpServletResponse);
        }

        httpServletResponse.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return handleCommonException(
                new CommonException(ResponseCodeEnum.SERVER_ERROR, ex.getMessage(), ex), httpServletResponse);
    }

    private static CommonException.CommonResponse handleCommonException(
            CommonException commonException, HttpServletResponse httpServletResponse) {
        return ExceptionHandlingUtil.handleCommonException(commonException, httpServletResponse);
    }
}
