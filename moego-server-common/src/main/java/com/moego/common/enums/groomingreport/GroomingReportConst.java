package com.moego.common.enums.groomingreport;

import java.util.List;

public interface GroomingReportConst {
    String DEFAULT_THEME_COLOR = "#F96B18";
    String DEFAULT_LIGHT_THEME_COLOR = "#FEEFE6";
    String DEFAULT_THANK_YOU_MESSAGE = "Thank you for trusting and supporting us!";

    String DEFAULT_TITLE = "Grooming Report";

    String DEFAULT_THEME_CODE = "Default";

    String QUESTION_KEY_OVERALL_FEEDBACK = "overall_feedback";
    String QUESTION_KEY_COMMENT = "comment";
    String QUESTION_KEY_MOOD = "mood";
    String QUESTION_KEY_CUSTOMIZED_FEEDBACK = "customized_feedback";

    Integer SENDING_MANUALLY = 1;
    Integer SENDING_AUTOMATICALLY = 2;

    byte SEND_BY_SMS = 1;
    byte SEND_BY_EMAIL = 2;

    // uuid prefix for share
    String SHARE_UUID_PREFIX = "s_";

    // sample data
    String SAMPLE_COMMENT = "You can tell from the wagging tail!!!";

    String SAMPLE_PET_NAME = "Demo";
    String SAMPLE_PET_BREED = "Demo breed";
    String SAMPLE_PET_WEIGHT = "15";
    String SAMPLE_PET_AVATAR =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16863058128431eee721074570b411ca6ef22bcc45.png?name=pet-avatar.png";

    String SAMPLE_STAFF_FIRST_NAME = "Test";
    String SAMPLE_STAFF_LAST_NAME = "Groomer";

    Integer SAMPLE_APPOINTMENT_START_TIME = 570;
    Integer SAMPLE_APPOINTMENT_END_TIME = 630;
    String SAMPLE_SERVICE_NAME = "Full grooming - small";
    Integer SAMPLE_SERVICE_DURATION = 60;
    String SAMPLE_ADDON_NAME = "Special shampoo";
    Integer SAMPLE_ADDON_DURATION = 0;

    Integer SAMPLE_CUSTOMER_FREQUENCY = 28;

    String SAMPLE_SHOWCASE_BEFORE =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/169044845699449bae8ffb43a2a0989095ca3ba68f.jpg?name=grooming-report-default-img-before.jpg";
    String SAMPLE_SHOWCASE_AFTER =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16904484632e511c9269244c9b8dc62c4fad3dc735.jpg?name=grooming-report-default-img-after.jpg";

    String BODY_VIEW_LEFT_CHOICE_PREFIX = "L_";
    String BODY_VIEW_RIGHT_CHOICE_PREFIX = "R_";

    String SAMPLE_BODY_VIEW_LEFT =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16842917382cc63b95f7784b4fb4d031c2698f4981.png?name=grooming-report-dog-leg.png";
    String SAMPLE_BODY_VIEW_RIGHT =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1684827394ca8c70eed6e748eb83f8e3829df937e8.png?name=grooming-report-dog.png";

    // body view 默认图
    String CAT_DEFAULT_BODY_VIEW_LEFT =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1684834434845adfa246c74be2ac8279ef4e6ff526.png?name=grooming-report-cat-left.png";
    String CAT_DEFAULT_BODY_VIEW_RIGHT =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16848344349f55a7cb351743b68502e20b7d1e58f9.png?name=grooming-report-cat-right.png";
    String DOG_DEFAULT_BODY_VIEW_LEFT =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1684834434759c680c6983403f84f5527a79f77156.png?name=grooming-report-dog-left.png";
    String DOG_DEFAULT_BODY_VIEW_RIGHT =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16848344340eb4cb52814d4fa0bd4a03eb211dd781.png?name=grooming-report-dog-right.png";

    String DEFAULT_CAT_AVATAR =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16853276441f02d8a176754ab8be5b6114a3e81f94.png?name=avatar-cat-default.png";

    String DEFAULT_DOG_AVATAR =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1685327644ac01acd1f67a4f9897f1d386a00205b4.png?name=avatar-dog-default.png";

    /**
     * @see <a href="https://moego.atlassian.net/jira/software/c/projects/ERP/issues/ERP-8136">ERP-8136</a>
     */
    List<String> PRESET_TAGS =
            List.of("Professional ✂️", "Patient & caring \uD83D\uDC97", "Clean & tidy \uD83E\uDDFC", "Responsive ☎️");

    byte APPOINTMENT_SHOW_ONLY_DATE = 1;
    byte APPOINTMENT_SHOW_DATE_AND_TIME = 2;

    byte THEME_STATUS_HIDE = 2;
    byte THEME_FLAG_GROWTH_ONLY = 1;

    byte THEME_TAG_NORMAL = 0;
    byte THEME_TAG_NEED_UPDATE_TO_GROWTH = 1;
}
