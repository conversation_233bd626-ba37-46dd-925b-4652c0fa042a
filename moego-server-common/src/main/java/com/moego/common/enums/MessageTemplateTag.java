package com.moego.common.enums;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public interface MessageTemplateTag {
    String TEMPLATE_TAG_AGREEMENT_LINK = "{agreementLink}";

    String AGREEMENT_LINK_TYPE = "agreementLink";
    String MEMBERSHIP_LINK_TYPE = "membershipLink";

    String UPCOMING_LINK_TYPE = "upcomingLink";

    String CUSTOMER_NAME_TYPE = "customerName";

    String CUSTOMER_CODE_TYPE = "customerCode";

    String PET_NAME_TYPE = "petName";

    String PET_PARENT_PORTAL_LINK_TYPE = "petParentPortalLink";

    String STORE_NAME_TYPE = "storeName";

    String BUSINESS_PHONE_NUMBER_TYPE = "businessPhoneNumber";

    String ONLINE_BOOKING_LINK_TYPE = "onlineBookingLink";

    /**
     * match following parameters:
     * {agreementLink:agreement id:agreement name}
     * {upcomingLink}
     * {customerName}
     * {petName}
     */
    Pattern MASS_TEXT_PATTERN = Pattern.compile(
            "\\{(agreementLink):(\\d+):([^{}]+?)\\}|\\{(membershipLink):(\\d+):([^{}]+?)\\}|\\{upcomingLink\\}|\\{customerName\\}|\\{petName\\}|\\{petParentPortalLink\\}|\\{customerCode\\}|\\{storeName\\}|\\{businessPhoneNumber\\}|\\{onlineBookingLink\\}");

    /**
     * agreement link suffix, default 11 chars
     */
    String AGREEMENT_LINK_SUFFIX = "12345678901234567890123456789012";

    /**
     * upcoming link suffix, default 22 chars
     */
    String UPCOMING_LINK_SUFFIX = "1234567890123456789012";

    /**
     * pet parent portal link suffix, default 8 chars
     */
    String PET_PARENT_PORTAL_LINK_SUFFIX = "12345678";

    Pattern RECIPIENT_ID_PATTERN = Pattern.compile("\\{RecipientId: (\\d+)}");
}
