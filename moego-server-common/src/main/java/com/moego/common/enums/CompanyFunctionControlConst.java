package com.moego.common.enums;

public interface CompanyFunctionControlConst {
    Integer PREMIUM_TYPE_FREE = 0;
    Integer PREMIUM_TYPE_39 = 1;
    Integer PREMIUM_TYPE_69 = 2;

    // tier 级别 和plan version对应级别
    // TIER_LEVEL_FIRST 等于 PREMIUM_TYPE_39
    Integer TIER_LEVEL_FIRST = 1;
    // TIER_LEVEL_SECOND 等于 PREMIUM_TYPE_69
    Integer TIER_LEVEL_SECOND = 2;
    // tier第三级别的套餐表示
    Integer TIER_LEVEL_THIRD = 3;
    // tier第四级别 - Enterprise
    Integer TIER_LEVEL_FOURTH = 4;
    // staff num
    // free级别的staff num
    Integer TIER_FREE_STAFF_NUM = 1;
    // tier 第一级别的staff num
    Integer TIER_FIRST_STAFF_NUM = 3;
    // tier 第二级别的staff num
    Integer TIER_SECOND_STAFF_NUM = -1;
    // tier 第三级别的staff num
    Integer TIER_THIRD_STAFF_NUM = -1;
    // staff数量为-1时，代表无限制
    Integer STAFF_NUM_UNLIMITED = -1;
    Integer LEVEL_0 = 0;
    Integer LEVEL_1 = 1;
    Integer LEVEL_4 = 4;
    Integer LEVEL_5 = 5;
    Integer LEVEL_7 = 7;
    Integer LEVEL_8 = 8;
    Integer LEVEL_10 = 10;
    Integer LEVEL_100 = 100;
    Integer LEVEL_37 = 37;

    Integer LEVEL_1000 = 1000;
    Integer APPOINTMENT_NUMBER_100 = 100;
    Integer APPOINTMENT_NUMBER_UNLIMITED = -1;
    /**
     * 限制商家预约，查询的天数范围长度
     */
    Integer APPOINTMENT_QUERY_MONTHS = 1;

    Integer APPOINTMENT_QUERY_DAYS = 30;

    /**
     * enable_square：1-开启，2-在白名单中（新增值：当用户没有链接过square，需要这个值=2，才会显示square入口）
     */
    Byte SQUARE_ENABLE = 1;

    Byte SQUARE_ENABLE_WHITELIST = 2;

    /**
     * enable_stripe_reader: 是否启用 stripe reader, 0-关闭, 1-开启
     */
    Byte ENABLE_STRIPE_READER = 1;

    /**
     * retail_enable: 1 开启
     */
    Byte RETAIL_ENABLE = 1;

    Byte RETAIL_CLOSE = 0;

    /**
     * 是否是最新定价
     */
    Integer IS_NEW_PRICING = 1;

    /**
     * 购买套餐的最大单位数
     */
    Integer MAX_BUSINESS_NUM = 500;

    /**
     * new pricing plan version
     */
    Integer PLAN_VERSION_1 = 1;

    Integer PLAN_VERSION_2 = 2;
    Integer PLAN_VERSION_3 = 3;
    Integer PLAN_VERSION_4 = 4;
    Integer PLAN_VERSION_5 = 5;

    /**
     * 新旧pricing判断的关键level
     */
    Integer OLD_NEW_LEVEL = 100;

    Integer OLD = 100;

    Integer PLAN_VERSION_1_START_LEVEL = 1;
    Integer PLAN_VERSION_1_END_LEVEL = 6;

    Integer PLAN_VERSION_2_START_LEVEL = 7;
    Integer PLAN_VERSION_2_END_LEVEL = 100;
    // plan 3 最小level
    Integer PLAN_VERSION_3_START_LEVEL = 1000;
    // plan 3 最大level
    Integer PLAN_VERSION_3_END_LEVEL = 2000;

    // plan 3 第一级别的level对应值
    Integer PLAN_VERSION_3_MAX_TIER_FIRST_LEVEL = 1100;
    // plan 3 第二级别的level对应值
    Integer PLAN_VERSION_3_MAX_TIER_SECOND_LEVEL = 1200;
    // plan 3 第三级别的level对应值
    Integer PLAN_VERSION_3_MAX_TIER_THIRD_LEVEL = 1300;
    // plan 3 第四级别的level对应值
    Integer PLAN_VERSION_3_MAX_TIER_FOURTH_LEVEL = 1400;

    // tier 1默认的level值
    Integer TIER_FIRST_SOLO_PLAN_LEVEL = 1010;

    Integer TIER_FIRST_DEFAULT_LEVEL = 1001;
    Integer TIER_SECOND_DEFAULT_LEVEL = 1101;
    Integer TIER_SECOND_BD_LEVEL = 1102;
    Integer TIER_THIRD_DEFAULT_LEVEL = 1201;
    Integer TIER_THIRD_BD_LEVEL = 1202;
    Integer TIER_FORTH_BD_LEVEL = 1302;
    /**
     * 为了兼容1.0 和2.0 pricing的level，将100以下的level都转换为下面的level
     * trueLevel  oldLevel
     * 138 [1, 4]
     * 139 [5]
     * 169 [2, 3, 6]
     * 239 [7, 9]
     * 269 [8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37]
     */
    Integer TRUE_LEVEL_138 = 138;

    Integer TRUE_LEVEL_139 = 139;
    Integer TRUE_LEVEL_169 = 169;
    Integer TRUE_LEVEL_239 = 239;
    Integer TRUE_LEVEL_269 = 269;

    String PLAN_NAME_GROWTH = "Growth";
    String PLAN_NAME_Ultimate = "Ultimate";
    String PLAN_NAME_ENTERPRISE = "Enterprise";
}
